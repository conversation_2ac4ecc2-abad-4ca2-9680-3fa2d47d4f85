# Full CI/CD Pipeline with Lighthouse Testing
# This workflow runs complete testing including Lighthouse audits

name: Full CI with Lighthouse

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  workflow_dispatch: # Allow manual triggering

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [20.x]
        
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: |
        npm ci
        npm install -g wait-on@8.0.3
      
    - name: Mask potentially sensitive environment values
      run: |
        echo "::add-mask::eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1tb2NrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTY5NzU5NDc2MCwiZXhwIjoyMDU3NTk0NzYwfQ.mock-service-role-key-for-ci-testing"
        echo "::add-mask::ci-jwt-secret-minimum-32-characters-long-for-github-actions-security-testing"
        echo "::add-mask::ci-test-default-secret-minimum-32-characters-long-for-github-actions"
        echo "::add-mask::1x0000000000000000000000000000000AA"
        echo "🔒 Sensitive values masked from CI logs"
        
    - name: Set up CI environment
      run: |
        # Create CI environment file with safe mock values
        cat > .env.ci << 'EOF'
        # CI/CD Environment Configuration
        NEXT_PUBLIC_SITE_URL=http://localhost:3000
        
        # Mock Supabase Configuration (safe for CI)
        NEXT_PUBLIC_SUPABASE_URL=https://mock-project.supabase.co
        NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1tb2NrIiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTc1OTQ3NjAsImV4cCI6MjA1NzU5NDc2MH0.mock-anon-key-for-ci-testing
        SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1tb2NrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTY5NzU5NDc2MCwiZXhwIjoyMDU3NTk0NzYwfQ.mock-service-role-key-for-ci-testing
        
        # Test Configuration
        NODE_ENV=test
        CI=true
        ENABLE_IP_ALLOWLIST=false
        ENABLE_RATE_LIMITING=false
        ENABLE_SENTRY=false
        ENABLE_CAPTCHA=false
        TEST_MODE_BYPASS_AUTH=true
        
        # Mock secrets (32+ chars required)
        PARTNER_SECRET_DEFAULT=ci-test-default-secret-minimum-32-characters-long-for-github-actions
        JWT_SECRET=ci-jwt-secret-minimum-32-characters-long-for-github-actions-security-testing
        
        # Cloudflare test keys (always pass)
        NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
        TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA
        EOF
        
        # Copy to test environment
        cp .env.ci .env.test
        echo "✅ CI environment configured with safe mock values"
      
    - name: Run linting
      run: npm run lint
      
    - name: Run tests (CI-compatible)
      run: npm run test:ci
      env:
        NODE_ENV: test
        CI: true
      
    - name: Build project
      run: npm run build
      
    - name: Security check - Scan build artifacts for test flags
      run: |
        echo "🔍 Scanning build artifacts for accidentally included test flags..."
        
        # Define unsafe flags that should never appear in production builds
        UNSAFE_FLAGS=(
          "TEST_MODE_BYPASS_AUTH=true"
          "ENABLE_RATE_LIMITING=false"
          "SKIP_ENV_VALIDATION=true"
          "DISABLE_HMAC_VALIDATE=true"
          "TEST_MODE_BYPASS_CORS=true"
          "BYPASS_IP_ALLOWLIST=true"
        )
        
        violations_found=false
        
        # Check for unsafe flags in the build output
        for flag in "${UNSAFE_FLAGS[@]}"; do
          if grep -r --include="*.js" --include="*.html" --include="*.json" "$flag" .next/ 2>/dev/null | head -5; then
            echo "❌ SECURITY VIOLATION: Found '$flag' in build artifacts"
            violations_found=true
          fi
        done
        
        # Check for common test patterns
        if grep -r --include="*.js" "NODE_ENV.*test.*bypass\|TEST.*MODE.*true" .next/ 2>/dev/null | head -3; then
          echo "❌ SECURITY VIOLATION: Found test bypass patterns in build artifacts"
          violations_found=true
        fi
        
        # Check for accidental real credential leakage
        if grep -r "mock-project.supabase.co" .next/ 2>/dev/null | head -3; then
          echo "❌ SECURITY VIOLATION: Real Supabase project URL found in build artifacts"
          violations_found=true
        fi
        
        if [ "$violations_found" = true ]; then
          echo ""
          echo "🚨 SECURITY CHECK FAILED!"
          echo "Test-only bypass flags or real credentials found in production build artifacts."
          echo "This could result in disabled security or credential exposure in production."
          echo ""
          echo "Please check your environment variables and build process."
          exit 1
        else
          echo "✅ Security check passed - No unsafe flags or credentials found in build artifacts"
        fi
      
    - name: Start production server
      run: npm run start &
      env:
        NODE_ENV: production
        
    - name: Wait for server to be ready
      run: wait-on http://localhost:3000 --timeout 60000
      
    - name: Install Lighthouse CI
      run: npm install -g @lhci/cli@0.15.x
      
    - name: Run Lighthouse CI
      run: lhci autorun
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      if: always()
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        
    - name: Archive test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: test-results-${{ matrix.node-version }}
        path: |
          coverage/
          .lighthouseci/