name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x, 22.x]
        
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Mask potentially sensitive environment values
      run: |
        echo "::add-mask::eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1tb2NrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTY5NzU5NDc2MCwiZXhwIjoyMDU3NTk0NzYwfQ.mock-service-role-key-for-ci-testing"
        echo "::add-mask::ci-jwt-secret-minimum-32-characters-long-for-github-actions-security-testing"
        echo "::add-mask::ci-test-default-secret-minimum-32-characters-long-for-github-actions"
        echo "::add-mask::1x0000000000000000000000000000000AA"
        echo "🔒 Sensitive values masked from CI logs"
        
    - name: Set up CI environment
      run: |
        # Create CI environment file with safe mock values
        cat > .env.ci << 'EOF'
        # CI/CD Environment Configuration
        NEXT_PUBLIC_SITE_URL=http://localhost:3000
        
        # Mock Supabase Configuration (safe for CI)
        NEXT_PUBLIC_SUPABASE_URL=https://mock-project.supabase.co
        NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1tb2NrIiwicm9sZSI6ImFub24iLCJpYXQiOjE2OTc1OTQ3NjAsImV4cCI6MjA1NzU5NDc2MH0.mock-anon-key-for-ci-testing
        SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1tb2NrIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTY5NzU5NDc2MCwiZXhwIjoyMDU3NTk0NzYwfQ.mock-service-role-key-for-ci-testing
        
        # Test Configuration
        NODE_ENV=test
        CI=true
        ENABLE_IP_ALLOWLIST=false
        ENABLE_RATE_LIMITING=false
        ENABLE_SENTRY=false
        ENABLE_CAPTCHA=false
        TEST_MODE_BYPASS_AUTH=true
        
        # Mock secrets (32+ chars required)
        PARTNER_SECRET_DEFAULT=ci-test-default-secret-minimum-32-characters-long-for-github-actions
        JWT_SECRET=ci-jwt-secret-minimum-32-characters-long-for-github-actions-security-testing
        
        # Cloudflare test keys (always pass)
        NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
        TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA
        EOF
        
        # Copy to test environment
        cp .env.ci .env.test
        echo "✅ CI environment configured with safe mock values"
      
    - name: Run linting
      run: npm run lint
      
    - name: Check for hardcoded domains
      run: |
        echo "🔍 Checking for hardcoded domain references..."
        
        # Enhanced domain hardcoding patterns
        DOMAIN_PATTERNS=(
          "https://localhost"
          "http://localhost:[0-9]+"
          "yourdomain\.com"
          "hardcoded.*domain"
          "https://[a-zA-Z0-9-]+\.vercel\.app"
          "https://[a-zA-Z0-9-]+\.netlify\.app" 
          "https://[a-zA-Z0-9-]+\.herokuapp\.com"
          "localhost:3000"
          "127\.0\.0\.1:[0-9]+"
        )
        
        violations_found=false
        
        for pattern in "${DOMAIN_PATTERNS[@]}"; do
          if grep -r --include="*.js" --include="*.ts" --include="*.tsx" --include="*.jsx" \
             -E "$pattern" src/ 2>/dev/null; then
            echo "❌ Found hardcoded domain pattern: $pattern"
            violations_found=true
          fi
        done
        
        if [ "$violations_found" = true ]; then
          echo ""
          echo "❌ DOMAIN CHECK FAILED: Found hardcoded domain references in source code"
          echo "Please use env.NEXT_PUBLIC_SITE_URL instead of hardcoded domains"
          echo "Allowed patterns: process.env.NEXT_PUBLIC_SITE_URL, \${env.NEXT_PUBLIC_SITE_URL}"
          exit 1
        else
          echo "✅ Domain check passed - No hardcoded domains found"
        fi
      
    - name: Run tests (CI-compatible)
        run: npm run test:ci
      
      - name: Run metadata tests
        run: npm run test:metadata
      env:
        NODE_ENV: test
        CI: true
      
    - name: Build project
      run: npm run build
      
    - name: Check for hardcoded domains in build artifacts
      run: |
        echo "🔍 Checking build artifacts for hardcoded domain references..."
        
        # Enhanced domain hardcoding patterns for build artifacts
        DOMAIN_PATTERNS=(\
          "https://localhost"\
          "http://localhost:[0-9]+"\
          "yourdomain\\.com"\
          "hardcoded.*domain"\
          "https://[a-zA-Z0-9-]+\\.vercel\\.app"\
          "https://[a-zA-Z0-9-]+\\.netlify\\.app" \
          "https://[a-zA-Z0-9-]+\\.herokuapp\\.com"\
          "localhost:3000"\
          "127\\.0\\.0\\.1:[0-9]+"\
          # Additional patterns for build-generated code
          "https://.*\\.amplifyapp\\.com/[^\"'\s]+"\
          "https://example\\.com/[^\"'\s]+"\
          "development\\.domain\\.com"\
        )
        
        violations_found=false
        
        for pattern in "${DOMAIN_PATTERNS[@]}"; do
          if grep -r --include="*.js" --include="*.html" --include="*.json" --include="*.css" \
             -E "$pattern" .next/ 2>/dev/null; then
            echo "❌ Found hardcoded domain pattern in build artifacts: $pattern"
            violations_found=true
          fi
        done
        
        if [ "$violations_found" = true ]; then
          echo ""
          echo "❌ POST-BUILD DOMAIN CHECK FAILED: Found hardcoded domain references in build artifacts"
          echo "This indicates build-time tools or bundlers injected hardcoded URLs into the compiled output"
          echo "Common causes:"
          echo "  - Environment variables not properly resolved at build time"
          echo "  - Static analysis tools generating absolute URLs"
          echo "  - Third-party packages with embedded development URLs"
          echo "  - Build plugins or transformers adding hardcoded references"
          exit 1
        else
          echo "✅ Post-build domain check passed - No hardcoded domains found in build artifacts"
        fi
      
    - name: Security check - Scan build artifacts for test flags
      run: |
        echo "🔍 Scanning build artifacts for accidentally included test flags..."
        
        # Define unsafe flags that should never appear in production builds
        UNSAFE_FLAGS=(
          "TEST_MODE_BYPASS_AUTH=true"
          "ENABLE_RATE_LIMITING=false"
          "SKIP_ENV_VALIDATION=true"
          "DISABLE_HMAC_VALIDATE=true"
          "TEST_MODE_BYPASS_CORS=true"
          "BYPASS_IP_ALLOWLIST=true"
        )
        
        violations_found=false
        
        # Check for unsafe flags in the build output
        for flag in "${UNSAFE_FLAGS[@]}"; do
          if grep -r --include="*.js" --include="*.html" --include="*.json" "$flag" .next/ 2>/dev/null | head -5; then
            echo "❌ SECURITY VIOLATION: Found '$flag' in build artifacts"
            violations_found=true
          fi
        done
        
        # Check for common test patterns
        if grep -r --include="*.js" "NODE_ENV.*test.*bypass\|TEST.*MODE.*true" .next/ 2>/dev/null | head -3; then
          echo "❌ SECURITY VIOLATION: Found test bypass patterns in build artifacts"
          violations_found=true
        fi
        
        # Check for accidental real credential leakage
        if grep -r "mock-project.supabase.co" .next/ 2>/dev/null | head -3; then
          echo "❌ SECURITY VIOLATION: Real Supabase project URL found in build artifacts"
          violations_found=true
        fi
        
        if [ "$violations_found" = true ]; then
          echo ""
          echo "🚨 SECURITY CHECK FAILED!"
          echo "Test-only bypass flags or real credentials found in production build artifacts."
          echo "This could result in disabled security or credential exposure in production."
          echo ""
          echo "Please check your environment variables and build process."
          exit 1
        else
          echo "✅ Security check passed - No unsafe flags or credentials found in build artifacts"
        fi
      
    - name: Run security audit
      run: npm audit --audit-level=high --production
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      if: matrix.node-version == '20.x'
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        
  lighthouse:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build project
      run: npm run build
      
    - name: Run Lighthouse SEO audit (CI)
      run: npm run audit:seo:ci
      
    - name: Run Lighthouse performance audit (CI)
      run: npm run audit:performance:ci