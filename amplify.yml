version: 1
frontend:
  phases:
    preBuild:
      commands:
        - node -v   # sanity-check
        - echo "Installing dependencies including devDependencies for build tools"
        - npm ci --production=false --prefer-offline --no-audit --no-fund
        - echo "Generating environment configuration for AWS Amplify"
        - node scripts/create-env-mjs.js
        
    build:
      commands:
        - echo "Starting Next.js production build"
        - export NODE_OPTIONS="--max-old-space-size=8192"
        - export NEXT_TELEMETRY_DISABLED=1
        - npm run build
        
    postBuild:
      commands:
        - echo "Build completed successfully"
        - echo "Environment variables will be resolved at runtime"
        
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
      
  cache:
    paths:
      - node_modules/**/*
      - .next/cache/**/*
      - "!.next/cache/images/**/*"   # 🚫 omit ~500 MB of image artefacts
      - '!*.next/cache/fetch-cache/**/*'   # ⬅ NEW: drops ISR/fetch blobs

  customHeaders:
    - pattern: '**/*'
      headers:
        - key: 'Content-Security-Policy'
          value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://challenges.cloudflare.com https://*.sentry.io https://js.sentry-cdn.com https://browser.sentry-cdn.com https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; img-src 'self' data: blob: https: https://*.supabase.co https://images.unsplash.com https://via.placeholder.com https://picsum.photos https://www.google-analytics.com; font-src 'self' https://fonts.gstatic.com data:; connect-src 'self' https://*.supabase.co https://*.sentry.io https://vitals.vercel-insights.com https://www.google-analytics.com https://analytics.google.com wss://*.supabase.co; frame-src 'self' https://challenges.cloudflare.com; worker-src 'self' blob:; manifest-src 'self'; media-src 'self' data: blob:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests;"
        - key: 'Strict-Transport-Security'
          value: 'max-age=31536000; includeSubDomains; preload'
        - key: 'X-Frame-Options'
          value: 'DENY'
        - key: 'X-Content-Type-Options'
          value: 'nosniff'
        - key: 'X-XSS-Protection'
          value: '1; mode=block'
        - key: 'Referrer-Policy'
          value: 'strict-origin-when-cross-origin'
        - key: 'Permissions-Policy'
          value: "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), picture-in-picture=()"
        - key: 'Cross-Origin-Embedder-Policy'
          value: 'credentialless'
        - key: 'Cross-Origin-Opener-Policy'
          value: 'same-origin-allow-popups'
        - key: 'Cross-Origin-Resource-Policy'
          value: 'same-site'
    - pattern: '/static/**/*'
      headers:
        - key: 'Cache-Control'
          value: 'public, max-age=31536000, immutable'
        - key: 'X-Content-Type-Options'
          value: 'nosniff'
    - pattern: '/_next/static/**/*'
      headers:
        - key: 'Cache-Control'
          value: 'public, max-age=31536000, immutable'
        - key: 'X-Content-Type-Options'
          value: 'nosniff'
    - pattern: '/images/**/*'
      headers:
        - key: 'Cache-Control'
          value: 'public, max-age=31536000, immutable'
        - key: 'X-Content-Type-Options'
          value: 'nosniff'
    - pattern: '/icons/**/*'
      headers:
        - key: 'Cache-Control'
          value: 'public, max-age=31536000, immutable'
        - key: 'X-Content-Type-Options'
          value: 'nosniff'
    - pattern: '/favicon.ico'
      headers:
        - key: 'Cache-Control'
          value: 'public, max-age=86400'
    - pattern: '/manifest.json'
      headers:
        - key: 'Cache-Control'
          value: 'public, max-age=86400'
        - key: 'Content-Type'
          value: 'application/manifest+json'
    - pattern: '/api/**/*'
      headers:
        - key: 'Cache-Control'
          value: 'no-cache, no-store, must-revalidate, max-age=0'
        - key: 'Pragma'
          value: 'no-cache'
        - key: 'Expires'
          value: '0'
        - key: 'X-Content-Type-Options'
          value: 'nosniff'
        - key: 'X-Frame-Options'
          value: 'DENY'
    - pattern: '/robots.txt'
      headers:
        - key: 'Cache-Control'
          value: 'public, max-age=86400'
        - key: 'Content-Type'
          value: 'text/plain'
    - pattern: '**/*.css'
      headers:
        - key: 'Cache-Control'
          value: 'public, max-age=31536000, immutable'
        - key: 'Content-Type'
          value: 'text/css'
        - key: 'X-Content-Type-Options'
          value: 'nosniff'
    - pattern: '**/*.js'
      headers:
        - key: 'Cache-Control'
          value: 'public, max-age=31536000, immutable'
        - key: 'Content-Type'
          value: 'application/javascript'
        - key: 'X-Content-Type-Options'
          value: 'nosniff'
    - pattern: '**/*.{jpg,jpeg,png,gif,webp,avif,svg}'
      headers:
        - key: 'Cache-Control'
          value: 'public, max-age=31536000, immutable'
        - key: 'X-Content-Type-Options'
          value: 'nosniff'
    - pattern: '**/*.{woff,woff2,ttf,eot}'
      headers:
        - key: 'Cache-control'
          value: 'public, max-age=31536000, immutable'
        - key: 'Access-Control-Allow-Origin'
          value: '*'
        - key: 'X-Content-Type-Options'
          value: 'nosniff'