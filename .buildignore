# Build Exclusion Rules for Production
# These files/directories are excluded from production builds but included in repository

# Documentation (include in staging, exclude from production)
docs/

# Development tools and configurations
.vscode/
.idea/
*.log
.env.local
.env.development
.env.test

# Test files and coverage
__tests__/
**/*.test.js
**/*.test.ts
**/*.test.tsx
**/*.spec.js
**/*.spec.ts
**/*.spec.tsx
coverage/
.nyc_output/

# Development dependencies artifacts
node_modules/
.next/
.vercel/
.turbo/

# Editor and OS files
.DS_Store
Thumbs.db
*.swp
*.swo
*~

# Temporary files
tmp/
temp/
*.tmp
*.bak

# Documentation build artifacts
docs/**/*.html
docs/**/*.pdf
docs/automation/node_modules/