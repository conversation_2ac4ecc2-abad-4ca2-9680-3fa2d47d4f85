
const fs = require('fs');
const path = require('path');

const envFilePath = path.join(__dirname, '../src/env.mjs');

// Define all the variables your application expects with fallback values for AWS Amplify
const envVariables = {
  NEXT_PUBLIC_SITE_URL: process.env.NODE_ENV === 'development'
    ? (process.env.NEXT_PUBLIC_SITE_URL || `http://localhost:${process.env.PORT || 3000}`)
    : process.env.NEXT_PUBLIC_SITE_URL || process.env.NEXT_PUBLIC_APP_URL || 'https://main.d3pcuskj59hcq9.amplifyapp.com',
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
  SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
  EMAIL_SERVER: process.env.EMAIL_SERVER || 'smtp.gmail.com',
  EMAIL_PORT: process.env.EMAIL_PORT || '587',
  EMAIL_SECURE: process.env.EMAIL_SECURE || 'true',
  EMAIL_USER: process.env.EMAIL_USER || '',
  EMAIL_PASSWORD: process.env.EMAIL_PASSWORD || '',
  EMAIL_FROM: process.env.EMAIL_FROM || '"Cashback Deals" <<EMAIL>>',
  NEXT_PUBLIC_TURNSTILE_SITE_KEY: process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY || '1x00000000000000000000AA',
  TURNSTILE_SECRET_KEY: process.env.TURNSTILE_SECRET_KEY || '1x0000000000000000000000000000000AA',
  SENTRY_DSN: process.env.SENTRY_DSN || '',
  ENABLE_SENTRY: process.env.ENABLE_SENTRY || 'false',
  ENABLE_SENTRY_LOCAL: process.env.ENABLE_SENTRY_LOCAL || 'false',
  SENTRY_TRACES_SAMPLE_RATE: process.env.SENTRY_TRACES_SAMPLE_RATE || '0.1',
  SENTRY_ENVIRONMENT: process.env.SENTRY_ENVIRONMENT || 'development',
  ENABLE_IP_ALLOWLIST: process.env.ENABLE_IP_ALLOWLIST || 'false',
  IP_ALLOWLIST_CIDRS: process.env.IP_ALLOWLIST_CIDRS || '',
  IP_ALLOWLIST_LOG_VIOLATIONS: process.env.IP_ALLOWLIST_LOG_VIOLATIONS || 'true',
  IP_ALLOWLIST_BLOCK_BY_DEFAULT: process.env.IP_ALLOWLIST_BLOCK_BY_DEFAULT || 'false',
  IP_ALLOWLIST_INCLUDE_DEBUG: process.env.IP_ALLOWLIST_INCLUDE_DEBUG || 'false',
  ENABLE_SEARCH_AUTH: process.env.ENABLE_SEARCH_AUTH || 'true',
  ENABLE_HMAC_AUTH: process.env.ENABLE_HMAC_AUTH || 'true',
  PARTNER_SECRET_DEFAULT: process.env.PARTNER_SECRET_DEFAULT || 'dev-partner-secret-minimum-32-characters',
  PARTNER_SECRET_TEST_PARTNER: process.env.PARTNER_SECRET_TEST_PARTNER || 'dev-test-partner-secret-minimum-32-characters',
  HMAC_TIMESTAMP_WINDOW: process.env.HMAC_TIMESTAMP_WINDOW || '300',
  NEXT_PUBLIC_DEBUG_ENABLED: process.env.NEXT_PUBLIC_DEBUG_ENABLED || 'false',
  NEXT_PUBLIC_DEBUG_LEVEL: process.env.NEXT_PUBLIC_DEBUG_LEVEL || 'error',
  NEXT_PUBLIC_ENV: process.env.NEXT_PUBLIC_ENV || 'production',
  NODE_ENV: process.env.NODE_ENV || 'production',
  JWT_SECRET: process.env.JWT_SECRET || 'dev-jwt-secret-minimum-32-characters-for-local-development',
  NEXT_PUBLIC_SHOW_DEPRECATION_BANNER: process.env.NEXT_PUBLIC_SHOW_DEPRECATION_BANNER || 'false',
  NEXT_PUBLIC_DEPRECATION_SERVICE: process.env.NEXT_PUBLIC_DEPRECATION_SERVICE || 'CloudFront',
  NEXT_PUBLIC_DEPRECATION_DATE: process.env.NEXT_PUBLIC_DEPRECATION_DATE || '2025-12-31',
  NEXT_PUBLIC_DEPRECATION_INFO_URL: process.env.NEXT_PUBLIC_DEPRECATION_INFO_URL || ''
};

// Always include all variables with their fallback values for AWS Amplify compatibility
const content = `// This file is generated by scripts/create-env-mjs.js at build time
// Generated on: ${new Date().toISOString()}
// Environment: ${process.env.NODE_ENV || 'unknown'}

export const env = {
  NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || ${JSON.stringify(envVariables.NEXT_PUBLIC_SITE_URL)},
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || ${JSON.stringify(envVariables.NEXT_PUBLIC_SUPABASE_URL)},
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ${JSON.stringify(envVariables.NEXT_PUBLIC_SUPABASE_ANON_KEY)},
  SUPABASE_SERVICE_ROLE_KEY: process.env.SUPABASE_SERVICE_ROLE_KEY || ${JSON.stringify(envVariables.SUPABASE_SERVICE_ROLE_KEY)},
  EMAIL_SERVER: process.env.EMAIL_SERVER || ${JSON.stringify(envVariables.EMAIL_SERVER)},
  EMAIL_PORT: process.env.EMAIL_PORT ? parseInt(process.env.EMAIL_PORT) : ${envVariables.EMAIL_PORT},
  EMAIL_SECURE: process.env.EMAIL_SECURE === 'true',
  EMAIL_USER: process.env.EMAIL_USER || ${JSON.stringify(envVariables.EMAIL_USER)},
  EMAIL_PASSWORD: process.env.EMAIL_PASSWORD || ${JSON.stringify(envVariables.EMAIL_PASSWORD)},
  EMAIL_FROM: process.env.EMAIL_FROM || ${JSON.stringify(envVariables.EMAIL_FROM)},
  NEXT_PUBLIC_TURNSTILE_SITE_KEY: process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY || ${JSON.stringify(envVariables.NEXT_PUBLIC_TURNSTILE_SITE_KEY)},
  TURNSTILE_SECRET_KEY: process.env.TURNSTILE_SECRET_KEY || ${JSON.stringify(envVariables.TURNSTILE_SECRET_KEY)},
  SENTRY_DSN: process.env.SENTRY_DSN || ${JSON.stringify(envVariables.SENTRY_DSN)},
  ENABLE_SENTRY: process.env.ENABLE_SENTRY === 'true',
  ENABLE_SENTRY_LOCAL: process.env.ENABLE_SENTRY_LOCAL === 'true',
  SENTRY_TRACES_SAMPLE_RATE: process.env.SENTRY_TRACES_SAMPLE_RATE || ${JSON.stringify(envVariables.SENTRY_TRACES_SAMPLE_RATE)},
  SENTRY_ENVIRONMENT: process.env.SENTRY_ENVIRONMENT || ${JSON.stringify(envVariables.SENTRY_ENVIRONMENT)},
  ENABLE_IP_ALLOWLIST: process.env.ENABLE_IP_ALLOWLIST === 'true',
  IP_ALLOWLIST_CIDRS: process.env.IP_ALLOWLIST_CIDRS || ${JSON.stringify(envVariables.IP_ALLOWLIST_CIDRS)},
  IP_ALLOWLIST_LOG_VIOLATIONS: process.env.IP_ALLOWLIST_LOG_VIOLATIONS !== 'false',
  IP_ALLOWLIST_BLOCK_BY_DEFAULT: process.env.IP_ALLOWLIST_BLOCK_BY_DEFAULT === 'true',
  IP_ALLOWLIST_INCLUDE_DEBUG: process.env.IP_ALLOWLIST_INCLUDE_DEBUG === 'true',
  ENABLE_SEARCH_AUTH: process.env.ENABLE_SEARCH_AUTH !== 'false',
  ENABLE_HMAC_AUTH: process.env.ENABLE_HMAC_AUTH !== 'false', 
  PARTNER_SECRET_DEFAULT: process.env.PARTNER_SECRET_DEFAULT || ${JSON.stringify(envVariables.PARTNER_SECRET_DEFAULT)},
  PARTNER_SECRET_TEST_PARTNER: process.env.PARTNER_SECRET_TEST_PARTNER || ${JSON.stringify(envVariables.PARTNER_SECRET_TEST_PARTNER)},
  HMAC_TIMESTAMP_WINDOW: process.env.HMAC_TIMESTAMP_WINDOW || ${JSON.stringify(envVariables.HMAC_TIMESTAMP_WINDOW)},
  NEXT_PUBLIC_DEBUG_ENABLED: process.env.NEXT_PUBLIC_DEBUG_ENABLED === 'true',
  NEXT_PUBLIC_DEBUG_LEVEL: process.env.NEXT_PUBLIC_DEBUG_LEVEL || ${JSON.stringify(envVariables.NEXT_PUBLIC_DEBUG_LEVEL)},
  NEXT_PUBLIC_ENV: process.env.NEXT_PUBLIC_ENV || ${JSON.stringify(envVariables.NEXT_PUBLIC_ENV)},
  NODE_ENV: process.env.NODE_ENV || ${JSON.stringify(envVariables.NODE_ENV)},
  JWT_SECRET: process.env.JWT_SECRET || ${JSON.stringify(envVariables.JWT_SECRET)},
  NEXT_PUBLIC_SHOW_DEPRECATION_BANNER: process.env.NEXT_PUBLIC_SHOW_DEPRECATION_BANNER === 'true',
  NEXT_PUBLIC_DEPRECATION_SERVICE: process.env.NEXT_PUBLIC_DEPRECATION_SERVICE || ${JSON.stringify(envVariables.NEXT_PUBLIC_DEPRECATION_SERVICE)},
  NEXT_PUBLIC_DEPRECATION_DATE: process.env.NEXT_PUBLIC_DEPRECATION_DATE || ${JSON.stringify(envVariables.NEXT_PUBLIC_DEPRECATION_DATE)},
  NEXT_PUBLIC_DEPRECATION_INFO_URL: process.env.NEXT_PUBLIC_DEPRECATION_INFO_URL || ${JSON.stringify(envVariables.NEXT_PUBLIC_DEPRECATION_INFO_URL)}
};
`;

fs.writeFileSync(envFilePath, content);

console.log(`Successfully created src/env.mjs with runtime environment variable access.`);
console.log(`Environment variables will be resolved at runtime from process.env with fallback values.`);
