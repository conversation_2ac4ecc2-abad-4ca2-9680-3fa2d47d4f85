# Integration Tests

**UPDATED <as of 28 July 2025:13:00 PM>**

This directory contains integration tests that verify the interaction between different parts of the application.

## 📚 Complete Documentation

For comprehensive testing guidance, see the centralized documentation:
- **Testing Strategy & Setup:** [`docs/development/TESTING.md`](../../docs/development/TESTING.md)
- **Integration Testing Guidelines:** [`docs/development/TESTING.md#integration-testing`](../../docs/development/TESTING.md#integration-testing)

## Structure

```
integration/
├── api/                 # API route integration tests
│   ├── search/         # Search API tests
│   ├── contact/        # Contact API tests
│   └── auth/           # Authentication API tests
├── database/           # Database operation tests
│   ├── products/       # Product data tests
│   ├── users/          # User data tests
│   └── rls/            # Row Level Security tests
└── auth/               # Authentication flow tests
    ├── jwt/            # JWT authentication tests
    └── hmac/           # HMAC authentication tests
```

## Testing Guidelines

### API Integration Tests
- Test complete API request/response cycles
- Test authentication and authorization
- Test error handling and validation
- Test rate limiting and security measures

Example:
```typescript
// tests/integration/api/search/search-auth.test.ts
import { GET } from '@/app/api/search/route'
import { NextRequest } from 'next/server'

describe('Search API Authentication', () => {
  it('requires valid authentication', async () => {
    const request = new NextRequest('http://localhost:3000/api/search')
    const response = await GET(request)
    
    expect(response.status).toBe(401)
  })
  
  it('returns results with valid auth', async () => {
    const request = new NextRequest('http://localhost:3000/api/search?q=test', {
      headers: { 'Authorization': 'Bearer valid-token' }
    })
    const response = await GET(request)
    
    expect(response.status).toBe(200)
  })
})
```

### Database Integration Tests
- Test database operations with real/test database
- Test data consistency and integrity
- Test complex queries and joins
- Test Row Level Security (RLS) policies

Example:
```typescript
// tests/integration/database/products/data-consistency.test.ts
import { getProducts } from '@/lib/data/products'
import { createTestSupabaseClient } from '@/tests/setup/test-utils'

describe('Product Data Consistency', () => {
  it('returns consistent product data', async () => {
    const products = await getProducts({ limit: 10 })
    
    expect(products).toBeDefined()
    expect(products.length).toBeGreaterThan(0)
    expect(products[0]).toHaveProperty('id')
    expect(products[0]).toHaveProperty('name')
  })
})
```

### Authentication Integration Tests
- Test complete authentication flows
- Test JWT and HMAC compatibility
- Test session management
- Test authorization checks

Example:
```typescript
// tests/integration/auth/jwt-hmac-compatibility.test.ts
import { verifyJWT } from '@/lib/auth/jwt'
import { verifyHMAC } from '@/lib/auth/hmac'

describe('JWT/HMAC Compatibility', () => {
  it('both methods validate same user', async () => {
    const jwtResult = await verifyJWT(validToken)
    const hmacResult = await verifyHMAC(validSignature, payload)
    
    expect(jwtResult.userId).toBe(hmacResult.userId)
  })
})
```

## Test Environment

### Database Setup
- Use test database or database transactions for isolation
- Clean up test data after each test
- Use factories for creating test data

### API Testing
- Mock external services when appropriate
- Use real HTTP requests for integration testing
- Test with different authentication states

## Coverage Goals

- **API Routes**: 70% coverage minimum
- **Database Operations**: 80% coverage minimum
- **Authentication Flows**: 90% coverage minimum

## Running Integration Tests

```bash
# Run all integration tests
npm run test:integration

# Run specific integration tests
npm test -- --testPathPattern=integration/api
npm test -- --testPathPattern=integration/database

# Run with test database
TEST_DB=true npm run test:integration
```
