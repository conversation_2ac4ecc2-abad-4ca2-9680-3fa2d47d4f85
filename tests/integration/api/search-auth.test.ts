/**
 * Test Audit Update: 2025-07-28
 * Integration tests for search endpoint authentication
 * Tests search API authentication and authorization workflows
 */

// src/__tests__/api/search-auth.test.ts
// Integration tests for search endpoint authentication

import { NextRequest } from 'next/server'
import { createJWT } from '@/lib/security/jwt'
import { createHMACHeaders, clearReplayCache } from '@/lib/security/hmac'
import { authenticateSearchRequest } from '@/lib/security/auth-middleware'

// Test configuration
const TEST_SECRET = 'test-secret-minimum-32-characters-long'
const TEST_PARTNER_ID = 'test-partner'

// Mock environment setup
beforeAll(() => {
  process.env.JWT_SECRET = 'test-jwt-secret-minimum-32-characters-long'
  process.env.PARTNER_SECRET_TEST_PARTNER = TEST_SECRET
  process.env.PARTNER_SECRET_DEFAULT = TEST_SECRET
  process.env.HMAC_TIMESTAMP_WINDOW = '300'
  process.env.ENABLE_SEARCH_AUTH = 'true'
  process.env.ENABLE_HMAC_AUTH = 'true'
  // Override the global disable flag for authentication tests
  process.env.DISABLE_HMAC_VALIDATE = 'false'
})

afterAll(() => {
  delete process.env.JWT_SECRET
  delete process.env.PARTNER_SECRET_TEST_PARTNER
  delete process.env.PARTNER_SECRET_DEFAULT
  delete process.env.HMAC_TIMESTAMP_WINDOW
  delete process.env.ENABLE_SEARCH_AUTH
  delete process.env.ENABLE_HMAC_AUTH
  // Reset to original state
  process.env.DISABLE_HMAC_VALIDATE = 'true'
})

// Clear replay cache before each test to ensure clean state
beforeEach(() => {
  clearReplayCache()
})

describe('Search API Authentication', () => {
  describe('JWT Authentication', () => {
    it('allows access with valid JWT in Authorization header', async () => {
      const jwt = await createJWT()
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: { 'Authorization': `Bearer ${jwt}` }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('JWT')
      expect(authResult.payload).not.toBeNull()
    })

    it('allows access with valid JWT in cookie', async () => {
      const jwt = await createJWT()
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: { 'Cookie': `auth-token=${jwt}` }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('JWT')
    })

    it('rejects access with invalid JWT', async () => {
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: { 'Authorization': 'Bearer invalid-jwt-token' }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
      expect(authResult.method).toBeNull()
    })

    it('rejects access with expired JWT', async () => {
      // Create JWT with past expiration (this would need a modified createJWT for testing)
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: { 'Authorization': 'Bearer expired.jwt.token' }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })
  })

  describe('HMAC Authentication', () => {
    it('allows access with valid HMAC signature', async () => {
      const headers = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
      const request = new NextRequest('http://localhost/api/search?q=laptop', { headers })

      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('HMAC')
      expect(authResult.payload).toMatchObject({
        partnerId: TEST_PARTNER_ID,
        method: 'GET',
        path: '/api/search',
        isValid: true
      })
    })

    it('rejects access with invalid HMAC signature', async () => {
      const headers = {
        'X-Signature': 'sha256=invalid-signature',
        'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
        'X-Partner-ID': TEST_PARTNER_ID
      }
      const request = new NextRequest('http://localhost/api/search?q=laptop', { headers })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })

    it('rejects access with expired HMAC timestamp', async () => {
      const expiredTimestamp = Math.floor(Date.now() / 1000) - 400 // 400 seconds ago
      const headers = {
        'X-Signature': 'sha256=some-signature',
        'X-Timestamp': expiredTimestamp.toString(),
        'X-Partner-ID': TEST_PARTNER_ID
      }
      const request = new NextRequest('http://localhost/api/search?q=laptop', { headers })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })

    it('rejects access with unknown partner ID', async () => {
      const headers = createHMACHeaders('GET', '/api/search?q=laptop', 'unknown-partner', '', 'unknown-secret')
      const request = new NextRequest('http://localhost/api/search?q=laptop', { headers })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })
  })

  describe('Dual Authentication Priority', () => {
    it('JWT takes precedence when both JWT and HMAC are present', async () => {
      const jwt = await createJWT()
      const hmacHeaders = createHMACHeaders('GET', '/api/search?q=laptop', TEST_PARTNER_ID, '')
      
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'Authorization': `Bearer ${jwt}`,
          ...hmacHeaders
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('JWT') // JWT should take precedence
    })

    it('falls back to HMAC when JWT is invalid but HMAC is valid', async () => {
      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')

      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'Authorization': 'Bearer invalid-jwt',
          ...hmacHeaders
        }
      })

      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('HMAC') // Should fall back to HMAC
    })

    it('rejects when both JWT and HMAC are invalid', async () => {
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'Authorization': 'Bearer invalid-jwt',
          'X-Signature': 'sha256=invalid-signature',
          'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
          'X-Partner-ID': TEST_PARTNER_ID
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })
  })

  describe('Feature Flag Behavior', () => {
    it('bypasses authentication when ENABLE_SEARCH_AUTH is false', async () => {
      process.env.ENABLE_SEARCH_AUTH = 'false'
      
      const request = new NextRequest('http://localhost/api/search?q=laptop')
      const authResult = await authenticateSearchRequest(request)
      
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBeNull()
      
      process.env.ENABLE_SEARCH_AUTH = 'true'
    })

    it('only allows JWT when ENABLE_HMAC_AUTH is false', async () => {
      process.env.ENABLE_HMAC_AUTH = 'false'
      
      // HMAC request should fail
      const hmacHeaders = createHMACHeaders('GET', '/api/search?q=laptop', TEST_PARTNER_ID, '')
      const hmacRequest = new NextRequest('http://localhost/api/search?q=laptop', { headers: hmacHeaders })
      const hmacResult = await authenticateSearchRequest(hmacRequest)
      expect(hmacResult.success).toBe(false)
      
      // JWT request should still work
      const jwt = await createJWT()
      const jwtRequest = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: { 'Authorization': `Bearer ${jwt}` }
      })
      const jwtResult = await authenticateSearchRequest(jwtRequest)
      expect(jwtResult.success).toBe(true)
      expect(jwtResult.method).toBe('JWT')
      
      process.env.ENABLE_HMAC_AUTH = 'true'
    })
  })

  describe('No Authentication', () => {
    it('rejects request without any authentication', async () => {
      const request = new NextRequest('http://localhost/api/search?q=laptop')
      const authResult = await authenticateSearchRequest(request)
      
      expect(authResult.success).toBe(false)
      expect(authResult.method).toBeNull()
      expect(authResult.error).toBe('No valid authentication found')
      expect(authResult.traceId).toMatch(/^hmac-[a-z]+-[a-z0-9]+-[a-z0-9]+$/)
    })

    it('rejects request with incomplete HMAC headers', async () => {
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'X-Signature': 'sha256=some-signature',
          // Missing X-Timestamp and X-Partner-ID
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })

    it('rejects request with malformed Authorization header', async () => {
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'Authorization': 'InvalidFormat token-here'
        }
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })
  })

  describe('Different Search Endpoints', () => {
    it('allows public access to /api/search/suggestions endpoint', async () => {
      // Search suggestions are public - no authentication required
      const request = new NextRequest('http://localhost/api/search/suggestions?q=lap')

      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe(null) // No authentication method used
    })

    it('authenticates /api/search/more endpoint', async () => {
      const headers = createHMACHeaders('GET', '/api/search/more', TEST_PARTNER_ID, '')
      const request = new NextRequest('http://localhost/api/search/more?q=laptop&page=2', { headers })

      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('HMAC')
    })
  })

  describe('Request Body Handling', () => {
    it('handles POST requests with JSON body', async () => {
      const body = JSON.stringify({ query: 'laptop', filters: { brand: 'samsung' } })
      const headers = createHMACHeaders('POST', '/api/search', TEST_PARTNER_ID, body)
      
      const request = new NextRequest('http://localhost/api/search', {
        method: 'POST',
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        body
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('HMAC')
    })

    it('rejects POST request with body hash mismatch', async () => {
      // Generate signature with empty body
      const headers = createHMACHeaders('POST', '/api/search', TEST_PARTNER_ID, '')
      
      // Send request with actual body (should fail)
      const request = new NextRequest('http://localhost/api/search', {
        method: 'POST',
        headers: {
          ...headers,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ query: 'laptop' })
      })
      
      const authResult = await authenticateSearchRequest(request)
      expect(authResult.success).toBe(false)
    })
  })
})
