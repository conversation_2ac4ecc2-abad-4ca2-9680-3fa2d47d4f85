/**
 * Integration tests for API routes to ensure they return camelCase data
 * These tests validate that the API endpoints return properly transformed data
 */

import { NextRequest } from 'next/server'

// Mock the data layer functions
jest.mock('@/lib/data', () => ({
  getProducts: jest.fn(),
  getProduct: jest.fn(),
  getBrands: jest.fn(),
  getBrand: jest.fn(),
  getRetailers: jest.fn(),
  getRetailer: jest.fn(),
  searchProducts: jest.fn(),
}))

// Mock Supabase
jest.mock('@/lib/supabase/server', () => ({
  createCacheableSupabaseClient: jest.fn(() => ({
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          limit: jest.fn(() => ({
            single: jest.fn(() => Promise.resolve({ data: null, error: null }))
          }))
        }))
      }))
    }))
  }))
}))

// Mock rate limiter
jest.mock('@/lib/rateLimiter', () => ({
  rateLimiter: {
    check: jest.fn(() => Promise.resolve({ success: true }))
  }
}))

describe('API Data Consistency Tests', () => {
  /**
   * Helper function to validate camelCase in API responses
   */
  function validateApiResponseCamelCase(obj: any, path = ''): string[] {
    const errors: string[] = []
    
    if (obj === null || obj === undefined) {
      return errors
    }
    
    if (Array.isArray(obj)) {
      obj.forEach((item, index) => {
        errors.push(...validateApiResponseCamelCase(item, `${path}[${index}]`))
      })
      return errors
    }
    
    if (typeof obj === 'object') {
      Object.keys(obj).forEach(key => {
        const currentPath = path ? `${path}.${key}` : key
        
        // Check for any snake_case fields - none should be allowed now
        if (/_[a-z]/.test(key)) {
          errors.push(`Unexpected snake case property: ${currentPath}`)
        }
        
        errors.push(...validateApiResponseCamelCase(obj[key], currentPath))
      })
    }
    
    return errors
  }

  describe('Products API', () => {
    it('should return camelCase product data', async () => {
      const mockProductsResponse = {
        data: [
          {
            id: 'prod-1',
            name: 'Test Product',
            slug: 'test-product',
            isFeatured: true,
            isSponsored: false,
            cashbackAmount: 25.50,
            modelNumber: 'TEST-123',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-02T00:00:00Z',
            retailerOffers: [
              {
                id: 'offer-1',
                retailer: {
                  id: 'retailer-1',
                  name: 'Test Retailer',
                  logoUrl: 'logo.jpg',
                  websiteUrl: 'https://retailer.com'
                },
                stockStatus: 'in_stock',
                createdAt: '2024-01-01T00:00:00Z'
              }
            ]
          }
        ],
        pagination: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      }

      const errors = validateApiResponseCamelCase(mockProductsResponse)
      expect(errors).toEqual([])
    })

    it('should reject snake_case in product data', () => {
      const badProductsResponse = {
        data: [
          {
            id: 'prod-1',
            name: 'Test Product',
            is_featured: true, // Should be isFeatured
            model_number: 'TEST-123', // Should be modelNumber
            retailer_offers: [ // Should be retailerOffers
              {
                stock_status: 'in_stock', // Should be stockStatus
                retailer: {
                  logo_url: 'logo.jpg' // Should be logoUrl
                }
              }
            ]
          }
        ]
      }

      const errors = validateApiResponseCamelCase(badProductsResponse)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors).toContain('Unexpected snake case property: data[0].is_featured')
      expect(errors).toContain('Unexpected snake case property: data[0].model_number')
      expect(errors).toContain('Unexpected snake case property: data[0].retailer_offers')
    })
  })

  describe('Brands API', () => {
    it('should return camelCase brand data', () => {
      const mockBrandsResponse = {
        data: [
          {
            id: 'brand-1',
            name: 'Test Brand',
            slug: 'test-brand',
            logoUrl: 'logo.jpg',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-02T00:00:00Z',
            productsCount: 25,
            activePromotions: []
          }
        ],
        pagination: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      }

      const errors = validateApiResponseCamelCase(mockBrandsResponse)
      expect(errors).toEqual([])
    })
  })

  describe('Search API', () => {
    it('should return camelCase search results', () => {
      const mockSearchResponse = {
        products: [
          {
            id: 'prod-1',
            name: 'Test Product',
            isFeatured: true,
            isSponsored: false,
            cashbackAmount: 25.50,
            modelNumber: 'TEST-123',
            createdAt: '2024-01-01T00:00:00Z',
            retailerOffers: [
              {
                stockStatus: 'in_stock',
                retailer: {
                  logoUrl: 'logo.jpg',
                  websiteUrl: 'https://retailer.com'
                }
              }
            ]
          }
        ],
        total: 1,
        filtersApplied: {
          query: 'test',
          minPrice: 10,
          maxPrice: 100,
          sortBy: 'relevance'
        },
        suggestions: []
      }

      const errors = validateApiResponseCamelCase(mockSearchResponse)
      expect(errors).toEqual([])
    })
  })

  describe('Retailers API', () => {
    it('should return camelCase retailer data', () => {
      const mockRetailersResponse = {
        data: [
          {
            id: 'retailer-1',
            name: 'Test Retailer',
            slug: 'test-retailer',
            logoUrl: 'logo.jpg',
            websiteUrl: 'https://retailer.com',
            claimPeriod: '30 days',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-02T00:00:00Z'
          }
        ],
        pagination: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false
        }
      }

      const errors = validateApiResponseCamelCase(mockRetailersResponse)
      expect(errors).toEqual([])
    })
  })

  describe('Promotions Data', () => {
    it('should return camelCase promotion data', () => {
      const mockPromotionData = {
        id: 'promo-1',
        title: 'Test Promotion',
        description: 'Test description',
        maxCashbackAmount: 50.00,
        purchaseStartDate: '2024-01-01T00:00:00Z',
        purchaseEndDate: '2024-12-31T23:59:59Z',
        termsUrl: 'https://example.com/terms',
        termsDescription: 'Terms and conditions',
        isFeatured: true
      }

      const errors = validateApiResponseCamelCase(mockPromotionData)
      expect(errors).toEqual([])
    })
  })

  describe('Filter Objects', () => {
    it('should use camelCase filter properties', () => {
      const mockFilters = {
        brandId: 'brand-123',
        categoryId: 'cat-123',
        promotionId: 'promo-123',
        isFeatured: true,
        isSponsored: false,
        minPrice: 10,
        maxPrice: 100,
        sortBy: 'relevance'
      }

      const errors = validateApiResponseCamelCase(mockFilters)
      expect(errors).toEqual([])
    })

    it('should reject snake_case filter properties', () => {
      const badFilters = {
        brand_id: 'brand-123', // Should be brandId
        category_id: 'cat-123', // Should be categoryId
        is_featured: true, // Should be isFeatured
        min_price: 10, // Should be minPrice
        max_price: 100, // Should be maxPrice
        sort_by: 'relevance' // Should be sortBy
      }

      const errors = validateApiResponseCamelCase(badFilters)
      expect(errors.length).toBeGreaterThan(0)
      expect(errors).toContain('Unexpected snake case property: brand_id')
      expect(errors).toContain('Unexpected snake case property: category_id')
      expect(errors).toContain('Unexpected snake case property: is_featured')
      expect(errors).toContain('Unexpected snake case property: min_price')
      expect(errors).toContain('Unexpected snake case property: max_price')
      expect(errors).toContain('Unexpected snake case property: sort_by')
    })
  })
})
