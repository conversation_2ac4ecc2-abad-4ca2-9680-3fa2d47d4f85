# Testing Infrastructure

**UPDATED <as of 28 July 2025:13:00 PM>**

This directory contains all test files for the Cashback Deals application, organized by test type and functionality.

## 📚 Complete Documentation

For comprehensive testing guidance, see the centralized documentation:
- **Testing Strategy & Setup:** [`docs/development/TESTING.md`](../docs/development/TESTING.md)
- **Environment Configuration:** [`docs/development/ENVIRONMENT_SETUP.md`](../docs/development/ENVIRONMENT_SETUP.md)
- **Troubleshooting Guide:** [`docs/development/TROUBLESHOOTING.md`](../docs/development/TROUBLESHOOTING.md)

## Directory Structure

```
tests/
├── __mocks__/                     # Global mocks (supabase.ts, next-auth.ts, etc.)
├── setup/                         # Test configuration and utilities
├── unit/                          # Unit tests
│   ├── components/               # Component unit tests
│   ├── lib/                     # Library function tests
│   └── hooks/                   # Custom hook tests
├── integration/                   # Integration tests
│   ├── api/                     # API route tests
│   ├── database/                # Database operation tests
│   └── auth/                    # Authentication integration tests
├── e2e/                          # End-to-end tests (Playwright)
│   ├── user-flows/              # Complete user journey tests
│   ├── performance/             # Performance testing
│   └── accessibility/           # Accessibility testing
├── security/                     # Security-specific tests
│   ├── auth/                    # Authentication security tests
│   ├── api/                     # API security tests
│   └── xss/                     # XSS prevention tests
├── fixtures/                     # Test data files
├── Test_Archives/                # Archived/redundant tests
└── README.md                     # This file
```

## Test Types and Coverage Goals

| Test Type | Framework | Coverage Target | Focus |
|-----------|-----------|----------------|--------|
| **Unit Tests** | Jest + RTL | 80% | Functions, components, hooks |
| **Integration Tests** | Jest + Supertest | 70% | API routes, database operations |
| **E2E Tests** | Playwright | Critical paths | User journeys, workflows |
| **Security Tests** | Jest + Custom | 100% | XSS, injection, auth |
| **Performance Tests** | Lighthouse CI | Web Vitals | Core metrics, budgets |

## Running Tests

### All Tests
```bash
npm test                    # Run all Jest tests
npm run test:coverage      # Run with coverage report
npm run test:watch         # Run in watch mode
```

### Specific Test Types
```bash
npm run test:unit          # Unit tests only
npm run test:integration   # Integration tests only
npm run test:e2e           # Playwright E2E tests
npm run test:security      # Security tests only
npm run test:performance   # Performance tests only
```

### CI/CD Testing
```bash
npm run test:ci            # CI-optimized test run
npm run test:simple        # Simplified test suite
```

## Test Configuration

- **Jest Config**: `jest.config.js` (main), `jest.config.ci.js` (CI), `jest.config.simple.js` (simplified)
- **Playwright Config**: `playwright.config.ts`
- **Setup Files**: `tests/setup/` directory
- **Global Mocks**: `tests/__mocks__/` directory

## Writing Tests

### Naming Conventions
- Unit tests: `ComponentName.test.tsx` or `functionName.test.ts`
- Integration tests: `feature-name.test.ts`
- E2E tests: `user-flow-name.spec.ts`
- Security tests: `security-feature.test.ts`

### Import Paths
Use the configured path aliases:
```typescript
import { Component } from '@/components/Component'
import { dataFunction } from '@/lib/data/dataFunction'
import { mockSupabase } from '@/__mocks__/supabase'
```

### Test Structure
Follow the Arrange-Act-Assert pattern:
```typescript
describe('ComponentName', () => {
  it('should perform expected behavior', () => {
    // Arrange
    const props = { ... }
    
    // Act
    render(<Component {...props} />)
    
    // Assert
    expect(screen.getByText('Expected Text')).toBeInTheDocument()
  })
})
```

## Migration Notes

This directory was created as part of the test files reorganization project (July 2025). All test files have been migrated from their original scattered locations to this centralized structure for better maintainability and discoverability.

### Original Locations
- `src/__tests__/` → Moved to appropriate subdirectories
- `__tests__/` → Moved to appropriate subdirectories  
- `tests/` (old structure) → Reorganized within this directory
- `src/components/**/__tests__/` → Moved to `tests/unit/components/`

### Archived Files
Files that were duplicates or obsolete have been moved to `tests/Test_Archives/` with proper audit headers explaining the archival reason.
