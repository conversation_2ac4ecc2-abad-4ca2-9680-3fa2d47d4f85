# End-to-End Tests

**UPDATED <as of 28 July 2025:13:00 PM>**

This directory contains Playwright-based end-to-end tests that simulate real user interactions.

## 📚 Complete Documentation

For comprehensive testing guidance, see the centralized documentation:
- **Testing Strategy & Setup:** [`docs/development/TESTING.md`](../../docs/development/TESTING.md)
- **E2E Testing Guidelines:** [`docs/development/TESTING.md#end-to-end-testing`](../../docs/development/TESTING.md#end-to-end-testing)

## Structure

```
e2e/
├── user-flows/          # Complete user journey tests
│   ├── search/         # Search functionality flows
│   ├── products/       # Product browsing flows
│   └── contact/        # Contact form flows
├── performance/        # Performance testing
│   ├── page-load/     # Page load performance
│   └── interaction/   # Interaction performance
└── accessibility/      # Accessibility testing
    ├── keyboard/      # Keyboard navigation
    └── screen-reader/ # Screen reader compatibility
```

## Testing Guidelines

### User Flow Tests
- Test complete user journeys from start to finish
- Test critical business functionality
- Test cross-browser compatibility
- Test responsive design

Example:
```typescript
// tests/e2e/user-flows/search/product-search.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Product Search Flow', () => {
  test('user can search and find products', async ({ page }) => {
    // Navigate to homepage
    await page.goto('/')
    
    // Search for products
    await page.fill('[data-testid="search-input"]', 'laptop')
    await page.click('[data-testid="search-button"]')
    
    // Verify results
    await expect(page.locator('[data-testid="product-card"]')).toHaveCount({ min: 1 })
    await expect(page.locator('h1')).toContainText('Search Results')
  })
})
```

### Performance Tests
- Test page load times
- Test Core Web Vitals
- Test interaction responsiveness
- Test resource loading

Example:
```typescript
// tests/e2e/performance/page-load/homepage.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Homepage Performance', () => {
  test('loads within performance budget', async ({ page }) => {
    const startTime = Date.now()
    
    await page.goto('/')
    await page.waitForLoadState('networkidle')
    
    const loadTime = Date.now() - startTime
    expect(loadTime).toBeLessThan(3000) // 3 second budget
  })
})
```

### Accessibility Tests
- Test keyboard navigation
- Test screen reader compatibility
- Test ARIA attributes
- Test color contrast

Example:
```typescript
// tests/e2e/accessibility/keyboard/navigation.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Keyboard Navigation', () => {
  test('user can navigate with keyboard only', async ({ page }) => {
    await page.goto('/')
    
    // Tab through navigation
    await page.keyboard.press('Tab')
    await expect(page.locator(':focus')).toHaveAttribute('data-testid', 'nav-home')
    
    await page.keyboard.press('Tab')
    await expect(page.locator(':focus')).toHaveAttribute('data-testid', 'nav-products')
  })
})
```

## Test Configuration

### Playwright Setup
```typescript
// playwright.config.ts
export default defineConfig({
  testDir: './tests/e2e',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },
  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
    { name: 'firefox', use: { ...devices['Desktop Firefox'] } },
    { name: 'webkit', use: { ...devices['Desktop Safari'] } },
  ],
})
```

### Test Data
- Use test-specific data that doesn't affect production
- Clean up test data after tests
- Use page object models for complex interactions

### Browser Configuration
- Test on multiple browsers (Chrome, Firefox, Safari)
- Test on different viewport sizes
- Test with different network conditions

## Running E2E Tests

```bash
# Run all E2E tests
npm run test:e2e

# Run in headed mode (see browser)
npm run test:e2e:headed

# Run with UI mode
npm run test:e2e:ui

# Run specific test file
npx playwright test user-flows/search

# Run on specific browser
npx playwright test --project=chromium
```

## Best Practices

1. **Page Object Model**: Use page objects for complex interactions
2. **Data Attributes**: Use `data-testid` attributes for reliable element selection
3. **Wait Strategies**: Use appropriate wait strategies for dynamic content
4. **Test Isolation**: Each test should be independent and clean up after itself
5. **Realistic Data**: Use realistic test data that represents actual user scenarios
