#!/usr/bin/env node

/**
 * Search Suggestions Performance Test
 * 
 * This test measures the exact performance of typing "ovens" and getting 
 * suggested responses back in the dropdown. The test ensures the server
 * is warmed up before running actual performance measurements.
 */

const https = require('https');
const http = require('http');

const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  endpoint: '/api/search/suggestions',
  query: 'ovens',
  warmupRequests: 5,
  performanceTests: 20,
  maxAcceptableResponseTime: 150, // ms - current baseline
  targetResponseTime: 50, // ms - optimization target
};

// Performance tracking
const performanceResults = {
  warmupTimes: [],
  testTimes: [],
  errors: [],
  responses: []
};

/**
 * Make HTTP request and measure response time
 */
function makeRequest(query, isWarmup = false) {
  return new Promise((resolve, reject) => {
    const startTime = process.hrtime.bigint();
    const url = `${TEST_CONFIG.baseUrl}${TEST_CONFIG.endpoint}?q=${encodeURIComponent(query)}`;
    
    const requestStart = Date.now();
    
    const req = http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const endTime = process.hrtime.bigint();
        const responseTime = Number(endTime - startTime) / 1000000; // Convert to milliseconds
        
        try {
          const parsedData = JSON.parse(data);
          resolve({
            responseTime,
            statusCode: res.statusCode,
            data: parsedData,
            isWarmup,
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          reject({
            error: 'JSON parse error',
            data,
            responseTime,
            statusCode: res.statusCode
          });
        }
      });
    });
    
    req.on('error', (error) => {
      const endTime = process.hrtime.bigint();
      const responseTime = Number(endTime - startTime) / 1000000;
      reject({
        error: error.message,
        responseTime,
        timestamp: new Date().toISOString()
      });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      reject({
        error: 'Request timeout',
        responseTime: 5000,
        timestamp: new Date().toISOString()
      });
    });
  });
}

/**
 * Run warmup requests to ensure server is not cold
 */
async function runWarmup() {
  console.log(`🔥 Running ${TEST_CONFIG.warmupRequests} warmup requests...`);
  
  for (let i = 0; i < TEST_CONFIG.warmupRequests; i++) {
    try {
      const result = await makeRequest(TEST_CONFIG.query, true);
      performanceResults.warmupTimes.push(result.responseTime);
      process.stdout.write(`  Warmup ${i + 1}: ${result.responseTime.toFixed(2)}ms\r`);
    } catch (error) {
      console.error(`Warmup ${i + 1} failed:`, error.error);
      performanceResults.errors.push({ phase: 'warmup', error });
    }
    
    // Small delay between requests to simulate real usage
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log(`\n✅ Warmup completed. Average: ${(performanceResults.warmupTimes.reduce((a, b) => a + b, 0) / performanceResults.warmupTimes.length).toFixed(2)}ms\n`);
}

/**
 * Run performance test measurements
 */
async function runPerformanceTests() {
  console.log(`⚡ Running ${TEST_CONFIG.performanceTests} performance tests...`);
  
  for (let i = 0; i < TEST_CONFIG.performanceTests; i++) {
    try {
      const result = await makeRequest(TEST_CONFIG.query, false);
      performanceResults.testTimes.push(result.responseTime);
      performanceResults.responses.push({
        test: i + 1,
        responseTime: result.responseTime,
        suggestions: result.data.suggestions || [],
        suggestionsCount: (result.data.suggestions || []).length
      });
      
      process.stdout.write(`  Test ${i + 1}: ${result.responseTime.toFixed(2)}ms (${(result.data.suggestions || []).length} suggestions)\r`);
    } catch (error) {
      console.error(`\nTest ${i + 1} failed:`, error.error);
      performanceResults.errors.push({ phase: 'test', test: i + 1, error });
    }
    
    // Simulate realistic typing delay
    await new Promise(resolve => setTimeout(resolve, 150));
  }
  
  console.log('\n');
}

/**
 * Calculate and display performance statistics
 */
function calculateStatistics() {
  const times = performanceResults.testTimes;
  
  if (times.length === 0) {
    console.error('❌ No successful test results to analyze');
    return;
  }
  
  const sorted = times.slice().sort((a, b) => a - b);
  const min = sorted[0];
  const max = sorted[sorted.length - 1];
  const avg = times.reduce((a, b) => a + b, 0) / times.length;
  const median = sorted.length % 2 === 0 
    ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
    : sorted[Math.floor(sorted.length / 2)];
  
  const p95Index = Math.ceil(sorted.length * 0.95) - 1;
  const p95 = sorted[p95Index];
  
  const p99Index = Math.ceil(sorted.length * 0.99) - 1;
  const p99 = sorted[p99Index];
  
  // Calculate standard deviation
  const variance = times.reduce((acc, time) => acc + Math.pow(time - avg, 2), 0) / times.length;
  const stdDev = Math.sqrt(variance);
  
  return {
    min,
    max,
    avg,
    median,
    p95,
    p99,
    stdDev,
    successRate: (times.length / TEST_CONFIG.performanceTests) * 100
  };
}

/**
 * Display comprehensive test results
 */
function displayResults() {
  console.log('\n' + '='.repeat(80));
  console.log('🎯 SEARCH SUGGESTIONS PERFORMANCE TEST RESULTS');
  console.log('='.repeat(80));
  
  // Test Configuration
  console.log('\n📊 Test Configuration:');
  console.log(`   Query: "${TEST_CONFIG.query}"`);
  console.log(`   Endpoint: ${TEST_CONFIG.endpoint}`);
  console.log(`   Warmup Requests: ${TEST_CONFIG.warmupRequests}`);
  console.log(`   Performance Tests: ${TEST_CONFIG.performanceTests}`);
  console.log(`   Current Baseline: ${TEST_CONFIG.maxAcceptableResponseTime}ms`);
  console.log(`   Optimization Target: ${TEST_CONFIG.targetResponseTime}ms`);
  
  // Error Summary
  if (performanceResults.errors.length > 0) {
    console.log('\n❌ Errors Encountered:');
    performanceResults.errors.forEach((error, i) => {
      console.log(`   ${i + 1}. [${error.phase}] ${error.error.error || error.error}`);
    });
  }
  
  // Performance Statistics
  const stats = calculateStatistics();
  if (stats) {
    console.log('\n⚡ Performance Statistics:');
    console.log(`   Success Rate: ${stats.successRate.toFixed(1)}%`);
    console.log(`   Minimum: ${stats.min.toFixed(2)}ms`);
    console.log(`   Average: ${stats.avg.toFixed(2)}ms`);
    console.log(`   Median: ${stats.median.toFixed(2)}ms`);
    console.log(`   Maximum: ${stats.max.toFixed(2)}ms`);
    console.log(`   95th Percentile: ${stats.p95.toFixed(2)}ms`);
    console.log(`   99th Percentile: ${stats.p99.toFixed(2)}ms`);
    console.log(`   Standard Deviation: ${stats.stdDev.toFixed(2)}ms`);
    
    // Performance Assessment
    console.log('\n🎯 Performance Assessment:');
    
    if (stats.avg <= TEST_CONFIG.targetResponseTime) {
      console.log(`   ✅ EXCELLENT: Average response time (${stats.avg.toFixed(2)}ms) meets optimization target`);
    } else if (stats.avg <= TEST_CONFIG.maxAcceptableResponseTime) {
      console.log(`   ⚠️  ACCEPTABLE: Average response time (${stats.avg.toFixed(2)}ms) within baseline`);
      console.log(`   🎯 OPPORTUNITY: ${(stats.avg - TEST_CONFIG.targetResponseTime).toFixed(2)}ms improvement needed to reach target`);
    } else {
      console.log(`   ❌ NEEDS IMPROVEMENT: Average response time (${stats.avg.toFixed(2)}ms) exceeds baseline`);
      console.log(`   🎯 CRITICAL: ${(stats.avg - TEST_CONFIG.maxAcceptableResponseTime).toFixed(2)}ms over acceptable threshold`);
    }
    
    if (stats.p95 > TEST_CONFIG.maxAcceptableResponseTime) {
      console.log(`   ⚠️  95th percentile (${stats.p95.toFixed(2)}ms) indicates inconsistent performance`);
    }
    
    if (stats.stdDev > 20) {
      console.log(`   ⚠️  High standard deviation (${stats.stdDev.toFixed(2)}ms) indicates variable performance`);
    }
  }
  
  // Suggestions Analysis
  if (performanceResults.responses.length > 0) {
    const suggestionCounts = performanceResults.responses.map(r => r.suggestionsCount);
    const avgSuggestions = suggestionCounts.reduce((a, b) => a + b, 0) / suggestionCounts.length;
    const uniqueSuggestions = new Set();
    
    performanceResults.responses.forEach(response => {
      response.suggestions.forEach(suggestion => {
        if (typeof suggestion === 'string') {
          uniqueSuggestions.add(suggestion);
        } else if (suggestion.name) {
          uniqueSuggestions.add(suggestion.name);
        }
      });
    });
    
    console.log('\n💡 Suggestions Analysis:');
    console.log(`   Average Suggestions per Request: ${avgSuggestions.toFixed(1)}`);
    console.log(`   Unique Suggestions Found: ${uniqueSuggestions.size}`);
    
    // Show sample suggestions from first successful response
    const firstResponse = performanceResults.responses.find(r => r.suggestions.length > 0);
    if (firstResponse) {
      console.log(`   Sample Suggestions: ${firstResponse.suggestions.slice(0, 3).map(s => typeof s === 'string' ? s : s.name || s).join(', ')}`);
    }
  }
  
  // Recommendations
  console.log('\n📈 Optimization Recommendations:');
  if (stats && stats.avg > TEST_CONFIG.targetResponseTime) {
    const improvementNeeded = ((stats.avg - TEST_CONFIG.targetResponseTime) / stats.avg * 100);
    console.log(`   🎯 Target improvement: ${improvementNeeded.toFixed(1)}% reduction in response time`);
    console.log('   💡 Consider implementing:');
    console.log('      - Server-side caching for popular queries');
    console.log('      - Database query optimization');
    console.log('      - Connection pooling improvements');
    console.log('      - Response compression');
  }
  
  if (stats && stats.stdDev > 20) {
    console.log('   📊 Reduce performance variability:');
    console.log('      - Implement consistent database connection handling');
    console.log('      - Add response time monitoring');
    console.log('      - Consider connection warming');
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('✅ Performance test completed successfully');
  console.log('='.repeat(80));
}

/**
 * Main test execution
 */
async function runTest() {
  console.log('🚀 Starting Search Suggestions Performance Test');
  console.log(`📅 Test Date: ${new Date().toISOString()}`);
  console.log(`🌐 Target URL: ${TEST_CONFIG.baseUrl}${TEST_CONFIG.endpoint}?q=${TEST_CONFIG.query}\n`);
  
  try {
    // Warmup phase
    await runWarmup();
    
    // Performance testing phase
    await runPerformanceTests();
    
    // Results analysis
    displayResults();
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = { runTest, TEST_CONFIG };