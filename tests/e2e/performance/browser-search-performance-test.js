#!/usr/bin/env node

/**
 * Browser-Based Search Performance Test
 * 
 * This test uses <PERSON><PERSON> to simulate real user interaction:
 * - Navigate to the homepage
 * - Type "ovens" character by character
 * - Measure the time for suggestions to appear
 * - Verify the dropdown functionality
 */

const { chromium } = require('playwright');

const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  searchQuery: 'ovens',
  iterations: 10,
  typingDelay: 100, // ms between character typing
  maxWaitTime: 2000, // max wait for suggestions
  warmupIterations: 3
};

const results = {
  iterations: [],
  errors: [],
  summary: {}
};

/**
 * Measure typing and suggestion response performance
 */
async function measureSearchPerformance(page, iteration, isWarmup = false) {
  try {
    console.log(`${isWarmup ? '🔥 Warmup' : '⚡ Test'} ${iteration}: Starting search performance test...`);
    
    // Navigate to homepage
    await page.goto(TEST_CONFIG.baseUrl, { waitUntil: 'networkidle' });
    
    // Find the search input
    const searchInput = await page.locator('[data-testid="search-input"], input[type="search"], .search-input, #search').first();
    await searchInput.waitFor({ state: 'visible', timeout: 5000 });
    
    // Clear any existing content
    await searchInput.fill('');
    
    // Start performance measurement
    const startTime = performance.now();
    let suggestionAppearTime = null;
    let typingCompleteTime = null;
    
    // Set up listener for suggestions to appear
    const suggestionsPromise = page.locator('.suggestions, [data-testid="search-suggestions"], .search-suggestions').first().waitFor({ 
      state: 'visible', 
      timeout: TEST_CONFIG.maxWaitTime 
    }).then(() => {
      suggestionAppearTime = performance.now();
      return suggestionAppearTime - startTime;
    }).catch(() => null);
    
    // Type the search query character by character
    for (let i = 0; i < TEST_CONFIG.searchQuery.length; i++) {
      await searchInput.type(TEST_CONFIG.searchQuery[i], { delay: TEST_CONFIG.typingDelay });
    }
    
    typingCompleteTime = performance.now();
    const typingDuration = typingCompleteTime - startTime;
    
    // Wait for suggestions to appear and measure
    const suggestionResponseTime = await suggestionsPromise;
    
    // Get suggestion details
    let suggestions = [];
    let suggestionCount = 0;
    
    try {
      const suggestionElements = await page.locator('.suggestions li, [data-testid="search-suggestions"] li, .search-suggestions li').all();
      suggestionCount = suggestionElements.length;
      
      // Extract suggestion text
      for (const element of suggestionElements.slice(0, 5)) { // Limit to first 5
        const text = await element.textContent();
        if (text && text.trim()) {
          suggestions.push(text.trim());
        }
      }
    } catch (error) {
      console.log(`  Warning: Could not extract suggestion details: ${error.message}`);
    }
    
    const result = {
      iteration,
      isWarmup,
      typingDuration,
      suggestionResponseTime,
      suggestionCount,
      suggestions: suggestions.slice(0, 3), // Keep first 3 for logging
      success: suggestionResponseTime !== null,
      timestamp: new Date().toISOString()
    };
    
    if (!isWarmup) {
      results.iterations.push(result);
    }
    
    if (result.success) {
      console.log(`  ✅ Typing: ${typingDuration.toFixed(2)}ms | Suggestions: ${suggestionResponseTime.toFixed(2)}ms | Count: ${suggestionCount}`);
    } else {
      console.log(`  ❌ Suggestions did not appear within ${TEST_CONFIG.maxWaitTime}ms`);
    }
    
    return result;
    
  } catch (error) {
    const errorResult = {
      iteration,
      isWarmup,
      error: error.message,
      success: false,
      timestamp: new Date().toISOString()
    };
    
    if (!isWarmup) {
      results.errors.push(errorResult);
    }
    
    console.log(`  ❌ Error: ${error.message}`);
    return errorResult;
  }
}

/**
 * Calculate performance statistics
 */
function calculateStatistics() {
  const successfulTests = results.iterations.filter(r => r.success);
  
  if (successfulTests.length === 0) {
    return null;
  }
  
  const responseTimes = successfulTests.map(r => r.suggestionResponseTime);
  const typingTimes = successfulTests.map(r => r.typingDuration);
  
  const sorted = responseTimes.slice().sort((a, b) => a - b);
  
  return {
    totalTests: results.iterations.length,
    successfulTests: successfulTests.length,
    successRate: (successfulTests.length / results.iterations.length) * 100,
    
    responseTime: {
      min: Math.min(...responseTimes),
      max: Math.max(...responseTimes),
      avg: responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length,
      median: sorted.length % 2 === 0 
        ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
        : sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.ceil(sorted.length * 0.95) - 1],
      stdDev: Math.sqrt(responseTimes.reduce((acc, time) => {
        const avg = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
        return acc + Math.pow(time - avg, 2);
      }, 0) / responseTimes.length)
    },
    
    typing: {
      avg: typingTimes.reduce((a, b) => a + b, 0) / typingTimes.length,
      min: Math.min(...typingTimes),
      max: Math.max(...typingTimes)
    },
    
    suggestions: {
      avgCount: successfulTests.reduce((sum, test) => sum + (test.suggestionCount || 0), 0) / successfulTests.length,
      uniqueSuggestions: new Set(successfulTests.flatMap(test => test.suggestions || [])).size
    }
  };
}

/**
 * Display comprehensive test results
 */
function displayResults() {
  console.log('\n' + '='.repeat(80));
  console.log('🌐 BROWSER SEARCH SUGGESTIONS PERFORMANCE TEST RESULTS');
  console.log('='.repeat(80));
  
  console.log('\n📊 Test Configuration:');
  console.log(`   Search Query: "${TEST_CONFIG.searchQuery}"`);
  console.log(`   Base URL: ${TEST_CONFIG.baseUrl}`);
  console.log(`   Iterations: ${TEST_CONFIG.iterations}`);
  console.log(`   Typing Delay: ${TEST_CONFIG.typingDelay}ms between characters`);
  console.log(`   Max Wait Time: ${TEST_CONFIG.maxWaitTime}ms`);
  
  const stats = calculateStatistics();
  
  if (!stats) {
    console.log('\n❌ No successful tests to analyze');
    return;
  }
  
  console.log('\n⚡ Performance Results:');
  console.log(`   Success Rate: ${stats.successRate.toFixed(1)}% (${stats.successfulTests}/${stats.totalTests})`);
  
  console.log('\n🎯 Suggestion Response Time:');
  console.log(`   Average: ${stats.responseTime.avg.toFixed(2)}ms`);
  console.log(`   Median: ${stats.responseTime.median.toFixed(2)}ms`);
  console.log(`   Min: ${stats.responseTime.min.toFixed(2)}ms`);
  console.log(`   Max: ${stats.responseTime.max.toFixed(2)}ms`);
  console.log(`   95th Percentile: ${stats.responseTime.p95.toFixed(2)}ms`);
  console.log(`   Standard Deviation: ${stats.responseTime.stdDev.toFixed(2)}ms`);
  
  console.log('\n⌨️  Typing Performance:');
  console.log(`   Average Typing Duration: ${stats.typing.avg.toFixed(2)}ms`);
  console.log(`   Min: ${stats.typing.min.toFixed(2)}ms`);
  console.log(`   Max: ${stats.typing.max.toFixed(2)}ms`);
  
  console.log('\n💡 Suggestions Quality:');
  console.log(`   Average Suggestions per Query: ${stats.suggestions.avgCount.toFixed(1)}`);
  console.log(`   Unique Suggestions Found: ${stats.suggestions.uniqueSuggestions}`);
  
  // Show sample suggestions
  const firstSuccessful = results.iterations.find(r => r.success && r.suggestions.length > 0);
  if (firstSuccessful) {
    console.log(`   Sample Suggestions: ${firstSuccessful.suggestions.join(', ')}`);
  }
  
  // Performance Assessment
  console.log('\n🎯 Performance Assessment:');
  
  if (stats.responseTime.avg <= 50) {
    console.log('   ✅ EXCELLENT: Response time meets optimization target (<50ms)');
  } else if (stats.responseTime.avg <= 150) {
    console.log('   ⚠️  ACCEPTABLE: Response time within baseline (<150ms)');
    console.log(`   🎯 OPPORTUNITY: ${(stats.responseTime.avg - 50).toFixed(2)}ms improvement needed`);
  } else {
    console.log('   ❌ NEEDS IMPROVEMENT: Response time exceeds baseline (>150ms)');
  }
  
  if (stats.responseTime.stdDev > 30) {
    console.log('   ⚠️  High variability detected - response times are inconsistent');
  }
  
  if (stats.successRate < 95) {
    console.log('   ⚠️  Success rate below 95% - reliability issues detected');
  }
  
  if (results.errors.length > 0) {
    console.log('\n❌ Errors Encountered:');
    results.errors.forEach((error, i) => {
      console.log(`   ${i + 1}. Test ${error.iteration}: ${error.error}`);
    });
  }
  
  console.log('\n' + '='.repeat(80));
  console.log('✅ Browser performance test completed');
  console.log('='.repeat(80));
}

/**
 * Main test execution
 */
async function runBrowserTest() {
  console.log('🚀 Starting Browser-Based Search Performance Test');
  console.log(`📅 Test Date: ${new Date().toISOString()}\n`);
  
  let browser = null;
  let page = null;
  
  try {
    // Launch browser
    console.log('🌐 Launching browser...');
    browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const context = await browser.newContext({
      viewport: { width: 1280, height: 720 },
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
    });
    
    page = await context.newPage();
    
    // Warmup runs
    console.log(`🔥 Running ${TEST_CONFIG.warmupIterations} warmup iterations...`);
    for (let i = 1; i <= TEST_CONFIG.warmupIterations; i++) {
      await measureSearchPerformance(page, i, true);
      await page.waitForTimeout(500); // Small delay between warmup runs
    }
    
    console.log('\n⚡ Running performance test iterations...');
    
    // Performance test runs
    for (let i = 1; i <= TEST_CONFIG.iterations; i++) {
      await measureSearchPerformance(page, i, false);
      
      if (i < TEST_CONFIG.iterations) {
        await page.waitForTimeout(300); // Small delay between tests
      }
    }
    
    // Display results
    displayResults();
    
  } catch (error) {
    console.error('❌ Browser test failed:', error);
    process.exit(1);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
if (require.main === module) {
  runBrowserTest().catch(console.error);
}

module.exports = { runBrowserTest, TEST_CONFIG };