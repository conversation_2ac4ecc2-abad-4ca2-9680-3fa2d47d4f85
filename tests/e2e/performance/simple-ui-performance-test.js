#!/usr/bin/env node

/**
 * Simple UI Performance Test - CSP Bypass Version
 * 
 * This test measures real UI performance by:
 * - Using basic DOM interaction without complex JS
 * - Monitoring network requests directly
 * - Measuring visual changes in the DOM
 * - Working around CSP restrictions
 */

const { chromium } = require('playwright');

const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  testQueries: ['ovens', 'samsung'],
  typingSpeed: 100, // ms between characters
  iterations: 3,
  timeout: 10000
};

const results = {
  tests: [],
  errors: [],
  summary: {}
};

/**
 * Simple UI performance measurement focusing on network and visual feedback
 */
async function measureSimpleUIPerformance(page, query, iteration) {
  console.log(`\n🔍 Test ${iteration}: UI performance for "${query}"`);
  
  try {
    // Clear any existing state
    await page.goto('about:blank');
    await page.waitForTimeout(100);
    
    // Navigate to search page first (avoiding homepage CSP issues)
    console.log('  📄 Loading search page...');
    await page.goto(`${TEST_CONFIG.baseUrl}/search`, { 
      waitUntil: 'domcontentloaded',
      timeout: 5000 
    });
    
    // Wait for page to stabilize
    await page.waitForTimeout(1000);
    
    // Look for any input field (more flexible approach)
    console.log('  🔍 Looking for input fields...');
    
    let searchInput = null;
    const inputSelectors = [
      'input[type="search"]',
      'input[type="text"]',
      'input',
      'textarea'
    ];
    
    for (const selector of inputSelectors) {
      try {
        const inputs = await page.locator(selector).all();
        if (inputs.length > 0) {
          searchInput = inputs[0];
          console.log(`  ✅ Found input with selector: ${selector}`);
          break;
        }
      } catch (e) {
        continue;
      }
    }
    
    // If no input found, try creating a test scenario
    if (!searchInput) {
      console.log('  ⚠️  No search input found, testing direct API calls...');
      return await measureDirectAPIPerformance(page, query, iteration);
    }
    
    // Setup network monitoring
    const networkRequests = [];
    const networkPromise = new Promise((resolve) => {
      const timeout = setTimeout(() => resolve(null), TEST_CONFIG.timeout);
      
      page.on('response', (response) => {
        if (response.url().includes('/api/search/suggestions')) {
          clearTimeout(timeout);
          networkRequests.push({
            url: response.url(),
            status: response.status(),
            timing: Date.now()
          });
          resolve(response);
        }
      });
    });
    
    // Start timing
    const startTime = Date.now();
    console.log(`  ⌨️  Typing "${query}"...`);
    
    // Clear and focus input
    await searchInput.click();
    await searchInput.fill('');
    
    // Type character by character
    let typingCompleteTime = null;
    for (let i = 0; i < query.length; i++) {
      await searchInput.type(query[i], { delay: TEST_CONFIG.typingSpeed });
      if (i === query.length - 1) {
        typingCompleteTime = Date.now();
      }
    }
    
    console.log(`  ⏳ Waiting for network request...`);
    
    // Wait for network request
    const networkResponse = await networkPromise;
    const networkCompleteTime = networkResponse ? Date.now() : null;
    
    // Wait a bit more to see if UI updates
    await page.waitForTimeout(500);
    
    // Try to detect any dynamic content changes
    const finalTime = Date.now();
    
    // Look for suggestion containers
    let suggestionsFound = false;
    let suggestionCount = 0;
    
    const suggestionSelectors = [
      '.suggestions',
      '[role="listbox"]',
      '.dropdown',
      '.search-results',
      'ul li',
      '.suggestion-item'
    ];
    
    for (const selector of suggestionSelectors) {
      try {
        const elements = await page.locator(selector).all();
        if (elements.length > 0) {
          suggestionsFound = true;
          suggestionCount = elements.length;
          console.log(`  💡 Found ${elements.length} suggestion elements`);
          break;
        }
      } catch (e) {
        continue;
      }
    }
    
    const result = {
      iteration,
      query,
      success: networkResponse !== null,
      timings: {
        totalDuration: finalTime - startTime,
        typingDuration: typingCompleteTime - startTime,
        networkResponseTime: networkCompleteTime ? networkCompleteTime - startTime : null,
        timeAfterTyping: networkCompleteTime ? networkCompleteTime - typingCompleteTime : null
      },
      network: {
        requestMade: networkRequests.length > 0,
        responseStatus: networkResponse ? networkResponse.status() : null,
        responseTime: networkCompleteTime ? networkCompleteTime - typingCompleteTime : null
      },
      ui: {
        suggestionsFound,
        suggestionCount
      },
      timestamp: new Date().toISOString()
    };
    
    results.tests.push(result);
    
    console.log(`  📊 Results:`);
    console.log(`     Typing Duration: ${result.timings.typingDuration}ms`);
    if (result.timings.networkResponseTime) {
      console.log(`     Network Response: ${result.timings.networkResponseTime}ms from start`);
      console.log(`     Response After Typing: ${result.timings.timeAfterTyping}ms`);
    }
    console.log(`     Network Status: ${result.network.responseStatus || 'No response'}`);
    console.log(`     UI Suggestions: ${result.ui.suggestionCount} found`);
    
    return result;
    
  } catch (error) {
    const errorResult = {
      iteration,
      query,
      error: error.message,
      success: false,
      timestamp: new Date().toISOString()
    };
    
    results.errors.push(errorResult);
    console.log(`  ❌ Error: ${error.message}`);
    return errorResult;
  }
}

/**
 * Fallback: Test API performance directly from browser context
 */
async function measureDirectAPIPerformance(page, query, iteration) {
  console.log('  🔗 Testing direct API performance...');
  
  try {
    const startTime = Date.now();
    
    // Make direct fetch request to API
    const response = await page.evaluate(async (query, baseUrl) => {
      const url = `${baseUrl}/api/search/suggestions?q=${encodeURIComponent(query)}`;
      const startTime = performance.now();
      
      try {
        const response = await fetch(url);
        const endTime = performance.now();
        const data = await response.json();
        
        return {
          success: true,
          status: response.status,
          responseTime: endTime - startTime,
          data: data,
          suggestionsCount: (data.categories?.length || 0) + (data.brands?.length || 0) + (data.products?.length || 0)
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          responseTime: performance.now() - startTime
        };
      }
    }, query, TEST_CONFIG.baseUrl);
    
    const result = {
      iteration,
      query,
      success: response.success,
      type: 'direct-api',
      timings: {
        apiResponseTime: response.responseTime
      },
      network: {
        responseStatus: response.status,
        responseTime: response.responseTime
      },
      api: {
        suggestionsCount: response.suggestionsCount || 0
      },
      timestamp: new Date().toISOString()
    };
    
    results.tests.push(result);
    
    console.log(`  📊 Direct API Results:`);
    console.log(`     API Response Time: ${response.responseTime.toFixed(1)}ms`);
    console.log(`     Status: ${response.status}`);
    console.log(`     Suggestions: ${response.suggestionsCount || 0}`);
    
    return result;
    
  } catch (error) {
    console.log(`  ❌ Direct API test failed: ${error.message}`);
    return {
      iteration,
      query,
      error: error.message,
      success: false,
      type: 'direct-api',
      timestamp: new Date().toISOString()
    };
  }
}

/**
 * Calculate and display results
 */
function displaySimpleResults() {
  console.log('\n' + '='.repeat(70));
  console.log('🌐 SIMPLE UI PERFORMANCE TEST RESULTS');
  console.log('='.repeat(70));
  
  const successfulTests = results.tests.filter(t => t.success);
  const uiTests = successfulTests.filter(t => t.type !== 'direct-api');
  const apiTests = successfulTests.filter(t => t.type === 'direct-api');
  
  console.log('\n📊 Test Summary:');
  console.log(`   Total Tests: ${results.tests.length}`);
  console.log(`   Successful: ${successfulTests.length}`);
  console.log(`   UI Tests: ${uiTests.length}`);
  console.log(`   Direct API Tests: ${apiTests.length}`);
  console.log(`   Errors: ${results.errors.length}`);
  
  if (uiTests.length > 0) {
    console.log('\n⚡ UI Test Results:');
    
    const typingTimes = uiTests.map(t => t.timings.typingDuration);
    const responseTimes = uiTests.filter(t => t.timings.timeAfterTyping).map(t => t.timings.timeAfterTyping);
    
    console.log(`   Average Typing Duration: ${(typingTimes.reduce((a, b) => a + b, 0) / typingTimes.length).toFixed(1)}ms`);
    
    if (responseTimes.length > 0) {
      console.log(`   Average Response After Typing: ${(responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length).toFixed(1)}ms`);
      console.log(`   Min Response: ${Math.min(...responseTimes)}ms`);
      console.log(`   Max Response: ${Math.max(...responseTimes)}ms`);
    }
  }
  
  if (apiTests.length > 0) {
    console.log('\n🔗 Direct API Test Results:');
    
    const apiResponseTimes = apiTests.map(t => t.timings.apiResponseTime);
    console.log(`   Average API Response: ${(apiResponseTimes.reduce((a, b) => a + b, 0) / apiResponseTimes.length).toFixed(1)}ms`);
    console.log(`   Min API Response: ${Math.min(...apiResponseTimes).toFixed(1)}ms`);
    console.log(`   Max API Response: ${Math.max(...apiResponseTimes).toFixed(1)}ms`);
  }
  
  console.log('\n🎯 Performance Assessment:');
  
  if (uiTests.length > 0) {
    const avgResponseTime = uiTests.filter(t => t.timings.timeAfterTyping).map(t => t.timings.timeAfterTyping);
    if (avgResponseTime.length > 0) {
      const avg = avgResponseTime.reduce((a, b) => a + b, 0) / avgResponseTime.length;
      
      if (avg <= 200) {
        console.log(`   ✅ EXCELLENT: UI responds in ${avg.toFixed(1)}ms after typing (feels instant)`);
      } else if (avg <= 500) {
        console.log(`   ⚠️  ACCEPTABLE: UI responds in ${avg.toFixed(1)}ms (noticeable but reasonable)`);
      } else {
        console.log(`   ❌ SLOW: UI takes ${avg.toFixed(1)}ms to respond (users notice delay)`);
      }
    }
  }
  
  if (apiTests.length > 0) {
    const avgApiTime = apiTests.map(t => t.timings.apiResponseTime).reduce((a, b) => a + b, 0) / apiTests.length;
    console.log(`   🔗 API Baseline: ${avgApiTime.toFixed(1)}ms (consistent with previous tests)`);
  }
  
  if (results.errors.length > 0) {
    console.log('\n❌ Errors Encountered:');
    results.errors.forEach((error, i) => {
      console.log(`   ${i + 1}. "${error.query}": ${error.error}`);
    });
  }
  
  console.log('\n💡 Key Insights:');
  if (uiTests.length === 0) {
    console.log('   • UI testing was limited due to CSP restrictions');
    console.log('   • Focus on direct API performance for baseline');
    console.log('   • Consider implementing frontend performance monitoring');
  } else {
    console.log('   • UI performance includes typing, network, and rendering time');
    console.log('   • Real user experience is significantly slower than pure API time');
    console.log('   • Frontend optimizations may provide bigger impact than API optimization');
  }
  
  console.log('\n' + '='.repeat(70));
  console.log('✅ Simple UI performance test completed');
  console.log('='.repeat(70));
}

/**
 * Main test execution
 */
async function runSimpleUITest() {
  console.log('🚀 Simple UI Performance Test (CSP-Compatible)');
  console.log(`📅 ${new Date().toISOString()}`);
  console.log(`🌐 Testing: ${TEST_CONFIG.baseUrl}\n`);
  
  let browser = null;
  
  try {
    console.log('🌐 Launching browser...');
    browser = await chromium.launch({ 
      headless: true,
      args: [
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-extensions'
      ]
    });
    
    const context = await browser.newContext({
      viewport: { width: 1280, height: 720 },
      ignoreHTTPSErrors: true
    });
    
    const page = await context.newPage();
    
    // Run tests for each query
    for (const query of TEST_CONFIG.testQueries) {
      console.log(`\n${'='.repeat(40)}`);
      console.log(`🔍 Testing query: "${query}"`);
      console.log(`${'='.repeat(40)}`);
      
      for (let i = 1; i <= TEST_CONFIG.iterations; i++) {
        await measureSimpleUIPerformance(page, query, i);
        
        if (i < TEST_CONFIG.iterations) {
          await page.waitForTimeout(1000);
        }
      }
    }
    
    // Display results
    displaySimpleResults();
    
  } catch (error) {
    console.error('❌ Simple UI test failed:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
if (require.main === module) {
  runSimpleUITest().catch(console.error);
}

module.exports = { runSimpleUITest };