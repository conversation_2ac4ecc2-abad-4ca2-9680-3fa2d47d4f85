#!/usr/bin/env node

/**
 * UI Search Suggestions Performance Test
 * 
 * This test measures the REAL user experience including:
 * - Typing simulation with realistic delays
 * - Frontend debouncing behavior
 * - Network request time
 * - JavaScript processing
 * - DOM rendering and suggestions display
 * - Visual feedback timing
 */

const { chromium } = require('playwright');

const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  testQueries: ['ovens', 'samsung', 'cookers'],
  typingSpeed: 150, // ms between characters (realistic typing)
  iterations: 5,
  timeout: 5000
};

const results = {
  tests: [],
  errors: [],
  summary: {}
};

/**
 * Measure complete user experience from typing to suggestions display
 */
async function measureUISearchExperience(page, query, iteration) {
  console.log(`\n🔍 Test ${iteration}: Measuring UI experience for "${query}"`);
  
  try {
    // Navigate to homepage
    await page.goto(TEST_CONFIG.baseUrl, { waitUntil: 'networkidle' });
    
    // Find search input - try multiple selectors
    let searchInput;
    const selectors = [
      'input[type="search"]',
      '[data-testid="search-input"]', 
      '.search-input',
      '#search',
      'input[placeholder*="search" i]',
      'input[name="search"]',
      'input[name="q"]'
    ];
    
    for (const selector of selectors) {
      try {
        searchInput = page.locator(selector).first();
        await searchInput.waitFor({ state: 'visible', timeout: 2000 });
        console.log(`  ✅ Found search input: ${selector}`);
        break;
      } catch (e) {
        continue;
      }
    }
    
    if (!searchInput) {
      throw new Error('Could not find search input element');
    }
    
    // Clear any existing content
    await searchInput.fill('');
    await page.waitForTimeout(100);
    
    // Start timing the complete user experience
    const startTime = Date.now();
    
    // Set up listeners for various stages
    const stages = {
      typing: null,
      firstNetwork: null,
      suggestionsAppear: null,
      suggestionsReady: null
    };
    
    // Listen for network requests to suggestions API
    const networkPromise = new Promise((resolve) => {
      page.on('response', (response) => {
        if (response.url().includes('/api/search/suggestions')) {
          stages.firstNetwork = Date.now();
          resolve(response);
        }
      });
    });
    
    // Listen for suggestions container to appear
    const suggestionsPromise = Promise.race([
      // Try different possible suggestion selectors
      page.locator('.suggestions').first().waitFor({ state: 'visible', timeout: TEST_CONFIG.timeout }),
      page.locator('[data-testid="search-suggestions"]').first().waitFor({ state: 'visible', timeout: TEST_CONFIG.timeout }),
      page.locator('.search-suggestions').first().waitFor({ state: 'visible', timeout: TEST_CONFIG.timeout }),
      page.locator('.dropdown').first().waitFor({ state: 'visible', timeout: TEST_CONFIG.timeout }),
      page.locator('[role="listbox"]').first().waitFor({ state: 'visible', timeout: TEST_CONFIG.timeout })
    ]).then(() => {
      stages.suggestionsAppear = Date.now();
      return stages.suggestionsAppear;
    }).catch(() => null);
    
    // Start typing character by character (realistic user behavior)
    console.log(`  ⌨️  Typing "${query}" with ${TEST_CONFIG.typingSpeed}ms delays...`);
    
    for (let i = 0; i < query.length; i++) {
      await searchInput.type(query[i]);
      await page.waitForTimeout(TEST_CONFIG.typingSpeed);
      
      if (i === query.length - 1) {
        stages.typing = Date.now();
        console.log(`  ✅ Typing completed: ${stages.typing - startTime}ms`);
      }
    }
    
    // Wait for network request and suggestions to appear
    const [networkResponse, suggestionsTime] = await Promise.all([
      networkPromise.catch(() => null),
      suggestionsPromise
    ]);
    
    // Wait a bit more to ensure suggestions are fully rendered
    await page.waitForTimeout(100);
    stages.suggestionsReady = Date.now();
    
    // Try to get suggestion details
    let suggestionCount = 0;
    let suggestions = [];
    
    try {
      const suggestionSelectors = [
        '.suggestions li',
        '[data-testid="search-suggestions"] li', 
        '.search-suggestions li',
        '.dropdown li',
        '[role="option"]'
      ];
      
      for (const selector of suggestionSelectors) {
        const elements = await page.locator(selector).all();
        if (elements.length > 0) {
          suggestionCount = elements.length;
          
          // Get text from first few suggestions
          for (let i = 0; i < Math.min(3, elements.length); i++) {
            const text = await elements[i].textContent();
            if (text && text.trim()) {
              suggestions.push(text.trim());
            }
          }
          break;
        }
      }
    } catch (error) {
      console.log(`  ⚠️  Could not extract suggestion details: ${error.message}`);
    }
    
    const result = {
      iteration,
      query,
      success: stages.suggestionsAppear !== null,
      timings: {
        totalUserExperience: stages.suggestionsReady - startTime,
        typingDuration: stages.typing - startTime,
        timeToFirstNetwork: stages.firstNetwork ? stages.firstNetwork - startTime : null,
        timeToSuggestionsVisible: stages.suggestionsAppear ? stages.suggestionsAppear - startTime : null,
        timeToSuggestionsReady: stages.suggestionsReady - startTime,
        
        // Derived metrics
        networkToDisplay: stages.suggestionsAppear && stages.firstNetwork ? 
          stages.suggestionsAppear - stages.firstNetwork : null,
        typingToDisplay: stages.suggestionsAppear ? 
          stages.suggestionsAppear - stages.typing : null
      },
      suggestions: {
        count: suggestionCount,
        items: suggestions
      },
      timestamp: new Date().toISOString()
    };
    
    results.tests.push(result);
    
    console.log(`  📊 Results:`);
    console.log(`     Total UI Experience: ${result.timings.totalUserExperience}ms`);
    console.log(`     Typing Duration: ${result.timings.typingDuration}ms`);
    if (result.timings.timeToFirstNetwork) {
      console.log(`     Time to API Call: ${result.timings.timeToFirstNetwork}ms`);
    }
    if (result.timings.timeToSuggestionsVisible) {
      console.log(`     Time to Suggestions Visible: ${result.timings.timeToSuggestionsVisible}ms`);
      console.log(`     Typing → Suggestions: ${result.timings.typingToDisplay}ms`);
    }
    console.log(`     Suggestions Found: ${suggestionCount}`);
    
    return result;
    
  } catch (error) {
    const errorResult = {
      iteration,
      query,
      error: error.message,
      success: false,
      timestamp: new Date().toISOString()
    };
    
    results.errors.push(errorResult);
    console.log(`  ❌ Error: ${error.message}`);
    return errorResult;
  }
}

/**
 * Calculate performance statistics
 */
function calculateUIStatistics() {
  const successfulTests = results.tests.filter(t => t.success);
  
  if (successfulTests.length === 0) {
    return null;
  }
  
  const totalExperience = successfulTests.map(t => t.timings.totalUserExperience);
  const typingToDisplay = successfulTests
    .filter(t => t.timings.typingToDisplay !== null)
    .map(t => t.timings.typingToDisplay);
  const networkToDisplay = successfulTests
    .filter(t => t.timings.networkToDisplay !== null)
    .map(t => t.timings.networkToDisplay);
  
  return {
    totalTests: results.tests.length,
    successfulTests: successfulTests.length,
    successRate: (successfulTests.length / results.tests.length) * 100,
    
    totalUserExperience: {
      avg: totalExperience.reduce((a, b) => a + b, 0) / totalExperience.length,
      min: Math.min(...totalExperience),
      max: Math.max(...totalExperience),
      median: totalExperience.sort()[Math.floor(totalExperience.length / 2)]
    },
    
    typingToSuggestions: typingToDisplay.length > 0 ? {
      avg: typingToDisplay.reduce((a, b) => a + b, 0) / typingToDisplay.length,
      min: Math.min(...typingToDisplay),
      max: Math.max(...typingToDisplay),
      median: typingToDisplay.sort()[Math.floor(typingToDisplay.length / 2)]
    } : null,
    
    networkToDisplay: networkToDisplay.length > 0 ? {
      avg: networkToDisplay.reduce((a, b) => a + b, 0) / networkToDisplay.length,
      min: Math.min(...networkToDisplay),
      max: Math.max(...networkToDisplay)
    } : null,
    
    suggestions: {
      avgCount: successfulTests.reduce((sum, test) => sum + test.suggestions.count, 0) / successfulTests.length
    }
  };
}

/**
 * Display comprehensive UI test results
 */
function displayUIResults() {
  console.log('\n' + '='.repeat(80));
  console.log('🌐 REAL UI SEARCH SUGGESTIONS PERFORMANCE RESULTS');
  console.log('='.repeat(80));
  
  const stats = calculateUIStatistics();
  
  if (!stats) {
    console.log('\n❌ No successful UI tests to analyze');
    if (results.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      results.errors.forEach((error, i) => {
        console.log(`   ${i + 1}. "${error.query}": ${error.error}`);
      });
    }
    return;
  }
  
  console.log('\n📊 UI Performance Statistics:');
  console.log(`   Success Rate: ${stats.successRate.toFixed(1)}% (${stats.successfulTests}/${stats.totalTests})`);
  
  console.log('\n⏱️  Complete User Experience:');
  console.log(`   Average Time: ${stats.totalUserExperience.avg.toFixed(1)}ms`);
  console.log(`   Min: ${stats.totalUserExperience.min}ms`);
  console.log(`   Max: ${stats.totalUserExperience.max}ms`);
  console.log(`   Median: ${stats.totalUserExperience.median}ms`);
  
  if (stats.typingToSuggestions) {
    console.log('\n⚡ Typing → Suggestions Appear:');
    console.log(`   Average: ${stats.typingToSuggestions.avg.toFixed(1)}ms`);
    console.log(`   Min: ${stats.typingToSuggestions.min}ms`);
    console.log(`   Max: ${stats.typingToSuggestions.max}ms`);
    console.log(`   Median: ${stats.typingToSuggestions.median}ms`);
  }
  
  if (stats.networkToDisplay) {
    console.log('\n🌐 Network → UI Display:');
    console.log(`   Average: ${stats.networkToDisplay.avg.toFixed(1)}ms`);
    console.log(`   Min: ${stats.networkToDisplay.min}ms`);
    console.log(`   Max: ${stats.networkToDisplay.max}ms`);
  }
  
  console.log('\n🎯 UI Performance Assessment:');
  
  if (stats.typingToSuggestions) {
    const avg = stats.typingToSuggestions.avg;
    if (avg <= 200) {
      console.log(`   ✅ EXCELLENT: Suggestions appear in ${avg.toFixed(1)}ms after typing (feels instant)`);
    } else if (avg <= 500) {
      console.log(`   ⚠️  ACCEPTABLE: Suggestions appear in ${avg.toFixed(1)}ms (noticeable but reasonable)`);
    } else {
      console.log(`   ❌ SLOW: Suggestions take ${avg.toFixed(1)}ms to appear (users will notice delay)`);
    }
  }
  
  if (stats.totalUserExperience.avg > 1000) {
    console.log(`   ⚠️  Total experience time (${stats.totalUserExperience.avg.toFixed(1)}ms) may feel slow to users`);
  }
  
  console.log('\n📈 Comparison with API-Only Tests:');
  console.log(`   API Response Time: ~8ms (from previous test)`);
  console.log(`   UI Experience Time: ~${stats.typingToSuggestions ? stats.typingToSuggestions.avg.toFixed(1) : 'N/A'}ms`);
  console.log(`   UI Overhead: ~${stats.typingToSuggestions ? (stats.typingToSuggestions.avg - 8).toFixed(1) : 'N/A'}ms`);
  
  console.log('\n' + '='.repeat(80));
  console.log('✅ UI performance test completed');
  console.log('='.repeat(80));
}

/**
 * Main test execution
 */
async function runUITest() {
  console.log('🚀 Starting UI Search Suggestions Performance Test');
  console.log(`📅 ${new Date().toISOString()}`);
  console.log(`🌐 Testing: ${TEST_CONFIG.baseUrl}`);
  console.log(`⌨️  Typing Speed: ${TEST_CONFIG.typingSpeed}ms between characters\n`);
  
  let browser = null;
  
  try {
    // Launch browser
    console.log('🌐 Launching browser...');
    browser = await chromium.launch({ 
      headless: false,
      slowMo: 100 // Add slight delay to make it more realistic
    });
    
    const context = await browser.newContext({
      viewport: { width: 1280, height: 720 }
    });
    
    const page = await context.newPage();
    
    // Run tests for each query
    for (const query of TEST_CONFIG.testQueries) {
      console.log(`\n${'='.repeat(50)}`);
      console.log(`🔍 Testing query: "${query}"`);
      console.log(`${'='.repeat(50)}`);
      
      for (let i = 1; i <= TEST_CONFIG.iterations; i++) {
        await measureUISearchExperience(page, query, i);
        
        if (i < TEST_CONFIG.iterations) {
          console.log('  ⏳ Waiting before next test...');
          await page.waitForTimeout(1000);
        }
      }
    }
    
    // Display results
    displayUIResults();
    
  } catch (error) {
    console.error('❌ UI test failed:', error);
    process.exit(1);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
if (require.main === module) {
  runUITest().catch(console.error);
}

module.exports = { runUITest };