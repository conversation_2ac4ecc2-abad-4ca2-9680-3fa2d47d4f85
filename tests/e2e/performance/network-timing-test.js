#!/usr/bin/env node

/**
 * Network Timing Performance Test
 * 
 * This test simulates realistic user experience by measuring:
 * - Frontend debouncing delays
 * - Network roundtrip time from browser
 * - Complete request/response cycle
 * - Realistic typing simulation
 */

const { chromium } = require('playwright');

const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  queries: ['ovens', 'samsung', 'test'],
  typingSpeed: 120, // ms between characters (realistic)
  debounceDelay: 150, // optimized frontend debounce
  iterations: 5
};

const results = {
  tests: [],
  summary: {}
};

/**
 * Measure complete network timing including realistic user behavior
 */
async function measureNetworkTiming(page, query, iteration) {
  console.log(`\n⚡ Test ${iteration}: Network timing for "${query}"`);
  
  try {
    // Go to a blank page first
    await page.goto('about:blank');
    
    const measurements = {
      query,
      iteration,
      typingDuration: query.length * TEST_CONFIG.typingSpeed,
      debounceDelay: TEST_CONFIG.debounceDelay,
      networkStart: null,
      networkEnd: null,
      networkDuration: null,
      totalUserExperience: null,
      success: false
    };
    
    // Set up network monitoring before making any requests
    let networkStartTime = null;
    let networkEndTime = null;
    
    page.on('request', (request) => {
      if (request.url().includes('/api/search/suggestions')) {
        networkStartTime = Date.now();
        measurements.networkStart = networkStartTime;
        console.log(`  🌐 Network request started: ${request.url()}`);
      }
    });
    
    page.on('response', (response) => {
      if (response.url().includes('/api/search/suggestions')) {
        networkEndTime = Date.now();
        measurements.networkEnd = networkEndTime;
        measurements.networkDuration = networkEndTime - networkStartTime;
        measurements.success = response.status() === 200;
        console.log(`  ✅ Network response received: ${measurements.networkDuration}ms (Status: ${response.status()})`);
      }
    });
    
    // Simulate user typing experience
    const userStartTime = Date.now();
    console.log(`  ⌨️  Simulating user typing "${query}" (${TEST_CONFIG.typingSpeed}ms per character)...`);
    
    // Simulate typing delay
    await page.waitForTimeout(measurements.typingDuration);
    console.log(`  ⏳ Simulating debounce delay (${TEST_CONFIG.debounceDelay}ms)...`);
    
    // Simulate debounce delay
    await page.waitForTimeout(measurements.debounceDelay);
    
    // Now make the actual API request (simulating what frontend would do)
    const apiUrl = `${TEST_CONFIG.baseUrl}/api/search/suggestions?q=${encodeURIComponent(query)}`;
    console.log(`  🔗 Making API request: ${apiUrl}`);
    
    try {
      await page.goto(apiUrl, { timeout: 5000 });
      
      // Wait a bit more to ensure response is fully processed
      await page.waitForTimeout(100);
      
      if (measurements.networkDuration) {
        measurements.totalUserExperience = measurements.typingDuration + measurements.debounceDelay + measurements.networkDuration;
        console.log(`  📊 Complete user experience: ${measurements.totalUserExperience}ms`);
        console.log(`     - Typing: ${measurements.typingDuration}ms`);
        console.log(`     - Debounce: ${measurements.debounceDelay}ms`);
        console.log(`     - Network: ${measurements.networkDuration}ms`);
      }
      
    } catch (error) {
      console.log(`  ❌ Request failed: ${error.message}`);
      measurements.success = false;
    }
    
    results.tests.push(measurements);
    return measurements;
    
  } catch (error) {
    console.log(`  ❌ Test failed: ${error.message}`);
    const errorResult = {
      query,
      iteration,
      error: error.message,
      success: false
    };
    results.tests.push(errorResult);
    return errorResult;
  }
}

/**
 * Calculate and display realistic performance statistics
 */
function displayNetworkResults() {
  console.log('\n' + '='.repeat(80));
  console.log('🌐 REALISTIC USER EXPERIENCE PERFORMANCE RESULTS');
  console.log('='.repeat(80));
  
  const successfulTests = results.tests.filter(t => t.success);
  
  console.log('\n📊 Test Summary:');
  console.log(`   Total Tests: ${results.tests.length}`);
  console.log(`   Successful: ${successfulTests.length}`);
  console.log(`   Success Rate: ${(successfulTests.length / results.tests.length * 100).toFixed(1)}%`);
  
  if (successfulTests.length === 0) {
    console.log('\n❌ No successful tests to analyze');
    return;
  }
  
  // Calculate statistics
  const networkTimes = successfulTests.map(t => t.networkDuration);
  const totalExperiences = successfulTests.map(t => t.totalUserExperience);
  const typingTimes = successfulTests.map(t => t.typingDuration);
  
  const stats = {
    network: {
      avg: networkTimes.reduce((a, b) => a + b, 0) / networkTimes.length,
      min: Math.min(...networkTimes),
      max: Math.max(...networkTimes),
      median: networkTimes.sort()[Math.floor(networkTimes.length / 2)]
    },
    totalExperience: {
      avg: totalExperiences.reduce((a, b) => a + b, 0) / totalExperiences.length,
      min: Math.min(...totalExperiences),
      max: Math.max(...totalExperiences),
      median: totalExperiences.sort()[Math.floor(totalExperiences.length / 2)]
    },
    typing: {
      avg: typingTimes.reduce((a, b) => a + b, 0) / typingTimes.length
    }
  };
  
  console.log('\n⚡ Network Performance:');
  console.log(`   Average API Response: ${stats.network.avg.toFixed(1)}ms`);
  console.log(`   Min: ${stats.network.min}ms`);
  console.log(`   Max: ${stats.network.max}ms`);
  console.log(`   Median: ${stats.network.median}ms`);
  
  console.log('\n👤 Complete User Experience:');
  console.log(`   Average Total Time: ${stats.totalExperience.avg.toFixed(1)}ms`);
  console.log(`   Min: ${stats.totalExperience.min}ms`);
  console.log(`   Max: ${stats.totalExperience.max}ms`);
  console.log(`   Median: ${stats.totalExperience.median}ms`);
  
  console.log('\n📋 Experience Breakdown:');
  console.log(`   Typing Duration: ${stats.typing.avg.toFixed(1)}ms (varies by query length)`);
  console.log(`   Frontend Debounce: ${TEST_CONFIG.debounceDelay}ms (fixed)`);
  console.log(`   Network + API: ${stats.network.avg.toFixed(1)}ms (variable)`);
  
  console.log('\n🎯 Performance Assessment:');
  
  const avgTotal = stats.totalExperience.avg;
  if (avgTotal <= 300) {
    console.log(`   ✅ EXCELLENT: Total experience ${avgTotal.toFixed(1)}ms (feels very responsive)`);
  } else if (avgTotal <= 500) {
    console.log(`   ✅ GOOD: Total experience ${avgTotal.toFixed(1)}ms (acceptable responsiveness)`);
  } else if (avgTotal <= 1000) {
    console.log(`   ⚠️  ACCEPTABLE: Total experience ${avgTotal.toFixed(1)}ms (noticeable but reasonable)`);
  } else {
    console.log(`   ❌ SLOW: Total experience ${avgTotal.toFixed(1)}ms (users will notice delay)`);
  }
  
  console.log('\n💡 Optimization Opportunities:');
  
  const networkPortion = (stats.network.avg / avgTotal) * 100;
  const debouncePortion = (TEST_CONFIG.debounceDelay / avgTotal) * 100;
  const typingPortion = (stats.typing.avg / avgTotal) * 100;
  
  console.log(`   Network/API: ${networkPortion.toFixed(1)}% of total time (${stats.network.avg.toFixed(1)}ms)`);
  console.log(`   Debounce: ${debouncePortion.toFixed(1)}% of total time (${TEST_CONFIG.debounceDelay}ms)`);
  console.log(`   Typing: ${typingPortion.toFixed(1)}% of total time (${stats.typing.avg.toFixed(1)}ms)`);
  
  if (debouncePortion > 30) {
    console.log(`   🎯 Reduce debounce delay from ${TEST_CONFIG.debounceDelay}ms to 150ms for ${TEST_CONFIG.debounceDelay - 150}ms improvement`);
  }
  
  if (networkPortion > 40) {
    console.log(`   🎯 Implement caching to reduce network requests`);
  }
  
  console.log('\n📈 Comparison with Previous Tests:');
  console.log(`   Pure API Time: ~8ms (from previous test)`);
  console.log(`   Network Round-trip: ~${stats.network.avg.toFixed(1)}ms`);
  console.log(`   Complete UI Experience: ~${avgTotal.toFixed(1)}ms`);
  console.log(`   UI Overhead: ${(avgTotal / 8).toFixed(1)}x slower than pure API`);
  
  console.log('\n' + '='.repeat(80));
  console.log('✅ Realistic performance test completed');
  console.log('='.repeat(80));
}

/**
 * Main test execution
 */
async function runNetworkTimingTest() {
  console.log('🚀 Realistic User Experience Performance Test');
  console.log(`📅 ${new Date().toISOString()}`);
  console.log(`🌐 Testing: ${TEST_CONFIG.baseUrl}`);
  console.log(`⌨️  Typing Speed: ${TEST_CONFIG.typingSpeed}ms per character`);
  console.log(`⏳ Debounce Delay: ${TEST_CONFIG.debounceDelay}ms\n`);
  
  let browser = null;
  
  try {
    console.log('🌐 Launching browser for network timing...');
    browser = await chromium.launch({ 
      headless: true,
      args: ['--disable-web-security']
    });
    
    const context = await browser.newContext({
      viewport: { width: 1280, height: 720 }
    });
    
    const page = await context.newPage();
    
    // Run tests for each query
    for (const query of TEST_CONFIG.queries) {
      console.log(`\n${'='.repeat(50)}`);
      console.log(`🔍 Testing realistic experience for: "${query}"`);
      console.log(`${'='.repeat(50)}`);
      
      for (let i = 1; i <= TEST_CONFIG.iterations; i++) {
        await measureNetworkTiming(page, query, i);
        
        if (i < TEST_CONFIG.iterations) {
          await page.waitForTimeout(500); // Brief pause between tests
        }
      }
    }
    
    // Display comprehensive results
    displayNetworkResults();
    
  } catch (error) {
    console.error('❌ Network timing test failed:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the test
if (require.main === module) {
  runNetworkTimingTest().catch(console.error);
}

module.exports = { runNetworkTimingTest };