#!/usr/bin/env node

/**
 * Simple Search Suggestions Performance Test
 * 
 * Measures API response time for search suggestions endpoint
 * regardless of whether data is returned or not.
 */

const http = require('http');

const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  endpoint: '/api/search/suggestions',
  queries: ['ovens', 'samsung', 'test', 'a', 'cookers'],
  warmupRequests: 3,
  testIterations: 15
};

async function measureRequest(query) {
  return new Promise((resolve, reject) => {
    const startTime = process.hrtime.bigint();
    const url = `${TEST_CONFIG.baseUrl}${TEST_CONFIG.endpoint}?q=${encodeURIComponent(query)}`;
    
    const req = http.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const endTime = process.hrtime.bigint();
        const responseTime = Number(endTime - startTime) / 1000000; // Convert to milliseconds
        
        try {
          const parsedData = JSON.parse(data);
          resolve({
            query,
            responseTime,
            statusCode: res.statusCode,
            dataReceived: data.length,
            suggestions: {
              categories: parsedData.categories?.length || 0,
              brands: parsedData.brands?.length || 0,
              products: parsedData.products?.length || 0,
              total: (parsedData.categories?.length || 0) + (parsedData.brands?.length || 0) + (parsedData.products?.length || 0)
            },
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          resolve({
            query,
            responseTime,
            statusCode: res.statusCode,
            error: 'JSON parse error',
            dataReceived: data.length,
            timestamp: new Date().toISOString()
          });
        }
      });
    });
    
    req.on('error', (error) => {
      const endTime = process.hrtime.bigint();
      const responseTime = Number(endTime - startTime) / 1000000;
      reject({
        query,
        error: error.message,
        responseTime,
        timestamp: new Date().toISOString()
      });
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      reject({
        query,
        error: 'Request timeout',
        responseTime: 5000,
        timestamp: new Date().toISOString()
      });
    });
  });
}

async function runTest() {
  console.log('🚀 Search Suggestions Performance Test');
  console.log(`📅 ${new Date().toISOString()}`);
  console.log(`🌐 Testing: ${TEST_CONFIG.baseUrl}${TEST_CONFIG.endpoint}\n`);
  
  const results = [];
  const errors = [];
  
  // Test each query
  for (const query of TEST_CONFIG.queries) {
    console.log(`\n🔍 Testing query: "${query}"`);
    
    // Warmup
    console.log('  🔥 Warmup...');
    for (let i = 0; i < TEST_CONFIG.warmupRequests; i++) {
      try {
        await measureRequest(query);
        process.stdout.write(`    ${i + 1}`);
      } catch (error) {
        process.stdout.write(`    ❌`);
      }
      await new Promise(r => setTimeout(r, 50));
    }
    
    console.log('\n  ⚡ Performance tests...');
    const queryResults = [];
    
    // Performance tests
    for (let i = 0; i < TEST_CONFIG.testIterations; i++) {
      try {
        const result = await measureRequest(query);
        queryResults.push(result);
        results.push(result);
        
        if (i % 5 === 4) {
          console.log(`    Tests ${i - 3}-${i + 1}: ${queryResults.slice(-5).map(r => r.responseTime.toFixed(1)).join('ms, ')}ms`);
        }
        
      } catch (error) {
        errors.push(error);
        console.log(`    Test ${i + 1}: ❌ ${error.error}`);
      }
      
      await new Promise(r => setTimeout(r, 100));
    }
    
    // Quick stats for this query
    if (queryResults.length > 0) {
      const times = queryResults.map(r => r.responseTime);
      const avg = times.reduce((a, b) => a + b, 0) / times.length;
      const min = Math.min(...times);
      const max = Math.max(...times);
      
      console.log(`  📊 "${query}": ${avg.toFixed(1)}ms avg (${min.toFixed(1)}-${max.toFixed(1)}ms) | ${queryResults[0]?.suggestions?.total || 0} suggestions`);
    }
  }
  
  // Overall results
  console.log('\n' + '='.repeat(70));
  console.log('📊 OVERALL PERFORMANCE RESULTS');
  console.log('='.repeat(70));
  
  if (results.length === 0) {
    console.log('❌ No successful requests to analyze');
    return;
  }
  
  const allTimes = results.map(r => r.responseTime);
  const sorted = allTimes.slice().sort((a, b) => a - b);
  
  const stats = {
    totalRequests: results.length + errors.length,
    successfulRequests: results.length,
    successRate: (results.length / (results.length + errors.length)) * 100,
    min: sorted[0],
    max: sorted[sorted.length - 1],
    avg: allTimes.reduce((a, b) => a + b, 0) / allTimes.length,
    median: sorted.length % 2 === 0 
      ? (sorted[sorted.length / 2 - 1] + sorted[sorted.length / 2]) / 2
      : sorted[Math.floor(sorted.length / 2)],
    p95: sorted[Math.ceil(sorted.length * 0.95) - 1],
    p99: sorted[Math.ceil(sorted.length * 0.99) - 1]
  };
  
  console.log(`\n⚡ Performance Statistics:`);
  console.log(`   Success Rate: ${stats.successRate.toFixed(1)}% (${stats.successfulRequests}/${stats.totalRequests})`);
  console.log(`   Average Response Time: ${stats.avg.toFixed(2)}ms`);
  console.log(`   Median: ${stats.median.toFixed(2)}ms`);
  console.log(`   Min: ${stats.min.toFixed(2)}ms`);
  console.log(`   Max: ${stats.max.toFixed(2)}ms`);
  console.log(`   95th Percentile: ${stats.p95.toFixed(2)}ms`);
  console.log(`   99th Percentile: ${stats.p99.toFixed(2)}ms`);
  
  // Performance assessment
  console.log(`\n🎯 Performance Assessment:`);
  
  if (stats.avg <= 50) {
    console.log(`   ✅ EXCELLENT: Average response time meets optimization target (<50ms)`);
  } else if (stats.avg <= 150) {
    console.log(`   ⚠️  ACCEPTABLE: Response time within current baseline (<150ms)`);
    console.log(`   🎯 Target improvement: ${(stats.avg - 50).toFixed(1)}ms (${((stats.avg - 50) / stats.avg * 100).toFixed(1)}% reduction needed)`);
  } else {
    console.log(`   ❌ NEEDS IMPROVEMENT: Response time exceeds baseline (>150ms)`);
    console.log(`   🚨 Critical improvement needed: ${(stats.avg - 150).toFixed(1)}ms over threshold`);
  }
  
  if (stats.p95 > stats.avg * 1.5) {
    console.log(`   ⚠️  High tail latency: 95th percentile is ${((stats.p95 / stats.avg - 1) * 100).toFixed(1)}% higher than average`);
  }
  
  if (stats.successRate < 95) {
    console.log(`   ⚠️  Reliability concern: Success rate below 95%`);
  }
  
  // Breakdown by query
  console.log(`\n🔍 Performance by Query:`);
  for (const query of TEST_CONFIG.queries) {
    const queryResults = results.filter(r => r.query === query);
    if (queryResults.length > 0) {
      const queryTimes = queryResults.map(r => r.responseTime);
      const queryAvg = queryTimes.reduce((a, b) => a + b, 0) / queryTimes.length;
      const suggestionsCount = queryResults[0]?.suggestions?.total || 0;
      
      console.log(`   "${query}": ${queryAvg.toFixed(1)}ms avg | ${suggestionsCount} suggestions | ${queryResults.length} tests`);
    }
  }
  
  if (errors.length > 0) {
    console.log(`\n❌ Errors (${errors.length}):`);
    errors.forEach((error, i) => {
      console.log(`   ${i + 1}. "${error.query}": ${error.error}`);
    });
  }
  
  console.log('\n' + '='.repeat(70));
  console.log(`✅ Test completed successfully`);
  console.log(`📈 Baseline established: ${stats.avg.toFixed(1)}ms average response time`);
  console.log('='.repeat(70));
}

// Run the test
if (require.main === module) {
  runTest().catch(console.error);
}

module.exports = { runTest };