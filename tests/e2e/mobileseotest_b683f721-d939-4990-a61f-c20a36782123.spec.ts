
import { test } from '@playwright/test';
import { expect } from '@playwright/test';

test('MobileSEOTest_2025-08-06', async ({ page, context }) => {
  
    // Navigate to URL
    await page.goto('http://localhost:3002/products/samsung-series-5-nq5b5763dbk-compact-oven-with-microwave-combi-clean-black-nq5b5763dbku4');

    // Take screenshot
    await page.screenshot({ path: 'mobile-seo-test-iphone-se-375x667.png', { fullPage: true } });

    // Navigate to URL
    await page.goto('http://localhost:3002/products/samsung-series-5-nq5b5763dbk-compact-oven-with-microwave-combi-clean-black-nq5b5763dbku4');

    // Take screenshot
    await page.screenshot({ path: 'mobile-seo-test-iphone-12-pro-390x844.png', { fullPage: true } });

    // Navigate to URL
    await page.goto('http://localhost:3002/products/samsung-series-5-nq5b5763dbk-compact-oven-with-microwave-combi-clean-black-nq5b5763dbku4');

    // Take screenshot
    await page.screenshot({ path: 'mobile-seo-test-galaxy-s21-360x800.png', { fullPage: true } });
});