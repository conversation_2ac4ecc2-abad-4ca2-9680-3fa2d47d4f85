#!/usr/bin/env node

/**
 * Comprehensive Pagination State Management Test
 * 
 * This script tests the actual user experience of pagination state persistence
 * across navigation scenarios to identify the root cause of the issue.
 */

const { chromium } = require('playwright');

async function testPaginationState() {
  console.log('🚀 Starting Pagination State Management Test...\n');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Test 1: Basic pagination navigation
    console.log('📋 Test 1: Basic Pagination Navigation');
    await page.goto('http://localhost:3000/products');
    await page.waitForLoadState('networkidle');
    
    // Check initial state
    const initialPage = await page.locator('button[aria-current="page"]').textContent();
    console.log(`  ✓ Initial page: ${initialPage}`);
    
    // Navigate to page 2
    await page.click('button[aria-label="Go to page 2"]');
    await page.waitForLoadState('networkidle');
    
    const page2Active = await page.locator('button[aria-current="page"]').textContent();
    console.log(`  ✓ After clicking page 2: ${page2Active}`);
    
    // Check URL
    const url2 = page.url();
    console.log(`  ✓ URL after page 2: ${url2}`);
    
    // Test 2: Product detail navigation and back
    console.log('\n📋 Test 2: Product Detail Navigation and Back');
    
    // Click on first product
    const firstProduct = page.locator('[data-testid="product-item"]').first();
    await firstProduct.click();
    await page.waitForLoadState('networkidle');
    
    const productUrl = page.url();
    console.log(`  ✓ Product page URL: ${productUrl}`);
    
    // Go back using browser back button
    await page.goBack();
    await page.waitForLoadState('networkidle');

    const backUrl = page.url();

    // Check if we're back on the products page (pagination controls should be visible)
    const paginationExists = await page.locator('button[aria-current="page"]').count() > 0;
    if (paginationExists) {
      const backPage = await page.locator('button[aria-current="page"]').textContent();
      console.log(`  ✓ After browser back - URL: ${backUrl}`);
      console.log(`  ✓ After browser back - Active page: ${backPage}`);
    } else {
      console.log(`  ✓ After browser back - URL: ${backUrl}`);
      console.log(`  ⚠ No pagination controls found (might not be on products page)`);
    }
    
    // Test 3: Direct URL navigation
    console.log('\n📋 Test 3: Direct URL Navigation');
    
    await page.goto('http://localhost:3000/products?page=3');
    await page.waitForLoadState('networkidle');
    
    const directPage = await page.locator('button[aria-current="page"]').textContent();
    const directUrl = page.url();
    console.log(`  ✓ Direct navigation to page 3 - Active page: ${directPage}`);
    console.log(`  ✓ Direct navigation URL: ${directUrl}`);
    
    // Test 4: Refresh behavior
    console.log('\n📋 Test 4: Page Refresh Behavior');
    
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    const refreshPage = await page.locator('button[aria-current="page"]').textContent();
    const refreshUrl = page.url();
    console.log(`  ✓ After refresh - Active page: ${refreshPage}`);
    console.log(`  ✓ After refresh URL: ${refreshUrl}`);
    
    // Test 5: Multiple navigation scenario
    console.log('\n📋 Test 5: Multiple Navigation Scenario');
    
    // Go to page 2
    await page.click('button[aria-label="Go to page 2"]');
    await page.waitForLoadState('networkidle');
    
    // Click product
    const secondProduct = page.locator('[data-testid="product-item"]').first();
    await secondProduct.click();
    await page.waitForLoadState('networkidle');
    
    // Go back
    await page.goBack();
    await page.waitForLoadState('networkidle');
    
    // Go to page 4
    await page.click('button[aria-label="Go to page 4"]');
    await page.waitForLoadState('networkidle');
    
    // Click another product
    const fourthProduct = page.locator('[data-testid="product-item"]').first();
    await fourthProduct.click();
    await page.waitForLoadState('networkidle');
    
    // Go back
    await page.goBack();
    await page.waitForLoadState('networkidle');
    
    const finalPage = await page.locator('button[aria-current="page"]').textContent();
    const finalUrl = page.url();
    console.log(`  ✓ Final state - Active page: ${finalPage}`);
    console.log(`  ✓ Final state URL: ${finalUrl}`);
    
    // Test 6: React Query cache inspection
    console.log('\n📋 Test 6: React Query Cache Inspection');
    
    const queryCache = await page.evaluate(() => {
      // Try to access React Query cache if available
      if (window.__REACT_QUERY_STATE__) {
        return window.__REACT_QUERY_STATE__;
      }
      return 'React Query state not accessible';
    });
    
    console.log(`  ✓ React Query cache: ${JSON.stringify(queryCache, null, 2)}`);
    
    // Test 7: Local Storage and Session Storage
    console.log('\n📋 Test 7: Storage Inspection');
    
    const localStorage = await page.evaluate(() => {
      const storage = {};
      for (let i = 0; i < window.localStorage.length; i++) {
        const key = window.localStorage.key(i);
        storage[key] = window.localStorage.getItem(key);
      }
      return storage;
    });
    
    const sessionStorage = await page.evaluate(() => {
      const storage = {};
      for (let i = 0; i < window.sessionStorage.length; i++) {
        const key = window.sessionStorage.key(i);
        storage[key] = window.sessionStorage.getItem(key);
      }
      return storage;
    });
    
    console.log(`  ✓ Local Storage: ${JSON.stringify(localStorage, null, 2)}`);
    console.log(`  ✓ Session Storage: ${JSON.stringify(sessionStorage, null, 2)}`);
    
    console.log('\n✅ Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
  }
}

// Run the test
testPaginationState().catch(console.error);
