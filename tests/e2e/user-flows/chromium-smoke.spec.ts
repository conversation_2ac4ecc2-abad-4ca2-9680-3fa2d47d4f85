import { test, expect } from '@playwright/test';

test('basic test', async ({ page }) => {
  const errors: any[] = [];
  page.on('pageerror', (err) => {
    errors.push(err.message);
  });

  await page.goto('http://localhost:3000/');
  const title = page.locator('h1');
  await expect(title).toHaveText('Get cashback and reward rebates from your favorite brands');

  expect(errors).toHaveLength(0);
});
