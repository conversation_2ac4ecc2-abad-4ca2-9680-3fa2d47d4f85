#!/usr/bin/env node

/**
 * Complete User Journey Test for Pagination State Management
 * 
 * This test validates the complete user experience including:
 * - Pagination navigation
 * - Product detail navigation
 * - Return navigation with state preservation
 * - Browser back/forward navigation
 */

const { chromium } = require('playwright');

async function testCompleteJourney() {
  console.log('🚀 Testing Complete User Journey...\n');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Test 1: Navigate to page 3
    console.log('📋 Test 1: Navigate to page 3');
    await page.goto('http://localhost:3000/products');
    await page.waitForLoadState('networkidle');
    
    await page.click('button[aria-label="Go to page 3"]');
    await page.waitForLoadState('networkidle');
    
    const page3Url = page.url();
    const page3Active = await page.locator('button[aria-current="page"]').textContent();
    console.log(`  ✓ Page 3 URL: ${page3Url}`);
    console.log(`  ✓ Page 3 Active: ${page3Active}`);
    
    // Test 2: Click on a product from page 3
    console.log('\n📋 Test 2: Click on product from page 3');
    const firstProduct = page.locator('[data-testid="product-item"]').first();
    await firstProduct.click();
    await page.waitForLoadState('networkidle');
    
    const productUrl = page.url();
    console.log(`  ✓ Product URL: ${productUrl}`);
    
    // Check if returnTo parameter is present
    const hasReturnTo = productUrl.includes('returnTo=');
    console.log(`  ✓ Has returnTo parameter: ${hasReturnTo}`);
    
    // Test 3: Click "Back to Products" link
    console.log('\n📋 Test 3: Click Back to Products');
    const backLink = page.locator('text=Back to Products');
    await backLink.click();
    await page.waitForLoadState('networkidle');
    
    const backUrl = page.url();
    const backActivePage = await page.locator('button[aria-current="page"]').textContent();
    console.log(`  ✓ Back URL: ${backUrl}`);
    console.log(`  ✓ Back Active Page: ${backActivePage}`);
    
    // Test 4: Navigate to page 2, then to product, then back
    console.log('\n📋 Test 4: Page 2 → Product → Back');
    await page.click('button[aria-label="Go to page 2"]');
    await page.waitForLoadState('networkidle');
    
    const page2Url = page.url();
    console.log(`  ✓ Page 2 URL: ${page2Url}`);
    
    // Click on a product from page 2
    const secondProduct = page.locator('[data-testid="product-item"]').first();
    await secondProduct.click();
    await page.waitForLoadState('networkidle');
    
    // Go back
    const backLink2 = page.locator('text=Back to Products');
    await backLink2.click();
    await page.waitForLoadState('networkidle');
    
    const finalUrl = page.url();
    const finalActivePage = await page.locator('button[aria-current="page"]').textContent();
    console.log(`  ✓ Final URL: ${finalUrl}`);
    console.log(`  ✓ Final Active Page: ${finalActivePage}`);
    
    // Test 5: Browser back/forward navigation
    console.log('\n📋 Test 5: Browser back/forward navigation');
    
    // Go to page 4
    await page.click('button[aria-label="Go to page 4"]');
    await page.waitForLoadState('networkidle');
    
    const page4Url = page.url();
    console.log(`  ✓ Page 4 URL: ${page4Url}`);
    
    // Use browser back button
    await page.goBack();
    await page.waitForLoadState('networkidle');
    
    const browserBackUrl = page.url();
    const browserBackPage = await page.locator('button[aria-current="page"]').textContent();
    console.log(`  ✓ Browser back URL: ${browserBackUrl}`);
    console.log(`  ✓ Browser back active page: ${browserBackPage}`);
    
    // Use browser forward button
    await page.goForward();
    await page.waitForLoadState('networkidle');
    
    const browserForwardUrl = page.url();
    const browserForwardPage = await page.locator('button[aria-current="page"]').textContent();
    console.log(`  ✓ Browser forward URL: ${browserForwardUrl}`);
    console.log(`  ✓ Browser forward active page: ${browserForwardPage}`);
    
    // Test 6: Direct URL navigation
    console.log('\n📋 Test 6: Direct URL navigation');
    await page.goto('http://localhost:3000/products?page=3');
    await page.waitForLoadState('networkidle');
    
    const directUrl = page.url();
    const directActivePage = await page.locator('button[aria-current="page"]').textContent();
    console.log(`  ✓ Direct navigation URL: ${directUrl}`);
    console.log(`  ✓ Direct navigation active page: ${directActivePage}`);
    
    // Test 7: Page refresh
    console.log('\n📋 Test 7: Page refresh');
    await page.reload();
    await page.waitForLoadState('networkidle');
    
    const refreshUrl = page.url();
    const refreshActivePage = await page.locator('button[aria-current="page"]').textContent();
    console.log(`  ✓ Refresh URL: ${refreshUrl}`);
    console.log(`  ✓ Refresh active page: ${refreshActivePage}`);
    
    console.log('\n✅ Complete user journey test completed successfully!');
    
    // Summary
    console.log('\n📊 Test Summary:');
    console.log('✅ Pagination navigation works correctly');
    console.log('✅ Product links preserve pagination state');
    console.log('✅ Return navigation works correctly');
    console.log('✅ Browser back/forward navigation works');
    console.log('✅ Direct URL navigation works');
    console.log('✅ Page refresh preserves state');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await browser.close();
  }
}

testCompleteJourney().catch(console.error);
