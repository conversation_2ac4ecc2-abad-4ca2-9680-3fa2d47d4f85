// tests/e2e/auth-compatibility.spec.ts
// End-to-end tests for JWT + HMAC compatibility using Playwright

import { test, expect } from '@playwright/test'
import crypto from 'crypto'

// Test configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000'
const PARTNER_ID = 'test-partner'
const PARTNER_SECRET = 'test-secret-minimum-32-characters-long'

// HMAC helper functions
function generateHMACSignature(
  method: string,
  path: string,
  timestamp: number,
  body: string = '',
  secret: string = PARTNER_SECRET
): string {
  const bodyHash = crypto.createHash('sha256').update(body).digest('hex')
  const message = `${method}\n${path}\n${timestamp}\n${bodyHash}`
  return crypto.createHmac('sha256', secret).update(message).digest('hex')
}

function createHMACHeaders(
  method: string,
  path: string,
  body: string = ''
): Record<string, string> {
  const timestamp = Math.floor(Date.now() / 1000)
  const signature = generateHMACSignature(method, path, timestamp, body)
  
  return {
    'X-Signature': `sha256=${signature}`,
    'X-Timestamp': timestamp.toString(),
    'X-Partner-ID': PARTNER_ID,
    'Content-Type': 'application/json'
  }
}

test.describe('JWT + HMAC Compatibility Tests', () => {
  test.describe('Contact Form JWT Authentication (PR 1 Functionality)', () => {
    test('should still protect contact form with JWT', async ({ page, request }) => {
      // Test that contact form still requires JWT authentication
      const contactData = {
        name: 'Test User',
        email: '<EMAIL>',
        message: 'Test message for compatibility check'
      }
      
      // Try to submit without JWT (should fail)
      const response = await request.post(`${BASE_URL}/api/contact`, {
        headers: { 'Content-Type': 'application/json' },
        data: JSON.stringify(contactData)
      })
      
      expect(response.status()).toBe(401)
    })

    test('should allow contact form submission with valid JWT', async ({ page }) => {
      // Navigate to contact page
      await page.goto(`${BASE_URL}/contact`)
      
      // Fill out the form
      await page.fill('[name="name"]', 'Test User')
      await page.fill('[name="email"]', '<EMAIL>')
      await page.fill('[name="message"]', 'Test message for compatibility')
      
      // Complete CAPTCHA (this would need to be mocked in test environment)
      // For now, we'll just check that the form exists and is functional
      expect(await page.locator('form').count()).toBeGreaterThan(0)
      expect(await page.locator('[name="name"]').inputValue()).toBe('Test User')
    })
  })

  test.describe('Search API Dual Authentication', () => {
    test('should accept HMAC authentication for search', async ({ request }) => {
      const path = '/api/search'
      const headers = createHMACHeaders('GET', path)
      
      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
      
      expect(response.status()).toBe(200)
      
      const body = await response.json()
      expect(body).toHaveProperty('products')
    })

    test('should handle mixed authentication scenarios', async ({ request }) => {
      // Test 1: Valid HMAC only
      const hmacHeaders = createHMACHeaders('GET', '/api/search')
      const hmacResponse = await request.get(`${BASE_URL}/api/search?q=laptop`, {
        headers: hmacHeaders
      })
      expect(hmacResponse.status()).toBe(200)
      
      // Test 2: Invalid JWT with valid HMAC (should fall back to HMAC)
      const mixedHeaders = {
        ...hmacHeaders,
        'Authorization': 'Bearer invalid-jwt-token'
      }
      const mixedResponse = await request.get(`${BASE_URL}/api/search?q=laptop`, {
        headers: mixedHeaders
      })
      expect(mixedResponse.status()).toBe(200)
      
      // Test 3: No authentication (should fail)
      const noAuthResponse = await request.get(`${BASE_URL}/api/search?q=laptop`)
      expect(noAuthResponse.status()).toBe(401)
    })
  })

  test.describe('Authentication Method Detection', () => {
    test('should identify HMAC authentication in logs', async ({ request }) => {
      const path = '/api/search'
      const headers = createHMACHeaders('GET', path)
      
      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
      
      expect(response.status()).toBe(200)
      
      // Check that trace ID indicates HMAC authentication
      const body = await response.json()
      if (body.traceId) {
        expect(body.traceId).toMatch(/^hmac-/)
      }
    })

    test('should handle malformed authentication gracefully', async ({ request }) => {
      // Test various malformed authentication scenarios
      const testCases = [
        {
          name: 'Malformed Authorization header',
          headers: { 'Authorization': 'Malformed header format' }
        },
        {
          name: 'Empty Authorization header',
          headers: { 'Authorization': '' }
        },
        {
          name: 'Incomplete HMAC headers',
          headers: {
            'X-Signature': 'sha256=incomplete',
            'X-Timestamp': Math.floor(Date.now() / 1000).toString()
            // Missing X-Partner-ID
          }
        },
        {
          name: 'Invalid timestamp format',
          headers: {
            'X-Signature': 'sha256=test',
            'X-Timestamp': 'not-a-number',
            'X-Partner-ID': PARTNER_ID
          }
        }
      ]
      
      for (const testCase of testCases) {
        const response = await request.get(`${BASE_URL}/api/search?q=laptop`, {
          headers: testCase.headers
        })
        
        expect(response.status()).toBe(401)
        
        const body = await response.json()
        expect(body.error).toBe('Unauthorized')
      }
    })
  })

  test.describe('Backward Compatibility Validation', () => {
    test('should maintain existing API response formats', async ({ request }) => {
      const path = '/api/search'
      const headers = createHMACHeaders('GET', path)
      
      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
      
      expect(response.status()).toBe(200)
      
      const body = await response.json()
      
      // Verify the response format hasn't changed
      expect(body).toHaveProperty('products')
      expect(body).toHaveProperty('pagination')
      expect(body).toHaveProperty('filters')
      expect(body).toHaveProperty('total')
      expect(body).toHaveProperty('query')
      
      // Verify products structure
      if (body.products.length > 0) {
        const product = body.products[0]
        expect(product).toHaveProperty('id')
        expect(product).toHaveProperty('title')
        expect(product).toHaveProperty('price')
        expect(product).toHaveProperty('cashback')
      }
    })

    test('should maintain search suggestions format', async ({ request }) => {
      const path = '/api/search/suggestions'
      const headers = createHMACHeaders('GET', path)
      
      const response = await request.get(`${BASE_URL}${path}?q=lap`, { headers })
      
      expect(response.status()).toBe(200)
      
      const body = await response.json()
      expect(body).toHaveProperty('suggestions')
      expect(Array.isArray(body.suggestions)).toBe(true)
      
      if (body.suggestions.length > 0) {
        const suggestion = body.suggestions[0]
        expect(suggestion).toHaveProperty('text')
        expect(suggestion).toHaveProperty('category')
      }
    })
  })

  test.describe('Performance Impact Assessment', () => {
    test('should not significantly impact response times', async ({ request }) => {
      const path = '/api/search'
      
      // Test multiple requests to get average response time
      const requestCount = 5
      const responseTimes: number[] = []
      
      for (let i = 0; i < requestCount; i++) {
        const headers = createHMACHeaders('GET', `${path}?q=test${i}`)
        
        const startTime = Date.now()
        const response = await request.get(`${BASE_URL}${path}?q=test${i}`, { headers })
        const endTime = Date.now()
        
        expect(response.status()).toBe(200)
        responseTimes.push(endTime - startTime)
      }
      
      const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
      
      // Authentication overhead should be minimal
      expect(averageResponseTime).toBeLessThan(5000) // 5 seconds max
      
      console.log(`Average response time with HMAC auth: ${averageResponseTime}ms`)
    })

    test('should handle concurrent requests efficiently', async ({ request }) => {
      const path = '/api/search'
      
      // Create 10 concurrent requests
      const concurrentRequests = Array.from({ length: 10 }, (_, i) => {
        const headers = createHMACHeaders('GET', `${path}?q=concurrent${i}`)
        return request.get(`${BASE_URL}${path}?q=concurrent${i}`, { headers })
      })
      
      const startTime = Date.now()
      const responses = await Promise.all(concurrentRequests)
      const endTime = Date.now()
      
      // All requests should succeed
      responses.forEach(response => {
        expect(response.status()).toBe(200)
      })
      
      const totalTime = endTime - startTime
      console.log(`10 concurrent requests completed in: ${totalTime}ms`)
      
      // Should complete within reasonable time
      expect(totalTime).toBeLessThan(10000) // 10 seconds max for 10 concurrent requests
    })
  })

  test.describe('Error Consistency', () => {
    test('should provide consistent error formats across endpoints', async ({ request }) => {
      const endpoints = [
        '/api/search',
        '/api/search/suggestions',
        '/api/search/more'
      ]
      
      for (const endpoint of endpoints) {
        const response = await request.get(`${BASE_URL}${endpoint}?q=test`)
        
        expect(response.status()).toBe(401)
        
        const body = await response.json()
        expect(body).toHaveProperty('error')
        expect(body).toHaveProperty('message')
        expect(body).toHaveProperty('code')
        expect(body).toHaveProperty('traceId')
        expect(body).toHaveProperty('supportedMethods')
        
        expect(body.supportedMethods).toContain('JWT')
        expect(body.supportedMethods).toContain('HMAC')
      }
    })
  })

  test.describe('Feature Flag Compatibility', () => {
    test('should work with authentication enabled', async ({ request }) => {
      // This test validates that the feature flags are working correctly
      // In a real test environment, you might toggle these flags
      
      const path = '/api/search'
      const headers = createHMACHeaders('GET', path)
      
      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
      
      // Should succeed when authentication is enabled and HMAC is provided
      expect(response.status()).toBe(200)
    })
  })
})
