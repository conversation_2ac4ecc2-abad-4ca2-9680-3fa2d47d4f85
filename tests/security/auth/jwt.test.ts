// src/__tests__/security/jwt.test.ts
// Test suite for JWT authentication helper

/**
 * @jest-environment node
 */

import { createJWT, verifyJWT, extractJWTFromRequest, verifyRequestJWT } from '@/lib/security/jwt'

// Mock environment variables
const originalEnv = process.env
beforeEach(() => {
  process.env = { ...originalEnv }
  process.env.JWT_SECRET = 'test-secret-key-minimum-32-characters-long'
})

afterEach(() => {
  process.env = originalEnv
})

// Simplified mock without edge-runtime complexity
function createMockRequest(headers: Record<string, string> = {}, cookies: Record<string, string> = {}): any {
  return {
    headers: {
      get: (name: string) => headers[name] || null
    },
    cookies: {
      get: (name: string) => cookies[name] ? { value: cookies[name] } : undefined
    }
  }
}

describe('JWT Core Functions', () => {
  describe('createJWT', () => {
    it('should create a valid JWT token', async () => {
      const token = await createJWT()
      
      expect(typeof token).toBe('string')
      expect(token.split('.')).toHaveLength(3) // Header.Payload.Signature
    })

    it('should create tokens with correct payload structure', async () => {
      const token = await createJWT()
      const payload = await verifyJWT(token)
      
      expect(payload).toBeTruthy()
      expect(payload?.sub).toBe('frontend')
      expect(payload?.iat).toBeTruthy()
      expect(payload?.exp).toBeTruthy()
    })
  })

  describe('verifyJWT', () => {
    it('should verify a valid JWT token', async () => {
      const token = await createJWT()
      const payload = await verifyJWT(token)
      
      expect(payload).toBeTruthy()
      expect(payload?.sub).toBe('frontend')
    })

    it('should reject an invalid JWT token', async () => {
      const invalidToken = 'invalid.jwt.token'
      const payload = await verifyJWT(invalidToken)
      
      expect(payload).toBeNull()
    })

    it('should reject token with wrong subject', async () => {
      // Create a token with wrong subject using crypto.createSecretKey
      const { SignJWT } = await import('jose')
      
      const secretString = process.env.JWT_SECRET!
      const secret = require('crypto').createSecretKey(Buffer.from(secretString, 'utf8'))
      
      const wrongToken = await new SignJWT({ sub: 'wrong-subject' })
        .setProtectedHeader({ alg: 'HS256' })
        .setIssuedAt()
        .setExpirationTime('5m')
        .sign(secret)
      
      const payload = await verifyJWT(wrongToken)
      expect(payload).toBeNull()
    })
  })

  describe('JWT expiry validation', () => {
    it('should create tokens with 5-minute expiry', async () => {
      const token = await createJWT()
      const payload = await verifyJWT(token)
      
      expect(payload).toBeTruthy()
      
      // Verify expiry is set correctly (should be ~5 minutes from now)
      const now = Math.floor(Date.now() / 1000)
      const expiry = payload?.exp || 0
      
      // Allow 30 second tolerance for test execution time
      expect(expiry).toBeGreaterThan(now + (4 * 60))
      expect(expiry).toBeLessThan(now + (6 * 60))
    })
  })

  describe('JWT security validation', () => {
    it('should use HS256 algorithm', async () => {
      const token = await createJWT()
      
      // Use jose's decodeProtectedHeader utility
      const { decodeProtectedHeader } = await import('jose')
      const header = decodeProtectedHeader(token)
      
      expect(header.alg).toBe('HS256')
      expect(header.typ).toBe('JWT')
    })

    it('should handle missing JWT_SECRET gracefully in development', async () => {
      delete process.env.JWT_SECRET
      process.env.NODE_ENV = 'development'
      
      // Should still work with fallback secret in development
      const token = await createJWT()
      expect(token).toBeTruthy()
      
      const payload = await verifyJWT(token)
      expect(payload?.sub).toBe('frontend')
    })

    it('should throw in production without JWT_SECRET', async () => {
      delete process.env.JWT_SECRET
      process.env.NODE_ENV = 'production'
      
      // Should throw when trying to create JWT in production without secret
      await expect(createJWT()).rejects.toThrow('JWT_SECRET environment variable is required in production')
    })
  })

  describe('extractJWTFromRequest', () => {
    // Simplified mock without edge-runtime complexity
    function createMockRequest(headers: Record<string, string> = {}, cookies: Record<string, string> = {}): any {
      return {
        headers: {
          get: (name: string) => headers[name] || null
        },
        cookies: {
          get: (name: string) => cookies[name] ? { value: cookies[name] } : undefined
        }
      }
    }

    it('should extract JWT from Authorization header', () => {
      const mockRequest = createMockRequest({
        'Authorization': 'Bearer test-jwt-token'
      })

      const token = extractJWTFromRequest(mockRequest)
      expect(token).toBe('test-jwt-token')
    })

    it('should extract JWT from cookie', () => {
      const mockRequest = createMockRequest({}, {
        'auth-token': 'test-jwt-token'
      })

      const token = extractJWTFromRequest(mockRequest)
      expect(token).toBe('test-jwt-token')
    })

    it('should prefer Authorization header over cookie', () => {
      const mockRequest = createMockRequest({
        'Authorization': 'Bearer header-token'
      }, {
        'auth-token': 'cookie-token'
      })

      const token = extractJWTFromRequest(mockRequest)
      expect(token).toBe('header-token')
    })

    it('should return null when no token present', () => {
      const mockRequest = createMockRequest()

      const token = extractJWTFromRequest(mockRequest)
      expect(token).toBeNull()
    })
  })

  describe('verifyRequestJWT', () => {
    it('should verify JWT from request and return payload', async () => {
      const token = await createJWT()
      const mockRequest = createMockRequest({
        'Authorization': `Bearer ${token}`
      })

      const payload = await verifyRequestJWT(mockRequest)
      expect(payload).toBeTruthy()
      expect(payload?.sub).toBe('frontend')
    })

    it('should return null for request without JWT', async () => {
      const mockRequest = createMockRequest()

      const payload = await verifyRequestJWT(mockRequest)
      expect(payload).toBeNull()
    })
  })
})