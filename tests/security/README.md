# Security Tests

**UPDATED <as of 28 July 2025:13:00 PM>**

This directory contains security-focused tests to ensure the application is protected against common vulnerabilities and attacks.

## 📚 Complete Documentation

For comprehensive security testing guidance, see the centralized documentation:
- **Security Implementation:** [`docs/technical/SECURITY.md`](../../docs/technical/SECURITY.md)
- **Security Testing Guidelines:** [`docs/development/TESTING.md#security-testing`](../../docs/development/TESTING.md#security-testing)

## Structure

```
security/
├── auth/                # Authentication security tests
│   ├── jwt/            # JWT security tests
│   ├── hmac/           # HMAC security tests
│   └── session/        # Session security tests
├── api/                # API security tests
│   ├── rate-limiting/  # Rate limiting tests
│   ├── validation/     # Input validation tests
│   └── headers/        # Security headers tests
├── xss/                # XSS prevention tests
│   ├── input/          # Input sanitization tests
│   └── output/         # Output encoding tests
└── infrastructure/     # Infrastructure security tests
    ├── ip-allowlist/   # IP allowlist tests
    └── turnstile/      # CAPTCHA tests
```

## Testing Guidelines

### Authentication Security Tests
- Test JWT token validation and expiration
- Test HMAC signature verification
- Test session hijacking prevention
- Test authentication bypass attempts

Example:
```typescript
// tests/security/auth/jwt/token-validation.test.ts
import { verifyJWT } from '@/lib/auth/jwt'

describe('JWT Security', () => {
  it('rejects expired tokens', async () => {
    const expiredToken = 'expired.jwt.token'
    
    await expect(verifyJWT(expiredToken)).rejects.toThrow('Token expired')
  })
  
  it('rejects malformed tokens', async () => {
    const malformedToken = 'invalid-token'
    
    await expect(verifyJWT(malformedToken)).rejects.toThrow('Invalid token')
  })
})
```

### API Security Tests
- Test rate limiting enforcement
- Test input validation and sanitization
- Test SQL injection prevention
- Test authorization checks

Example:
```typescript
// tests/security/api/rate-limiting/search-api.test.ts
import { GET } from '@/app/api/search/route'

describe('Search API Rate Limiting', () => {
  it('enforces rate limits', async () => {
    const requests = Array(100).fill(null).map(() => 
      GET(new NextRequest('http://localhost:3000/api/search?q=test'))
    )
    
    const responses = await Promise.all(requests)
    const rateLimitedResponses = responses.filter(r => r.status === 429)
    
    expect(rateLimitedResponses.length).toBeGreaterThan(0)
  })
})
```

### XSS Prevention Tests
- Test input sanitization
- Test output encoding
- Test Content Security Policy (CSP)
- Test DOM manipulation safety

Example:
```typescript
// tests/security/xss/input/sanitization.test.tsx
import { render, screen } from '@/tests/setup/test-utils'
import { SearchInput } from '@/components/search/SearchInput'

describe('XSS Prevention', () => {
  it('sanitizes malicious input', () => {
    const maliciousInput = '<script>alert("xss")</script>'
    
    render(<SearchInput defaultValue={maliciousInput} />)
    
    const input = screen.getByRole('textbox')
    expect(input.value).not.toContain('<script>')
  })
})
```

### Infrastructure Security Tests
- Test IP allowlist functionality
- Test CAPTCHA integration
- Test security headers
- Test HTTPS enforcement

Example:
```typescript
// tests/security/infrastructure/ip-allowlist/validation.test.ts
import { isIPAllowed } from '@/lib/security/ip-allowlist'

describe('IP Allowlist Security', () => {
  it('blocks unauthorized IPs', () => {
    const unauthorizedIP = '***********00'
    
    expect(isIPAllowed(unauthorizedIP)).toBe(false)
  })
  
  it('allows authorized IPs', () => {
    const authorizedIP = '***********'
    
    expect(isIPAllowed(authorizedIP)).toBe(true)
  })
})
```

## Security Test Categories

### OWASP Top 10 Coverage
1. **Injection**: SQL injection, NoSQL injection, command injection
2. **Broken Authentication**: Session management, password policies
3. **Sensitive Data Exposure**: Data encryption, secure transmission
4. **XML External Entities (XXE)**: XML parsing security
5. **Broken Access Control**: Authorization, privilege escalation
6. **Security Misconfiguration**: Headers, CORS, CSP
7. **Cross-Site Scripting (XSS)**: Input validation, output encoding
8. **Insecure Deserialization**: Object deserialization security
9. **Using Components with Known Vulnerabilities**: Dependency scanning
10. **Insufficient Logging & Monitoring**: Security event logging

### Custom Security Requirements
- IP allowlist enforcement
- Rate limiting per endpoint
- HMAC signature validation
- Turnstile CAPTCHA integration
- JWT token security

## Coverage Goals

- **Authentication**: 100% coverage (critical security component)
- **API Security**: 95% coverage
- **XSS Prevention**: 90% coverage
- **Infrastructure**: 85% coverage

## Running Security Tests

```bash
# Run all security tests
npm run test:security

# Run specific security test categories
npm test -- --testPathPattern=security/auth
npm test -- --testPathPattern=security/xss

# Run with security-focused coverage
npm run test:coverage -- --testPathPattern=security
```

## Security Testing Best Practices

1. **Threat Modeling**: Base tests on identified threats
2. **Negative Testing**: Test what should NOT work
3. **Boundary Testing**: Test edge cases and limits
4. **Real Attack Simulation**: Use actual attack vectors
5. **Regular Updates**: Keep tests updated with new threats
6. **Automated Scanning**: Integrate with security scanning tools
