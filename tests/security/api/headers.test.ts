/**
 * Security Headers Test Suite
 * 
 * This test suite validates the implementation of HTTP security headers
 * across different environments and routes to ensure proper security
 * configuration and prevent security regressions.
 */

import { NextRequest, NextResponse } from 'next/server';

// Mock Next.js configuration for testing
const mockNextConfig = {
  async headers() {
    // Environment detection for security headers
    const isProduction = process.env.NODE_ENV === 'production';
    const isStaging = process.env.VERCEL_ENV === 'preview' || process.env.NODE_ENV === 'staging';
    const isDevelopment = process.env.NODE_ENV === 'development';

    // Content Security Policy configuration
    const cspDirectives = {
      'default-src': ["'self'"],
      'script-src': [
        "'self'",
        // Allow unsafe-eval only in development for Next.js dev tools
        ...(isDevelopment ? ["'unsafe-eval'"] : [])
      ],
      'style-src': [
        "'self'", 
        "'unsafe-inline'" // Required for Tailwind CSS and styled-jsx
      ],
      'img-src': [
        "'self'",
        "data:",
        "https://*.supabase.co",
        "https://placehold.co",
        "https://via.placeholder.com", 
        "https://dummyimage.com",
        "https://*.amazonaws.com",
        "https://*.cloudfront.net",
        "https://images.samsung.com",
        "https://supabase.com",
        ...(isDevelopment ? ["https://example.com"] : [])
      ],
      'connect-src': [
        "'self'",
        "https://*.supabase.co",
        "wss://*.supabase.co"
      ],
      'font-src': ["'self'", "data:"],
      'object-src': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"],
      'frame-ancestors': ["'none'"], // Replaces X-Frame-Options
      'upgrade-insecure-requests': []
    };

    // Convert CSP object to string
    const cspString = Object.entries(cspDirectives)
      .map(([directive, sources]) => {
        if (sources.length === 0) return directive;
        return `${directive} ${sources.join(' ')}`;
      })
      .join('; ');

    // Base security headers for all environments
    const baseHeaders = [
      {
        key: 'X-Content-Type-Options',
        value: 'nosniff'
      },
      {
        key: 'Referrer-Policy', 
        value: 'strict-origin-when-cross-origin'
      },
      {
        key: 'Permissions-Policy',
        value: 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), browsing-topics=()'
      },
      {
        key: 'Content-Security-Policy',
        value: cspString
      },
      {
        key: 'X-DNS-Prefetch-Control',
        value: 'on'
      }
    ];

    // Add HSTS only in production and staging
    if (isProduction || isStaging) {
      baseHeaders.push({
        key: 'Strict-Transport-Security',
        value: 'max-age=63072000; includeSubDomains; preload'
      });
    }

    return [
      {
        source: '/(.*)',
        headers: baseHeaders,
      }
    ];
  }
};

describe('Security Headers Implementation', () => {
  let originalEnv: string | undefined;

  beforeEach(() => {
    originalEnv = process.env.NODE_ENV;
  });

  afterEach(() => {
    if (originalEnv) {
      process.env.NODE_ENV = originalEnv;
    } else {
      delete process.env.NODE_ENV;
    }
  });

  describe('Development Environment', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'development';
    });

    it('should include all required security headers in development', async () => {
      const headers = await mockNextConfig.headers();
      const securityHeaders = headers[0].headers;
      
      const headerKeys = securityHeaders.map(h => h.key);
      
      expect(headerKeys).toContain('X-Content-Type-Options');
      expect(headerKeys).toContain('Referrer-Policy');
      expect(headerKeys).toContain('Permissions-Policy');
      expect(headerKeys).toContain('Content-Security-Policy');
      expect(headerKeys).toContain('X-DNS-Prefetch-Control');
      expect(headerKeys).not.toContain('Strict-Transport-Security');
    });

    it('should allow unsafe-eval in development CSP', async () => {
      const headers = await mockNextConfig.headers();
      const cspHeader = headers[0].headers.find(h => h.key === 'Content-Security-Policy');
      
      expect(cspHeader?.value).toContain("'unsafe-eval'");
      expect(cspHeader?.value).toContain("script-src 'self' 'unsafe-eval'");
    });

    it('should include development-specific image sources', async () => {
      const headers = await mockNextConfig.headers();
      const cspHeader = headers[0].headers.find(h => h.key === 'Content-Security-Policy');
      
      expect(cspHeader?.value).toContain('https://example.com');
    });
  });

  describe('Production Environment', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'production';
    });

    it('should include HSTS header in production', async () => {
      const headers = await mockNextConfig.headers();
      const securityHeaders = headers[0].headers;
      
      const hstsHeader = securityHeaders.find(h => h.key === 'Strict-Transport-Security');
      expect(hstsHeader).toBeDefined();
      expect(hstsHeader?.value).toBe('max-age=63072000; includeSubDomains; preload');
    });

    it('should not allow unsafe-eval in production CSP', async () => {
      const headers = await mockNextConfig.headers();
      const cspHeader = headers[0].headers.find(h => h.key === 'Content-Security-Policy');
      
      expect(cspHeader?.value).not.toContain("'unsafe-eval'");
      expect(cspHeader?.value).toContain("script-src 'self'");
    });

    it('should not include development image sources in production', async () => {
      const headers = await mockNextConfig.headers();
      const cspHeader = headers[0].headers.find(h => h.key === 'Content-Security-Policy');
      
      expect(cspHeader?.value).not.toContain('https://example.com');
    });
  });

  describe('Staging Environment', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'production';
      process.env.VERCEL_ENV = 'preview';
    });

    afterEach(() => {
      delete process.env.VERCEL_ENV;
    });

    it('should include HSTS header in staging', async () => {
      const headers = await mockNextConfig.headers();
      const securityHeaders = headers[0].headers;
      
      const hstsHeader = securityHeaders.find(h => h.key === 'Strict-Transport-Security');
      expect(hstsHeader).toBeDefined();
      expect(hstsHeader?.value).toBe('max-age=63072000; includeSubDomains; preload');
    });
  });

  describe('Content Security Policy Validation', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'production';
    });

    it('should have proper CSP directives', async () => {
      const headers = await mockNextConfig.headers();
      const cspHeader = headers[0].headers.find(h => h.key === 'Content-Security-Policy');
      
      expect(cspHeader?.value).toContain("default-src 'self'");
      expect(cspHeader?.value).toContain("frame-ancestors 'none'");
      expect(cspHeader?.value).toContain("object-src 'none'");
      expect(cspHeader?.value).toContain("upgrade-insecure-requests");
    });

    it('should allow Supabase connections', async () => {
      const headers = await mockNextConfig.headers();
      const cspHeader = headers[0].headers.find(h => h.key === 'Content-Security-Policy');
      
      expect(cspHeader?.value).toContain('https://*.supabase.co');
      expect(cspHeader?.value).toContain('wss://*.supabase.co');
    });

    it('should allow required image sources', async () => {
      const headers = await mockNextConfig.headers();
      const cspHeader = headers[0].headers.find(h => h.key === 'Content-Security-Policy');
      
      const requiredImageSources = [
        'data:',
        'https://*.supabase.co',
        'https://placehold.co',
        'https://via.placeholder.com',
        'https://dummyimage.com',
        'https://*.amazonaws.com',
        'https://*.cloudfront.net',
        'https://images.samsung.com',
        'https://supabase.com'
      ];

      requiredImageSources.forEach(source => {
        expect(cspHeader?.value).toContain(source);
      });
    });
  });

  describe('Security Header Values', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'production';
    });

    it('should have correct X-Content-Type-Options value', async () => {
      const headers = await mockNextConfig.headers();
      const header = headers[0].headers.find(h => h.key === 'X-Content-Type-Options');
      
      expect(header?.value).toBe('nosniff');
    });

    it('should have correct Referrer-Policy value', async () => {
      const headers = await mockNextConfig.headers();
      const header = headers[0].headers.find(h => h.key === 'Referrer-Policy');
      
      expect(header?.value).toBe('strict-origin-when-cross-origin');
    });

    it('should have correct Permissions-Policy value', async () => {
      const headers = await mockNextConfig.headers();
      const header = headers[0].headers.find(h => h.key === 'Permissions-Policy');
      
      expect(header?.value).toBe('camera=(), microphone=(), geolocation=(), payment=(), usb=(), browsing-topics=()');
    });

    it('should have correct X-DNS-Prefetch-Control value', async () => {
      const headers = await mockNextConfig.headers();
      const header = headers[0].headers.find(h => h.key === 'X-DNS-Prefetch-Control');
      
      expect(header?.value).toBe('on');
    });
  });

  describe('Security Regression Prevention', () => {
    beforeEach(() => {
      process.env.NODE_ENV = 'production';
    });

    it('should not include deprecated X-Frame-Options header', async () => {
      const headers = await mockNextConfig.headers();
      const securityHeaders = headers[0].headers;
      
      const xFrameOptionsHeader = securityHeaders.find(h => h.key === 'X-Frame-Options');
      expect(xFrameOptionsHeader).toBeUndefined();
    });

    it('should use frame-ancestors instead of X-Frame-Options', async () => {
      const headers = await mockNextConfig.headers();
      const cspHeader = headers[0].headers.find(h => h.key === 'Content-Security-Policy');
      
      expect(cspHeader?.value).toContain("frame-ancestors 'none'");
    });

    it('should not allow unsafe-inline for scripts in production', async () => {
      const headers = await mockNextConfig.headers();
      const cspHeader = headers[0].headers.find(h => h.key === 'Content-Security-Policy');
      
      // Should not contain unsafe-inline in script-src
      const scriptSrcMatch = cspHeader?.value.match(/script-src[^;]*/);
      expect(scriptSrcMatch?.[0]).not.toContain("'unsafe-inline'");
    });
  });
});
