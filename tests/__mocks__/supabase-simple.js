/**
 * Test Audit Update: 2025-07-28
 * Simple Supabase mock for CI testing environments
 * Provides basic mock without complex module resolution
 */

// Simple Supabase mock for CI testing
// Provides basic mock without complex module resolution

const mockSupabaseClient = {
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  neq: jest.fn().mockReturnThis(),
  gte: jest.fn().mockReturnThis(),
  lte: jest.fn().mockReturnThis(),
  like: jest.fn().mockReturnThis(),
  ilike: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  range: jest.fn().mockReturnThis(),
  single: jest.fn().mockResolvedValue({
    data: {
      id: 'mock-id',
      name: 'Mock Product',
      slug: 'mock-product',
      description: 'Mock description',
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-01T00:00:00Z',
    },
    error: null,
  }),
  // Default mock response
  then: jest.fn().mockResolvedValue({
    data: [
      {
        id: 'mock-id-1',
        name: 'Mock Product 1',
        slug: 'mock-product-1',
        description: 'Mock description 1',
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z',
      },
    ],
    error: null,
    count: 1,
  }),
}

// Export the mock
module.exports = {
  createClient: jest.fn(() => mockSupabaseClient),
  __esModule: true,
  default: {
    createClient: jest.fn(() => mockSupabaseClient),
  },
}