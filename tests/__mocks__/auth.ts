// Mock authentication functions for CI/CD testing
// Provides consistent mock responses without requiring real HMAC/JWT setup

export const mockAuthFunctions = {
  // Mock HMAC authentication - always succeeds in CI
  authenticateHMAC: jest.fn().mockResolvedValue({
    success: true,
    method: 'HMAC',
    partnerId: 'test-partner',
    timestamp: Date.now(),
  }),
  
  // Mock JWT authentication - always succeeds in CI
  authenticateJWT: jest.fn().mockResolvedValue({
    success: true,
    method: 'JWT',
    partnerId: 'test-partner',
    timestamp: Date.now(),
  }),
  
  // Mock dual authentication - always succeeds in CI
  authenticateDual: jest.fn().mockResolvedValue({
    success: true,
    method: 'HMAC',
    partnerId: 'test-partner',
    timestamp: Date.now(),
  }),
  
  // Mock CORS validation - always passes in CI
  validateCORS: jest.fn().mockReturnValue({
    allowed: true,
    origin: 'http://localhost:3000',
  }),
  
  // Mock rate limiting - never blocks in CI
  checkRateLimit: jest.fn().mockResolvedValue({
    allowed: true,
    remaining: 100,
    resetTime: Date.now() + 60000,
  }),
}

// Mock the authentication modules
jest.mock('@/lib/security/auth', () => mockAuthFunctions)
jest.mock('@/lib/security/hmac', () => ({
  generateHMAC: jest.fn().mockReturnValue('mock-hmac-signature'),
  validateHMAC: jest.fn().mockReturnValue(true),
}))
jest.mock('@/lib/security/jwt', () => ({
  generateJWT: jest.fn().mockReturnValue('mock.jwt.token'),
  validateJWT: jest.fn().mockReturnValue({ valid: true, payload: { partnerId: 'test' } }),
}))

export default mockAuthFunctions;