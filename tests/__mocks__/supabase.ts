// Mock Supabase client for CI/CD testing
// Provides consistent mock responses for GitHub Actions

// Create a comprehensive mock query builder that properly chains
const createMockQueryBuilder = () => {
  const mockBuilder = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({
      data: {
        id: 'mock-id',
        name: 'Mock Product',
        slug: 'mock-product',
        description: 'Mock description',
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z',
      },
      error: null,
    }),
    // Default mock response for queries (when .then() is called)
    then: jest.fn().mockResolvedValue({
      data: [
        {
          id: 'mock-id-1',
          name: 'Mock Product 1',
          slug: 'mock-product-1',
          description: 'Mock description 1',
          created_at: '2025-01-01T00:00:00Z',
          updated_at: '2025-01-01T00:00:00Z',
        },
        {
          id: 'mock-id-2',
          name: 'Mock Product 2', 
          slug: 'mock-product-2',
          description: 'Mock description 2',
          created_at: '2025-01-01T00:00:00Z',
          updated_at: '2025-01-01T00:00:00Z',
        },
      ],
      error: null,
      count: 2,
    }),
  }
  
  // Ensure all methods return the same builder instance for proper chaining
  Object.keys(mockBuilder).forEach(key => {
    if (typeof mockBuilder[key] === 'function' && key !== 'single' && key !== 'then') {
      mockBuilder[key].mockReturnValue(mockBuilder)
    }
  })
  
  return mockBuilder
}

export const mockSupabaseClient = {
  ...createMockQueryBuilder(),
  from: jest.fn(() => createMockQueryBuilder()),
}

// Mock the Supabase module
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => mockSupabaseClient),
}))

// Mock our server-side Supabase client
jest.mock('@/lib/supabase/server', () => ({
  createServerSupabaseReadOnlyClient: jest.fn(() => mockSupabaseClient),
  createServerSupabaseClient: jest.fn(() => mockSupabaseClient),
}))

// Mock our client-side Supabase client
jest.mock('@/lib/supabase/client', () => ({
  createBrowserSupabaseClient: jest.fn(() => mockSupabaseClient),
}))

export default mockSupabaseClient;