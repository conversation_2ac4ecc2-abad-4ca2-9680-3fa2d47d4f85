// src/__tests__/performance/auth-performance.test.ts
// Performance tests for authentication overhead

import { NextRequest } from 'next/server'
import { generateHMACSignature, verifyRequestHMAC, clearReplayCache } from '@/lib/security/hmac'
import { createHMACHeaders } from '@/lib/security/hmac'
import { authenticateSearchRequest } from '@/lib/security/auth-middleware'
import { createJWT, verifyRequestJWT } from '@/lib/security/jwt'

// Test configuration
const TEST_SECRET = 'test-secret-minimum-32-characters-long'
const TEST_PARTNER_ID = 'test-partner'

// Setup test environment
beforeAll(() => {
  process.env.JWT_SECRET = 'test-jwt-secret-minimum-32-characters-long'
  process.env.PARTNER_SECRET_TEST_PARTNER = TEST_SECRET
  process.env.PARTNER_SECRET_DEFAULT = TEST_SECRET
  process.env.HMAC_TIMESTAMP_WINDOW = '300'
  process.env.ENABLE_SEARCH_AUTH = 'true'
  process.env.ENABLE_HMAC_AUTH = 'true'
})

beforeEach(() => {
  // Clear replay cache to avoid conflicts between tests
  clearReplayCache()
})

afterAll(() => {
  delete process.env.JWT_SECRET
  delete process.env.PARTNER_SECRET_TEST_PARTNER
  delete process.env.PARTNER_SECRET_DEFAULT
  delete process.env.HMAC_TIMESTAMP_WINDOW
  delete process.env.ENABLE_SEARCH_AUTH
  delete process.env.ENABLE_HMAC_AUTH
})

describe('Authentication Performance', () => {
  describe('HMAC Performance', () => {
    it('HMAC verification completes within 5ms', async () => {
      const timestamp = Math.floor(Date.now() / 1000)
      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
      
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'x-signature': `sha256=${signature}`,
          'x-timestamp': timestamp.toString(),
          'x-partner-id': TEST_PARTNER_ID
        }
      })
      
      const startTime = performance.now()
      await verifyRequestHMAC(request)
      const endTime = performance.now()
      
      expect(endTime - startTime).toBeLessThan(5)
    })

    it('HMAC signature generation completes within 2ms', () => {
      const timestamp = Math.floor(Date.now() / 1000)
      
      const startTime = performance.now()
      generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
      const endTime = performance.now()
      
      expect(endTime - startTime).toBeLessThan(2)
    })

    it('handles 100 concurrent HMAC verifications under 500ms', async () => {
      const baseTimestamp = Math.floor(Date.now() / 1000)
      const requests = Array.from({ length: 100 }, (_, i) => {
        // Use slightly different timestamps to avoid replay detection
        const timestamp = baseTimestamp + i
        const path = `/api/search?q=test${i}`
        const signature = generateHMACSignature('GET', path, timestamp, '', TEST_SECRET)

        return new NextRequest(`http://localhost${path}`, {
          headers: {
            'x-signature': `sha256=${signature}`,
            'x-timestamp': timestamp.toString(),
            'x-partner-id': TEST_PARTNER_ID
          }
        })
      })

      const startTime = performance.now()
      const results = await Promise.all(requests.map(verifyRequestHMAC))
      const endTime = performance.now()

      expect(results.every(result => result !== null)).toBe(true)
      expect(endTime - startTime).toBeLessThan(500) // 500ms for 100 concurrent requests
    })

    it('stress test: 1000 sequential HMAC verifications', async () => {
      const startTime = performance.now()
      const baseTimestamp = Math.floor(Date.now() / 1000)

      for (let i = 0; i < 1000; i++) {
        // Use different timestamps and paths to avoid replay detection
        const timestamp = baseTimestamp + i
        const path = `/api/search?iteration=${i}`
        const signature = generateHMACSignature('GET', path, timestamp, '', TEST_SECRET)

        const request = new NextRequest(`http://localhost${path}`, {
          headers: {
            'x-signature': `sha256=${signature}`,
            'x-timestamp': timestamp.toString(),
            'x-partner-id': TEST_PARTNER_ID
          }
        })

        const result = await verifyRequestHMAC(request)
        expect(result).not.toBeNull()
      }
      
      const endTime = performance.now()
      const totalTime = endTime - startTime
      const avgTime = totalTime / 1000
      
      expect(avgTime).toBeLessThan(2) // Average less than 2ms per verification
    })

    it('memory usage remains stable during extended operation', async () => {
      const initialMemory = process.memoryUsage().heapUsed
      
      // Perform 500 HMAC operations
      for (let i = 0; i < 500; i++) {
        const timestamp = Math.floor(Date.now() / 1000)
        const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
        
        const request = new NextRequest('http://localhost/api/search', {
          headers: {
            'x-signature': `sha256=${signature}`,
            'x-timestamp': timestamp.toString(),
            'x-partner-id': TEST_PARTNER_ID
          }
        })
        
        await verifyRequestHMAC(request)
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }
      
      const finalMemory = process.memoryUsage().heapUsed
      const memoryIncrease = finalMemory - initialMemory
      
      // Memory increase should be minimal (less than 10MB)
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024)
    })
  })

  describe('JWT Performance', () => {
    it('JWT verification completes within 5ms', async () => {
      const jwt = await createJWT()
      const request = new NextRequest('http://localhost/api/search', {
        headers: {
          'authorization': `Bearer ${jwt}`
        }
      })
      
      const startTime = performance.now()
      await verifyRequestJWT(request)
      const endTime = performance.now()
      
      expect(endTime - startTime).toBeLessThan(5)
    })

    it('JWT creation completes within 3ms', async () => {
      const startTime = performance.now()
      await createJWT()
      const endTime = performance.now()
      
      expect(endTime - startTime).toBeLessThan(3)
    })
  })

  describe('Dual Authentication Performance', () => {
    it('dual authentication check completes within 10ms', async () => {
      const headers = createHMACHeaders('GET', '/api/search?q=laptop', TEST_PARTNER_ID, '', TEST_SECRET)
      const request = new NextRequest('http://localhost/api/search?q=laptop', { headers })

      const startTime = performance.now()
      const authResult = await authenticateSearchRequest(request)
      const endTime = performance.now()

      expect(authResult.success).toBe(true)
      expect(endTime - startTime).toBeLessThan(10)
    })

    it('JWT fallback to HMAC completes within 15ms', async () => {
      const hmacHeaders = createHMACHeaders('GET', '/api/search?q=laptop', TEST_PARTNER_ID, '', TEST_SECRET)
      const request = new NextRequest('http://localhost/api/search?q=laptop', {
        headers: {
          'authorization': 'Bearer invalid-jwt-token',
          ...hmacHeaders
        }
      })

      const startTime = performance.now()
      const authResult = await authenticateSearchRequest(request)
      const endTime = performance.now()

      expect(authResult.success).toBe(true)
      expect(authResult.method).toBe('HMAC')
      expect(endTime - startTime).toBeLessThan(15) // Slightly higher due to JWT attempt first
    })

    it('authentication failure detection is fast', async () => {
      const request = new NextRequest('http://localhost/api/search?q=laptop')
      
      const startTime = performance.now()
      const authResult = await authenticateSearchRequest(request)
      const endTime = performance.now()
      
      expect(authResult.success).toBe(false)
      expect(endTime - startTime).toBeLessThan(5) // Should fail fast
    })
  })

  describe('Concurrent Authentication Performance', () => {
    it('handles mixed authentication methods concurrently', async () => {
      // Create mix of JWT and HMAC requests
      const jwtPromises = Array.from({ length: 25 }, async (_, i) => {
        const jwt = await createJWT()
        const request = new NextRequest(`http://localhost/api/search?q=jwt${i}`, {
          headers: { 'authorization': `Bearer ${jwt}` }
        })
        return authenticateSearchRequest(request)
      })

      const hmacPromises = Array.from({ length: 25 }, (_, i) => {
        const headers = createHMACHeaders('GET', `/api/search?q=hmac${i}`, TEST_PARTNER_ID, '', TEST_SECRET)
        const request = new NextRequest(`http://localhost/api/search?q=hmac${i}`, { headers })
        return authenticateSearchRequest(request)
      })

      const startTime = performance.now()
      const results = await Promise.all([...jwtPromises, ...hmacPromises])
      const endTime = performance.now()

      // All should succeed
      expect(results.every(result => result.success)).toBe(true)
      
      // Should complete within reasonable time
      expect(endTime - startTime).toBeLessThan(1000) // 1 second for 50 mixed requests
    })

    it('authentication overhead scales linearly', async () => {
      // Test with different batch sizes
      const batchSizes = [10, 50, 100]
      const timings = []

      for (const batchSize of batchSizes) {
        const requests = Array.from({ length: batchSize }, (_, i) => {
          const headers = createHMACHeaders('GET', `/api/search?q=test${i}`, TEST_PARTNER_ID, '', TEST_SECRET)
          return new NextRequest(`http://localhost/api/search?q=test${i}`, { headers })
        })

        const startTime = performance.now()
        const results = await Promise.all(requests.map(authenticateSearchRequest))
        const endTime = performance.now()

        expect(results.every(result => result.success)).toBe(true)
        timings.push({
          batchSize,
          totalTime: endTime - startTime,
          avgTime: (endTime - startTime) / batchSize
        })
      }

      // Average time per request should remain relatively stable
      const avgTimes = timings.map(t => t.avgTime)
      const maxAvgTime = Math.max(...avgTimes)
      const minAvgTime = Math.min(...avgTimes)
      
      // Variation should be less than 100% (linear scaling)
      expect((maxAvgTime - minAvgTime) / minAvgTime).toBeLessThan(1.0)
    })
  })

  describe('Real-world Performance Simulation', () => {
    it('simulates realistic API usage patterns', async () => {
      // Simulate realistic mix: 70% HMAC (API partners), 30% JWT (browsers)
      const totalRequests = 100
      const hmacRequests = Math.floor(totalRequests * 0.7)
      const jwtRequests = totalRequests - hmacRequests

      const startTime = performance.now()

      // Create HMAC requests (API partners)
      const hmacPromises = Array.from({ length: hmacRequests }, (_, i) => {
        const headers = createHMACHeaders('GET', `/api/search?q=partner${i}`, TEST_PARTNER_ID, '', TEST_SECRET)
        const request = new NextRequest(`http://localhost/api/search?q=partner${i}`, { headers })
        return authenticateSearchRequest(request)
      })

      // Create JWT requests (browser users)
      const jwtPromises = Array.from({ length: jwtRequests }, async (_, i) => {
        const jwt = await createJWT()
        const request = new NextRequest(`http://localhost/api/search?q=browser${i}`, {
          headers: { 'authorization': `Bearer ${jwt}` }
        })
        return authenticateSearchRequest(request)
      })

      const results = await Promise.all([...hmacPromises, ...jwtPromises])
      const endTime = performance.now()

      // All should succeed
      expect(results.every(result => result.success)).toBe(true)

      // Performance should be acceptable for realistic load
      const totalTime = endTime - startTime
      const avgTimePerRequest = totalTime / totalRequests
      
      expect(avgTimePerRequest).toBeLessThan(10) // Less than 10ms average per request
      expect(totalTime).toBeLessThan(1000) // Total under 1 second for 100 requests
    })

    it('measures authentication overhead vs no authentication', async () => {
      // Simulate requests without authentication (feature flag disabled)
      process.env.ENABLE_SEARCH_AUTH = 'false'
      
      const noAuthRequests = Array.from({ length: 50 }, (_, i) => {
        return new NextRequest(`http://localhost/api/search?q=noauth${i}`)
      })

      const noAuthStart = performance.now()
      const noAuthResults = await Promise.all(noAuthRequests.map(authenticateSearchRequest))
      const noAuthEnd = performance.now()
      const noAuthTime = noAuthEnd - noAuthStart

      // Re-enable authentication
      process.env.ENABLE_SEARCH_AUTH = 'true'

      // Simulate requests with authentication
      const authRequests = Array.from({ length: 50 }, (_, i) => {
        const headers = createHMACHeaders('GET', `/api/search?q=auth${i}`, TEST_PARTNER_ID, '', TEST_SECRET)
        return new NextRequest(`http://localhost/api/search?q=auth${i}`, { headers })
      })

      const authStart = performance.now()
      const authResults = await Promise.all(authRequests.map(authenticateSearchRequest))
      const authEnd = performance.now()
      const authTime = authEnd - authStart

      // All should succeed
      expect(noAuthResults.every(result => result.success)).toBe(true)
      expect(authResults.every(result => result.success)).toBe(true)

      // Authentication overhead should be minimal
      const overhead = authTime - noAuthTime
      const overheadPerRequest = overhead / 50
      
      expect(overheadPerRequest).toBeLessThan(5) // Less than 5ms overhead per request
    })
  })
})
