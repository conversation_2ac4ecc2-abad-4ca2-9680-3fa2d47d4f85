# Test Duplicates Audit Log

**Date:** 2025-07-28  
**Purpose:** Document test file duplications identified and moved to archive

## Duplicates Identified and Moved

### 1. ProductsContent.test.tsx
**Duplicate:** `tests/components/ProductsContent.test.tsx`  
**Kept:** `tests/unit/components/ProductsContent.test.tsx`  
**Moved to:** `tests/Test_Archives/to-delete/ProductsContent.test-duplicate1.tsx`  
**Reason:** Exact duplicate with minor import differences, unit version is more comprehensive

### 2. camelcase-validation.test.ts
**Duplicate:** `tests/lib/data/camelcase-validation.test.ts`  
**Kept:** `tests/unit/lib/camelcase-validation.test.ts`  
**Moved to:** `tests/Test_Archives/to-delete/camelcase-validation.test-duplicate1.ts`  
**Reason:** Identical content

### 3. products.test.ts
**Duplicate:** `tests/lib/data/products.test.ts` (simplified version)  
**Kept:** `tests/unit/lib/products.test.ts` (comprehensive version)  
**Moved to:** `tests/Test_Archives/to-delete/products.test-simplified.ts`  
**Reason:** Simplified version with less test coverage than unit version

### 4. data-consistency.test.ts
**Duplicate:** `tests/api/data-consistency.test.ts`  
**Kept:** `tests/integration/database/data-consistency.test.ts`  
**Moved to:** `tests/Test_Archives/to-delete/data-consistency.test-duplicate1.ts`  
**Reason:** Identical content, integration folder more appropriate

### 5. rls-simple.test.ts
**Duplicate:** `tests/rls-simple.test.ts`  
**Kept:** `tests/integration/database/rls-simple.test.ts`  
**Moved to:** `tests/Test_Archives/to-delete/rls-simple.test-duplicate1.ts`  
**Reason:** Identical content, integration folder more appropriate

### 6. auth-compatibility.spec.ts
**Duplicate:** `tests/e2e/auth-compatibility.spec.ts`  
**Kept:** `tests/e2e/user-flows/auth-compatibility.spec.ts`  
**Moved to:** `tests/Test_Archives/to-delete/auth-compatibility.spec-duplicate1.ts`  
**Reason:** Identical content, user-flows subfolder better organized

### 7. auth-hmac.spec.ts
**Duplicate:** `tests/e2e/auth-hmac.spec.ts`  
**Kept:** `tests/e2e/user-flows/auth-hmac.spec.ts`  
**Moved to:** `tests/Test_Archives/to-delete/auth-hmac.spec-duplicate1.ts`  
**Reason:** Identical content, user-flows subfolder better organized

### 8. chromium-smoke.spec.ts
**Duplicate:** `tests/app/chromium-smoke.spec.ts`  
**Kept:** `tests/e2e/user-flows/chromium-smoke.spec.ts`  
**Moved to:** `tests/Test_Archives/to-delete/chromium-smoke.spec-duplicate1.ts`  
**Reason:** Identical content, e2e folder more appropriate for Playwright tests

### 9. contact-form.spec.ts
**Duplicate:** `tests/app/contact-form.spec.ts`  
**Kept:** `tests/e2e/user-flows/contact-form.spec.ts`  
**Moved to:** `tests/Test_Archives/to-delete/contact-form.spec-duplicate1.ts`  
**Reason:** Identical content, e2e folder more appropriate for Playwright tests

### 10. product-page.test.tsx
**Duplicate:** `tests/app/products/product-page.test.tsx`  
**Kept:** `tests/integration/api/product-page.test.tsx`  
**Moved to:** `tests/Test_Archives/to-delete/product-page.test-duplicate1.tsx`  
**Reason:** Nearly identical content with minor import differences, integration folder more appropriate

## Additional Duplicates Found in Second Pass

### 11. pagination.spec.ts (Second Pass)
**Duplicate:** `tests/app/products/pagination.spec.ts`  
**Kept:** `tests/e2e/user-flows/product-pagination.spec.ts`  
**Moved to:** `tests/Test_Archives/to-delete/pagination.spec-duplicate2.ts`  
**Reason:** Identical content, e2e/user-flows more appropriate for Playwright tests

### 12. product-page-unified.test.tsx (Second Pass)
**Duplicate:** `tests/app/products/product-page-unified.test.tsx`  
**Kept:** `tests/integration/api/product-page.test.tsx`  
**Moved to:** `tests/Test_Archives/to-delete/product-page-unified.test-duplicate3.tsx`  
**Reason:** Nearly identical to integration version with minor differences

### 13. auth-performance.test.ts (Archived Duplicate)
**Duplicate:** `tests/Test_Archives/problematic-tests/auth-performance.test.ts`  
**Kept:** `tests/e2e/performance/auth-performance.test.ts`  
**Moved to:** `tests/Test_Archives/to-delete/auth-performance.test-duplicate4.ts`  
**Reason:** Identical content, archived version superseded

### 14. cors-and-flood.spec.ts (Archived Duplicate)
**Duplicate:** `tests/Test_Archives/problematic-tests/cors-and-flood.spec.ts`  
**Kept:** `tests/e2e/user-flows/cors-and-flood.spec.ts`  
**Moved to:** `tests/Test_Archives/to-delete/cors-and-flood.spec-duplicate5.ts`  
**Reason:** Identical content, archived version superseded

### 15. ip-allowlist-lockout.test.ts (Archived Duplicate)
**Duplicate:** `tests/Test_Archives/problematic-tests/ip-allowlist-lockout.test.ts`  
**Kept:** `tests/security/infrastructure/ip-allowlist-lockout.test.ts`  
**Moved to:** `tests/Test_Archives/to-delete/ip-allowlist-lockout.test-duplicate6.ts`  
**Reason:** Identical content, archived version superseded

### 16. xss-prevention.test.tsx (Archived Duplicate)
**Duplicate:** `tests/Test_Archives/problematic-tests/xss-prevention.test.tsx`  
**Kept:** `tests/security/xss/xss-prevention.test.tsx`  
**Moved to:** `tests/Test_Archives/to-delete/xss-prevention.test-duplicate7.tsx`  
**Reason:** Identical content, archived version superseded

### 17. cors-and-flood.spec.ts (Another Archived Duplicate)
**Duplicate:** `tests/Test_Archives/duplicates/__tests__-archived/cors-and-flood.spec.ts`  
**Kept:** `tests/e2e/user-flows/cors-and-flood.spec.ts`  
**Moved to:** `tests/Test_Archives/to-delete/cors-and-flood.spec-duplicate8.ts`  
**Reason:** Identical content, archived version superseded

## Summary

- **Total duplicates found:** 17
- **Files moved to archive:** 17
- **Primary criterion:** Kept versions in more appropriate directory structure
- **Secondary criterion:** Kept versions with better test coverage when applicable

## Recommendations

1. Maintain the organized directory structure: `unit/`, `integration/`, `e2e/`, `security/`
2. Avoid creating test files in multiple locations
3. Use centralized test setup utilities in `tests/setup/`
4. Follow naming conventions: `.test.ts` for unit/integration, `.spec.ts` for e2e

## Directory Structure After Cleanup

```
tests/
├── unit/           # Unit tests for components, libs, hooks
├── integration/    # API and database integration tests  
├── e2e/           # End-to-end Playwright tests
├── security/      # Security-focused tests
├── setup/         # Test utilities and setup
├── fixtures/      # Test data and fixtures
└── Test_Archives/ # Archived and duplicate tests
```