# Test Archives

**UPDATED <as of 28 July 2025:13:00 PM>**

This directory contains archived test files that are no longer active but preserved for historical reference and audit purposes.

## 📚 Complete Documentation

For comprehensive testing guidance, see the centralized documentation:
- **Testing Strategy & Setup:** [`docs/development/TESTING.md`](../../docs/development/TESTING.md)
- **Archive Management:** [`docs/development/TESTING.md#test-archive-management`](../../docs/development/TESTING.md#test-archive-management)

## Purpose

Files in this directory have been archived for various reasons:
- **Duplicates**: Multiple versions of the same test
- **Obsolete**: Tests for features that no longer exist
- **Superseded**: Tests replaced by better implementations
- **Experimental**: Proof-of-concept tests that didn't make it to production

## Audit Header Format

All archived files include a standardized audit header:

```typescript
/*
 * AUDIT LOG
 * Action Taken: ARCHIVED from [original_path] to [new_path]
 * Date: [current_date]
 * Rationale: [specific reason for archiving]
 * Team Review: [which team should reference this]
 * Historical Context: [brief description of file's original purpose]
 * Category: [DUPLICATE/OBSOLETE/SUPERSEDED/EXPERIMENTAL]
 */
```

## Categories

### DUPLICATE
Files that are exact or near-exact copies of other tests.
- Usually result from copy-paste during development
- One version is kept active, others archived
- Check active version before referencing

### OBSOLETE
Tests for features or code that no longer exists.
- API endpoints that were removed
- Components that were deleted
- Business logic that changed completely

### SUPERSEDED
Tests that were replaced by better implementations.
- Old test approach replaced by new methodology
- Tests moved to different framework
- Tests consolidated into broader test suites

### EXPERIMENTAL
Proof-of-concept or experimental tests.
- Tests for features that were never released
- Performance experiments
- Alternative testing approaches that weren't adopted

## File Organization

Files are organized by their original location and category:

```
Test_Archives/
├── src-__tests__/           # From src/__tests__/
│   ├── duplicates/
│   ├── obsolete/
│   └── superseded/
├── root-__tests__/          # From __tests__/
├── tests-old-structure/     # From old tests/ structure
└── components/              # From component __tests__ directories
```

## Accessing Archived Files

### When to Reference
- Understanding historical implementation decisions
- Recovering test logic for similar features
- Audit and compliance requirements
- Learning from past approaches

### How to Reference
1. Check the audit header for context
2. Verify the file hasn't been superseded
3. Understand why it was archived
4. Consider if the archived approach is still relevant

## Maintenance

### Regular Cleanup
- Review archived files annually
- Remove files older than 2 years (unless required for compliance)
- Consolidate similar archived files
- Update documentation as needed

### Adding New Archives
When archiving a file:
1. Add the complete audit header
2. Place in appropriate category directory
3. Update this README if needed
4. Document the archival in commit message

## Recovery Process

If an archived file needs to be restored:
1. Review the audit header for context
2. Check if the original issue still exists
3. Update the file for current codebase
4. Move back to active test directory
5. Update configurations to include the test
6. Document the restoration

## Compliance and Audit

This archive serves compliance and audit purposes:
- Maintains history of testing approaches
- Documents decision-making process
- Provides evidence of thorough testing practices
- Supports regulatory requirements

## Best Practices

1. **Complete Headers**: Always include full audit headers
2. **Clear Rationale**: Explain why the file was archived
3. **Proper Categorization**: Use correct category for easy finding
4. **Regular Review**: Periodically review archived files
5. **Documentation**: Keep this README updated with changes
