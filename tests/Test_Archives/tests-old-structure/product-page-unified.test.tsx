/*
 * AUDIT LOG
 * Action Taken: ARCHIVED from tests/app/products/product-page-unified.test.tsx to tests/Test_Archives/tests-old-structure/product-page-unified.test.tsx
 * Date: 2025-07-28
 * Rationale: Duplicate of tests/app/products/product-page.test.tsx with minimal differences - unified version superseded by standard version
 * Team Review: Frontend team should reference the active product-page.test.tsx for product page testing patterns
 * Historical Context: This was a unified fetching approach test that was replaced by the standard product page test implementation
 * Category: SUPERSEDED
 */

import { render, screen, waitFor } from '@testing-library/react';
import ProductPage from '@/app/products/[id]/page';
import * as dataLayer from '@/lib/data/products';
import { notFound } from 'next/navigation';
// jest-dom is already configured in jest.setup.js

jest.mock('next/navigation', () => ({
  notFound: jest.fn(),
}));

jest.mock('@/components/pages/ProductPageClient', () => ({
  ProductPageClient: () => <div>ProductPageClient Component</div>,
}));

jest.mock('@/components/seo/StructuredData', () => ({
  ProductStructuredData: () => <div>ProductStructuredData Component</div>,
}));

describe('ProductPage Unified Fetching', () => {
  const mockProduct = {
    id: 'test-product-id',
    name: 'Test Product',
    slug: 'test-product',
    description: 'Test product description',
    price: 99.99,
    currency: 'GBP',
    image_url: 'https://example.com/image.jpg',
    retailer: {
      id: 'test-retailer',
      name: 'Test Retailer',
      slug: 'test-retailer',
      logo_url: 'https://example.com/logo.jpg',
    },
    category: {
      id: 'test-category',
      name: 'Test Category',
      slug: 'test-category',
    },
    brand: {
      id: 'test-brand',
      name: 'Test Brand',
      slug: 'test-brand',
    },
    cashback_rate: 5.0,
    cashback_amount: 4.99,
    deal_url: 'https://example.com/deal',
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders product page with valid product data', async () => {
    // Mock the getProductBySlug function
    jest.spyOn(dataLayer, 'getProductBySlug').mockResolvedValue(mockProduct);

    const params = { id: 'test-product' };
    
    // Render the component
    const result = await ProductPage({ params });
    
    // Render the JSX result
    render(result);

    // Wait for the component to render
    await waitFor(() => {
      expect(screen.getByText('ProductPageClient Component')).toBeInTheDocument();
    });

    // Verify that getProductBySlug was called with correct slug
    expect(dataLayer.getProductBySlug).toHaveBeenCalledWith('test-product');
  });

  it('calls notFound when product is not found', async () => {
    // Mock the getProductBySlug function to return null
    jest.spyOn(dataLayer, 'getProductBySlug').mockResolvedValue(null);

    const params = { id: 'non-existent-product' };
    
    // Call the component function
    await ProductPage({ params });

    // Verify that notFound was called
    expect(notFound).toHaveBeenCalled();
    expect(dataLayer.getProductBySlug).toHaveBeenCalledWith('non-existent-product');
  });

  it('handles database errors gracefully', async () => {
    // Mock the getProductBySlug function to throw an error
    const mockError = new Error('Database connection failed');
    jest.spyOn(dataLayer, 'getProductBySlug').mockRejectedValue(mockError);

    const params = { id: 'test-product' };
    
    // The component should handle the error and call notFound
    await expect(ProductPage({ params })).rejects.toThrow('Database connection failed');
    
    expect(dataLayer.getProductBySlug).toHaveBeenCalledWith('test-product');
  });

  it('passes correct props to ProductPageClient', async () => {
    // Mock the getProductBySlug function
    jest.spyOn(dataLayer, 'getProductBySlug').mockResolvedValue(mockProduct);

    const params = { id: 'test-product' };
    
    // Render the component
    const result = await ProductPage({ params });
    
    // Render the JSX result
    render(result);

    // Wait for the component to render
    await waitFor(() => {
      expect(screen.getByText('ProductPageClient Component')).toBeInTheDocument();
    });

    // The actual props verification would need to be done differently
    // since we're mocking the ProductPageClient component
    expect(dataLayer.getProductBySlug).toHaveBeenCalledWith('test-product');
  });

  it('renders structured data component', async () => {
    // Mock the getProductBySlug function
    jest.spyOn(dataLayer, 'getProductBySlug').mockResolvedValue(mockProduct);

    const params = { id: 'test-product' };
    
    // Render the component
    const result = await ProductPage({ params });
    
    // Render the JSX result
    render(result);

    // Wait for the structured data component to render
    await waitFor(() => {
      expect(screen.getByText('ProductStructuredData Component')).toBeInTheDocument();
    });
  });

  it('handles special characters in product slug', async () => {
    const specialSlug = 'test-product-with-special-chars-123';
    jest.spyOn(dataLayer, 'getProductBySlug').mockResolvedValue({
      ...mockProduct,
      slug: specialSlug,
    });

    const params = { id: specialSlug };
    
    // Render the component
    const result = await ProductPage({ params });
    
    // Render the JSX result
    render(result);

    // Wait for the component to render
    await waitFor(() => {
      expect(screen.getByText('ProductPageClient Component')).toBeInTheDocument();
    });

    expect(dataLayer.getProductBySlug).toHaveBeenCalledWith(specialSlug);
  });
});
