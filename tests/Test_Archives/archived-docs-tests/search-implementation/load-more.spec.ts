/**
 * 📋 TEST CONSOLIDATION AUDIT - JULY 29, 2025
 * ==========================================
 * 🔄 ACTION: ARCHIVED for test infrastructure transformation
 * 📁 ORIGINAL LOCATION: docs/archive/completed_features/search_implementation/load-more.spec.ts
 * 📁 NEW LOCATION: tests/Test_Archives/archived-docs-tests/search-implementation/load-more.spec.ts
 * 🎯 REASON: Test file consolidation - documentation archived test moved to enterprise test structure
 * 📝 STATUS: Content preserved unchanged, moved for proper test categorization
 * 👥 REVIEW REQUIRED: Development team can reference for Load More testing patterns
 * 🏷️ CATEGORY: Test Archive - Documentation Tests (Search Implementation)
 * 📅 PURPOSE: Historical record of Load More test patterns from documentation archive
 * 
 * 📋 PREVIOUS DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
 * ===========================================
 * 🔄 ACTION: ARCHIVED from docs/UPDATES/SEARCH/ to docs/archive/completed_features/search_implementation/
 * 📁 ORIGINAL LOCATION: /docs/UPDATES/SEARCH/load-more.spec.ts  
 * 📁 NEW LOCATION: /docs/archive/completed_features/search_implementation/load-more.spec.ts
 * 🎯 REASON: Completed Load More functionality testing implementation
 * 📝 STATUS: Content preserved unchanged, archived as completed feature for basic Load More testing
 * 👥 REVIEW REQUIRED: Development team can reference for Load More testing patterns and infinite scroll validation
 * 🏷️ CATEGORY: Archive - Completed Features (Search Implementation)
 * 📅 PURPOSE: Historical record of Load More test implementation and pagination testing procedures
 */

import { test, expect } from '@playwright/test';

/**
 * @file This test script verifies the "Load More" functionality on the search results page.
 *
 * @test-case TC-SEARCH-01
 * @description
 * 1. Navigates to a search results page with multiple pages of results.
 * 2. Verifies the total count of items is displayed correctly.
 * 3. Verifies the initial set of products is loaded.
 * 4. Scrolls to the bottom and clicks the "Load More" button.
 * 5. Verifies the API call to fetch more products is successful.
 * 6. Verifies that the new products are appended to the list.
 * 7. Verifies the "Load More" button is hidden after all results are loaded.
 * 8. Verifies the "Back to Top" link appears.
 * 9. Clicks the "Back to Top" link and verifies the page scrolls to the top.
 * 10. Checks for specific console errors related to duplicate keys.
 *
 * <AUTHOR> AI Copilot
 * @date 2025-07-01
 */

test.describe('Search Page: Load More & Infinite Scroll', () => {
  const SEARCH_URL = 'http://localhost:3000/search?q=series';
  const API_SEARCH_ENDPOINT = '**/api/search*'; // Glob pattern to catch the search API calls

  const PRODUCT_CARD_SELECTOR = '[data-testid="product-card"]';
  const RESULTS_COUNT_SELECTOR = '[data-testid="results-count"]';
  const LOAD_MORE_BUTTON_SELECTOR = 'button:has-text("Load More")';
  const BACK_TO_TOP_SELECTOR = 'a:has-text("Back to Top")';

  test('should load more products, hide load more button, and allow scrolling back to the top', async ({ page }) => {
    const consoleErrors: string[] = [];

    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
      }
    });

    // Step 1: Navigate to the search page.
    await page.goto(SEARCH_URL);

    // Step 2: Verify the total number of results is displayed.
    await expect(page.locator(`text="43 results found"`)).toBeVisible({ timeout: 10000 });

    // Step 3: Verify the initial number of products is loaded (e.g., 20 as per PRD).
    await expect(page.locator(PRODUCT_CARD_SELECTOR)).toHaveCount(20);

    // Step 4: Scroll to the bottom of the page to make the "Load More" button visible.
    await page.evaluate(() => window.scrollTo(0, document.body.scrollHeight));
    const loadMoreButton = page.locator(LOAD_MORE_BUTTON_SELECTOR);
    await expect(loadMoreButton).toBeVisible();

    // Step 5: Set up a listener for the API call that will be triggered by the click.
    const responsePromise = page.waitForResponse(API_SEARCH_ENDPOINT);

    // Step 6: Click the "Load More" button.
    await loadMoreButton.click();

    // Step 7: Wait for the network response and assert it was successful.
    const response = await responsePromise;
    expect(response.ok(), `API call to ${API_SEARCH_ENDPOINT} failed with status ${response.status()}`).toBeTruthy();

    // Step 8: Verify that the next 23 products have been loaded, bringing the total to 43.
    await expect(page.locator(PRODUCT_CARD_SELECTOR)).toHaveCount(43);

    // Step 9: Verify the "Load More" button is now hidden.
    await expect(loadMoreButton).toBeHidden();

    // Step 10: Verify the "Back to Top" link is now visible.
    const backToTopLink = page.locator(BACK_TO_TOP_SELECTOR);
    await expect(backToTopLink).toBeVisible();

    // Step 11: Click the "Back to Top" link.
    await backToTopLink.click();

    // Step 12: Verify the page has scrolled back to the top.
    await expect.poll(async () => {
      return page.evaluate(() => window.scrollY);
    }, {
      message: 'Page did not scroll to the top after clicking "Back to Top" link.',
      timeout: 5000
    }).toBe(0);

    // Step 13: Check for specific console errors.
    const duplicateKeyError = consoleErrors.find(error =>
      error.includes('Encountered two children with the same key')
    );
    expect(duplicateKeyError).toBeUndefined();
  });
});