/**
 * 📋 TEST CONSOLIDATION AUDIT - JULY 29, 2025
 * ==========================================
 * 🔄 ACTION: ARCHIVED for test infrastructure transformation
 * 📁 ORIGINAL LOCATION: docs/archive/completed_features/search_implementation/search-functionality.spec.ts
 * 📁 NEW LOCATION: tests/Test_Archives/archived-docs-tests/search-implementation/search-functionality.spec.ts
 * 🎯 REASON: Test file consolidation - documentation archived test moved to enterprise test structure
 * 📝 STATUS: Content preserved unchanged, moved for proper test categorization
 * 👥 REVIEW REQUIRED: Development team can reference for comprehensive search testing patterns
 * 🏷️ CATEGORY: Test Archive - Documentation Tests (Search Implementation)
 * 📅 PURPOSE: Historical record of comprehensive search test patterns from documentation archive
 * 
 * 📋 PREVIOUS DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
 * ===========================================
 * 🔄 ACTION: ARCHIVED from docs/UPDATES/SEARCH/ to docs/archive/completed_features/search_implementation/
 * 📁 ORIGINAL LOCATION: /docs/UPDATES/SEARCH/search-functionality.spec.ts  
 * 📁 NEW LOCATION: /docs/archive/completed_features/search_implementation/search-functionality.spec.ts
 * 🎯 REASON: Completed comprehensive search functionality testing implementation
 * 📝 STATUS: Content preserved unchanged, archived as completed feature for end-to-end search testing
 * 👥 REVIEW REQUIRED: Development team can reference for comprehensive search testing patterns and performance validation
 * 🏷️ CATEGORY: Archive - Completed Features (Search Implementation)
 * 📅 PURPOSE: Historical record of comprehensive search functionality test implementation and performance testing procedures
 */

import { test, expect, chromium } from '@playwright/test';
import { createObjectCsvWriter } from 'csv-writer';
import * as fs from 'fs';
import * as path from 'path';

// Test configuration
const config = {
  baseURL: 'http://localhost:3000',
  testQueries: [
    { query: 'samsung', expectedCount: 56 },
    { query: 'series', expectedCount: 42 }
  ],
  viewport: { width: 1280, height: 1024 },
  screenshotPath: (name: string): string => `screenshots/${name}-${new Date().toISOString().replace(/[:.]/g, '-')}.png`,
  timeout: 60000,
  slowMo: 100,
  headless: false,
  screenshot: 'on',
  video: 'on',
  trace: 'on',
  outputDir: path.join(__dirname, 'test-results')
};

// Ensure output directory exists
if (!fs.existsSync(config.outputDir)) {
  fs.mkdirSync(config.outputDir, { recursive: true });
}

// Test results storage
const testResults: any[] = [];

// Test suite
test.describe('Search Functionality Tests', () => {
  let browser: any;
  let context: any;
  let page: any;

  test.beforeAll(async () => {
    browser = await chromium.launch({
      headless: config.headless,
      slowMo: config.slowMo
    });
  });

  test.beforeEach(async () => {
    console.log('Creating new browser context...');
    context = await browser.newContext({
      viewport: config.viewport,
      recordVideo: config.video ? { dir: path.join(config.outputDir, 'videos') } : undefined,
      recordHar: { path: path.join(config.outputDir, 'network.har') },
      logger: {
        isEnabled: (name: string, severity: string) => true,
        log: (name: string, severity: string, message: string, args: any[]) => {
          console.log(`[${name}] ${message}`);
        }
      }
    });
    
    console.log('Creating new page...');
    page = await context.newPage();
    
    // Enhanced logging
    page.on('console', (msg: any) => {
      console.log(`[Console ${msg.type()}] ${msg.text()}`);
    });
    
    page.on('request', (request: any) => {
      console.log(`[Request] ${request.method()} ${request.url()}`);
    });
    
    page.on('response', async (response: any) => {
      const status = response.status();
      const url = response.url();
      console.log(`[Response ${status}] ${url}`);
      
      // Log response body for search API calls
      if (url.includes('/api/search') && status === 200) {
        try {
          const json = await response.json();
          console.log(`[Search API Response] ${JSON.stringify(json, null, 2)}`);
        } catch (e) {
          console.log(`[Search API Response] Could not parse JSON: ${e}`);
        }
      }
    });
    
    page.on('pageerror', (error: any) => {
      console.error(`[Page Error] ${error.message}`);
    });
    
    page.on('requestfailed', (request: any) => {
      console.error(`[Request Failed] ${request.failure()?.errorText} ${request.url()}`);
    });
    
    console.log(`Navigating to ${config.baseURL}...`);
    const response = await page.goto(config.baseURL, { waitUntil: 'domcontentloaded' });
    console.log(`Navigation complete. Status: ${response?.status()}`);
  });

  test.afterEach(async ({}, testInfo) => {
    // Capture screenshot on test failure
    if (testInfo.status === 'failed') {
      const screenshotPath = path.join(
        config.outputDir,
        `screenshots/failure-${testInfo.title.replace(/\s+/g, '-').toLowerCase()}.png`
      );
      await page.screenshot({ path: screenshotPath, fullPage: true });
    }
    
    await context.close();
  });

  test.afterAll(async () => {
    await browser.close();
    await generateTestReport();
  });

  // Test cases for each search query
  for (const { query, expectedCount } of config.testQueries) {
    test(`should return ${expectedCount} results for query "${query}"`, async ({ page }, testInfo) => {
      const testStartTime = Date.now();
      const testResult: any = {
        testName: testInfo.title,
        query,
        expectedCount,
        actualCount: 0,
        passed: false,
        duration: 0,
        timestamp: new Date().toISOString(),
        screenshots: [],
        errors: []
      };

      try {
        // Navigate to search page with query
        const searchUrl = `${config.baseURL}/search?q=${encodeURIComponent(query)}`;
        console.log(`Navigating to search URL: ${searchUrl}`);
        
        try {
          // Clear cookies and cache to prevent any redirects
          await page.goto('about:blank');
          await page.context().clearCookies();
          
          // Navigate to the search URL and wait for the page to be fully loaded
          console.log('Navigating to search page...');
          const response = await page.goto(searchUrl, { 
            waitUntil: 'domcontentloaded',
            timeout: 30000
          });
          
          console.log(`Page loaded. Status: ${response?.status()}`);
          console.log('Final URL:', page.url());
          
          // Wait for the page to be fully interactive
          await page.waitForLoadState('networkidle', { timeout: 15000 });
          
          // Take a screenshot of the initial page load
          const initialScreenshot = config.screenshotPath(`${query}-initial`);
          await page.screenshot({ path: initialScreenshot });
          console.log(`Saved initial screenshot to: ${initialScreenshot}`);
          
          // Wait for search results container with retry logic
          const maxRetries = 5;
          let retries = 0;
          let resultsFound = false;
          
          while (retries < maxRetries && !resultsFound) {
            try {
              console.log(`Waiting for search results (attempt ${retries + 1}/${maxRetries})...`);
              
              // First, check if we're on the correct page
              const currentUrl = page.url();
              if (!currentUrl.includes('/search')) {
                throw new Error(`Not on search results page. Current URL: ${currentUrl}`);
              }
              
              // Try multiple selectors for the search results container
              const selectors = [
                'div.grid', 
                '.search-results', 
                '[data-testid="search-results"]',
                'main', // Fallback to main content area
                'body'  // Last resort fallback
              ];
              
              for (const selector of selectors) {
                try {
                  await page.waitForSelector(selector, { 
                    timeout: 5000,
                    state: 'attached'
                  });
                  console.log(`Found container with selector: ${selector}`);
                  resultsFound = true;
                  break;
                } catch (e) {
                  console.log(`Selector '${selector}' not found`);
                }
              }
              
              if (!resultsFound) {
                throw new Error('No search results container found with any selector');
              }
              
              console.log('Search results container found');
            } catch (error) {
              retries++;
              console.warn(`Attempt ${retries} failed: ${error}`);
              if (retries < maxRetries) {
                console.log('Retrying...');
                await new Promise(resolve => setTimeout(resolve, 2000));
              } else {
                // Take a screenshot of the page to help with debugging
                const errorScreenshot = config.screenshotPath(`${query}-error-attempt-${retries}`);
                await page.screenshot({ path: errorScreenshot, fullPage: true });
                console.error(`Failed to find search results after ${maxRetries} attempts`);
                throw error;
              }
            }
          }

          // Take initial screenshot
          const initialScreenshotPath = config.screenshotPath(`${query}-initial`);
          await page.screenshot({ path: initialScreenshotPath, fullPage: true });
          testResult.screenshots.push(initialScreenshotPath);
          
          // Log the page content for debugging
          const initialPageContent = await page.content();
          fs.writeFileSync(path.join(config.outputDir, `${query}-initial-content.html`), initialPageContent);
        } catch (error) {
          console.error('Error during page navigation and initialization:', error);
          throw error; // Re-throw to be caught by the outer catch
        }

        // Check for "Load More" button and click if present
        let hasMoreResults = true;
        let totalResults = 0;
        let pageNumber = 1;
        const maxPages = 5; // Safety limit to prevent infinite loops

        while (hasMoreResults && pageNumber <= maxPages) {
          console.log(`Processing page ${pageNumber}...`);
          
          try {
            // Wait for results to be visible with a timeout
            console.log('Waiting for results to be visible...');
            await page.waitForSelector('div.grid > div > div > div[data-testid^="product-"], div.grid > div > div > .product-card', { 
              timeout: 10000,
              state: 'visible'
            });
            
            // Count results on current page - using motion.div > ProductCard structure
            const currentResults = await page.$$eval(
              'div.grid > div > div > div[data-testid^="product-"], div.grid > div > div > .product-card',
              (elements: any) => elements.length
            );
            
            // Alternative count using the grid structure if the above doesn't work
            // const currentResults = await page.$$eval(
            //   'div.grid > div',
            //   (divs: any[]) => divs.filter(div => 
            //     div.querySelector('div[data-testid^="product-"], .product-card')
            //   ).length
            // );
            
            console.log(`Found ${currentResults} results on page ${pageNumber}`);
            totalResults += currentResults;

            // Look for "Load More" button with multiple possible selectors
            const loadMoreSelectors = [
              'button:has-text("Load More")',
              'button:has-text("Show More")',
              '[data-testid="load-more"]',
              '.load-more',
              '#load-more'
            ];
            
            let loadMoreButton = null;
            for (const selector of loadMoreSelectors) {
              const button = await page.$(selector);
              if (button && await button.isVisible()) {
                loadMoreButton = button;
                console.log(`Found load more button with selector: ${selector}`);
                break;
              }
            }
            
            // Take screenshot before clicking
            const loadMoreScreenshot = config.screenshotPath(`${query}-page-${pageNumber}`);
            await page.screenshot({ path: loadMoreScreenshot, fullPage: true });
            testResult.screenshots.push(loadMoreScreenshot);

            if (!loadMoreButton) {
              hasMoreResults = false;
              console.log('No more pages to load');
              break;
            }

            // Click the button and wait for network to be idle
            console.log('Clicking load more button...');
            await Promise.all([
              page.waitForResponse((response: any) => 
                response.url().includes('/api/search') && 
                response.status() === 200
              ),
              loadMoreButton.click()
            ]);
            
            pageNumber++;
            console.log(`Waiting for new results to load (page ${pageNumber})...`);
            
            // Wait for either new results or a short timeout
            await Promise.race([
              page.waitForFunction(
                (prevCount: number) => {
                  const currentCount = document.querySelectorAll('div.grid > div, .product-card, [data-testid^="product-"]').length;
                  return currentCount > prevCount;
                },
                totalResults,
                { timeout: 10000 }
              ),
              new Promise(resolve => setTimeout(resolve, 3000)) // Fallback timeout
            ]);
            
            console.log(`Loaded page ${pageNumber}`);
          } catch (error) {
            console.error('Error during pagination:', error);
            hasMoreResults = false;
            // Take error screenshot
            const errorScreenshot = config.screenshotPath(`${query}-pagination-error`);
            await page.screenshot({ path: errorScreenshot, fullPage: true });
            testResult.screenshots.push(errorScreenshot);
            testResult.errors = testResult.errors || [];
            testResult.errors.push({
              type: 'pagination',
              message: error instanceof Error ? error.message : String(error),
              stack: error instanceof Error ? error.stack : undefined,
              page: pageNumber,
              timestamp: new Date().toISOString()
            });
          }
        }

        // Final count of results
        testResult.actualCount = totalResults;
        testResult.passed = totalResults === expectedCount;
        testResult.duration = Date.now() - testStartTime;

        // Take final screenshot and save page content
        const finalScreenshot = config.screenshotPath(`${query}-final`);
        await page.screenshot({ path: finalScreenshot, fullPage: true });
        testResult.screenshots.push(finalScreenshot);
        
        // Log the final page content for debugging
        const finalContent = await page.content();
        fs.writeFileSync(path.join(config.outputDir, `${query}-final-content.html`), finalContent);
        
        // Log the number of results found
        console.log(`Found ${totalResults} results for query "${query}"`);

        // Assert the result count
        console.log(`Total results found: ${totalResults}, expected: ${expectedCount}`);
        
        // Take a final screenshot of the results
        const finalScreenshotPath = config.screenshotPath(`${query}-final`);
        await page.screenshot({ path: finalScreenshotPath, fullPage: true });
        console.log(`Saved final screenshot to: ${finalScreenshotPath}`);
        
        // Dump page content for debugging
        const finalPageContent = await page.content();
        fs.writeFileSync(path.join(config.outputDir, `${query}-final-content.html`), finalPageContent);
        
        // Assert the result count
        expect(totalResults).toBe(expectedCount);

      } catch (error: any) {
        console.error('Test failed with error:', error);
        
        // Take a screenshot on error
        const errorScreenshot = config.screenshotPath(`${query}-error`);
        await page.screenshot({ path: errorScreenshot, fullPage: true });
        console.error(`Error screenshot saved to: ${errorScreenshot}`);
        
        // Dump page content for debugging
        try {
          const pageContent = await page.content();
          fs.writeFileSync(path.join(config.outputDir, `${query}-error-content.html`), pageContent);
          
          // Log all available data-testid attributes for debugging
          const testIds = await page.$$eval('[data-testid]', els => 
            els.map(el => el.getAttribute('data-testid'))
          );
          console.log('Available data-testid attributes:', testIds);
          
          // Log all button texts
          const buttonTexts = await page.$$eval('button', els => 
            els.map(btn => btn.textContent?.trim())
          );
          console.log('Button texts:', buttonTexts);
          
        } catch (e) {
          console.error('Failed to capture debug information:', e);
        }
        
        testResult.errors.push({
          message: error.message,
          stack: error.stack,
          screenshots: testResult.screenshots
        });
        
        throw error;
      } finally {
        testResults.push(testResult);
      }
    });
  }
});

// Generate test report
async function generateTestReport() {
  // Create HTML report
  const htmlReport = generateHtmlReport();
  const htmlReportPath = path.join(config.outputDir, 'test-report.html');
  fs.writeFileSync(htmlReportPath, htmlReport);

  // Create JSON report
  const jsonReportPath = path.join(config.outputDir, 'test-report.json');
  fs.writeFileSync(jsonReportPath, JSON.stringify(testResults, null, 2));

  // Create CSV report
  const csvWriter = createObjectCsvWriter({
    path: path.join(config.outputDir, 'test-report.csv'),
    header: [
      { id: 'testName', title: 'Test Name' },
      { id: 'query', title: 'Search Query' },
      { id: 'expectedCount', title: 'Expected Count' },
      { id: 'actualCount', title: 'Actual Count' },
      { id: 'passed', title: 'Passed' },
      { id: 'duration', title: 'Duration (ms)' },
      { id: 'timestamp', title: 'Timestamp' }
    ]
  });

  await csvWriter.writeRecords(testResults);

  console.log(`\nTest reports generated at: ${config.outputDir}`);
  console.log(`- HTML Report: ${htmlReportPath}`);
  console.log(`- JSON Report: ${jsonReportPath}`);
  console.log(`- CSV Report: ${path.join(config.outputDir, 'test-report.csv')}\n`);
}

// Helper function to generate HTML report
function generateHtmlReport() {
  const passCount = testResults.filter(r => r.passed).length;
  const failCount = testResults.length - passCount;
  const passRate = (passCount / testResults.length * 100).toFixed(2);

  return `
  <!DOCTYPE html>
  <html>
  <head>
    <title>Search Functionality Test Report</title>
    <style>
      body { font-family: Arial, sans-serif; margin: 20px; }
      h1 { color: #333; }
      .summary { 
        background-color: #f5f5f5; 
        padding: 15px; 
        border-radius: 5px; 
        margin-bottom: 20px;
      }
      .pass { color: green; }
      .fail { color: red; }
      table { 
        width: 100%; 
        border-collapse: collapse; 
        margin-top: 20px;
      }
      th, td { 
        border: 1px solid #ddd; 
        padding: 8px; 
        text-align: left; 
      }
      th { 
        background-color: #f2f2f2; 
      }
      tr:nth-child(even) { 
        background-color: #f9f9f9; 
      }
      .screenshots { margin-top: 20px; }
      .screenshot { 
        margin: 10px 0; 
        border: 1px solid #ddd;
        padding: 10px;
      }
      .screenshot img { 
        max-width: 100%; 
        height: auto;
        border: 1px solid #eee;
      }
    </style>
  </head>
  <body>
    <h1>Search Functionality Test Report</h1>
    <div class="summary">
      <h2>Test Summary</h2>
      <p>Total Tests: ${testResults.length}</p>
      <p class="pass">Passed: ${passCount}</p>
      <p class="fail">Failed: ${failCount}</p>
      <p>Pass Rate: ${passRate}%</p>
      <p>Generated at: ${new Date().toISOString()}</p>
    </div>

    <h2>Test Results</h2>
    <table>
      <tr>
        <th>Test Name</th>
        <th>Query</th>
        <th>Expected</th>
        <th>Actual</th>
        <th>Status</th>
        <th>Duration</th>
      </tr>
      ${testResults.map(result => `
        <tr>
          <td>${result.testName}</td>
          <td>${result.query}</td>
          <td>${result.expectedCount}</td>
          <td>${result.actualCount}</td>
          <td class="${result.passed ? 'pass' : 'fail'}">
            ${result.passed ? 'PASS' : 'FAIL'}
          </td>
          <td>${result.duration}ms</td>
        </tr>
      `).join('')}
    </table>

    ${testResults.map((result, index) => `
      <div class="test-detail">
        <h3>${result.testName}</h3>
        <p><strong>Status:</strong> <span class="${result.passed ? 'pass' : 'fail'}">
          ${result.passed ? 'PASSED' : 'FAILED'}
        </span></p>
        <p><strong>Query:</strong> ${result.query}</p>
        <p><strong>Expected Results:</strong> ${result.expectedCount}</p>
        <p><strong>Actual Results:</strong> ${result.actualCount}</p>
        <p><strong>Duration:</strong> ${result.duration}ms</p>
        
        ${result.errors.length > 0 ? `
          <div class="errors">
            <h4>Errors:</h4>
            <pre>${JSON.stringify(result.errors, null, 2)}</pre>
          </div>
        ` : ''}

        ${result.screenshots.length > 0 ? `
          <div class="screenshots">
            <h4>Screenshots:</h4>
            ${result.screenshots.map(screenshot => `
              <div class="screenshot">
                <img src="${path.relative(config.outputDir, screenshot)}" alt="Screenshot" />
                <p>${path.basename(screenshot)}</p>
              </div>
            `).join('')}
          </div>
        ` : ''}
      </div>
    `).join('')}
  </body>
  </html>
  `;
}
