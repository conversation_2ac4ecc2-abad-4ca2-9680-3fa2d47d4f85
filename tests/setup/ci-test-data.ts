// CI Test Data Generator
// Provides consistent mock data for GitHub Actions testing

export const mockProducts = [
  {
    id: 'prod-1',
    name: 'iPhone 15 Pro',
    slug: 'iphone-15-pro',
    description: 'Latest iPhone with advanced camera system',
    images: ['https://example.com/iphone15.jpg'],
    specifications: { storage: '256GB', color: 'Natural Titanium' },
    status: 'active',
    is_featured: true,
    is_sponsored: false,
    cashback_amount: 50,
    model_number: 'A3101',
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
    brand_id: 'brand-1',
    category_id: 'cat-1',
    promotion_id: 'promo-1',
  },
  {
    id: 'prod-2', 
    name: 'Samsung Galaxy S24',
    slug: 'samsung-galaxy-s24',
    description: 'Premium Android smartphone',
    images: ['https://example.com/galaxy-s24.jpg'],
    specifications: { storage: '512GB', color: 'Phantom Black' },
    status: 'active',
    is_featured: false,
    is_sponsored: true,
    cashback_amount: 75,
    model_number: 'SM-S921U',
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
    brand_id: 'brand-2',
    category_id: 'cat-1', 
    promotion_id: 'promo-2',
  },
]

export const mockBrands = [
  {
    id: 'brand-1',
    name: 'Apple',
    slug: 'apple',
    logo_url: 'https://example.com/apple-logo.png',
    description: 'Technology company known for innovative consumer electronics',
    featured: true,
    sponsored: false,
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
  },
  {
    id: 'brand-2',
    name: 'Samsung',
    slug: 'samsung',
    logo_url: 'https://example.com/samsung-logo.png', 
    description: 'South Korean multinational conglomerate',
    featured: true,
    sponsored: true,
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
  },
]

export const mockPromotions = [
  {
    id: 'promo-1',
    title: 'iPhone 15 Pro Cashback',
    description: 'Get $50 cashback on iPhone 15 Pro purchase',
    max_cashback_amount: 50,
    purchase_start_date: '2025-01-01',
    purchase_end_date: '2025-12-31',
    claim_start_offset_days: 7,
    claim_window_days: 30,
    terms_url: 'https://example.com/terms',
    terms_description: 'Valid for new purchases only',
    status: 'active',
    is_featured: true,
    brand_id: 'brand-1',
    category_id: 'cat-1',
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
  },
  {
    id: 'promo-2', 
    title: 'Galaxy S24 Cashback',
    description: 'Get $75 cashback on Galaxy S24 purchase',
    max_cashback_amount: 75,
    purchase_start_date: '2025-01-01',
    purchase_end_date: '2025-12-31', 
    claim_start_offset_days: 14,
    claim_window_days: 45,
    terms_url: 'https://example.com/samsung-terms',
    terms_description: 'Available for qualifying purchases',
    status: 'active',
    is_featured: false,
    brand_id: 'brand-2',
    category_id: 'cat-1',
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
  },
]

export const mockRetailers = [
  {
    id: 'retailer-1',
    name: 'Best Buy',
    slug: 'best-buy',
    logo_url: 'https://example.com/bestbuy-logo.png',
    website_url: 'https://bestbuy.com',
    status: 'active',
    featured: true,
    sponsored: false,
    claim_period: '30 days',
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
  },
  {
    id: 'retailer-2',
    name: 'Amazon',
    slug: 'amazon', 
    logo_url: 'https://example.com/amazon-logo.png',
    website_url: 'https://amazon.com',
    status: 'active',
    featured: true,
    sponsored: true,
    claim_period: '45 days',
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
  },
]

export const mockCategories = [
  {
    id: 'cat-1',
    name: 'Smartphones',
    slug: 'smartphones',
    parent_id: null,
    featured: true,
    sponsored: false,
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
  },
  {
    id: 'cat-2',
    name: 'Laptops',
    slug: 'laptops', 
    parent_id: null,
    featured: true,
    sponsored: false,
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
  },
]

// Mock API responses for CI
export const mockAPIResponses = {
  products: {
    data: mockProducts,
    pagination: {
      page: 1,
      pageSize: 20,
      total: mockProducts.length,
      totalPages: 1,
      hasNext: false,
      hasPrev: false,
    },
  },
  brands: {
    data: mockBrands,
    pagination: {
      page: 1,
      pageSize: 20,
      total: mockBrands.length,
      totalPages: 1,
      hasNext: false,
      hasPrev: false,
    },
  },
  promotions: {
    data: mockPromotions,
    pagination: {
      page: 1,
      pageSize: 20,
      total: mockPromotions.length,
      totalPages: 1,
      hasNext: false,
      hasPrev: false,
    },
  },
  retailers: {
    data: mockRetailers,
    pagination: {
      page: 1,
      pageSize: 20,
      total: mockRetailers.length,
      totalPages: 1,
      hasNext: false,
      hasPrev: false,
    },
  },
}

// Helper function to get mock data by type
export function getMockData(type: 'products' | 'brands' | 'promotions' | 'retailers') {
  switch (type) {
    case 'products':
      return mockProducts
    case 'brands':
      return mockBrands
    case 'promotions':
      return mockPromotions
    case 'retailers':
      return mockRetailers
    default:
      return []
  }
}