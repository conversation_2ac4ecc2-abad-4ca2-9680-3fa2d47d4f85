# Test Setup and Configuration

**UPDATED <as of 28 July 2025:13:00 PM>**

This directory contains test configuration files, setup utilities, and shared test helpers.

## 📚 Complete Documentation

For comprehensive testing guidance, see the centralized documentation:
- **Testing Strategy & Setup:** [`docs/development/TESTING.md`](../../docs/development/TESTING.md)
- **Environment Configuration:** [`docs/development/ENVIRONMENT_SETUP.md`](../../docs/development/ENVIRONMENT_SETUP.md)

## Files

### Configuration Files
- `jest.setup.ts` - Global Jest setup and configuration
- `test-utils.tsx` - React Testing Library utilities and custom render functions
- `ci-test-setup.ts` - CI-specific test setup and configuration
- `ci-test-data.ts` - Test data for CI environments

### Usage

#### Custom Render Function
```typescript
import { render } from '@/tests/setup/test-utils'

// Use instead of RTL's render for consistent setup
render(<Component />)
```

#### Test Environment Setup
The setup files are automatically loaded by Jest configuration:
```javascript
// jest.config.js
setupFilesAfterEnv: [
  '<rootDir>/tests/setup/jest.setup.ts',
  '<rootDir>/tests/setup/ci-test-setup.ts'
]
```

## Best Practices

1. **Global Setup**: Use `jest.setup.ts` for global test environment configuration
2. **Custom Utilities**: Add reusable test utilities to `test-utils.tsx`
3. **Environment-Specific**: Use separate files for different environments (CI, local, etc.)
4. **Mock Configuration**: Configure global mocks in setup files rather than individual tests

## Adding New Setup

When adding new global test setup:
1. Create the setup file in this directory
2. Add it to the Jest configuration
3. Document its purpose in this README
4. Ensure it doesn't conflict with existing setup
