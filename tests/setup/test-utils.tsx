/**
 * Test utilities and custom render functions
 * Provides common testing utilities used across the test suite
 */

import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

// Mock Supabase client for testing
export const mockSupabaseClient = {
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  neq: jest.fn().mockReturnThis(),
  gte: jest.fn().mockReturnThis(),
  lte: jest.fn().mockReturnThis(),
  like: jest.fn().mockReturnThis(),
  ilike: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  range: jest.fn().mockReturnThis(),
  single: jest.fn().mockResolvedValue({
    data: null,
    error: null,
  }),
  then: jest.fn().mockResolvedValue({
    data: [],
    error: null,
    count: 0,
  }),
}

// Create a test Supabase client
export const createTestSupabaseClient = () => mockSupabaseClient

// Custom render function with providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Test data factories
export const createMockProduct = (overrides = {}) => ({
  id: 'test-product-id',
  name: 'Test Product',
  slug: 'test-product',
  description: 'Test product description',
  price: 99.99,
  currency: 'GBP',
  image_url: 'https://example.com/image.jpg',
  retailer: {
    id: 'test-retailer',
    name: 'Test Retailer',
    slug: 'test-retailer',
    logo_url: 'https://example.com/logo.jpg',
  },
  category: {
    id: 'test-category',
    name: 'Test Category',
    slug: 'test-category',
  },
  brand: {
    id: 'test-brand',
    name: 'Test Brand',
    slug: 'test-brand',
  },
  cashback_rate: 5.0,
  cashback_amount: 4.99,
  deal_url: 'https://example.com/deal',
  created_at: '2025-01-01T00:00:00Z',
  updated_at: '2025-01-01T00:00:00Z',
  ...overrides,
})

export const createMockUser = (overrides = {}) => ({
  id: 'test-user-id',
  email: '<EMAIL>',
  created_at: '2025-01-01T00:00:00Z',
  updated_at: '2025-01-01T00:00:00Z',
  ...overrides,
})

export const createMockSearchResults = (overrides = {}) => ({
  success: true,
  data: {
    products: [createMockProduct()],
    total: 1,
    page: 1,
    limit: 20,
  },
  meta: {
    query: 'test search',
    filters: {},
    sort: 'relevance',
  },
  ...overrides,
})

// Test environment helpers
export const setupTestEnvironment = () => {
  // Set up test environment variables
  process.env.NODE_ENV = 'test'
  process.env.NEXT_PUBLIC_SUPABASE_URL = 'https://test.supabase.co'
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key'
  process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key'
}

export const cleanupTestEnvironment = () => {
  // Clean up any test data or mocks
  jest.clearAllMocks()
}

// Database test helpers
export const seedDatabase = async (table: string, data: any[]) => {
  // Mock database seeding for tests
  console.log(`Seeding ${table} with ${data.length} records`)
  return Promise.resolve(data)
}

export const clearDatabase = async (table: string) => {
  // Mock database clearing for tests
  console.log(`Clearing ${table}`)
  return Promise.resolve()
}

// API test helpers
export const createMockRequest = (url: string, options: RequestInit = {}) => {
  return new Request(url, {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  })
}

export const createMockResponse = (data: any, status = 200) => {
  return new Response(JSON.stringify(data), {
    status,
    headers: {
      'Content-Type': 'application/json',
    },
  })
}

// Wait utilities
export const waitForNextTick = () => new Promise(resolve => process.nextTick(resolve))
export const waitForTimeout = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))
