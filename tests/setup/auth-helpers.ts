// Test Authentication Helpers
// Utilities for creating valid authentication tokens in tests

import { createJWT } from '@/lib/security/jwt'
import { generateHMACSignature } from '@/lib/security/hmac'

/**
 * Creates a valid JWT token for testing
 */
export async function createTestJWT(): Promise<string> {
  try {
    return await createJWT()
  } catch (error) {
    console.error('Failed to create test JWT:', error)
    throw new Error('Test JWT creation failed')
  }
}

/**
 * Creates valid HMAC headers for testing
 */
export function createTestHMACHeaders(url: string, method: string = 'GET'): Record<string, string> {
  const timestamp = Date.now().toString()
  const partnerId = 'test-partner'
  const secret = process.env.PARTNER_SECRET_TEST_PARTNER || 'test-secret-minimum-32-characters-long-for-tests'
  
  // Create signature data (method + url + timestamp)
  const signatureData = `${method.toUpperCase()}${url}${timestamp}`
  
  try {
    const signature = generateHMACSignature(signatureData, secret)
    
    return {
      'x-signature': signature,
      'x-timestamp': timestamp,
      'x-partner-id': partnerId
    }
  } catch (error) {
    console.error('Failed to create test HMAC:', error)
    throw new Error('Test HMAC creation failed')
  }
}

/**
 * Creates a mock NextRequest with valid authentication
 */
export async function createAuthenticatedMockRequest(
  url: string,
  options: {
    origin?: string
    method?: string
    headers?: Record<string, string>
    auth?: 'jwt' | 'hmac' | 'none'
  } = {}
): Promise<Request> {
  const {
    origin,
    method = 'GET',
    headers = {},
    auth = 'none'
  } = options

  const requestHeaders = new Headers({
    'content-type': 'application/json',
    ...headers
  })

  if (origin) {
    requestHeaders.set('origin', origin)
  }

  // Add real authentication headers based on type
  if (auth === 'jwt') {
    const jwt = await createTestJWT()
    requestHeaders.set('authorization', `Bearer ${jwt}`)
  } else if (auth === 'hmac') {
    const hmacHeaders = createTestHMACHeaders(url, method)
    Object.entries(hmacHeaders).forEach(([key, value]) => {
      requestHeaders.set(key, value)
    })
  }

  return new Request(url, {
    method,
    headers: requestHeaders
  })
}