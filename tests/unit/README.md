# Unit Tests

**UPDATED <as of 28 July 2025:13:00 PM>**

This directory contains unit tests for individual components, functions, and hooks.

## 📚 Complete Documentation

For comprehensive testing guidance, see the centralized documentation:
- **Testing Strategy & Setup:** [`docs/development/TESTING.md`](../../docs/development/TESTING.md)
- **Unit Testing Best Practices:** [`docs/development/TESTING.md#unit-testing`](../../docs/development/TESTING.md#unit-testing)

## Structure

```
unit/
├── components/           # React component tests
│   ├── layout/          # Layout component tests
│   ├── search/          # Search component tests
│   ├── products/        # Product component tests
│   └── ui/              # UI component tests
├── lib/                 # Library function tests
│   ├── data/           # Data layer function tests
│   ├── utils/          # Utility function tests
│   └── validation/     # Validation function tests
└── hooks/              # Custom hook tests
```

## Testing Guidelines

### Component Tests
- Test component rendering and behavior
- Test prop handling and state changes
- Test user interactions (clicks, form submissions)
- Test accessibility features

Example:
```typescript
// tests/unit/components/ProductCard.test.tsx
import { render, screen } from '@/tests/setup/test-utils'
import { ProductCard } from '@/components/ProductCard'

describe('ProductCard', () => {
  const mockProduct = {
    id: '1',
    name: 'Test Product',
    price: 99.99
  }

  it('renders product information correctly', () => {
    render(<ProductCard product={mockProduct} />)
    
    expect(screen.getByText('Test Product')).toBeInTheDocument()
    expect(screen.getByText('£99.99')).toBeInTheDocument()
  })
})
```

### Function Tests
- Test pure functions with various inputs
- Test error handling and edge cases
- Test return values and side effects

Example:
```typescript
// tests/unit/lib/utils/formatPrice.test.ts
import { formatPrice } from '@/lib/utils/formatPrice'

describe('formatPrice', () => {
  it('formats price correctly', () => {
    expect(formatPrice(99.99)).toBe('£99.99')
    expect(formatPrice(0)).toBe('£0.00')
  })
})
```

### Hook Tests
- Test hook behavior and state changes
- Test hook effects and cleanup
- Use `@testing-library/react-hooks` for complex hooks

Example:
```typescript
// tests/unit/hooks/usePagination.test.ts
import { renderHook } from '@testing-library/react'
import { usePagination } from '@/hooks/usePagination'

describe('usePagination', () => {
  it('initializes with correct default values', () => {
    const { result } = renderHook(() => usePagination())
    
    expect(result.current.currentPage).toBe(1)
    expect(result.current.hasNextPage).toBe(false)
  })
})
```

## Coverage Goals

- **Components**: 80% line coverage minimum
- **Functions**: 90% line coverage minimum
- **Hooks**: 85% line coverage minimum

## Running Unit Tests

```bash
# Run all unit tests
npm run test:unit

# Run specific component tests
npm test -- --testPathPattern=components

# Run with coverage
npm run test:coverage -- --testPathPattern=unit
```
