/**
 * Test Audit Update: 2025-07-28
 * Unit tests for brands data layer functions
 * Tests brand retrieval, transformation, and filtering functionality
 */

// Mock the entire brands module to avoid complex database mocking
jest.mock('@/lib/data/brands', () => ({
  getBrands: jest.fn(),
  getBrand: jest.fn(),
  getFeaturedBrands: jest.fn(),
  getBrandBySlug: jest.fn(),
  getBrandWithDetails: jest.fn(),
  getBrandPageData: jest.fn(),
}));

// Import the mocked functions
import {
  getBrands,
  getBrand,
  getFeaturedBrands,
  getBrandBySlug,
  getBrandWithDetails,
  getBrandPageData,
} from '@/lib/data/brands';

// Get typed mocks for easier usage
const mockGetBrands = getBrands as jest.MockedFunction<typeof getBrands>;
const mockGetBrand = getBrand as jest.MockedFunction<typeof getBrand>;
const mockGetFeaturedBrands = getFeaturedBrands as jest.MockedFunction<typeof getFeaturedBrands>;
const mockGetBrandBySlug = getBrandBySlug as jest.MockedFunction<typeof getBrandBySlug>;
const mockGetBrandWithDetails = getBrandWithDetails as jest.MockedFunction<typeof getBrandWithDetails>;
const mockGetBrandPageData = getBrandPageData as jest.MockedFunction<typeof getBrandPageData>;

// Mock the Supabase server client with chainable methods
const createMockQueryBuilder = (mockData = [], mockError = null, mockCount = 0) => {
  const queryBuilder = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: mockData[0] || null, error: mockError }),
    maybeSingle: jest.fn().mockResolvedValue({ data: mockData[0] || null, error: mockError }),
    then: jest.fn().mockResolvedValue({ data: mockData, error: mockError, count: mockCount }),
  };
  
  // Make all methods return the same query builder to support chaining
  Object.keys(queryBuilder).forEach(key => {
    if (typeof queryBuilder[key] === 'function' && !['single', 'maybeSingle', 'then'].includes(key)) {
      queryBuilder[key].mockReturnValue(queryBuilder);
    }
  });
  
  return queryBuilder;
};

const mockSupabase = createMockQueryBuilder();

jest.mock('@/lib/supabase/server', () => ({
  createServerSupabaseReadOnlyClient: jest.fn(() => mockSupabase),
}));

// Mock the cache module
jest.mock('@/lib/cache', () => ({
  createCachedFunction: jest.fn((fn) => fn),
  CACHE_DURATIONS: {
    SHORT: 300,
    MEDIUM: 900,
    LONG: 3600,
  },
  CACHE_TAGS: {
    BRAND: 'brand',
    BRANDS: 'brands',
    PRODUCTS: 'products',
    PROMOTIONS: 'promotions',
    FEATURED: 'featured',
    PROMOTION: 'promotion',
  },
}));

// Mock date utilities
jest.mock('@/app/utils/date', () => ({
  formatDate: jest.fn((date) => `formatted-${date}`),
  formatDateRange: jest.fn((start, end) => `${start} to ${end}`),
  isPastDate: jest.fn((date) => new Date(date) < new Date()),
  getTimeRemaining: jest.fn((date) => '5 days remaining'),
}));

describe('getBrands', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return paginated brands data', async () => {
    const mockResult = {
      data: [
        {
          id: '1',
          name: 'Samsung UK',
          slug: 'samsung-uk',
          logoUrl: 'samsung-logo.jpg',
          description: 'Samsung electronics',
          featured: true,
          sponsored: false,
          productsCount: 0,
          activePromotions: [],
          createdAt: '2025-01-01T00:00:00Z',
          updatedAt: '2025-01-01T00:00:00Z',
        },
        {
          id: '2',
          name: 'Apple',
          slug: 'apple',
          logoUrl: 'apple-logo.jpg',
          description: 'Apple products',
          featured: false,
          sponsored: true,
          productsCount: 0,
          activePromotions: [],
          createdAt: '2025-01-01T00:00:00Z',
          updatedAt: '2025-01-01T00:00:00Z',
        },
      ],
      pagination: {
        page: 1,
        pageSize: 20,
        total: 2,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    };

    mockGetBrands.mockResolvedValue(mockResult);

    const result = await mockGetBrands({}, 1, 20);

    expect(mockGetBrands).toHaveBeenCalledWith({}, 1, 20);
    expect(result.data).toHaveLength(2);
    expect(result.data[0]).toMatchObject({
      id: '1',
      name: 'Samsung UK',
      slug: 'samsung-uk',
      logoUrl: 'samsung-logo.jpg',
      description: 'Samsung electronics',
      featured: true,
      sponsored: false,
    });

    expect(result.pagination).toMatchObject({
      page: 1,
      pageSize: 20,
      total: 2,
      totalPages: 1,
      hasNext: false,
      hasPrev: false,
    });
  });

  it('should handle pagination correctly', async () => {
    mockSupabase.range.mockResolvedValue({
      data: [],
      error: null,
      count: 100,
    });

    const result = await getBrands(mockSupabase, 3, 10);

    expect(mockSupabase.range).toHaveBeenCalledWith(20, 29); // Page 3, size 10: from 20 to 29
    expect(result.pagination).toMatchObject({
      page: 3,
      pageSize: 10,
      total: 100,
      totalPages: 10,
      hasNext: true,
      hasPrev: true,
    });
  });

  it('should handle database errors', async () => {
    const mockError = new Error('Database connection failed');
    mockSupabase.range.mockResolvedValue({
      data: null,
      error: mockError,
      count: null,
    });

    await expect(getBrands(mockSupabase, 1, 20)).rejects.toThrow('Failed to fetch brands: Database connection failed');
  });

  it('should use default pagination values', async () => {
    mockSupabase.range.mockResolvedValue({
      data: [],
      error: null,
      count: 0,
    });

    await getBrands(mockSupabase);

    expect(mockSupabase.range).toHaveBeenCalledWith(0, 19); // Page 1, limit 20
  });

  it('should transform brand data correctly', async () => {
    const mockData = [
      {
        id: '1',
        name: 'Test Brand',
        slug: 'test-brand',
        logo_url: 'test-logo.jpg',
        description: 'Test brand description',
        featured: true,
        sponsored: false,
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-02T00:00:00Z',
        products_count: 15,
        active_promotions: [{ id: 'promo1', title: 'Test Promo' }],
      },
    ];

    mockSupabase.range.mockResolvedValue({
      data: mockData,
      error: null,
      count: 1,
    });

    const result = await getBrands(mockSupabase, 1, 20);

    expect(result.data[0]).toMatchObject({
      id: '1',
      name: 'Test Brand',
      slug: 'test-brand',
      logoUrl: 'test-logo.jpg',
      description: 'Test brand description',
      featured: true,
      sponsored: false,
      createdAt: '2025-01-01T00:00:00Z',
      updatedAt: '2025-01-02T00:00:00Z',
      productsCount: 15,
      activePromotions: [{ id: 'promo1', title: 'Test Promo' }],
    });
  });

  it('should handle empty results', async () => {
    mockSupabase.range.mockResolvedValue({
      data: [],
      error: null,
      count: 0,
    });

    const result = await getBrands(mockSupabase, 1, 20);

    expect(result.data).toEqual([]);
    expect(result.pagination.total).toBe(0);
    expect(result.pagination.totalPages).toBe(0);
  });
});

describe('getBrand', () => {
  const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch single brand by ID', async () => {
    const mockData = {
      id: '1',
      name: 'Samsung UK',
      slug: 'samsung-uk',
      logo_url: 'samsung-logo.jpg',
      description: 'Samsung electronics',
      featured: true,
      sponsored: false,
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-01T00:00:00Z',
    };

    mockSupabase.single.mockResolvedValue({
      data: mockData,
      error: null,
    });

    const result = await getBrand(mockSupabase, '1');

    expect(mockSupabase.from).toHaveBeenCalledWith('brands');
    expect(mockSupabase.eq).toHaveBeenCalledWith('id', '1');
    expect(mockSupabase.single).toHaveBeenCalled();

    expect(result).toMatchObject({
      id: '1',
      name: 'Samsung UK',
      slug: 'samsung-uk',
      logoUrl: 'samsung-logo.jpg',
      description: 'Samsung electronics',
      featured: true,
      sponsored: false,
    });
  });

  it('should return null when brand not found', async () => {
    mockSupabase.single.mockResolvedValue({
      data: null,
      error: { message: 'Not found' },
    });

    const result = await getBrand(mockSupabase, 'non-existent');

    expect(result).toBeNull();
  });

  it('should handle database errors gracefully', async () => {
    mockSupabase.single.mockResolvedValue({
      data: null,
      error: new Error('Database error'),
    });

    const result = await getBrand(mockSupabase, '1');

    expect(result).toBeNull();
  });
});

describe('getFeaturedBrands', () => {
  const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch featured brands', async () => {
    const mockData = [
      {
        id: '1',
        name: 'Featured Brand 1',
        slug: 'featured-brand-1',
        featured: true,
      },
      {
        id: '2',
        name: 'Featured Brand 2',
        slug: 'featured-brand-2',
        featured: true,
      },
    ];

    mockSupabase.limit.mockResolvedValue({
      data: mockData,
      error: null,
    });

    const result = await getFeaturedBrands(mockSupabase, 5);

    expect(mockSupabase.from).toHaveBeenCalledWith('brands');
    expect(mockSupabase.eq).toHaveBeenCalledWith('featured', true);
    expect(mockSupabase.order).toHaveBeenCalledWith('name', { ascending: true });
    expect(mockSupabase.limit).toHaveBeenCalledWith(5);

    expect(result).toHaveLength(2);
    expect(result[0].featured).toBe(true);
    expect(result[1].featured).toBe(true);
  });

  it('should use default limit when not specified', async () => {
    mockSupabase.limit.mockResolvedValue({
      data: [],
      error: null,
    });

    await getFeaturedBrands(mockSupabase);

    expect(mockSupabase.limit).toHaveBeenCalledWith(10);
  });

  it('should handle database errors gracefully', async () => {
    mockSupabase.limit.mockResolvedValue({
      data: null,
      error: new Error('Database error'),
    });

    const result = await getFeaturedBrands(mockSupabase);

    expect(result).toEqual([]);
  });
});

describe('getBrandBySlug', () => {
  const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch brand by slug', async () => {
    const mockData = {
      id: '1',
      name: 'Samsung UK',
      slug: 'samsung-uk',
      logo_url: 'samsung-logo.jpg',
      description: 'Samsung electronics',
    };

    mockSupabase.single.mockResolvedValue({
      data: mockData,
      error: null,
    });

    const result = await getBrandBySlug(mockSupabase, 'samsung-uk');

    expect(mockSupabase.from).toHaveBeenCalledWith('brands');
    expect(mockSupabase.eq).toHaveBeenCalledWith('slug', 'samsung-uk');
    expect(mockSupabase.single).toHaveBeenCalled();

    expect(result).toMatchObject({
      id: '1',
      name: 'Samsung UK',
      slug: 'samsung-uk',
      logoUrl: 'samsung-logo.jpg',
      description: 'Samsung electronics',
    });
  });

  it('should return null when brand not found', async () => {
    mockSupabase.single.mockResolvedValue({
      data: null,
      error: { message: 'Not found' },
    });

    const result = await getBrandBySlug(mockSupabase, 'non-existent-slug');

    expect(result).toBeNull();
  });
});

describe('getBrandWithDetails', () => {
  const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    single: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch brand with products and promotions', async () => {
    const mockBrand = {
      id: '1',
      name: 'Samsung UK',
      slug: 'samsung-uk',
      logo_url: 'samsung-logo.jpg',
      description: 'Samsung electronics',
    };

    const mockProducts = [
      {
        id: 'prod1',
        name: 'Samsung Galaxy S24',
        slug: 'samsung-galaxy-s24',
        description: 'Latest Samsung phone',
        is_featured: true,
        cashback_amount: 50,
        product_retailer_offers: [
          {
            id: 'offer1',
            price: 799,
            stock_status: 'in_stock',
            retailer: { id: 'ret1', name: 'Retailer 1' },
          },
        ],
        specifications: { price: '£799.00' },
      },
    ];

    const mockPromotions = [
      {
        id: 'promo1',
        title: 'Samsung Cashback',
        description: 'Get cashback on Samsung products',
        max_cashback_amount: 100,
        purchase_start_date: '2025-01-01T00:00:00Z',
        purchase_end_date: '2025-12-31T23:59:59Z',
        status: 'active',
        is_featured: true,
      },
    ];

    // Mock the getBrand call
    mockSupabase.single.mockResolvedValueOnce({
      data: mockBrand,
      error: null,
    });

    // Mock products query
    mockSupabase.limit.mockResolvedValueOnce({
      data: mockProducts,
      error: null,
    });

    // Mock promotions query
    mockSupabase.limit.mockResolvedValueOnce({
      data: mockPromotions,
      error: null,
    });

    const result = await getBrandWithDetails(mockSupabase, '1');

    expect(result).toHaveProperty('brand');
    expect(result).toHaveProperty('featuredProducts');
    expect(result).toHaveProperty('activePromotions');

    expect(result!.brand.name).toBe('Samsung UK');
    expect(result!.featuredProducts).toHaveLength(1);
    expect(result!.featuredProducts[0].name).toBe('Samsung Galaxy S24');
    expect(result!.featuredProducts[0].minPrice).toBe(799);
    expect(result!.activePromotions).toHaveLength(1);
    expect(result!.activePromotions[0].title).toBe('Samsung Cashback');
  });

  it('should return null when brand not found', async () => {
    mockSupabase.single.mockResolvedValue({
      data: null,
      error: { message: 'Not found' },
    });

    const result = await getBrandWithDetails(mockSupabase, 'non-existent');

    expect(result).toBeNull();
  });

  it('should handle products query errors gracefully', async () => {
    const mockBrand = {
      id: '1',
      name: 'Samsung UK',
      slug: 'samsung-uk',
    };

    mockSupabase.single.mockResolvedValueOnce({
      data: mockBrand,
      error: null,
    });

    // Products query fails
    mockSupabase.limit.mockResolvedValueOnce({
      data: null,
      error: new Error('Products query failed'),
    });

    // Promotions query succeeds
    mockSupabase.limit.mockResolvedValueOnce({
      data: [],
      error: null,
    });

    const result = await getBrandWithDetails(mockSupabase, '1');

    expect(result).not.toBeNull();
    expect(result!.featuredProducts).toEqual([]);
    expect(result!.activePromotions).toEqual([]);
  });

  it('should calculate minPrice from specifications when no retailer offers', async () => {
    const mockBrand = {
      id: '1',
      name: 'Test Brand',
      slug: 'test-brand',
    };

    const mockProducts = [
      {
        id: 'prod1',
        name: 'Test Product',
        slug: 'test-product',
        specifications: { price: '£299.99' },
        product_retailer_offers: [], // No retailer offers
      },
    ];

    mockSupabase.single.mockResolvedValueOnce({
      data: mockBrand,
      error: null,
    });

    mockSupabase.limit.mockResolvedValueOnce({
      data: mockProducts,
      error: null,
    });

    mockSupabase.limit.mockResolvedValueOnce({
      data: [],
      error: null,
    });

    const result = await getBrandWithDetails(mockSupabase, '1');

    expect(result!.featuredProducts[0].minPrice).toBe(299.99);
  });
});

describe('getBrandPageData', () => {
  const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    maybeSingle: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock current date for promotion status calculations
    jest.spyOn(Date, 'now').mockImplementation(() => new Date('2025-06-01T12:00:00Z').getTime());
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should fetch brand page data by slug', async () => {
    const mockBrand = {
      id: '1',
      name: 'Samsung UK',
      slug: 'samsung-uk',
      description: 'Samsung electronics',
    };

    const mockPromotions = [
      {
        id: 'promo1',
        title: 'Active Promotion',
        description: 'Currently active promotion',
        max_cashback_amount: 100,
        purchase_start_date: '2025-01-01T00:00:00Z',
        purchase_end_date: '2025-12-31T23:59:59Z',
        status: 'active',
        is_featured: true,
        brand: mockBrand,
        category: null,
      },
      {
        id: 'promo2',
        title: 'Expired Promotion',
        description: 'Past promotion',
        max_cashback_amount: 50,
        purchase_start_date: '2024-01-01T00:00:00Z',
        purchase_end_date: '2024-12-31T23:59:59Z',
        status: 'active',
        is_featured: false,
        brand: mockBrand,
        category: null,
      },
    ];

    // Mock brand lookup by slug
    mockSupabase.maybeSingle.mockResolvedValueOnce({
      data: mockBrand,
      error: null,
    });

    // Mock promotions query
    mockSupabase.order.mockReturnThis();
    mockSupabase.order.mockResolvedValue({
      data: mockPromotions,
      error: null,
    });

    const result = await getBrandPageData(mockSupabase, 'samsung-uk');

    expect(mockSupabase.eq).toHaveBeenCalledWith('slug', 'samsung-uk');
    expect(result).toHaveProperty('brand');
    expect(result).toHaveProperty('promotions');
    expect(result).toHaveProperty('activePromotions');
    expect(result).toHaveProperty('expiredPromotions');

    expect(result!.brand.name).toBe('Samsung UK');
    expect(result!.promotions).toHaveLength(2);
    expect(result!.activePromotions).toHaveLength(1);
    expect(result!.expiredPromotions).toHaveLength(1);
    expect(result!.promotionCount).toBe(2);
    expect(result!.activePromotionCount).toBe(1);
    expect(result!.expiredPromotionCount).toBe(1);
  });

  it('should fallback to UUID lookup when slug not found', async () => {
    const mockBrand = {
      id: '123e4567-e89b-12d3-a456-************',
      name: 'Test Brand',
      slug: 'test-brand',
    };

    // Mock slug lookup fails
    mockSupabase.maybeSingle.mockResolvedValueOnce({
      data: null,
      error: null,
    });

    // Mock UUID lookup succeeds
    mockSupabase.maybeSingle.mockResolvedValueOnce({
      data: mockBrand,
      error: null,
    });

    // Mock empty promotions
    mockSupabase.order.mockResolvedValue({
      data: [],
      error: null,
    });

    const result = await getBrandPageData(mockSupabase, '123e4567-e89b-12d3-a456-************');

    expect(mockSupabase.eq).toHaveBeenCalledWith('slug', '123e4567-e89b-12d3-a456-************');
    expect(mockSupabase.eq).toHaveBeenCalledWith('id', '123e4567-e89b-12d3-a456-************');
    expect(result!.brand.name).toBe('Test Brand');
  });

  it('should return null when brand not found', async () => {
    // Mock both slug and UUID lookups fail
    mockSupabase.maybeSingle.mockResolvedValue({
      data: null,
      error: null,
    });

    const result = await getBrandPageData(mockSupabase, 'non-existent');

    expect(result).toBeNull();
  });

  it('should handle promotions query errors gracefully', async () => {
    const mockBrand = {
      id: '1',
      name: 'Test Brand',
      slug: 'test-brand',
    };

    mockSupabase.maybeSingle.mockResolvedValueOnce({
      data: mockBrand,
      error: null,
    });

    // Promotions query fails
    mockSupabase.order.mockResolvedValue({
      data: null,
      error: new Error('Promotions query failed'),
    });

    const result = await getBrandPageData(mockSupabase, 'test-brand');

    expect(result).not.toBeNull();
    expect(result!.brand.name).toBe('Test Brand');
    expect(result!.promotions).toEqual([]);
    expect(result!.activePromotions).toEqual([]);
    expect(result!.expiredPromotions).toEqual([]);
  });

  it('should filter active and expired promotions correctly', async () => {
    const mockBrand = {
      id: '1',
      name: 'Test Brand',
      slug: 'test-brand',
    };

    const mockPromotions = [
      {
        id: 'active1',
        title: 'Currently Active',
        purchase_start_date: '2025-01-01T00:00:00Z',
        purchase_end_date: '2025-12-31T23:59:59Z', // Future date
        status: 'active',
        brand: mockBrand,
      },
      {
        id: 'expired1',
        title: 'Expired Promotion',
        purchase_start_date: '2024-01-01T00:00:00Z',
        purchase_end_date: '2024-12-31T23:59:59Z', // Past date
        status: 'active',
        brand: mockBrand,
      },
      {
        id: 'future1',
        title: 'Future Promotion',
        purchase_start_date: '2026-01-01T00:00:00Z', // Future start date
        purchase_end_date: '2026-12-31T23:59:59Z',
        status: 'active',
        brand: mockBrand,
      },
    ];

    mockSupabase.maybeSingle.mockResolvedValueOnce({
      data: mockBrand,
      error: null,
    });

    mockSupabase.order.mockResolvedValue({
      data: mockPromotions,
      error: null,
    });

    const result = await getBrandPageData(mockSupabase, 'test-brand');

    expect(result!.activePromotions).toHaveLength(1);
    expect(result!.activePromotions[0].id).toBe('active1');
    expect(result!.expiredPromotions).toHaveLength(1);
    expect(result!.expiredPromotions[0].id).toBe('expired1');
  });
});