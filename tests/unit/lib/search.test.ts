/**
 * Test Audit Update: 2025-07-28
 * Unit tests for search data layer functions
 * Tests product search, suggestions, and popular search terms functionality
 */

import { searchProducts, getSearchSuggestions, getPopularSearchTerms } from '@/lib/data/search';
import type { SearchFilters } from '@/lib/data/types';

// Mock the Supabase server client
const rangeLimitMock = jest.fn();
const mockSupabase = {
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  textSearch: jest.fn().mockReturnThis(),
  or: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  limit: rangeLimitMock,
  range: rangeLimitMock,
  single: jest.fn(),
};

jest.mock('@/lib/supabase/server', () => ({
  createServerSupabaseReadOnlyClient: jest.fn(() => mockSupabase),
}));

// Mock the cache module
jest.mock('@/lib/cache', () => ({
  createCachedFunction: jest.fn((fn) => fn),
  CACHE_DURATIONS: {
    SHORT: 300,
    MEDIUM: 900,
    EXTENDED: 3600,
  },
  CACHE_TAGS: {
    SEARCH: 'search',
    PRODUCTS: 'products',
  },
}));

describe('searchProducts', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should search products with basic query', async () => {
    const mockData = [
      {
        id: '1',
        name: 'Samsung Galaxy Phone',
        slug: 'samsung-galaxy-phone',
        description: 'Latest Samsung smartphone',
        status: 'active',
        brand: { id: 'samsung', name: 'Samsung UK', slug: 'samsung-uk' },
        category: { id: 'phones', name: 'Phones', slug: 'phones' },
        product_retailer_offers: [
          { id: 'offer1', price: 599, retailer: { id: 'ret1', name: 'Retailer 1' } }
        ],
      },
    ];

    mockSupabase.range.mockResolvedValue({
      data: mockData,
      error: null,
      count: 1,
    });

    const filters: SearchFilters = { query: 'samsung phone' };
    const result = await searchProducts(mockSupabase, filters, null, 20);

    expect(mockSupabase.from).toHaveBeenCalledWith('products');
    expect(mockSupabase.select).toHaveBeenCalledWith(
      expect.stringContaining('brand:brand_id'),
      { count: 'exact' }
    );
    expect(mockSupabase.textSearch).toHaveBeenCalledWith(
      'search_vector',
      'samsung phone',
      { type: 'websearch', config: 'english' }
    );
    expect(mockSupabase.eq).toHaveBeenCalledWith('status', 'active');
    expect(mockSupabase.limit).toHaveBeenCalledWith(21);

    expect(result.products).toHaveLength(1);
    expect(result.total).toBe(1);
    expect(result.filtersApplied).toEqual(filters);
  });

  it('should fallback to fuzzy search when full-text search fails', async () => {
    const mockData = [
      {
        id: '1',
        name: 'iPhone',
        slug: 'iphone',
        description: 'Apple smartphone',
        status: 'active',
        brand: { id: 'apple', name: 'Apple', slug: 'apple' },
      },
    ];

    // Mock textSearch to throw error, triggering fallback
    mockSupabase.textSearch.mockImplementation(() => {
      throw new Error('Full-text search failed');
    });
    
    mockSupabase.range.mockResolvedValue({
      data: mockData,
      error: null,
      count: 1,
    });

    const filters: SearchFilters = { query: 'iphone' };
    const result = await searchProducts(mockSupabase, filters);

    expect(mockSupabase.or).toHaveBeenCalledWith(
      'name.ilike.%iphone%,description.ilike.%iphone%,brand.name.ilike.%iphone%'
    );
    expect(result.products).toHaveLength(1);
  });

  it('should use ILIKE for short queries (less than 3 characters)', async () => {
    const mockData = [];
    mockSupabase.range.mockResolvedValue({
      data: mockData,
      error: null,
      count: 0,
    });

    const filters: SearchFilters = { query: 'tv' };
    await searchProducts(mockSupabase, filters);

    expect(mockSupabase.textSearch).not.toHaveBeenCalled();
    expect(mockSupabase.or).toHaveBeenCalledWith(
      'name.ilike.%tv%,description.ilike.%tv%,brand.name.ilike.%tv%'
    );
  });

  it('should apply brand filter correctly', async () => {
    mockSupabase.range.mockResolvedValue({
      data: [],
      error: null,
      count: 0,
    });

    const filters: SearchFilters = { 
      query: 'phone',
      brand: 'samsung-uk'
    };
    await searchProducts(mockSupabase, filters);

    expect(mockSupabase.eq).toHaveBeenCalledWith('brand.slug', 'samsung-uk');
    expect(mockSupabase.eq).toHaveBeenCalledWith('status', 'active');
  });

  it('should apply category filter correctly', async () => {
    mockSupabase.range.mockResolvedValue({
      data: [],
      error: null,
      count: 0,
    });

    const filters: SearchFilters = { 
      query: 'laptop',
      category: 'computers'
    };
    await searchProducts(mockSupabase, filters);

    expect(mockSupabase.eq).toHaveBeenCalledWith('category.slug', 'computers');
  });

  it('should apply sorting correctly', async () => {
    mockSupabase.range.mockResolvedValue({
      data: [],
      error: null,
      count: 0,
    });

    // Test newest sorting
    const filtersNewest: SearchFilters = { 
      query: 'test',
      sortBy: 'newest'
    };
    await searchProducts(mockSupabase, filtersNewest);
    expect(mockSupabase.order).toHaveBeenCalledWith('created_at', { ascending: false });

    jest.clearAllMocks();
    mockSupabase.range.mockResolvedValue({ data: [], error: null, count: 0 });

    // Test featured sorting
    const filtersFeatured: SearchFilters = { 
      query: 'test',
      sortBy: 'featured'
    };
    await searchProducts(mockSupabase, filtersFeatured);
    expect(mockSupabase.order).toHaveBeenCalledWith('is_featured', { ascending: false });
    expect(mockSupabase.order).toHaveBeenCalledWith('created_at', { ascending: false });
  });

  it('should handle pagination correctly', async () => {
    mockSupabase.range.mockResolvedValue({
      data: [],
      error: null,
      count: 50,
    });

    const filters: SearchFilters = { query: 'test' };
    await searchProducts(mockSupabase, filters, { createdAt: '2025-01-01T00:00:00Z', id: 'abc' }, 10);

    expect(mockSupabase.limit).toHaveBeenCalledWith(11);
  });

  it('should transform search results correctly', async () => {
    const mockData = [
      {
        id: '1',
        name: 'Test Product',
        slug: 'test-product',
        description: 'Test description',
        status: 'active',
        is_featured: true,
        is_sponsored: false,
        cashback_amount: 25,
        model_number: 'TEST-001',
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z',
        brand: {
          id: '1',
          name: 'Test Brand',
          slug: 'test-brand',
          logo_url: 'logo.jpg',
        },
        category: { id: 'cat1', name: 'Category 1', slug: 'category-1' },
        promotion: {
          id: 'promo1',
          title: 'Special Offer',
          max_cashback_amount: 100,
        },
        product_retailer_offers: [
          {
            id: 'offer1',
            price: 199,
            stock_status: 'in_stock',
            retailer: { id: 'ret1', name: 'Retailer 1' },
          },
        ],
        specifications: { price: '£199.00' },
      },
    ];

    mockSupabase.range.mockResolvedValue({
      data: mockData,
      error: null,
      count: 1,
    });

    const filters: SearchFilters = { query: 'test' };
    const result = await searchProducts(mockSupabase, filters);

    expect(result.products[0]).toMatchObject({
      id: '1',
      name: 'Test Product',
      slug: 'test-product',
      description: 'Test description',
      status: 'active',
      isFeatured: true,
      isSponsored: false,
      cashbackAmount: 25,
      modelNumber: 'TEST-001',
      minPrice: 199, // Should be calculated from retailer offers
      brand: {
        id: '1',
        name: 'Test Brand',
        slug: 'test-brand',
        logoUrl: 'logo.jpg',
      },
      category: { id: 'cat1', name: 'Category 1', slug: 'category-1' },
      promotion: {
        id: 'promo1',
        title: 'Special Offer',
        maxCashbackAmount: 100,
      },
    });

    expect(result.products[0].retailerOffers).toHaveLength(1);
    expect(result.products[0].retailerOffers[0]).toMatchObject({
      id: 'offer1',
      price: 199,
      stockStatus: 'in_stock',
      retailer: { id: 'ret1', name: 'Retailer 1' },
    });
  });

  it('should handle database errors', async () => {
    const mockError = new Error('Database connection failed');
    mockSupabase.range.mockResolvedValue({
      data: null,
      error: mockError,
      count: null,
    });

    const filters: SearchFilters = { query: 'test' };
    
    await expect(searchProducts(mockSupabase, filters)).rejects.toThrow('Search failed: Database connection failed');
  });

  it('should handle empty search query', async () => {
    mockSupabase.range.mockResolvedValue({
      data: [],
      error: null,
      count: 0,
    });

    const filters: SearchFilters = { query: '' };
    const result = await searchProducts(mockSupabase, filters);

    expect(mockSupabase.textSearch).not.toHaveBeenCalled();
    expect(mockSupabase.or).not.toHaveBeenCalled();
    expect(result.products).toEqual([]);
  });

  it('should calculate minPrice from specifications when no retailer offers', async () => {
    const mockData = [
      {
        id: '1',
        name: 'Test Product',
        slug: 'test-product',
        status: 'active',
        specifications: { price: '£329.00' },
        product_retailer_offers: [],
      },
    ];

    mockSupabase.range.mockResolvedValue({
      data: mockData,
      error: null,
      count: 1,
    });

    const filters: SearchFilters = { query: 'test' };
    const result = await searchProducts(mockSupabase, filters);

    expect(result.products[0].minPrice).toBe(329); // Parsed from specifications
  });
});

describe('getSearchSuggestions', () => {
  const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return product and brand suggestions', async () => {
    const mockProductSuggestions = [
      { name: 'Samsung Galaxy S24' },
      { name: 'Samsung Galaxy Tab' },
    ];
    const mockBrandSuggestions = [
      { name: 'Samsung UK' },
    ];

    mockSupabase.limit.mockResolvedValueOnce({
      data: mockProductSuggestions,
      error: null,
    }).mockResolvedValueOnce({
      data: mockBrandSuggestions,
      error: null,
    }).mockResolvedValue({
      data: [],
      error: null,
    });

    const result = await getSearchSuggestions(mockSupabase, 'samsung', 5);

    expect(mockSupabase.from).toHaveBeenCalledWith('products');
    expect(mockSupabase.from).toHaveBeenCalledWith('brands');
    expect(mockSupabase.ilike).toHaveBeenCalledWith('name', '%samsung%');
    expect(result).toEqual(['Samsung Galaxy S24', 'Samsung Galaxy Tab', 'Samsung UK']);
  });

  it('should include brand aliases in suggestions', async () => {
    // Mock the brand alias lookup
    mockSupabase.limit.mockResolvedValueOnce({
      data: [{ name: 'Samsung Phone' }],
      error: null,
    }).mockResolvedValueOnce({
      data: [{ name: 'Samsung UK' }],
      error: null,
    }).mockResolvedValue({
      data: [{ name: 'Samsung Electronics' }],
      error: null,
    });

    const result = await getSearchSuggestions(mockSupabase, 'samsung', 5);

    expect(result).toContain('Samsung Electronics'); // From brand alias mapping
  });

  it('should return empty array for short queries', async () => {
    const result = await getSearchSuggestions(mockSupabase, 'a', 5);
    expect(result).toEqual([]);
    expect(mockSupabase.from).not.toHaveBeenCalled();
  });

  it('should handle database errors gracefully', async () => {
    mockSupabase.limit.mockResolvedValue({
      data: null,
      error: new Error('Database error'),
    });

    const result = await getSearchSuggestions(mockSupabase, 'test', 5);
    expect(result).toEqual([]);
  });

  it('should deduplicate suggestions', async () => {
    const mockProductSuggestions = [
      { name: 'iPhone 15' },
      { name: 'iPhone 14' },
    ];
    const mockBrandSuggestions = [
      { name: 'iPhone 15' }, // Duplicate
      { name: 'Apple' },
    ];

    mockSupabase.limit.mockResolvedValueOnce({
      data: mockProductSuggestions,
      error: null,
    }).mockResolvedValueOnce({
      data: mockBrandSuggestions,
      error: null,
    }).mockResolvedValue({
      data: [],
      error: null,
    });

    const result = await getSearchSuggestions(mockSupabase, 'iphone', 5);

    expect(result).toEqual(['iPhone 15', 'iPhone 14', 'Apple']);
    expect(result).toHaveLength(3); // No duplicates
  });
});

describe('getPopularSearchTerms', () => {
  it('should return popular search terms', async () => {
    const result = await getPopularSearchTerms(5);
    
    expect(result).toHaveLength(5);
    expect(result).toContain('iPhone');
    expect(result).toContain('Samsung');
    expect(result).toContain('Laptop');
  });

  it('should respect limit parameter', async () => {
    const result = await getPopularSearchTerms(3);
    expect(result).toHaveLength(3);
  });

  it('should return default terms when no limit specified', async () => {
    const result = await getPopularSearchTerms();
    expect(result).toHaveLength(10);
  });
});