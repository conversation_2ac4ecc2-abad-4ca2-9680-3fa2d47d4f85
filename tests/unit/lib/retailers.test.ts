/**
 * Test Audit Update: 2025-07-28
 * Unit tests for retailers data layer functions
 * Tests retailer retrieval, filtering, and featured retailer functionality
 */

import { 
  getRetailers, 
  getRetailer, 
  getRetailerBySlug, 
  getFeaturedRetailers,
  getRetailerWithProducts 
} from '@/lib/data/retailers';
import type { RetailerFilters } from '@/lib/data/types';

// Mock the Supabase server client
const mockSupabase = {
  from: jest.fn().mockReturnThis(),
  select: jest.fn().mockReturnThis(),
  eq: jest.fn().mockReturnThis(),
  neq: jest.fn().mockReturnThis(),
  ilike: jest.fn().mockReturnThis(),
  in: jest.fn().mockReturnThis(),
  order: jest.fn().mockReturnThis(),
  range: jest.fn().mockReturnThis(),
  limit: jest.fn().mockReturnThis(),
  single: jest.fn(),
};

jest.mock('@/lib/supabase/server', () => ({
  createServerSupabaseReadOnlyClient: jest.fn(() => mockSupabase),
}));

// Mock the cache module
jest.mock('@/lib/cache', () => ({
  createCachedFunction: jest.fn((fn) => fn),
  CACHE_DURATIONS: {
    SHORT: 300,
    MEDIUM: 900,
    LONG: 3600,
  },
  CACHE_TAGS: {
    RETAILERS: 'retailers',
    PRODUCTS: 'products',
  },
}));

describe('getRetailers', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch retailers with pagination', async () => {
    const mockData = [
      {
        id: '1',
        name: 'Amazon UK',
        slug: 'amazon-uk',
        logo_url: 'amazon-logo.jpg',
        website_url: 'https://amazon.co.uk',
        status: 'active',
        featured: true,
        sponsored: false,
        claim_period: 30,
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z',
      },
      {
        id: '2',
        name: 'eBay UK',
        slug: 'ebay-uk',
        logo_url: 'ebay-logo.jpg',
        website_url: 'https://ebay.co.uk',
        status: 'active',
        featured: false,
        sponsored: true,
        claim_period: 21,
        created_at: '2025-01-01T00:00:00Z',
      },
    ];

    mockSupabase.range.mockResolvedValue({
      data: mockData,
      error: null,
      count: 2,
    });

    const result = await getRetailers(mockSupabase, {}, 1, 20);

    expect(mockSupabase.from).toHaveBeenCalledWith('retailers');
    expect(mockSupabase.select).toHaveBeenCalledWith('*', { count: 'exact' });
    expect(mockSupabase.order).toHaveBeenCalledWith('featured', { ascending: false });
    expect(mockSupabase.order).toHaveBeenCalledWith('sponsored', { ascending: false });
    expect(mockSupabase.order).toHaveBeenCalledWith('name', { ascending: true });
    expect(mockSupabase.range).toHaveBeenCalledWith(0, 19);

    expect(result.data).toHaveLength(2);
    expect(result.data[0]).toMatchObject({
      id: '1',
      name: 'Amazon UK',
      slug: 'amazon-uk',
      logoUrl: 'amazon-logo.jpg',
      websiteUrl: 'https://amazon.co.uk',
      status: 'active',
      featured: true,
      sponsored: false,
      claimPeriod: 30,
    });

    expect(result.pagination).toMatchObject({
      page: 1,
      pageSize: 20,
      total: 2,
      totalPages: 1,
      hasNext: false,
      hasPrev: false,
    });
  });

  it('should apply featured filter correctly', async () => {
    mockSupabase.range.mockResolvedValue({
      data: [],
      error: null,
      count: 0,
    });

    const filters: RetailerFilters = { featured: true };
    await getRetailers(mockSupabase, filters, 1, 20);

    expect(mockSupabase.eq).toHaveBeenCalledWith('featured', true);
  });

  it('should apply sponsored filter correctly', async () => {
    mockSupabase.range.mockResolvedValue({
      data: [],
      error: null,
      count: 0,
    });

    const filters: RetailerFilters = { sponsored: false };
    await getRetailers(mockSupabase, filters, 1, 20);

    expect(mockSupabase.eq).toHaveBeenCalledWith('sponsored', false);
  });

  it('should apply status filter correctly', async () => {
    mockSupabase.range.mockResolvedValue({
      data: [],
      error: null,
      count: 0,
    });

    const filters: RetailerFilters = { status: 'active' };
    await getRetailers(mockSupabase, filters, 1, 20);

    expect(mockSupabase.eq).toHaveBeenCalledWith('status', 'active');
  });

  it('should apply search filter correctly', async () => {
    mockSupabase.range.mockResolvedValue({
      data: [],
      error: null,
      count: 0,
    });

    const filters: RetailerFilters = { search: 'amazon' };
    await getRetailers(mockSupabase, filters, 1, 20);

    expect(mockSupabase.ilike).toHaveBeenCalledWith('name', '%amazon%');
  });

  it('should handle pagination correctly', async () => {
    mockSupabase.range.mockResolvedValue({
      data: [],
      error: null,
      count: 100,
    });

    const result = await getRetailers(mockSupabase, {}, 3, 10);

    expect(mockSupabase.range).toHaveBeenCalledWith(20, 29); // Page 3, size 10: from 20 to 29
    expect(result.pagination).toMatchObject({
      page: 3,
      pageSize: 10,
      total: 100,
      totalPages: 10,
      hasNext: true,
      hasPrev: true,
    });
  });

  it('should handle database errors', async () => {
    const mockError = new Error('Database connection failed');
    mockSupabase.range.mockResolvedValue({
      data: null,
      error: mockError,
      count: null,
    });

    await expect(getRetailers(mockSupabase, {}, 1, 20)).rejects.toThrow('Failed to fetch retailers: Database connection failed');
  });

  it('should use default values when parameters not provided', async () => {
    mockSupabase.range.mockResolvedValue({
      data: [],
      error: null,
      count: 0,
    });

    await getRetailers(mockSupabase);

    expect(mockSupabase.range).toHaveBeenCalledWith(0, 19); // Page 1, limit 20
  });

  it('should transform retailer data correctly', async () => {
    const mockData = [
      {
        id: '1',
        name: 'Test Retailer',
        slug: 'test-retailer',
        logo_url: 'test-logo.jpg',
        website_url: 'https://test.com',
        status: 'active',
        featured: true,
        sponsored: false,
        claim_period: 14,
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-02T00:00:00Z',
      },
    ];

    mockSupabase.range.mockResolvedValue({
      data: mockData,
      error: null,
      count: 1,
    });

    const result = await getRetailers(mockSupabase, {}, 1, 20);

    expect(result.data[0]).toMatchObject({
      id: '1',
      name: 'Test Retailer',
      slug: 'test-retailer',
      logoUrl: 'test-logo.jpg',
      websiteUrl: 'https://test.com',
      status: 'active',
      featured: true,
      sponsored: false,
      claimPeriod: 14,
      createdAt: '2025-01-01T00:00:00Z',
      updatedAt: '2025-01-02T00:00:00Z',
    });
  });

  it('should handle missing updated_at field', async () => {
    const mockData = [
      {
        id: '1',
        name: 'Test Retailer',
        slug: 'test-retailer',
        created_at: '2025-01-01T00:00:00Z',
        // No updated_at field
      },
    ];

    mockSupabase.range.mockResolvedValue({
      data: mockData,
      error: null,
      count: 1,
    });

    const result = await getRetailers(mockSupabase, {}, 1, 20);

    expect(result.data[0].updatedAt).toBe('2025-01-01T00:00:00Z'); // Should fallback to created_at
  });
});

describe('getRetailer', () => {
  const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch single retailer by ID', async () => {
    const mockData = {
      id: '1',
      name: 'Amazon UK',
      slug: 'amazon-uk',
      logo_url: 'amazon-logo.jpg',
      website_url: 'https://amazon.co.uk',
      status: 'active',
      featured: true,
      sponsored: false,
      claim_period: 30,
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-01T00:00:00Z',
    };

    mockSupabase.single.mockResolvedValue({
      data: mockData,
      error: null,
    });

    const result = await getRetailer(mockSupabase, '1');

    expect(mockSupabase.from).toHaveBeenCalledWith('retailers');
    expect(mockSupabase.select).toHaveBeenCalledWith('*');
    expect(mockSupabase.eq).toHaveBeenCalledWith('id', '1');
    expect(mockSupabase.single).toHaveBeenCalled();

    expect(result).toMatchObject({
      id: '1',
      name: 'Amazon UK',
      slug: 'amazon-uk',
      logoUrl: 'amazon-logo.jpg',
      websiteUrl: 'https://amazon.co.uk',
      status: 'active',
      featured: true,
      sponsored: false,
      claimPeriod: 30,
    });
  });

  it('should return null when retailer not found (PGRST116 error)', async () => {
    mockSupabase.single.mockResolvedValue({
      data: null,
      error: { code: 'PGRST116', message: 'Not found' },
    });

    const result = await getRetailer(mockSupabase, 'non-existent');

    expect(result).toBeNull();
  });

  it('should throw error for other database errors', async () => {
    const mockError = { code: 'PGRST001', message: 'Database error' };
    mockSupabase.single.mockResolvedValue({
      data: null,
      error: mockError,
    });

    await expect(getRetailer(mockSupabase, '1')).rejects.toThrow('Failed to fetch retailer: Database error');
  });
});

describe('getRetailerBySlug', () => {
  const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch retailer by slug', async () => {
    const mockData = {
      id: '1',
      name: 'Amazon UK',
      slug: 'amazon-uk',
      logo_url: 'amazon-logo.jpg',
      website_url: 'https://amazon.co.uk',
    };

    mockSupabase.single.mockResolvedValue({
      data: mockData,
      error: null,
    });

    const result = await getRetailerBySlug(mockSupabase, 'amazon-uk');

    expect(mockSupabase.from).toHaveBeenCalledWith('retailers');
    expect(mockSupabase.eq).toHaveBeenCalledWith('slug', 'amazon-uk');
    expect(mockSupabase.single).toHaveBeenCalled();

    expect(result).toMatchObject({
      id: '1',
      name: 'Amazon UK',
      slug: 'amazon-uk',
      logoUrl: 'amazon-logo.jpg',
      websiteUrl: 'https://amazon.co.uk',
    });
  });

  it('should return null when retailer not found', async () => {
    mockSupabase.single.mockResolvedValue({
      data: null,
      error: { code: 'PGRST116', message: 'Not found' },
    });

    const result = await getRetailerBySlug(mockSupabase, 'non-existent-slug');

    expect(result).toBeNull();
  });
});

describe('getFeaturedRetailers', () => {
  const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch featured retailers', async () => {
    const mockData = [
      {
        id: '1',
        name: 'Featured Retailer 1',
        slug: 'featured-retailer-1',
        featured: true,
        sponsored: false,
      },
      {
        id: '2',
        name: 'Featured Retailer 2',
        slug: 'featured-retailer-2',
        featured: true,
        sponsored: true,
      },
    ];

    mockSupabase.limit.mockResolvedValue({
      data: mockData,
      error: null,
    });

    const result = await getFeaturedRetailers(mockSupabase, 5);

    expect(mockSupabase.from).toHaveBeenCalledWith('retailers');
    expect(mockSupabase.eq).toHaveBeenCalledWith('featured', true);
    expect(mockSupabase.order).toHaveBeenCalledWith('sponsored', { ascending: false });
    expect(mockSupabase.order).toHaveBeenCalledWith('name', { ascending: true });
    expect(mockSupabase.limit).toHaveBeenCalledWith(5);

    expect(result).toHaveLength(2);
    expect(result[0].featured).toBe(true);
    expect(result[1].featured).toBe(true);
  });

  it('should use default limit when not specified', async () => {
    mockSupabase.limit.mockResolvedValue({
      data: [],
      error: null,
    });

    await getFeaturedRetailers(mockSupabase);

    expect(mockSupabase.limit).toHaveBeenCalledWith(10);
  });

  it('should handle database errors', async () => {
    const mockError = new Error('Database error');
    mockSupabase.limit.mockResolvedValue({
      data: null,
      error: mockError,
    });

    await expect(getFeaturedRetailers(mockSupabase)).rejects.toThrow('Failed to fetch featured retailers: Database error');
  });
});

describe('getRetailerWithProducts', () => {
  const mockSupabase = {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    single: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch retailer with featured products and offer count', async () => {
    const mockRetailer = {
      id: '1',
      name: 'Amazon UK',
      slug: 'amazon-uk',
      logo_url: 'amazon-logo.jpg',
      website_url: 'https://amazon.co.uk',
    };

    const mockOffers = [
      { product_id: 'prod1' },
      { product_id: 'prod2' },
    ];

    const mockProducts = [
      {
        id: 'prod1',
        name: 'Featured Product 1',
        slug: 'featured-product-1',
        description: 'Test product',
        is_featured: true,
        cashback_amount: 25,
        specifications: { price: '£199.99' },
        brand: { id: 'brand1', name: 'Brand 1' },
        category: { id: 'cat1', name: 'Category 1' },
        promotion: { id: 'promo1', title: 'Test Promotion' },
        retailer_offers: [
          {
            id: 'offer1',
            retailer_id: '1',
            retailer_name: 'Amazon UK',
            price: 199.99,
            stock_status: 'in_stock',
            url: 'https://amazon.co.uk/product',
            created_at: '2025-01-01T00:00:00Z',
          },
        ],
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z',
      },
    ];

    // Mock getRetailer call
    mockSupabase.single.mockResolvedValueOnce({
      data: mockRetailer,
      error: null,
    });

    // Mock offers query
    mockSupabase.select.mockReturnValueOnce(mockSupabase);
    mockSupabase.eq.mockReturnValueOnce({
      data: mockOffers,
      error: null,
    });

    // Mock products query
    mockSupabase.select.mockReturnValueOnce(mockSupabase);
    mockSupabase.eq.mockReturnValueOnce(mockSupabase);
    mockSupabase.in.mockReturnValueOnce(mockSupabase);
    mockSupabase.limit.mockResolvedValueOnce({
      data: mockProducts,
      error: null,
    });

    // Mock offers count query
    mockSupabase.select.mockReturnValueOnce(mockSupabase);
    mockSupabase.eq.mockResolvedValueOnce({
      count: 10,
      error: null,
    });

    const result = await getRetailerWithProducts(mockSupabase, '1');

    expect(result).toHaveProperty('retailer');
    expect(result).toHaveProperty('featuredProducts');
    expect(result).toHaveProperty('activeOffersCount');
    expect(result).toHaveProperty('productsCount');

    expect(result!.retailer.name).toBe('Amazon UK');
    expect(result!.featuredProducts).toHaveLength(1);
    expect(result!.featuredProducts[0].name).toBe('Featured Product 1');
    expect(result!.featuredProducts[0].minPrice).toBe(199.99);
    expect(result!.activeOffersCount).toBe(10);
    expect(result!.productsCount).toBe(1);
  });

  it('should return null when retailer not found', async () => {
    mockSupabase.single.mockResolvedValue({
      data: null,
      error: { code: 'PGRST116', message: 'Not found' },
    });

    const result = await getRetailerWithProducts(mockSupabase, 'non-existent');

    expect(result).toBeNull();
  });

  it('should handle no products available', async () => {
    const mockRetailer = {
      id: '1',
      name: 'Test Retailer',
      slug: 'test-retailer',
    };

    // Mock getRetailer call
    mockSupabase.single.mockResolvedValueOnce({
      data: mockRetailer,
      error: null,
    });

    // Mock empty offers query
    mockSupabase.select.mockReturnValueOnce(mockSupabase);
    mockSupabase.eq.mockReturnValueOnce({
      data: [],
      error: null,
    });

    // Mock offers count query
    mockSupabase.select.mockReturnValueOnce(mockSupabase);
    mockSupabase.eq.mockResolvedValueOnce({
      count: 0,
      error: null,
    });

    const result = await getRetailerWithProducts(mockSupabase, '1');

    expect(result!.retailer.name).toBe('Test Retailer');
    expect(result!.featuredProducts).toEqual([]);
    expect(result!.activeOffersCount).toBe(0);
    expect(result!.productsCount).toBe(0);
  });

  it('should calculate minPrice from specifications when no retailer offers', async () => {
    const mockRetailer = {
      id: '1',
      name: 'Test Retailer',
      slug: 'test-retailer',
    };

    const mockOffers = [{ product_id: 'prod1' }];

    const mockProducts = [
      {
        id: 'prod1',
        name: 'Test Product',
        slug: 'test-product',
        specifications: { price: '£299.99' },
        retailer_offers: [], // No retailer offers
        created_at: '2025-01-01T00:00:00Z',
      },
    ];

    mockSupabase.single.mockResolvedValueOnce({
      data: mockRetailer,
      error: null,
    });

    mockSupabase.select.mockReturnValueOnce(mockSupabase);
    mockSupabase.eq.mockReturnValueOnce({
      data: mockOffers,
      error: null,
    });

    mockSupabase.select.mockReturnValueOnce(mockSupabase);
    mockSupabase.eq.mockReturnValueOnce(mockSupabase);
    mockSupabase.in.mockReturnValueOnce(mockSupabase);
    mockSupabase.limit.mockResolvedValueOnce({
      data: mockProducts,
      error: null,
    });

    mockSupabase.select.mockReturnValueOnce(mockSupabase);
    mockSupabase.eq.mockResolvedValueOnce({
      count: 1,
      error: null,
    });

    const result = await getRetailerWithProducts(mockSupabase, '1');

    expect(result!.featuredProducts[0].minPrice).toBe(299.99);
  });

  it('should handle offers query errors', async () => {
    const mockRetailer = {
      id: '1',
      name: 'Test Retailer',
      slug: 'test-retailer',
    };

    mockSupabase.single.mockResolvedValueOnce({
      data: mockRetailer,
      error: null,
    });

    mockSupabase.select.mockReturnValueOnce(mockSupabase);
    mockSupabase.eq.mockReturnValueOnce({
      data: null,
      error: new Error('Offers query failed'),
    });

    await expect(getRetailerWithProducts(mockSupabase, '1')).rejects.toThrow('Failed to fetch retailer offers: Offers query failed');
  });

  it('should handle products query errors', async () => {
    const mockRetailer = {
      id: '1',
      name: 'Test Retailer',
      slug: 'test-retailer',
    };

    const mockOffers = [{ product_id: 'prod1' }];

    mockSupabase.single.mockResolvedValueOnce({
      data: mockRetailer,
      error: null,
    });

    mockSupabase.select.mockReturnValueOnce(mockSupabase);
    mockSupabase.eq.mockReturnValueOnce({
      data: mockOffers,
      error: null,
    });

    mockSupabase.select.mockReturnValueOnce(mockSupabase);
    mockSupabase.eq.mockReturnValueOnce(mockSupabase);
    mockSupabase.in.mockReturnValueOnce(mockSupabase);
    mockSupabase.limit.mockResolvedValueOnce({
      data: null,
      error: new Error('Products query failed'),
    });

    await expect(getRetailerWithProducts(mockSupabase, '1')).rejects.toThrow('Failed to fetch retailer products: Products query failed');
  });

  it('should handle offers count query errors', async () => {
    const mockRetailer = {
      id: '1',
      name: 'Test Retailer',
      slug: 'test-retailer',
    };

    mockSupabase.single.mockResolvedValueOnce({
      data: mockRetailer,
      error: null,
    });

    mockSupabase.select.mockReturnValueOnce(mockSupabase);
    mockSupabase.eq.mockReturnValueOnce({
      data: [],
      error: null,
    });

    mockSupabase.select.mockReturnValueOnce(mockSupabase);
    mockSupabase.eq.mockResolvedValueOnce({
      count: null,
      error: new Error('Count query failed'),
    });

    await expect(getRetailerWithProducts(mockSupabase, '1')).rejects.toThrow('Failed to count retailer offers: Count query failed');
  });
});