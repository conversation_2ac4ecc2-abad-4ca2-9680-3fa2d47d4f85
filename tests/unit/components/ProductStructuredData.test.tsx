/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render } from '@testing-library/react';
import { TransformedProduct, TransformedRetailerOffer } from '@/lib/data/types';

jest.mock('@/config/domains', () => ({
  SITE_URL: 'https://example.com'
}));

import { ProductStructuredData } from '@/components/seo/StructuredData';

describe('ProductStructuredData', () => {
  it('renders without throwing when offer lacks price', () => {
    const product: TransformedProduct = {
      id: 'prod-1',
      name: 'Test Product',
      slug: 'test-product',
      description: '',
      images: [],
      specifications: null,
      status: 'active',
      isFeatured: false,
      isSponsored: false,
      cashbackAmount: null,
      minPrice: null,
      modelNumber: '',
      createdAt: '2024-01-01',
      updatedAt: '2024-01-01',
      brand: null,
      category: null,
      promotion: null,
      retailerOffers: []
    };

    const offers: TransformedRetailerOffer[] = [
      {
        id: 'offer-1',
        retailer: { id: 'ret-1', name: 'Retailer', logoUrl: null, websiteUrl: null },
        price: null,
        stockStatus: 'in_stock',
        url: 'https://example.com',
        createdAt: '2024-01-01'
      }
    ];

    expect(() => render(<ProductStructuredData product={product} retailerOffers={offers} />)).not.toThrow();
  });
});
