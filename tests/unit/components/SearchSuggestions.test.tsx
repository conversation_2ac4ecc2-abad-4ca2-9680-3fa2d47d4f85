/**
 * Test Audit Update: 2025-07-28
 * Unit tests for SearchSuggestions component
 * Tests search suggestion display and interaction functionality
 */

/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useRouter } from 'next/navigation';
import { SearchSuggestions } from '@/components/search/SearchSuggestions';
import { logger } from '@/lib/utils/logger';

// Mock dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

jest.mock('@/lib/utils/logger', () => ({
  logger: {
    info: jest.fn(),
  },
}));

jest.mock('@/hooks/useDebounce', () => ({
  useDebounce: jest.fn((value) => value),
}));

// Mock fetch globally
global.fetch = jest.fn();

// Mock scrollIntoView for DOM elements
Object.defineProperty(HTMLElement.prototype, 'scrollIntoView', {
  value: jest.fn(),
  writable: true,
});

const mockPush = jest.fn();
(useRouter as jest.Mock).mockReturnValue({
  push: mockPush,
});

const mockApiResponse = {
  categories: [
    { name: 'Electronics', slug: 'electronics', id: 'cat-1' },
    { name: 'Home & Garden', slug: 'home-garden', id: 'cat-2' },
  ],
  brands: [
    { name: 'Samsung', slug: 'samsung', id: 'brand-1' },
    { name: 'Sony', slug: 'sony', id: 'brand-2' },
  ],
  products: [
    { name: 'Samsung Galaxy S23', slug: 'samsung-galaxy-s23', id: 'prod-1' },
    { name: 'Sony WH-1000XM4', slug: 'sony-wh-1000xm4', id: 'prod-2' },
  ],
};

describe('SearchSuggestions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => mockApiResponse,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders suggestions after debounced query', async () => {
    render(<SearchSuggestions query="samsung" />);

    await waitFor(() => {
      expect(screen.getByText('Samsung')).toBeInTheDocument();
      expect(screen.getByText('Samsung Galaxy S23')).toBeInTheDocument();
    });
  });

  it('does not render suggestions for queries shorter than 3 characters', () => {
    render(<SearchSuggestions query="sa" />);
    
    expect(screen.queryByText('Samsung')).not.toBeInTheDocument();
  });

  it('navigates to brand search when brand suggestion is clicked', async () => {
    render(<SearchSuggestions query="samsung" />);

    await waitFor(() => {
      expect(screen.getByText('Samsung')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Samsung'));

    expect(mockPush).toHaveBeenCalledWith('/search?brand=samsung');
  });

  it('navigates to category search when category suggestion is clicked', async () => {
    render(<SearchSuggestions query="electronics" />);

    await waitFor(() => {
      expect(screen.getByText('Electronics')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Electronics'));

    expect(mockPush).toHaveBeenCalledWith('/search?category=electronics');
  });

  it('navigates to product page when product suggestion is clicked', async () => {
    render(<SearchSuggestions query="samsung" />);

    await waitFor(() => {
      expect(screen.getByText('Samsung Galaxy S23')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Samsung Galaxy S23'));

    expect(mockPush).toHaveBeenCalledWith('/products/samsung-galaxy-s23');
  });

  it('tracks analytics when suggestion is clicked', async () => {
    render(<SearchSuggestions query="samsung" />);

    await waitFor(() => {
      expect(screen.getByText('Samsung')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Samsung'));

    expect(logger.info).toHaveBeenCalledWith('Search suggestion selected', {
      query: 'samsung',
      suggestionType: 'brand',
      suggestionId: 'brand-1',
      suggestionName: 'Samsung'
    });

    expect(logger.info).toHaveBeenCalledWith('Search suggestion navigation', {
      query: 'samsung',
      suggestionType: 'brand',
      suggestionId: 'brand-1',
      destinationUrl: '/search?brand=samsung'
    });
  });

  it('handles keyboard navigation correctly', async () => {
    render(<SearchSuggestions query="samsung" />);

    await waitFor(() => {
      expect(screen.getByText('Samsung')).toBeInTheDocument();
    });

    // Simulate Arrow Down key to select first suggestion
    fireEvent.keyDown(document, { key: 'ArrowDown' });
    
    // Wait for state update
    await waitFor(() => {
      // Simulate Enter key to select the highlighted suggestion
      fireEvent.keyDown(document, { key: 'Enter' });
    });

    expect(mockPush).toHaveBeenCalled();
  });

  it('calls onSelect callback for backward compatibility', async () => {
    const mockOnSelect = jest.fn();
    render(<SearchSuggestions query="samsung" onSelect={mockOnSelect} />);

    await waitFor(() => {
      expect(screen.getByText('Samsung')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Samsung'));

    // onSelect is now commented out to prevent conflicts with direct navigation
    // expect(mockOnSelect).toHaveBeenCalledWith('Samsung');
  });

  it('displays loading state while fetching suggestions', () => {
    (fetch as jest.Mock).mockReturnValue(new Promise(() => {})); // Never resolves
    
    render(<SearchSuggestions query="samsung" />);

    expect(screen.getByText('Loading suggestions...')).toBeInTheDocument();
  });

  it('handles API errors gracefully', async () => {
    (fetch as jest.Mock).mockRejectedValue(new Error('API Error'));
    
    render(<SearchSuggestions query="samsung" />);

    await waitFor(() => {
      expect(screen.queryByText('Samsung')).not.toBeInTheDocument();
    });
  });

  it('displays no suggestions message when API returns empty results', async () => {
    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({ categories: [], brands: [], products: [] }),
    });

    render(<SearchSuggestions query="xyz" />);

    await waitFor(() => {
      expect(screen.getByText('No suggestions found for "xyz"')).toBeInTheDocument();
    });
  });

  it('limits suggestions to 6 items', async () => {
    const largeResponse = {
      categories: Array.from({ length: 10 }, (_, i) => ({ 
        name: `Category ${i}`, 
        slug: `category-${i}`, 
        id: `cat-${i}` 
      })),
      brands: [],
      products: [],
    };

    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => largeResponse,
    });

    render(<SearchSuggestions query="category" />);

    await waitFor(() => {
      const suggestions = screen.getAllByText(/Category/);
      expect(suggestions.length).toBe(6);
    });
  });

  it('handles special characters in suggestion names', async () => {
    const specialResponse = {
      categories: [],
      brands: [{ name: 'Brand & Co.', slug: 'brand-co', id: 'brand-special' }],
      products: [],
    };

    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => specialResponse,
    });

    render(<SearchSuggestions query="brand" />);

    await waitFor(() => {
      expect(screen.getByText('Brand & Co.')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByText('Brand & Co.'));

    expect(mockPush).toHaveBeenCalledWith('/search?brand=brand-co');
  });

  it('auto-hides suggestions when no results after 500ms', async () => {
    const mockOnClose = jest.fn();
    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({ categories: [], brands: [], products: [] }),
    });

    render(<SearchSuggestions query="nonexistent" onClose={mockOnClose} />);

    await waitFor(() => {
      expect(screen.getByText('No suggestions found for "nonexistent"')).toBeInTheDocument();
    });

    // Wait for auto-hide timeout
    await waitFor(() => {
      expect(mockOnClose).toHaveBeenCalled();
    }, { timeout: 750 });
  });

  it('submits form when Enter is pressed with no suggestions', async () => {
    const mockOnSubmitSearch = jest.fn();
    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({ categories: [], brands: [], products: [] }),
    });

    render(<SearchSuggestions query="nosuggest" onSubmitSearch={mockOnSubmitSearch} />);

    await waitFor(() => {
      expect(screen.getByText('No suggestions found for "nosuggest"')).toBeInTheDocument();
    });

    fireEvent.keyDown(document, { key: 'Enter' });

    expect(mockOnSubmitSearch).toHaveBeenCalledWith('nosuggest');
  });

  it('closes suggestions when Escape is pressed', async () => {
    const mockOnClose = jest.fn();
    render(<SearchSuggestions query="samsung" onClose={mockOnClose} />);

    await waitFor(() => {
      expect(screen.getByText('Samsung')).toBeInTheDocument();
    });

    fireEvent.keyDown(document, { key: 'Escape' });

    expect(mockOnClose).toHaveBeenCalled();
  });

  it('shows helper text for no results state', async () => {
    (fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => ({ categories: [], brands: [], products: [] }),
    });

    render(<SearchSuggestions query="noresults" />);

    await waitFor(() => {
      expect(screen.getByText('No suggestions found for "noresults"')).toBeInTheDocument();
      expect(screen.getByText('Press Enter to search')).toBeInTheDocument();
    });
  });
});