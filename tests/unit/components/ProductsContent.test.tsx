/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import ProductsContent from '@/app/products/components/ProductsContent';
import { TransformedProduct } from '@/lib/data/types';

// Helper function to generate mock products
const generateMockProducts = (count: number, startId = 1): TransformedProduct[] => {
  return Array.from({ length: count }, (_, i) => ({
    id: (startId + i).toString(),
    slug: `product-${startId + i}`,
    name: `Product ${startId + i}`,
    description: `Description ${startId + i}`,
    images: [],
    status: 'active' as const,
    isFeatured: false,
    isSponsored: false,
    cashbackAmount: 0,
    minPrice: (i + 1) * 10,
    maxPrice: (i + 1) * 15,
    rating: 4.5,
    reviewCount: 10,
    categories: [],
    tags: [],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    modelNumber: `MODEL-${startId + i}`,
    brand: null,
    category: null,
    promotion: null,
    retailerOffers: [],
  }));
};

// Mock data for products pages
const mockProductsPage1 = generateMockProducts(20, 1);
const mockProductsPage2 = generateMockProducts(20, 21);
const mockProductsPage3 = generateMockProducts(16, 41); // Last page with 16 items

// Mock all products for testing total count
const allMockProducts = [...mockProductsPage1, ...mockProductsPage2, ...mockProductsPage3];

describe('ProductsContent', () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

  function renderComponent(initialPage = 1, hasMore = true) {
    const initialData = {
      data: initialPage === 1 ? mockProductsPage1 : initialPage === 2 ? mockProductsPage2 : mockProductsPage3,
      error: null,
      pagination: { 
        page: initialPage, 
        pageSize: 20,
        total: allMockProducts.length, 
        totalPages: 3, 
        hasNext: hasMore,
        hasPrev: initialPage > 1
      } 
    };
    
    render(
      <QueryClientProvider client={queryClient}>
        <ProductsContent 
          initialData={initialData} 
          filterOptions={{ brands: [], promotions: [] }} 
          initialPage={initialPage}
        />
      </QueryClientProvider>
    );
    
    return { initialData };
  }

  test('renders products correctly', () => {
    renderComponent();
    
    // Should render some products
    expect(screen.getByText('Unknown Brand - Product 1')).toBeInTheDocument();
    expect(screen.getByText('Unknown Brand - Product 2')).toBeInTheDocument();
  });

  test('renders pagination controls', () => {
    renderComponent();
    
    // Should show pagination controls
    expect(screen.getByRole('button', { name: /page 1/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /page 2/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /page 3/i })).toBeInTheDocument();
  });

  test('displays pagination info', () => {
    renderComponent();
    
    // Should show pagination info - using getAllByText and checking the first one
    const paginationElements = screen.getAllByText((content, element) => {
      return element?.textContent?.includes('Showing 1 to 20 of 56 results') || false;
    });
    expect(paginationElements[0]).toBeInTheDocument();
  });

  test('handles error state', () => {
    const initialData = {
      data: null,
      error: 'Failed to load products',
      pagination: null
    };
    
    render(
      <QueryClientProvider client={queryClient}>
        <ProductsContent 
          initialData={initialData} 
          filterOptions={{ brands: [], promotions: [] }} 
        />
      </QueryClientProvider>
    );
    
    expect(screen.getByText('Error loading products: Failed to load products')).toBeInTheDocument();
  });

  test('handles empty state', () => {
    const initialData = {
      data: [],
      error: null,
      pagination: null
    };
    
    render(
      <QueryClientProvider client={queryClient}>
        <ProductsContent 
          initialData={initialData} 
          filterOptions={{ brands: [], promotions: [] }} 
        />
      </QueryClientProvider>
    );
    
    expect(screen.getByText('No products found')).toBeInTheDocument();
  });
});