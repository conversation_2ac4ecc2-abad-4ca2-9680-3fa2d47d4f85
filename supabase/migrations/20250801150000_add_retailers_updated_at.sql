-- Add updated_at column to retailers table for better sitemap lastmod accuracy
-- Migration: 20250801150000_add_retailers_updated_at.sql

-- Add updated_at column with default current timestamp
ALTER TABLE retailers 
ADD COLUMN updated_at timestamp with time zone DEFAULT CURRENT_TIMESTAMP;

-- Create extension for automatic updated_at trigger if not exists
CREATE EXTENSION IF NOT EXISTS moddatetime SCHEMA extensions;

-- Create trigger to automatically update the updated_at column
CREATE TRIGGER handle_updated_at 
  BEFORE UPDATE ON retailers
  FOR EACH ROW 
  EXECUTE PROCEDURE moddatetime(updated_at);

-- Initialize updated_at for existing rows with created_at value
UPDATE retailers 
SET updated_at = created_at 
WHERE updated_at IS NULL;

-- Add comment for documentation
COMMENT ON COLUMN retailers.updated_at IS 'Automatically updated timestamp when record is modified, used for sitemap lastmod accuracy';