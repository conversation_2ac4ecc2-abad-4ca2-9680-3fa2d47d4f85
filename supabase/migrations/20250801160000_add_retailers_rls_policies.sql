-- Add Row Level Security policies for retailers table
-- Migration: 20250801160000_add_retailers_rls_policies.sql

-- Enable RLS on retailers table
ALTER TABLE retailers ENABLE ROW LEVEL SECURITY;

-- Policy 1: Allow public read access to active retailers (status = 'active')
-- This is the main policy for public-facing features like sitemaps, product listings, etc.
CREATE POLICY "Public read access to active retailers" ON retailers
    FOR SELECT 
    USING (status = 'active');

-- Policy 2: Allow unrestricted read access for service role
-- This allows backend operations and admin functions to access all retailers regardless of status
CREATE POLICY "Service role unrestricted access" ON retailers
    FOR ALL 
    USING (auth.jwt() ->> 'role' = 'service_role');

-- Policy 3: Allow unrestricted read access for authenticated users with admin role
-- This allows admin users to manage retailers including inactive ones
CREATE POLICY "Admin unrestricted access" ON retailers
    FOR ALL 
    USING (
        auth.jwt() ->> 'role' = 'authenticated' 
        AND auth.jwt() ->> 'user_role' = 'admin'
    );

-- Add comments for documentation
COMMENT ON POLICY "Public read access to active retailers" ON retailers IS 
    'Allows public read access only to retailers with active status, improving sitemap and SEO performance';

COMMENT ON POLICY "Service role unrestricted access" ON retailers IS 
    'Service role bypass for backend operations and data management';

COMMENT ON POLICY "Admin unrestricted access" ON retailers IS 
    'Admin users can access and modify all retailers regardless of status';

-- Additional policies for other tables that reference retailers can be added here
-- For example, if product_retailer_offers should only show offers from active retailers:

-- Enable RLS on product_retailer_offers table
ALTER TABLE product_retailer_offers ENABLE ROW LEVEL SECURITY;

-- Policy: Only show offers from active retailers
CREATE POLICY "Show offers from active retailers only" ON product_retailer_offers
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM retailers 
            WHERE retailers.id = product_retailer_offers.retailer_id 
            AND retailers.status = 'active'
        )
    );

-- Service role bypass for product_retailer_offers
CREATE POLICY "Service role unrestricted offers access" ON product_retailer_offers
    FOR ALL 
    USING (auth.jwt() ->> 'role' = 'service_role');

-- Admin unrestricted access for product_retailer_offers
CREATE POLICY "Admin unrestricted offers access" ON product_retailer_offers
    FOR ALL 
    USING (
        auth.jwt() ->> 'role' = 'authenticated' 
        AND auth.jwt() ->> 'user_role' = 'admin'
    );

-- Add comments for product_retailer_offers policies
COMMENT ON POLICY "Show offers from active retailers only" ON product_retailer_offers IS 
    'Ensures only offers from active retailers are visible to public users';

COMMENT ON POLICY "Service role unrestricted offers access" ON product_retailer_offers IS 
    'Service role bypass for backend operations on retailer offers';

COMMENT ON POLICY "Admin unrestricted offers access" ON product_retailer_offers IS 
    'Admin users can access all retailer offers regardless of retailer status';