# Deprecation Banner Component

The `DeprecationBanner` component provides a configurable, user-dismissible banner for announcing service deprecations and migrations.

## Features

- **Environment-controlled**: Show/hide via environment variables
- **User-dismissible**: Users can dismiss and banner won't show again (localStorage)
- **Accessible**: ARIA labels and proper semantic HTML
- **Responsive**: Works on all device sizes
- **Customizable**: Support for custom messages and external links

## Usage

### Environment Variables

Configure the banner via environment variables:

```bash
# Show/hide the banner
NEXT_PUBLIC_SHOW_DEPRECATION_BANNER=true

# Service being deprecated
NEXT_PUBLIC_DEPRECATION_SERVICE=CloudFront

# Deprecation date (ISO format)
NEXT_PUBLIC_DEPRECATION_DATE=2025-12-31

# Optional: URL for more information
NEXT_PUBLIC_DEPRECATION_INFO_URL=https://docs.example.com/migration-guide
```

### Component Integration

The banner is automatically included in the root layout (`src/app/layout.tsx`):

```tsx
<DeprecationBanner
  service={env.NEXT_PUBLIC_DEPRECATION_SERVICE}
  deprecationDate={env.NEXT_PUBLIC_DEPRECATION_DATE}
  infoUrl={env.NEXT_PUBLIC_DEPRECATION_INFO_URL || undefined}
  show={env.NEXT_PUBLIC_SHOW_DEPRECATION_BANNER}
/>
```

### Manual Usage

You can also use the component directly:

```tsx
import DeprecationBanner from '@/components/layout/DeprecationBanner';

<DeprecationBanner
  service="CloudFront"
  deprecationDate="2025-12-31"
  infoUrl="https://docs.example.com/migration"
  show={true}
  customMessage="Custom deprecation message here"
/>
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `service` | `string` | Yes | Name of the service being deprecated |
| `deprecationDate` | `string` | Yes | ISO date string for deprecation |
| `infoUrl` | `string` | No | URL for more information |
| `show` | `boolean` | No | Whether to show the banner (default: true) |
| `customMessage` | `string` | No | Override the default message |

## Styling

The banner uses Tailwind CSS classes with an amber color scheme to indicate caution:

- Background: `bg-amber-50`
- Border: `border-l-4 border-amber-400`
- Text colors: `text-amber-700`, `text-amber-800`
- Icon: Amber warning triangle

## Accessibility Features

- **ARIA role**: `role="alert"` for screen readers
- **ARIA labels**: Proper labeling for dismiss button
- **Focus management**: Keyboard navigation support
- **Color contrast**: Meets WCAG guidelines

## Browser Compatibility

- Uses `localStorage` for dismissal tracking
- Gracefully handles SSR (won't show until client-side hydration)
- Compatible with all modern browsers

## Examples

### CloudFront Deprecation

```bash
NEXT_PUBLIC_SHOW_DEPRECATION_BANNER=true
NEXT_PUBLIC_DEPRECATION_SERVICE=CloudFront
NEXT_PUBLIC_DEPRECATION_DATE=2025-12-31
NEXT_PUBLIC_DEPRECATION_INFO_URL=https://aws.amazon.com/cloudfront/migration
```

### Cloudflare Deprecation

```bash
NEXT_PUBLIC_SHOW_DEPRECATION_BANNER=true
NEXT_PUBLIC_DEPRECATION_SERVICE=Cloudflare
NEXT_PUBLIC_DEPRECATION_DATE=2025-06-30
NEXT_PUBLIC_DEPRECATION_INFO_URL=https://developers.cloudflare.com/migration
```

### Custom Message

```tsx
<DeprecationBanner
  service="Legacy API"
  deprecationDate="2025-03-31"
  show={true}
  customMessage="Our Legacy API will be discontinued on March 31, 2025. Please migrate to API v2 before this date to avoid service interruption."
/>
```

## Implementation Notes

1. **Dismissal Persistence**: Uses `localStorage` with key pattern `deprecation-banner-{service}-dismissed`
2. **Date Formatting**: Automatically formats dates to `en-GB` locale (e.g., "31 December 2025")
3. **Client-Side Only**: Banner only renders after client-side hydration to avoid SSR issues
4. **Performance**: Minimal impact with conditional rendering and localStorage caching

## Testing

To test the banner in development:

1. Set environment variables in `.env.local`:
   ```bash
   NEXT_PUBLIC_SHOW_DEPRECATION_BANNER=true
   ```

2. Clear localStorage to reset dismissal:
   ```javascript
   // In browser console
   localStorage.removeItem('deprecation-banner-cloudfront-dismissed');
   ```

3. Refresh the page to see the banner