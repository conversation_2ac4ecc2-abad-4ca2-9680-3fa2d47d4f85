# Security Guard-Rails Guide

*Complete guide to understanding and working with the security guard-rail system. Last updated: July 21, 2025*

## 🛡️ Overview

The Cashback Deals v2 application includes built-in security guard-rails that prevent unsafe configurations from being deployed to production. This system protects against common security vulnerabilities like disabled authentication, missing secrets, and test-only bypass flags in production environments.

## 🔍 How Security Guard-Rails Work

### **Guard-Rail Implementation**

The security system is implemented in `/src/lib/env-guard.ts` and automatically runs when the application starts:

```typescript
// Simplified guard-rail logic
const isTestEnv = process.env.NODE_ENV === 'test' || process.env.CI === 'true'
const isProduction = process.env.NODE_ENV === 'production'

if (isProduction && !isTestEnv) {
  // Check for unsafe environment variables
  // Throw security violation if found
}
```

### **When Guard-Rails Activate**

The security guard-rails **ONLY** activate when:
- `NODE_ENV=production` (automatic during `npm run build`)
- `CI≠true` (not in CI/test environment)

**Guard-rails are BYPASSED when:**
- `NODE_ENV=development` (during `npm run dev`)
- `NODE_ENV=test` 
- `CI=true`

## 🚨 Security Violations

### **Unsafe Environment Variables**

The following environment variables are **forbidden** in production:

#### **Authentication Bypass Flags**
```bash
ENABLE_SEARCH_AUTH=false          # ❌ Disables search authentication
ENABLE_HMAC_AUTH=false            # ❌ Disables HMAC authentication
ENABLE_RATE_LIMITING=false        # ❌ Disables API rate limiting
TEST_MODE_BYPASS_AUTH=true        # ❌ Enables authentication bypass
TEST_MODE_BYPASS_CORS=true        # ❌ Disables CORS protection
BYPASS_IP_ALLOWLIST=true          # ❌ Bypasses IP restrictions
```

#### **Security Bypass Flags**
```bash
SKIP_ENV_VALIDATION=true          # ❌ Skips environment validation
DISABLE_HMAC_VALIDATE=true        # ❌ Disables HMAC validation
DISABLE_SECURITY_HEADERS=true     # ❌ Removes security headers
```

#### **IP Allowlist Violations**
```bash
ENABLE_IP_ALLOWLIST=false         # ❌ Only unsafe in production
# This is allowed in development but blocked in production
```

### **Security Violation Examples**

When a security violation occurs, you'll see:

```bash
🚨 SECURITY VIOLATION: Test-only bypass flags detected in production environment!

The following unsafe environment variables are set:
  • ENABLE_SEARCH_AUTH=false is not allowed in production (NODE_ENV=production)
  • ENABLE_HMAC_AUTH=false is not allowed in production (NODE_ENV=production)
  • TEST_MODE_BYPASS_AUTH=true is not allowed in production (NODE_ENV=production)

These flags are only allowed when NODE_ENV=test or CI=true.
Please check your deployment configuration and environment variables.

For security reasons, the application will not start with these settings.
```

## 🔧 Working with Guard-Rails

### **Development Scenarios**

#### **Scenario 1: Daily Development (Recommended)**
```bash
# Use development mode - no guard-rails
npm run dev

# Environment: NODE_ENV=development automatically
# Guard-rails: BYPASSED
# Authentication: Optional, controlled by env vars
# Best for: Daily coding work
```

#### **Scenario 2: Production Build Testing**
```bash
# Option A: Production-safe configuration
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
npm run build && npm run start

# Option B: Test environment bypass
NODE_ENV=test npm run build && npm run start

# Option C: CI environment bypass
CI=true npm run build && npm run start
```

#### **Scenario 3: Authentication-Free Testing**
```bash
# Best approach: Use test environment
NODE_ENV=test npm run build && npm run start

# This bypasses guard-rails and allows:
ENABLE_SEARCH_AUTH=false
ENABLE_HMAC_AUTH=false
ENABLE_CAPTCHA=false
```

### **Environment-Specific Strategies**

#### **Local Development (.env.local)**

**Strategy 1: Production-Safe (Recommended)**
```bash
# Allows successful production builds
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
ENABLE_CAPTCHA=false  # Still good DX

# Commands:
npm run build        # ✅ Success
npm run dev          # ✅ Success, auth enabled but manageable
```

**Strategy 2: Development-Optimized**
```bash
# Optimized for development, requires test builds
ENABLE_SEARCH_AUTH=false
ENABLE_HMAC_AUTH=false
ENABLE_CAPTCHA=false

# Commands:
npm run build                    # ❌ Security violation
NODE_ENV=test npm run build     # ✅ Success
npm run dev                     # ✅ Success
```

#### **CI/CD Environments**

**GitHub Actions (Automatic)**
```yaml
# CI environments automatically bypass guard-rails
env:
  NODE_ENV: test
  CI: true
  
# These settings are allowed in CI:
ENABLE_SEARCH_AUTH: false
ENABLE_HMAC_AUTH: false
TEST_MODE_BYPASS_AUTH: true
```

**AWS Amplify (Production)**
```bash
# Production deployment requires secure configuration
NODE_ENV=production  # Automatically set by Amplify

# Required settings:
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
ENABLE_CAPTCHA=true
# All security features must be enabled
```

## 🎯 Bypass Methods

### **Method 1: Test Environment (Recommended)**
```bash
# Single command
NODE_ENV=test npm run build && npm run start

# Or set environment variable
export NODE_ENV=test
npm run build
npm run start
```

### **Method 2: CI Environment Flag**
```bash
# Single command
CI=true npm run build && npm run start

# Or set environment variable
export CI=true
npm run build
npm run start
```

### **Method 3: Development Mode (No Build)**
```bash
# Development server bypasses all guard-rails
npm run dev

# No authentication barriers
# No security violations
# Hot reload and fast development
```

### **Method 4: Environment File Switching**

Create separate environment files:

```bash
# .env.development.local
ENABLE_SEARCH_AUTH=false
ENABLE_HMAC_AUTH=false

# .env.production.local  
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
```

Load conditionally:
```bash
# Development
NODE_ENV=development npm run build

# Production
NODE_ENV=production npm run build
```

## 🔍 Debugging Security Issues

### **Identifying Security Violations**

#### **Check Current Environment**
```bash
# Check environment variables
echo "NODE_ENV: $NODE_ENV"
echo "CI: $CI"

# In code
console.log('Environment:', process.env.NODE_ENV)
console.log('CI:', process.env.CI)
console.log('Auth flags:', {
  searchAuth: process.env.ENABLE_SEARCH_AUTH,
  hmacAuth: process.env.ENABLE_HMAC_AUTH,
  captcha: process.env.ENABLE_CAPTCHA
})
```

#### **Trace Guard-Rail Execution**
```typescript
// Add to env-guard.ts for debugging
console.log('🔍 Security Guard-Rail Check:')
console.log('NODE_ENV:', process.env.NODE_ENV)
console.log('CI:', process.env.CI)
console.log('Is Test Environment:', isTestEnv)
console.log('Checking flags:', unsafeFlags)
```

### **Common Debugging Commands**

#### **Environment Variable Inspection**
```bash
# List all environment variables
env | grep -E "(NODE_ENV|CI|ENABLE_|TEST_MODE)"

# Check specific variables
echo "Search Auth: $ENABLE_SEARCH_AUTH"
echo "HMAC Auth: $ENABLE_HMAC_AUTH"
echo "Environment: $NODE_ENV"
```

#### **File Content Verification**
```bash
# Check .env.local contents (careful with secrets)
grep -E "(ENABLE_|TEST_MODE)" .env.local

# Check for hidden characters
cat -A .env.local | head -10
```

#### **Build Process Debugging**
```bash
# Debug build with environment info
NODE_ENV=test npm run build 2>&1 | grep -E "(Security|Environment|Guard)"

# Test different environments
NODE_ENV=development npm run build    # Should work
NODE_ENV=test npm run build          # Should work
NODE_ENV=production npm run build    # May trigger guard-rails
```

## 🛠️ Customizing Guard-Rails

### **Understanding the Implementation**

The guard-rail logic is in `/src/lib/env-guard.ts`:

```typescript
// Unsafe flag definitions
const UNSAFE_FLAG_VALUES: Record<string, string[]> = {
  'ENABLE_RATE_LIMITING': ['false'],
  'ENABLE_IP_ALLOWLIST': ['false'],
  'ENABLE_SEARCH_AUTH': ['false'],
  'ENABLE_HMAC_AUTH': ['false'],
  'TEST_MODE_BYPASS_AUTH': ['true'],
  'TEST_MODE_BYPASS_CORS': ['true'],
  'BYPASS_IP_ALLOWLIST': ['true'],
  'SKIP_ENV_VALIDATION': ['true'],
  'DISABLE_HMAC_VALIDATE': ['true'],
}

// Environment detection
const isTestEnv = process.env.NODE_ENV === 'test' || process.env.CI === 'true'

// Violation check
if (!isTestEnv && hasUnsafeFlags) {
  throw new Error('Security violation detected')
}
```

### **Safe Customization Guidelines**

If you need to modify the guard-rails (⚠️ **use caution**):

#### **Adding New Unsafe Flags**
```typescript
// Add new security checks
const UNSAFE_FLAG_VALUES: Record<string, string[]> = {
  // ... existing flags
  'ENABLE_NEW_SECURITY_FEATURE': ['false'],
  'DISABLE_NEW_PROTECTION': ['true'],
}
```

#### **Creating Custom Bypass Conditions**
```typescript
// Add custom bypass logic
const isTestEnv = 
  process.env.NODE_ENV === 'test' || 
  process.env.CI === 'true' ||
  process.env.CUSTOM_BYPASS === 'true'  // ⚠️ Be very careful
```

**⚠️ Warning**: Modifying guard-rails can introduce security vulnerabilities. Always:
1. Review changes with security team
2. Test thoroughly in all environments  
3. Document the security implications
4. Consider alternative solutions first

## 🚨 Troubleshooting Guide

### **Common Issues and Solutions**

#### **Issue 1: Build Fails with Security Violation**
```bash
Error: Security violation detected
```

**Solution Options:**
```bash
# Option A: Fix environment variables
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true

# Option B: Use test environment
NODE_ENV=test npm run build

# Option C: Use CI flag
CI=true npm run build

# Option D: Use development mode
npm run dev  # No build required
```

#### **Issue 2: Can't Test Authentication Features Locally**
```bash
# Problem: Want to test auth but keep getting CAPTCHA prompts
```

**Solution:**
```bash
# In .env.local - balance security and UX
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
ENABLE_CAPTCHA=false  # Disable annoying CAPTCHAs

# Build with:
npm run build && npm run start
```

#### **Issue 3: CI/CD Pipeline Failing**
```bash
# Problem: GitHub Actions build failing with security violations
```

**Solution:**
Check GitHub Actions workflow has:
```yaml
env:
  NODE_ENV: test
  CI: true
```

#### **Issue 4: Production Deployment Failing**
```bash
# Problem: AWS Amplify build failing
```

**Solution:**
Ensure AWS Amplify environment variables have:
```bash
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
ENABLE_CAPTCHA=true
# All security features enabled
```

### **Emergency Bypass Procedures**

#### **Temporary Production Bypass (⚠️ EXTREME CAUTION)**

If you absolutely must bypass guard-rails in production:

1. **Immediate bypass:**
   ```bash
   # Set CI flag in production environment
   CI=true
   ```

2. **Follow-up actions:**
   - Review security implications immediately
   - Plan proper fix deployment
   - Monitor for security issues
   - Restore guard-rails ASAP

#### **Recovery Procedures**

If security is compromised:

1. **Immediate steps:**
   - Enable all security features
   - Rotate all secrets and API keys
   - Check access logs for anomalies
   - Deploy fixed configuration

2. **Investigation:**
   - Review what bypass flags were active
   - Check for unauthorized access
   - Audit system security
   - Document incident and lessons learned

## 📊 Security Best Practices

### **Development Workflow Security**

1. **Use layered security:**
   ```bash
   # Local development - minimal friction
   npm run dev  # Guard-rails bypassed
   
   # Pre-commit testing - some security
   NODE_ENV=test npm run build
   
   # Production deployment - full security
   ENABLE_ALL_SECURITY=true npm run build
   ```

2. **Environment isolation:**
   - Separate secrets for each environment
   - Different database instances
   - Isolated authentication systems

3. **Regular security audits:**
   - Review environment configurations
   - Check for accidentally committed secrets
   - Validate guard-rail effectiveness

### **Deployment Security**

1. **Production requirements:**
   - All security features must be enabled
   - No test bypass flags allowed
   - Strong secrets (32+ characters)
   - Proper IP restrictions

2. **Staging environment:**
   - Mirror production security settings
   - Use staging-specific secrets
   - Test security features thoroughly

3. **CI/CD security:**
   - Use secure secret management
   - Audit pipeline configurations
   - Monitor for security violations

---

## 📞 Quick Reference

**Emergency Commands:**
- Bypass guard-rails: `NODE_ENV=test npm run build`
- Check environment: `echo $NODE_ENV && echo $CI`
- Debug build: `npm run build 2>&1 | grep -i security`
- Development mode: `npm run dev` (no guard-rails)

**Security Checklist:**
- ✅ All auth features enabled in production
- ✅ No test bypass flags in production
- ✅ Strong secrets (32+ characters)
- ✅ Guard-rails active for production builds
- ✅ Separate configurations per environment

**Common Fixes:**
- Build failing: Set `NODE_ENV=test`  
- CAPTCHA issues: Set `ENABLE_CAPTCHA=false` in dev
- Auth errors: Check auth flags are enabled
- Guard-rail bypass: Use `CI=true` temporarily