# Environment Setup Guide

*Complete guide for environment variable configuration and setup. Last updated: July 21, 2025*

## 🎯 Overview

This guide covers all environment variable configurations needed for different environments: development, testing, staging, and production. It includes security requirements, service integrations, and troubleshooting.

## 🚀 Quick Start

### **Essential Files**

Create these environment files in your project root:

```bash
.env.local          # Local development (not committed to git)
.env.production     # Production defaults (committed)
.env.test          # Test configuration (committed)
.env.example       # Template file (committed)
```

### **Basic .env.local Template**

```bash
# Copy this template to create your .env.local file
# Never commit .env.local to git - it contains secrets

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here

# Authentication Settings
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
ENABLE_CAPTCHA=false

# Required Secrets (32+ characters)
JWT_SECRET=dev-jwt-secret-minimum-32-characters-for-local-development
PARTNER_SECRET_DEFAULT=dev-default-secret-minimum-32-characters

# Cloudflare Turnstile (test keys)
NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA

# Development Settings
NEXT_PUBLIC_DEBUG_ENABLED=true
ENABLE_SENTRY=false
```

## 🔧 Environment Variables Reference

### **Core Application Variables**

#### **Basic Configuration**
| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `NODE_ENV` | Application environment | Yes | `development` |
| `NEXT_PUBLIC_SITE_URL` | Public site URL | Yes | `http://localhost:3000` |
| `PORT` | Server port | No | `3000` |
| `CI` | CI environment flag | No | `false` |

#### **Supabase Database**
| Variable | Description | Required | Example |
|----------|-------------|----------|---------|
| `NEXT_PUBLIC_SUPABASE_URL` | Supabase project URL | Yes | `https://abc123.supabase.co` |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Public anonymous key | Yes | `eyJhbGciOiJ...` |
| `SUPABASE_SERVICE_ROLE_KEY` | Server-side service key | Yes | `eyJhbGciOiJ...` |

### **Security & Authentication Variables**

#### **Authentication Controls**
| Variable | Description | Required | Development | Production |
|----------|-------------|----------|-------------|------------|
| `ENABLE_SEARCH_AUTH` | Require auth for search | No | `false`/`true` | `true` |
| `ENABLE_HMAC_AUTH` | Enable HMAC authentication | No | `false`/`true` | `true` |
| `ENABLE_CAPTCHA` | Enable CAPTCHA verification | No | `false` | `true` |
| `ENABLE_IP_ALLOWLIST` | Enable IP restrictions | No | `true` | `true` |

#### **Security Secrets**
| Variable | Description | Required | Min Length | Example |
|----------|-------------|----------|------------|---------|
| `JWT_SECRET` | JWT token signing key | Yes | 32 chars | `dev-jwt-secret-minimum-32-chars...` |
| `PARTNER_SECRET_DEFAULT` | Default partner API secret | Yes | 32 chars | `dev-partner-secret-minimum-32-chars...` |
| `PARTNER_SECRET_TEST_PARTNER` | Test partner secret | No | 32 chars | `test-partner-secret-minimum-32-chars...` |

#### **IP Allowlist Configuration**
| Variable | Description | Required | Example |
|----------|-------------|----------|---------|
| `IP_ALLOWLIST_CIDRS` | Allowed IP ranges | Yes | `10.0.0.0/8,**********/12,***********/16` |
| `IP_ALLOWLIST_LOG_VIOLATIONS` | Log blocked IPs | No | `true` |
| `IP_ALLOWLIST_BLOCK_BY_DEFAULT` | Default block behavior | No | `true` |
| `IP_ALLOWLIST_INCLUDE_DEBUG` | Include debug info | No | `false` |

### **Third-Party Service Variables**

#### **Cloudflare Turnstile**
| Variable | Description | Required | Development | Production |
|----------|-------------|----------|-------------|------------|
| `NEXT_PUBLIC_TURNSTILE_SITE_KEY` | Public site key | Yes | Test key | Production key |
| `TURNSTILE_SECRET_KEY` | Secret validation key | Yes | Test key | Production key |

**Test Keys (always pass):**
```bash
NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA
```

#### **Sentry Error Monitoring**
| Variable | Description | Required | Example |
|----------|-------------|----------|---------|
| `SENTRY_DSN` | Sentry project DSN | Yes | `https://<EMAIL>/123` |
| `SENTRY_ENVIRONMENT` | Environment label | Yes | `development`/`production` |
| `SENTRY_TRACES_SAMPLE_RATE` | Performance sampling | No | `1.0` (dev), `0.1` (prod) |
| `SENTRY_PROFILES_SAMPLE_RATE` | Profiling sampling | No | `1.0` (dev), `0.1` (prod) |
| `ENABLE_SENTRY` | Enable Sentry reporting | No | `false` (dev), `true` (prod) |
| `ENABLE_SENTRY_LOCAL` | Local Sentry reporting | No | `false` |

#### **Email Configuration**
| Variable | Description | Required | Example |
|----------|-------------|----------|---------|
| `EMAIL_SERVER` | SMTP server hostname | Yes | `smtp.gmail.com` |
| `EMAIL_PORT` | SMTP port | Yes | `587` |
| `EMAIL_SECURE` | Use TLS/SSL | Yes | `true` |
| `EMAIL_USER` | SMTP username | Yes | `<EMAIL>` |
| `EMAIL_PASSWORD` | SMTP password/app password | Yes | `your-app-password` |
| `EMAIL_FROM` | From address | Yes | `<EMAIL>` |

### **Performance & Monitoring Variables**

#### **Caching Configuration**
| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `CACHE_TTL_SHORT` | Short cache duration (seconds) | No | `300` (5 min) |
| `CACHE_TTL_MEDIUM` | Medium cache duration (seconds) | No | `1800` (30 min) |
| `CACHE_TTL_EXTENDED` | Long cache duration (seconds) | No | `86400` (24 hours) |

#### **Rate Limiting**
| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `ENABLE_RATE_LIMITING` | Enable API rate limiting | No | `true` |
| `RATE_LIMIT_MAX` | Max requests per window | No | `100` |
| `RATE_LIMIT_WINDOW` | Rate limit window (ms) | No | `60000` (1 min) |

#### **Analytics & Monitoring**
| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `NEXT_PUBLIC_ANALYTICS_ENABLED` | Enable analytics | No | `false` (dev), `true` (prod) |
| `NEXT_PUBLIC_DEBUG_ENABLED` | Enable debug logging | No | `true` (dev), `false` (prod) |
| `NEXT_PUBLIC_DEBUG_LEVEL` | Debug level | No | `verbose` (dev), `error` (prod) |
| `ENABLE_PERFORMANCE_MONITORING` | Enable performance tracking | No | `false` (dev), `true` (prod) |
| `ENABLE_WEB_VITALS_REPORTING` | Enable Web Vitals | No | `false` (dev), `true` (prod) |

## 🌍 Environment-Specific Configurations

### **Development Environment (.env.local)**

**Recommended for smooth development:**

```bash
# Basic Configuration
NODE_ENV=development
NEXT_PUBLIC_SITE_URL=http://localhost:3000
PORT=3000

# Supabase - Use your development project
NEXT_PUBLIC_SUPABASE_URL=https://your-dev-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-dev-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-dev-service-role-key

# Authentication - Optimized for development
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
ENABLE_CAPTCHA=false  # No annoying CAPTCHAs during development
ENABLE_IP_ALLOWLIST=true

# Security - Development-safe secrets
JWT_SECRET=dev-jwt-secret-minimum-32-characters-for-local-development
PARTNER_SECRET_DEFAULT=dev-default-secret-minimum-32-characters
PARTNER_SECRET_TEST_PARTNER=test-secret-minimum-32-characters-long

# Cloudflare - Test keys (always pass)
NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA

# IP Allowlist - Local development IPs
IP_ALLOWLIST_CIDRS=10.0.0.0/8,**********/12,***********/16,127.0.0.1/32
IP_ALLOWLIST_LOG_VIOLATIONS=true
IP_ALLOWLIST_BLOCK_BY_DEFAULT=false
IP_ALLOWLIST_INCLUDE_DEBUG=true

# Sentry - Disabled for development
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=development
ENABLE_SENTRY=false
ENABLE_SENTRY_LOCAL=false
SENTRY_TRACES_SAMPLE_RATE=1.0

# Email - Development SMTP
EMAIL_SERVER=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# Performance - Development settings
NEXT_PUBLIC_DEBUG_ENABLED=true
NEXT_PUBLIC_DEBUG_LEVEL=verbose
NEXT_PUBLIC_ANALYTICS_ENABLED=false
ENABLE_PERFORMANCE_MONITORING=false
ENABLE_WEB_VITALS_REPORTING=false

# Cache - Shorter TTLs for development
CACHE_TTL_SHORT=60
CACHE_TTL_MEDIUM=300
CACHE_TTL_EXTENDED=1800

# Rate Limiting - Relaxed for development
ENABLE_RATE_LIMITING=false
RATE_LIMIT_MAX=1000
RATE_LIMIT_WINDOW=60000
```

### **Test Environment (.env.test)**

**For CI/CD and automated testing:**

```bash
# Basic Configuration
NODE_ENV=test
CI=true
NEXT_PUBLIC_SITE_URL=http://localhost:3000

# Mock Supabase - Safe for CI
NEXT_PUBLIC_SUPABASE_URL=https://mock-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.mock-anon-key
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.mock-service-role-key

# Authentication - Test bypasses allowed
ENABLE_SEARCH_AUTH=false
ENABLE_HMAC_AUTH=false
ENABLE_CAPTCHA=false
ENABLE_IP_ALLOWLIST=false
TEST_MODE_BYPASS_AUTH=true

# Mock secrets for testing
JWT_SECRET=ci-jwt-secret-minimum-32-characters-long-for-github-actions
PARTNER_SECRET_DEFAULT=ci-test-default-secret-minimum-32-characters-long
PARTNER_SECRET_TEST_PARTNER=ci-test-partner-secret-minimum-32-characters

# Test Cloudflare keys
NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA

# Disable external services in tests
ENABLE_SENTRY=false
ENABLE_RATE_LIMITING=false
ENABLE_PERFORMANCE_MONITORING=false
NEXT_PUBLIC_ANALYTICS_ENABLED=false
```

### **Production Environment**

**For AWS Amplify and production deployment:**

```bash
# Basic Configuration
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://cashbackdeals.com
AWS_REGION=us-east-1

# Supabase - Production database
NEXT_PUBLIC_SUPABASE_URL=https://your-prod-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-role-key

# Authentication - All security enabled
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
ENABLE_CAPTCHA=true
ENABLE_IP_ALLOWLIST=true

# Production secrets (32+ characters required)
JWT_SECRET=your-production-jwt-secret-minimum-32-characters
PARTNER_SECRET_DEFAULT=your-production-partner-secret-minimum-32-characters

# Cloudflare - Production keys
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your-production-turnstile-site-key
TURNSTILE_SECRET_KEY=your-production-turnstile-secret-key

# IP Allowlist - Production IP ranges
IP_ALLOWLIST_CIDRS=your-allowed-production-ips
IP_ALLOWLIST_LOG_VIOLATIONS=true
IP_ALLOWLIST_BLOCK_BY_DEFAULT=true
IP_ALLOWLIST_INCLUDE_DEBUG=false

# Sentry - Full monitoring enabled
SENTRY_DSN=https://<EMAIL>/project
SENTRY_ENVIRONMENT=production
ENABLE_SENTRY=true
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_PROFILES_SAMPLE_RATE=0.1

# Email - Production SMTP
EMAIL_SERVER=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_SECURE=true
EMAIL_USER=your-production-email
EMAIL_PASSWORD=your-production-password
EMAIL_FROM=<EMAIL>

# Performance - Production monitoring
NEXT_PUBLIC_DEBUG_ENABLED=false
NEXT_PUBLIC_DEBUG_LEVEL=error
NEXT_PUBLIC_ANALYTICS_ENABLED=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_WEB_VITALS_REPORTING=true

# Cache - Production TTLs
CACHE_TTL_SHORT=300
CACHE_TTL_MEDIUM=1800
CACHE_TTL_EXTENDED=86400

# Rate Limiting - Production protection
ENABLE_RATE_LIMITING=true
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=60000
```

## 🛡️ Security Guard-Rails

### **Understanding Security Protection**

The application has built-in security guard-rails that prevent unsafe configurations in production:

#### **Forbidden in Production**
These settings will cause build failures in production:
```bash
ENABLE_SEARCH_AUTH=false      # ❌ Not allowed in production
ENABLE_HMAC_AUTH=false        # ❌ Not allowed in production
ENABLE_RATE_LIMITING=false    # ❌ Not allowed in production
TEST_MODE_BYPASS_AUTH=true    # ❌ Not allowed in production
SKIP_ENV_VALIDATION=true      # ❌ Not allowed in production
```

#### **Allowed Bypass Conditions**
These settings bypass the security guard-rails:
```bash
NODE_ENV=test                 # ✅ Allows test-only flags
CI=true                       # ✅ Allows CI/test configurations
```

#### **Security Validation Rules**

The guard-rail checks for:
1. **Test bypass flags** in production environment
2. **Missing required secrets** (minimum 32 characters)
3. **Invalid authentication configurations**
4. **Unsafe IP allowlist settings**

### **Working with Security Guard-Rails**

#### **During Development**
```bash
# Option 1: Use development mode (recommended)
npm run dev  # No guard-rails, full development freedom

# Option 2: Use test environment for builds
NODE_ENV=test npm run build

# Option 3: Use CI flag
CI=true npm run build
```

#### **For Production Deployment**
```bash
# Must use production-safe configuration
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
ENABLE_CAPTCHA=true
# ... all security features enabled
```

## 🔧 Service-Specific Setup

### **Supabase Setup**

1. **Create Supabase Project**
   - Go to [Supabase Dashboard](https://app.supabase.com)
   - Create new project
   - Note down URL and keys

2. **Configure Environment Variables**
   ```bash
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
   ```

3. **Setup Database Schema**
   - Import schema from `supabase/migrations/`
   - Configure Row Level Security (RLS)
   - Set up authentication policies

### **Cloudflare Turnstile Setup**

1. **Get Turnstile Keys**
   - Go to [Cloudflare Dashboard](https://dash.cloudflare.com/)
   - Navigate to Turnstile
   - Create new site
   - Copy site key and secret key

2. **Configure Keys**
   ```bash
   # Production
   NEXT_PUBLIC_TURNSTILE_SITE_KEY=your-real-site-key
   TURNSTILE_SECRET_KEY=your-real-secret-key
   
   # Development (always pass)
   NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
   TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA
   ```

### **Sentry Setup**

1. **Create Sentry Project**
   - Go to [Sentry Dashboard](https://sentry.io)
   - Create new Next.js project
   - Copy DSN

2. **Configure Sentry**
   ```bash
   SENTRY_DSN=https://<EMAIL>/project-id
   SENTRY_ENVIRONMENT=development  # or production
   ENABLE_SENTRY=false  # for development
   ```

### **Email Setup (Gmail Example)**

1. **Enable App Passwords**
   - Enable 2FA on Gmail account
   - Generate app password

2. **Configure Email Variables**
   ```bash
   EMAIL_SERVER=smtp.gmail.com
   EMAIL_PORT=587
   EMAIL_SECURE=false
   EMAIL_USER=<EMAIL>
   EMAIL_PASSWORD=your-app-password
   EMAIL_FROM=<EMAIL>
   ```

## 🚨 Troubleshooting

### **Common Issues**

#### **Security Guard-Rail Violations**
```bash
🚨 SECURITY VIOLATION: Test-only bypass flags detected in production environment!
```

**Solutions:**
```bash
# Option 1: Fix environment variables
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true

# Option 2: Use test environment
NODE_ENV=test npm run build

# Option 3: Use CI environment
CI=true npm run build
```

#### **Missing Environment Variables**
```bash
Error: Required environment variable SUPABASE_SERVICE_ROLE_KEY is not set
```

**Solutions:**
1. Check `.env.local` file exists
2. Verify variable names (case-sensitive)
3. Ensure no extra spaces in values
4. Check file encoding (should be UTF-8)

#### **Invalid Secret Lengths**
```bash
Error: JWT_SECRET must be at least 32 characters
```

**Solutions:**
```bash
# Generate secure secret
JWT_SECRET=$(openssl rand -base64 32)

# Or use a manual 32+ character string
JWT_SECRET=your-very-long-secret-minimum-32-characters-required
```

### **Environment Variable Debugging**

#### **Check Environment Variables**
```bash
# In Node.js/React
console.log('Environment:', process.env.NODE_ENV)
console.log('Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)

# In terminal
echo $NODE_ENV
env | grep SUPABASE
```

#### **Validate Configuration**
```bash
# Check if file exists
ls -la .env.local

# Check file contents (be careful with secrets)
head .env.local

# Check for invisible characters
cat -A .env.local
```

#### **Test Environment Loading**
```javascript
// Add to a test page
export async function getServerSideProps() {
  console.log('Server environment:', {
    NODE_ENV: process.env.NODE_ENV,
    SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 20) + '...',
    HAS_SERVICE_KEY: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    HAS_JWT_SECRET: !!process.env.JWT_SECRET
  })
  
  return { props: {} }
}
```

## 📚 Best Practices

### **Security Best Practices**

1. **Never commit secrets to git**
   - Use `.env.local` for local secrets
   - Add sensitive files to `.gitignore`
   - Use separate keys for each environment

2. **Use strong secrets**
   - Minimum 32 characters for all secrets
   - Generate cryptographically secure random values
   - Rotate secrets regularly

3. **Environment isolation**
   - Separate databases for dev/staging/prod
   - Different API keys for each environment
   - Proper access controls

### **Development Best Practices**

1. **Environment file organization**
   ```bash
   .env.example          # Template (committed)
   .env.local           # Local development (not committed)
   .env.test           # Test configuration (committed)
   .env.production     # Production defaults (committed)
   ```

2. **Variable naming conventions**
   ```bash
   # Public variables (exposed to browser)
   NEXT_PUBLIC_SITE_URL=...
   NEXT_PUBLIC_ANALYTICS_ID=...
   
   # Private server-only variables
   SUPABASE_SERVICE_ROLE_KEY=...
   JWT_SECRET=...
   EMAIL_PASSWORD=...
   ```

3. **Documentation**
   - Document all environment variables
   - Include examples and default values
   - Explain security implications

### **Deployment Best Practices**

1. **Environment validation**
   - Validate all required variables at build time
   - Check secret lengths and formats
   - Verify service connectivity

2. **Secret management**
   - Use AWS Secrets Manager or similar
   - Rotate secrets regularly
   - Monitor secret usage

3. **Environment monitoring**
   - Alert on configuration changes
   - Monitor for security violations
   - Track environment variable usage

---

## 📞 Quick Reference

**Essential Commands:**
- Create template: `cp .env.example .env.local`
- Check variables: `env | grep SUPABASE`
- Test build: `NODE_ENV=test npm run build`
- Validate secrets: Check minimum 32 characters

**Security Reminders:**
- ❌ Never commit `.env.local`
- ✅ Use production-safe values for builds
- ✅ Enable all security features in production
- ✅ Use test keys for development

**Emergency Fixes:**
- Security violations: Set `NODE_ENV=test`
- Missing variables: Check `.env.local` exists
- Invalid secrets: Generate new 32+ character values