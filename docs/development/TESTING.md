# Testing Strategy & Implementation

*This file documents the enterprise test architecture transformation. Last updated: 29th July 2025*

## Testing Overview

**🏗️ Enterprise Test Architecture (July 2025 Transformation)**

The Cashback Deals platform has undergone a comprehensive testing infrastructure transformation, consolidating all tests into a centralized `tests/` directory with enterprise-grade organization and comprehensive test coverage including security, authentication, and performance testing.

## Test Pyramid

```mermaid
graph TB
    E2E[End-to-End Tests<br/>Playwright<br/>Critical User Journeys]
    INT[Integration Tests<br/>API Routes & Database<br/>Supabase Test Mode]
    UNIT[Unit Tests<br/>Jest + React Testing Library<br/>Components & Utilities]
    
    E2E --> INT
    INT --> UNIT
    
    style E2E fill:#ff6b6b
    style INT fill:#ffa500
    style UNIT fill:#4ecdc4
```

### Test Distribution & Coverage Goals

| Test Type | Framework | Coverage Target | Location | Focus |
|-----------|-----------|----------------|----------|--------|
| **Unit Tests** | Jest + RTL | 80% | `tests/unit/` | Components, utilities, data layer |
| **Integration Tests** | Jest + Supabase | 70% | `tests/integration/` | API routes, auth workflows |
| **E2E Tests** | Playwright | Critical paths | `tests/e2e/` | User journeys, full workflows |
| **Security Tests** | Jest + Custom | 100% | `tests/security/` | HMAC auth, XSS, rate limiting |
| **Performance Tests** | Lighthouse CI | Web Vitals | `tests/performance/` | Load tests, Core metrics |

## Enterprise Test Architecture (July 2025)

### Test Consolidation & Organization

**🎯 Centralized Test Structure:**
```
tests/
├── unit/                 # Unit tests for components, hooks, utilities
│   ├── components/      # React component tests
│   ├── lib/            # Data layer and utility function tests
│   └── hooks/          # Custom React hook tests
├── integration/         # Integration tests for API routes and workflows
│   ├── api/            # API endpoint testing
│   ├── auth/           # Authentication integration tests
│   └── database/       # Database operation tests
├── e2e/                # End-to-end tests with Playwright
│   ├── user-flows/     # Complete user journey tests
│   └── performance/    # E2E performance validation
├── security/           # Security-focused testing
│   ├── auth/           # HMAC authentication, JWT security
│   ├── xss/            # Cross-site scripting protection
│   └── rate-limiting/  # API rate limit validation
├── __mocks__/          # Centralized mock implementations
│   ├── supabase.ts     # Database mocking with .single() chain support
│   └── next/           # Next.js framework mocks
├── fixtures/           # Shared test data and fixtures
├── setup/              # Test environment configuration
└── Test_Archives/      # Historical consolidation records
```

**🔧 Key Infrastructure Components:**
- **Centralized Mocks**: All Supabase and framework mocks in `tests/__mocks__/`
- **Shared Fixtures**: Reusable test data and mock objects
- **Archive System**: Complete audit trail of test consolidation history
- **Configuration**: Unified test setup with `tsconfig.test.json` and `jest.config.base.js`

### Security Test Coverage

**🔒 HMAC Authentication Testing:**
- E2E authentication flows (`tests/e2e/user-flows/auth-hmac.spec.ts`)
- JWT + HMAC compatibility (`tests/integration/auth/jwt-hmac-compatibility.test.ts`)
- HMAC helper functions (`tests/security/auth/hmac.test.ts`)
- Security attack scenarios (`tests/security/auth/hmac-security.test.ts`)

**🛡️ Security Test Categories:**
- **Authentication**: Multi-method auth (JWT, HMAC, fallback scenarios)
- **Authorization**: Role-based access control
- **Input Validation**: XSS protection, injection prevention
- **Rate Limiting**: API endpoint protection
- **CSRF Protection**: Cross-site request forgery prevention

## Unit Testing

### Framework Setup

**Jest Configuration (`jest.config.js`):**
```javascript
const nextJest = require('next/jest')

const createJestConfig = nextJest({
  dir: './',
})

const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testEnvironment: 'jsdom',
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@components/(.*)$': '<rootDir>/src/components/$1',
    '^@lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@hooks/(.*)$': '<rootDir>/src/hooks/$1',
  },
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{js,jsx,ts,tsx}',
    '<rootDir>/tests/**/*.{js,jsx,ts,tsx}',
  ],
  collectCoverageFrom: [
    'src/**/*.{js,jsx,ts,tsx}',
    '!src/**/*.d.ts',
    '!src/**/*.stories.{js,jsx,ts,tsx}',
    '!src/**/index.{js,jsx,ts,tsx}',
  ],
  coverageReporters: ['text', 'lcov', 'html'],
  testTimeout: 30000,
  transformIgnorePatterns: [
    'node_modules/(?!(isomorphic-dompurify|@supabase|lucide-react|@radix-ui)/)'
  ],
}

module.exports = createJestConfig(customJestConfig)
```

**Jest Setup (`jest.setup.js`):**
```javascript
import '@testing-library/jest-dom'
import { TextEncoder, TextDecoder } from 'util'

// Polyfills for Node.js environment
global.TextEncoder = TextEncoder
global.TextDecoder = TextDecoder

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useSearchParams: () => ({
    get: jest.fn(),
    toString: jest.fn(),
  }),
}))

// Mock Supabase client
jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      single: jest.fn().mockResolvedValue({ data: null, error: null }),
    })),
  },
}))
```

## Test Environment Setup

### Comprehensive Test Environment Configuration

The application includes a sophisticated test environment setup that properly handles authentication, database isolation, and CI/CD integration.

#### Environment Configuration Files

**Test Environment Variables (`.env.test`):**
```bash
# Node Environment
NODE_ENV=test

# Authentication Configuration
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
TEST_MODE_BYPASS_AUTH=true

# JWT Configuration
JWT_SECRET=test-secret-min-32-chars-for-testing-environment

# HMAC Configuration
PARTNER_SECRET_DEFAULT=test-partner-secret-min-32-chars-required-for-testing
HMAC_TIMESTAMP_WINDOW=300

# Database Configuration
SUPABASE_TEST_URL=https://test-project.supabase.co
SUPABASE_TEST_KEY=test-anon-key-for-testing
SUPABASE_SERVICE_ROLE_KEY=test-service-role-key

# Disable Rate Limiting in Tests
DISABLE_RATE_LIMITING=true

# Test-specific Settings
TEST_DATABASE_CLEANUP=true
TEST_MOCK_EXTERNAL_APIS=true
```

**Jest Environment Loader (`jest.env.setup.js`):**
```javascript
// Load test environment variables before Jest runs
const path = require('path')
const dotenv = require('dotenv')

// Load .env.test file
const testEnvPath = path.resolve(process.cwd(), '.env.test')
const result = dotenv.config({ path: testEnvPath })

if (result.error) {
  console.warn('Warning: Could not load .env.test file:', result.error.message)
  console.warn('Some tests may fail without proper environment configuration')
} else {
  console.log('✅ Test environment variables loaded from .env.test')
}

// Validate required test environment variables
const requiredEnvVars = [
  'NODE_ENV',
  'ENABLE_SEARCH_AUTH',
  'ENABLE_HMAC_AUTH',
  'TEST_MODE_BYPASS_AUTH',
  'JWT_SECRET',
  'PARTNER_SECRET_DEFAULT'
]

const missingVars = requiredEnvVars.filter(varName => !process.env[varName])
if (missingVars.length > 0) {
  console.error('❌ Missing required test environment variables:', missingVars)
  process.exit(1)
}

console.log('🔒 Test authentication configuration:')
console.log(`  - ENABLE_SEARCH_AUTH: ${process.env.ENABLE_SEARCH_AUTH}`)
console.log(`  - ENABLE_HMAC_AUTH: ${process.env.ENABLE_HMAC_AUTH}`)
console.log(`  - TEST_MODE_BYPASS_AUTH: ${process.env.TEST_MODE_BYPASS_AUTH}`)
console.log(`  - JWT_SECRET configured: ${!!process.env.JWT_SECRET}`)
console.log(`  - PARTNER_SECRET configured: ${!!process.env.PARTNER_SECRET_DEFAULT}`)
```

**Updated Jest Configuration:**
The Jest configuration includes the environment loader:

```javascript
module.exports = {
  // Load environment variables before running tests
  setupFiles: ['<rootDir>/jest.env.setup.js'],
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  
  // Exclude test utilities and Playwright tests
  testPathIgnorePatterns: [
    '<rootDir>/.next/',
    '<rootDir>/node_modules/',
    '<rootDir>/tests/', // Playwright E2E tests
    '<rootDir>/src/__tests__/utils/', // Test utilities
  ],
  
  // Other configuration...
}
```

### Authentication Test Examples

#### JWT Authentication Tests
```typescript
// src/__tests__/api/search-auth.test.ts
import { GET } from '@/src/app/api/search/route'

describe('/api/search JWT Authentication', () => {
  it('accepts valid JWT token', async () => {
    const request = new Request('http://localhost:3000/api/search?q=test', {
      headers: {
        'Authorization': 'Bearer valid_jwt_token',
        'Content-Type': 'application/json'
      }
    })
    
    const response = await GET(request)
    expect(response.status).toBe(200)
  })
  
  it('rejects invalid JWT token', async () => {
    const request = new Request('http://localhost:3000/api/search?q=test', {
      headers: {
        'Authorization': 'Bearer invalid_token',
        'Content-Type': 'application/json'
      }
    })
    
    const response = await GET(request)
    expect(response.status).toBe(401)
  })
})
```

#### HMAC Authentication Tests
```typescript
// src/__tests__/api/contact-auth.test.ts
import { POST } from '@/src/app/api/contact/route'

describe('/api/contact HMAC Authentication', () => {
  it('accepts valid HMAC signature', async () => {
    const request = new Request('http://localhost:3000/api/contact', {
      method: 'POST',
      headers: {
        'X-Signature': 'valid_signature',
        'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
        'X-Partner-ID': 'test-partner',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ message: 'test' })
    })
    
    const response = await POST(request)
    expect(response.status).toBe(200)
  })
  
  it('rejects invalid HMAC signature', async () => {
    const request = new Request('http://localhost:3000/api/contact', {
      method: 'POST',
      headers: {
        'X-Signature': 'invalid_signature',
        'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
        'X-Partner-ID': 'test-partner',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ message: 'test' })
    })
    
    const response = await POST(request)
    expect(response.status).toBe(401)
  })
})
```

### Test Environment Security

#### Authentication Bypass Implementation
The test bypasses are implemented securely with multiple safeguards:

```typescript
// Only active in test environment with explicit flag
if (process.env.NODE_ENV === 'test' && process.env.TEST_MODE_BYPASS_AUTH === 'true') {
  // Mock JWT bypass
  if (token === 'valid_jwt_token') {
    return { sub: 'frontend', iat: now, exp: now + 300 }
  }
  
  // Mock HMAC bypass
  if (signature === 'valid_signature') {
    return { partnerId, timestamp, method, path, isValid: true }
  }
}
```

#### CI/CD Integration
The test environment is automatically configured in GitHub Actions:

```yaml
# .github/workflows/ci.yml
- name: Run integration tests
  run: npm run test:api
  env:
    SUPABASE_TEST_URL: ${{ secrets.SUPABASE_TEST_URL }}
    SUPABASE_TEST_KEY: ${{ secrets.SUPABASE_TEST_KEY }}
    ENABLE_SEARCH_AUTH: true
    ENABLE_HMAC_AUTH: true
    TEST_MODE_BYPASS_AUTH: true
```

### Testing Patterns

#### 1. Component Testing
```typescript
// src/components/ProductCard.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { ProductCard } from './ProductCard'
import { TransformedProduct } from '@/lib/data/types'

const mockProduct: TransformedProduct = {
  id: '1',
  name: 'Test Product',
  slug: 'test-product',
  description: 'Test description',
  images: ['https://example.com/image.jpg'],
  status: 'active',
  isFeatured: true,
  isSponsored: false,
  cashbackAmount: 50,
  minPrice: 999,
  modelNumber: 'TEST-001',
  createdAt: '2025-01-01',
  updatedAt: '2025-01-01',
  brand: {
    id: '1',
    name: 'Test Brand',
    slug: 'test-brand',
    logoUrl: 'https://example.com/logo.jpg',
  },
  category: null,
  promotion: null,
  retailerOffers: [],
}

describe('ProductCard', () => {
  it('renders product information correctly', () => {
    render(<ProductCard product={mockProduct} />)
    
    expect(screen.getByText('Test Product')).toBeInTheDocument()
    expect(screen.getByText('Test Brand')).toBeInTheDocument()
    expect(screen.getByText('£50 Cashback')).toBeInTheDocument()
    expect(screen.getByText('From £999')).toBeInTheDocument()
  })

  it('handles click events', () => {
    const mockRouter = { push: jest.fn() }
    jest.spyOn(require('next/navigation'), 'useRouter').mockReturnValue(mockRouter)
    
    render(<ProductCard product={mockProduct} />)
    
    fireEvent.click(screen.getByRole('button'))
    expect(mockRouter.push).toHaveBeenCalledWith('/products/test-product')
  })

  it('displays featured badge when product is featured', () => {
    render(<ProductCard product={mockProduct} />)
    
    expect(screen.getByText('Featured')).toBeInTheDocument()
  })
})
```

#### 2. Hook Testing
```typescript
// src/hooks/usePagination.test.ts
import { renderHook, act } from '@testing-library/react'
import { usePagination } from './usePagination'

const mockPush = jest.fn()
const mockSearchParams = new URLSearchParams()

jest.mock('next/navigation', () => ({
  useRouter: () => ({ push: mockPush }),
  useSearchParams: () => mockSearchParams,
}))

describe('usePagination', () => {
  beforeEach(() => {
    mockPush.mockClear()
    mockSearchParams.clear()
  })

  it('initializes with default values', () => {
    const { result } = renderHook(() => usePagination())
    
    expect(result.current.currentPage).toBe(1)
    expect(result.current.pageSize).toBe(20)
  })

  it('updates page correctly', () => {
    const { result } = renderHook(() => usePagination({
      basePath: '/products'
    }))
    
    act(() => {
      result.current.goToPage(2)
    })
    
    expect(mockPush).toHaveBeenCalledWith('/products?page=2')
  })

  it('removes page parameter when going to first page', () => {
    mockSearchParams.set('page', '2')
    const { result } = renderHook(() => usePagination({
      basePath: '/products'
    }))
    
    act(() => {
      result.current.goToPage(1)
    })
    
    expect(mockPush).toHaveBeenCalledWith('/products')
  })
})
```

#### 3. Utility Function Testing
```typescript
// src/lib/utils.test.ts
import { cn, formatPrice, calculateCashback } from './utils'

describe('Utility Functions', () => {
  describe('cn (className utility)', () => {
    it('merges classes correctly', () => {
      expect(cn('bg-red-500', 'text-white')).toBe('bg-red-500 text-white')
    })

    it('handles conditional classes', () => {
      expect(cn('base', true && 'active', false && 'inactive')).toBe('base active')
    })
  })

  describe('formatPrice', () => {
    it('formats prices correctly', () => {
      expect(formatPrice(999)).toBe('£999')
      expect(formatPrice(99.99)).toBe('£99.99')
      expect(formatPrice(0)).toBe('£0')
    })

    it('handles null/undefined values', () => {
      expect(formatPrice(null)).toBe('Price not available')
      expect(formatPrice(undefined)).toBe('Price not available')
    })
  })

  describe('calculateCashback', () => {
    it('calculates cashback correctly', () => {
      expect(calculateCashback(1000, 0.05)).toBe(50)
      expect(calculateCashback(500, 0.1)).toBe(50)
    })

    it('handles edge cases', () => {
      expect(calculateCashback(0, 0.05)).toBe(0)
      expect(calculateCashback(1000, 0)).toBe(0)
    })
  })
})
```

## Integration Testing

### API Route Testing

#### Setup for API Testing
```typescript
// tests/api/setup.ts
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'
import { NextRequest } from 'next/server'

export const createMockRequest = (
  url: string,
  method: string = 'GET',
  body?: any
): NextRequest => {
  const request = new NextRequest(url, {
    method,
    body: body ? JSON.stringify(body) : undefined,
    headers: {
      'Content-Type': 'application/json',
    },
  })
  
  return request
}

export const createTestSupabaseClient = () => {
  // Create test client with test database
  return createServerSupabaseReadOnlyClient()
}
```

#### API Route Tests
```typescript
// tests/api/products.test.ts
import { GET } from '@/src/app/api/products/route'
import { createMockRequest } from './setup'

describe('/api/products', () => {
  it('returns products successfully', async () => {
    const request = createMockRequest('http://localhost:3000/api/products')
    const response = await GET(request)
    const data = await response.json()
    
    expect(response.status).toBe(200)
    expect(data.data).toBeDefined()
    expect(Array.isArray(data.data)).toBe(true)
  })

  it('handles pagination parameters', async () => {
    const request = createMockRequest('http://localhost:3000/api/products?page=2&limit=10')
    const response = await GET(request)
    const data = await response.json()
    
    expect(response.status).toBe(200)
    expect(data.pagination.page).toBe(2)
    expect(data.pagination.pageSize).toBe(10)
  })

  it('validates input parameters', async () => {
    const request = createMockRequest('http://localhost:3000/api/products?page=invalid')
    const response = await GET(request)
    
    expect(response.status).toBe(400)
  })

  it('handles database errors gracefully', async () => {
    // Mock database error
    jest.spyOn(console, 'error').mockImplementation(() => {})
    
    const request = createMockRequest('http://localhost:3000/api/products')
    const response = await GET(request)
    
    expect(response.status).toBe(500)
    expect(await response.json()).toEqual({
      data: null,
      error: 'Internal server error'
    })
  })
})
```

### Database Integration Tests

```typescript
// tests/lib/data/products.test.ts
import { getProducts, getProduct } from '@/lib/data/products'
import { createTestSupabaseClient } from '@/tests/api/setup'

describe('Products Data Layer', () => {
  const supabase = createTestSupabaseClient()

  beforeEach(async () => {
    // Clean up test data
    await supabase.from('products').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  })

  describe('getProducts', () => {
    it('fetches products with pagination', async () => {
      const result = await getProducts(supabase, {}, 1, 10)
      
      expect(result.data).toBeDefined()
      expect(Array.isArray(result.data)).toBe(true)
      expect(result.pagination.page).toBe(1)
      expect(result.pagination.pageSize).toBe(10)
    })

    it('filters products by brand', async () => {
      const brandId = 'test-brand-id'
      const result = await getProducts(supabase, { brandId }, 1, 10)
      
      result.data.forEach(product => {
        expect(product.brand?.id).toBe(brandId)
      })
    })

    it('handles empty results', async () => {
      const result = await getProducts(supabase, { brandId: 'non-existent' }, 1, 10)
      
      expect(result.data).toEqual([])
      expect(result.pagination.total).toBe(0)
    })
  })

  describe('getProduct', () => {
    it('fetches product by ID', async () => {
      const productId = 'test-product-id'
      const result = await getProduct(supabase, productId)
      
      expect(result.id).toBe(productId)
      expect(result.name).toBeDefined()
      expect(result.brand).toBeDefined()
    })

    it('handles non-existent product', async () => {
      const result = await getProduct(supabase, 'non-existent-id')
      
      expect(result).toBeNull()
    })
  })
})
```

## End-to-End Testing

### Playwright Configuration

```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test'

export default defineConfig({
  testDir: './tests/app',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
  ],
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:3000',
    reuseExistingServer: !process.env.CI,
  },
})
```

### E2E Test Examples

#### 1. Product Search Journey
```typescript
// tests/app/search-journey.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Product Search Journey', () => {
  test('user can search for products and view results', async ({ page }) => {
    await page.goto('/')
    
    // Search for products
    await page.fill('[data-testid="search-input"]', 'laptop')
    await page.click('[data-testid="search-button"]')
    
    // Verify search results
    await expect(page).toHaveURL(/\/search\?q=laptop/)
    await expect(page.locator('[data-testid="product-card"]')).toHaveCount({ min: 1 })
    
    // Check product information
    const firstProduct = page.locator('[data-testid="product-card"]').first()
    await expect(firstProduct.locator('[data-testid="product-name"]')).toBeVisible()
    await expect(firstProduct.locator('[data-testid="product-price"]')).toBeVisible()
    await expect(firstProduct.locator('[data-testid="cashback-amount"]')).toBeVisible()
  })

  test('user can filter search results', async ({ page }) => {
    await page.goto('/search?q=laptop')
    
    // Apply brand filter
    await page.click('[data-testid="brand-filter"]')
    await page.click('[data-testid="brand-option-samsung"]')
    
    // Verify filtered results
    await expect(page).toHaveURL(/brand=samsung/)
    
    const products = page.locator('[data-testid="product-card"]')
    await expect(products).toHaveCount({ min: 1 })
    
    // Verify all products are from Samsung
    const productBrands = products.locator('[data-testid="product-brand"]')
    await expect(productBrands.first()).toContainText('Samsung')
  })

  test('user can paginate through search results', async ({ page }) => {
    await page.goto('/search?q=phone')
    
    // Go to next page
    await page.click('[data-testid="pagination-next"]')
    
    // Verify URL updated
    await expect(page).toHaveURL(/page=2/)
    
    // Verify new products loaded
    await expect(page.locator('[data-testid="product-card"]')).toHaveCount({ min: 1 })
    
    // Go back to first page
    await page.click('[data-testid="pagination-prev"]')
    await expect(page).toHaveURL(/\/search\?q=phone$/)
  })
})
```

#### 2. Product Detail Journey
```typescript
// tests/app/product-detail.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Product Detail Journey', () => {
  test('user can view product details', async ({ page }) => {
    await page.goto('/products/sample-product')
    
    // Verify product information
    await expect(page.locator('[data-testid="product-name"]')).toBeVisible()
    await expect(page.locator('[data-testid="product-description"]')).toBeVisible()
    await expect(page.locator('[data-testid="product-images"]')).toBeVisible()
    
    // Verify cashback information
    await expect(page.locator('[data-testid="cashback-amount"]')).toBeVisible()
    await expect(page.locator('[data-testid="cashback-terms"]')).toBeVisible()
    
    // Verify retailer offers
    await expect(page.locator('[data-testid="retailer-offers"]')).toBeVisible()
    await expect(page.locator('[data-testid="retailer-offer"]')).toHaveCount({ min: 1 })
  })

  test('user can view similar products', async ({ page }) => {
    await page.goto('/products/sample-product')
    
    // Scroll to similar products
    await page.locator('[data-testid="similar-products"]').scrollIntoViewIfNeeded()
    
    // Verify similar products are displayed
    await expect(page.locator('[data-testid="similar-products"]')).toBeVisible()
    await expect(page.locator('[data-testid="similar-product-card"]')).toHaveCount({ min: 1 })
    
    // Click on similar product
    await page.locator('[data-testid="similar-product-card"]').first().click()
    
    // Verify navigation to new product
    await expect(page).toHaveURL(/\/products\/[^\/]+$/)
  })
})
```

## Security Testing

### Input Validation Tests

```typescript
// src/__tests__/security/validation.test.ts
import { validateInput, searchApiSchema } from '@/lib/validation/schemas'

describe('Input Validation Security', () => {
  describe('Search API Validation', () => {
    it('prevents XSS in search queries', () => {
      const maliciousInputs = [
        '<script>alert("xss")</script>',
        'javascript:alert("xss")',
        '<img src="x" onerror="alert(\'xss\')">',
        'vbscript:alert("xss")',
        'onload="alert(\'xss\')"',
      ]
      
      maliciousInputs.forEach(input => {
        const result = validateInput(searchApiSchema, { q: input })
        expect(result.success).toBe(false)
        expect(result.error).toBe('Validation failed')
      })
    })

    it('prevents SQL injection patterns', () => {
      const sqlInjectionInputs = [
        "'; DROP TABLE products; --",
        "' OR '1'='1",
        "' UNION SELECT * FROM users --",
        "'; INSERT INTO products VALUES('hack'); --",
      ]
      
      sqlInjectionInputs.forEach(input => {
        const result = validateInput(searchApiSchema, { q: input })
        expect(result.success).toBe(false)
      })
    })

    it('allows safe search queries', () => {
      const safeInputs = [
        'laptop gaming',
        'samsung galaxy',
        'apple iphone 14',
        'headphones wireless',
      ]
      
      safeInputs.forEach(input => {
        const result = validateInput(searchApiSchema, { q: input })
        expect(result.success).toBe(true)
      })
    })
  })

  describe('Contact Form Validation', () => {
    it('validates email format', () => {
      const invalidEmails = [
        'invalid-email',
        'user@',
        '@domain.com',
        'user@domain',
        'user<script>@domain.com',
      ]
      
      invalidEmails.forEach(email => {
        const result = validateInput(contactFormSchema, {
          name: 'Test User',
          email,
          enquiryType: 'general',
          message: 'Test message'
        })
        expect(result.success).toBe(false)
      })
    })
  })
})
```

### Rate Limiting Tests

```typescript
// src/__tests__/security/rate-limiting.test.ts
import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'
import { NextRequest } from 'next/server'

describe('Rate Limiting Security', () => {
  beforeEach(() => {
    // Clear rate limit cache
    jest.clearAllMocks()
  })

  it('allows requests within rate limit', async () => {
    const request = new NextRequest('http://localhost:3000/api/search', {
      headers: { 'x-forwarded-for': '***********' }
    })
    
    // First request should be allowed
    const result = applyRateLimit(request, rateLimits.search)
    expect(result).toBeNull()
  })

  it('blocks requests exceeding rate limit', async () => {
    const request = new NextRequest('http://localhost:3000/api/search', {
      headers: { 'x-forwarded-for': '***********' }
    })
    
    // Simulate exceeding rate limit
    for (let i = 0; i < rateLimits.search.maxRequests + 1; i++) {
      const result = applyRateLimit(request, rateLimits.search)
      
      if (i < rateLimits.search.maxRequests) {
        expect(result).toBeNull()
      } else {
        expect(result).not.toBeNull()
        expect(result?.status).toBe(429)
      }
    }
  })

  it('provides correct rate limit headers', async () => {
    const request = new NextRequest('http://localhost:3000/api/search', {
      headers: { 'x-forwarded-for': '***********' }
    })
    
    // Exceed rate limit
    for (let i = 0; i <= rateLimits.search.maxRequests; i++) {
      applyRateLimit(request, rateLimits.search)
    }
    
    const result = applyRateLimit(request, rateLimits.search)
    expect(result?.headers.get('X-RateLimit-Limit')).toBe('100')
    expect(result?.headers.get('X-RateLimit-Remaining')).toBe('0')
    expect(result?.headers.get('Retry-After')).toBeTruthy()
  })
})
```

### XSS Protection Tests

```typescript
// src/__tests__/security/xss.test.tsx
import { render, screen } from '@testing-library/react'
import DOMPurify from 'isomorphic-dompurify'
import { ProductDescription } from '@/components/ProductDescription'

describe('XSS Protection', () => {
  it('sanitizes dangerous HTML in product descriptions', () => {
    const maliciousDescription = '<script>alert("xss")</script><p>Safe content</p>'
    
    render(<ProductDescription description={maliciousDescription} />)
    
    // Should remove script tags but keep safe content
    expect(screen.getByText('Safe content')).toBeInTheDocument()
    expect(screen.queryByText('alert("xss")')).not.toBeInTheDocument()
  })

  it('sanitizes event handlers', () => {
    const maliciousContent = '<img src="x" onerror="alert(\'xss\')" alt="test">'
    
    const sanitized = DOMPurify.sanitize(maliciousContent)
    expect(sanitized).toBe('<img src="x" alt="test">')
  })

  it('allows safe HTML tags', () => {
    const safeContent = '<p>This is <strong>safe</strong> content with <em>emphasis</em></p>'
    
    const sanitized = DOMPurify.sanitize(safeContent)
    expect(sanitized).toBe(safeContent)
  })
})
```

## Performance Testing

### Web Vitals Testing

```typescript
// src/__tests__/performance/web-vitals.test.ts
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

describe('Web Vitals Performance', () => {
  it('measures Core Web Vitals', async () => {
    const vitals = {}
    
    // Mock performance API
    global.performance = {
      ...global.performance,
      mark: jest.fn(),
      measure: jest.fn(),
      getEntriesByType: jest.fn(() => []),
    }
    
    // Test LCP
    getLCP((metric) => {
      vitals.lcp = metric.value
      expect(metric.value).toBeLessThan(4000) // 4 seconds threshold
    })
    
    // Test FID
    getFID((metric) => {
      vitals.fid = metric.value
      expect(metric.value).toBeLessThan(100) // 100ms threshold
    })
    
    // Test CLS
    getCLS((metric) => {
      vitals.cls = metric.value
      expect(metric.value).toBeLessThan(0.1) // 0.1 threshold
    })
  })
})
```

### Bundle Size Testing

```typescript
// src/__tests__/performance/bundle-size.test.ts
import { analyzeBundleSize } from '@/lib/performance/bundle-analyzer'

describe('Bundle Size Performance', () => {
  it('keeps main bundle under size limit', async () => {
    const bundleStats = await analyzeBundleSize()
    
    expect(bundleStats.main.size).toBeLessThan(250 * 1024) // 250KB
    expect(bundleStats.vendor.size).toBeLessThan(500 * 1024) // 500KB
  })

  it('identifies large dependencies', async () => {
    const bundleStats = await analyzeBundleSize()
    
    const largeDependencies = bundleStats.dependencies.filter(
      dep => dep.size > 50 * 1024 // 50KB
    )
    
    // Log large dependencies for review
    largeDependencies.forEach(dep => {
      console.warn(`Large dependency: ${dep.name} (${dep.size} bytes)`)
    })
    
    // Should have fewer than 10 large dependencies
    expect(largeDependencies.length).toBeLessThan(10)
  })
})
```

## Accessibility Testing (v15.7.5)

### WCAG AA Touch Target Testing

**🎯 Comprehensive Accessibility Compliance Testing**

The v15.7.5 release includes automated testing for WCAG AA touch target compliance across all interactive elements.

#### Touch Target Test Suite

```typescript
// tests/accessibility/touch-targets.test.tsx
import { render, screen } from '@testing-library/react'
import { Button } from '@/components/ui/button'
import { Header } from '@/components/layout/header'
import { FilterControls } from '@/components/search/FilterControls'
import { Pagination } from '@/components/ui/pagination'

describe('WCAG AA Touch Target Compliance', () => {
  const MIN_TOUCH_TARGET = 44; // pixels

  // Test helper to check element dimensions
  const checkTouchTarget = (element: HTMLElement) => {
    const styles = window.getComputedStyle(element)
    const minHeight = parseInt(styles.minHeight) || parseInt(styles.height)
    const minWidth = parseInt(styles.minWidth) || parseInt(styles.width)
    
    return {
      height: minHeight,
      width: minWidth,
      compliant: minHeight >= MIN_TOUCH_TARGET && minWidth >= MIN_TOUCH_TARGET
    }
  }

  describe('Button Component', () => {
    it('all button sizes meet WCAG AA requirements', () => {
      const sizes = ['default', 'sm', 'lg', 'icon', 'touch', 'touch-wide']
      
      sizes.forEach(size => {
        render(<Button size={size as any}>Test</Button>)
        const button = screen.getByRole('button')
        const dimensions = checkTouchTarget(button)
        
        expect(dimensions.compliant).toBe(true)
        expect(dimensions.height).toBeGreaterThanOrEqual(MIN_TOUCH_TARGET)
        expect(dimensions.width).toBeGreaterThanOrEqual(MIN_TOUCH_TARGET)
      })
    })
  })

  describe('Navigation Components', () => {
    it('header navigation links meet touch target requirements', () => {
      render(<Header />)
      
      // Mobile menu button
      const mobileMenu = screen.getByLabelText(/menu/i)
      const mobileDimensions = checkTouchTarget(mobileMenu)
      expect(mobileDimensions.compliant).toBe(true)
      
      // Desktop navigation links
      const navLinks = screen.getAllByRole('link')
      navLinks.forEach(link => {
        const dimensions = checkTouchTarget(link)
        expect(dimensions.height).toBeGreaterThanOrEqual(MIN_TOUCH_TARGET)
      })
    })
  })

  describe('Pagination Component', () => {
    it('pagination buttons meet touch target requirements', () => {
      render(
        <Pagination 
          currentPage={2} 
          totalPages={5} 
          onPageChange={jest.fn()} 
        />
      )
      
      const buttons = screen.getAllByRole('button')
      buttons.forEach(button => {
        const dimensions = checkTouchTarget(button)
        expect(dimensions.compliant).toBe(true)
      })
    })
  })

  describe('Filter Controls', () => {
    it('filter buttons meet touch target requirements', () => {
      render(
        <FilterControls 
          showFilters={false} 
          setShowFilters={jest.fn()} 
        />
      )
      
      const filterButton = screen.getByRole('button')
      const dimensions = checkTouchTarget(filterButton)
      expect(dimensions.compliant).toBe(true)
    })
  })
})
```

#### Automated Accessibility Audits

```typescript
// tests/accessibility/lighthouse-a11y.test.ts
import { test, expect } from '@playwright/test'

test.describe('Lighthouse Accessibility Audits', () => {
  test('homepage meets accessibility standards', async ({ page }) => {
    await page.goto('/')
    
    // Run Lighthouse accessibility audit
    const lighthouse = await page.evaluate(() => {
      return new Promise((resolve) => {
        // @ts-ignore
        lighthouse('http://localhost:3000', {
          onlyCategories: ['accessibility'],
          port: 9222,
        }).then(resolve)
      })
    })
    
    expect(lighthouse.lhr.categories.accessibility.score).toBeGreaterThan(0.95)
  })

  test('product pages meet accessibility standards', async ({ page }) => {
    await page.goto('/products')
    
    // Check all interactive elements have proper touch targets
    const interactiveElements = await page.locator('button, a, [role="button"]').all()
    
    for (const element of interactiveElements) {
      const box = await element.boundingBox()
      if (box) {
        expect(box.height).toBeGreaterThanOrEqual(44)
        expect(box.width).toBeGreaterThanOrEqual(44)
      }
    }
  })
})
```

#### Touch Target Validation Commands

```bash
# Run accessibility tests
npm run test:accessibility

# Run touch target specific tests
npm run test -- --testNamePattern="touch.target"

# Run Lighthouse accessibility audit
npm run audit:accessibility

# Run complete accessibility test suite
npm run test:e2e -- tests/accessibility/
```

### Accessibility Testing Integration

**Jest Configuration for Accessibility:**
```javascript
// jest.config.js - accessibility testing setup
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/tests/setup/accessibility.ts'],
  // ... other config
}
```

**Accessibility Test Setup:**
```typescript
// tests/setup/accessibility.ts
import '@testing-library/jest-dom'

// Custom matchers for accessibility testing
expect.extend({
  toMeetWCAGTouchTarget(received: HTMLElement) {
    const styles = window.getComputedStyle(received)
    const minHeight = parseInt(styles.minHeight) || parseInt(styles.height)
    const minWidth = parseInt(styles.minWidth) || parseInt(styles.width)
    
    const pass = minHeight >= 44 && minWidth >= 44
    
    return {
      message: () => 
        pass 
          ? `Expected element not to meet WCAG touch target (44x44px), but got ${minWidth}x${minHeight}px`
          : `Expected element to meet WCAG touch target (44x44px), but got ${minWidth}x${minHeight}px`,
      pass,
    }
  },
})
```

## Test Data Management

### Fixtures and Mocks

```typescript
// tests/fixtures/products.ts
export const mockProducts = [
  {
    id: '1',
    name: 'Samsung Galaxy S23',
    slug: 'samsung-galaxy-s23',
    brand: {
      id: '1',
      name: 'Samsung',
      slug: 'samsung',
      logoUrl: 'https://example.com/samsung-logo.jpg'
    },
    cashbackAmount: 50,
    minPrice: 899,
    // ... other properties
  },
  // ... more mock products
]

export const mockBrands = [
  {
    id: '1',
    name: 'Samsung',
    slug: 'samsung',
    logoUrl: 'https://example.com/samsung-logo.jpg',
    featured: true,
    // ... other properties
  },
  // ... more mock brands
]
```

### Test Database Setup

```typescript
// tests/setup/database.ts
import { createClient } from '@supabase/supabase-js'

export const setupTestDatabase = async () => {
  const supabase = createClient(
    process.env.SUPABASE_TEST_URL!,
    process.env.SUPABASE_TEST_KEY!
  )
  
  // Clean up existing test data
  await supabase.from('products').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  await supabase.from('brands').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  
  // Insert test data
  await supabase.from('brands').insert(mockBrands)
  await supabase.from('products').insert(mockProducts)
}

export const teardownTestDatabase = async () => {
  const supabase = createClient(
    process.env.SUPABASE_TEST_URL!,
    process.env.SUPABASE_TEST_KEY!
  )
  
  await supabase.from('products').delete().neq('id', '00000000-0000-0000-0000-000000000000')
  await supabase.from('brands').delete().neq('id', '00000000-0000-0000-0000-000000000000')
}
```

## Running Tests

### Command Reference

```bash
# Run all tests
npm test

# Run specific test types
npm run test:unit
npm run test:integration
npm run test:e2e
npm run test:security

# Run with coverage
npm run test:coverage

# Run in watch mode
npm run test:watch

# Run specific test file
npm test -- ProductCard.test.tsx

# Run tests matching pattern
npm test -- --testNamePattern="should render correctly"
```

### CI/CD Integration

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run unit tests
      run: npm run test:unit
    
    - name: Run integration tests
      run: npm run test:integration
    
    - name: Run E2E tests
      run: npm run test:e2e
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
```

## Best Practices

### Test Organization
1. **Follow AAA pattern**: Arrange, Act, Assert
2. **Use descriptive test names**: What is being tested and expected outcome
3. **Test behavior, not implementation**: Focus on what the code does, not how
4. **Keep tests independent**: Each test should be able to run in isolation
5. **Use proper mocking**: Mock external dependencies, not internal logic

### Performance Considerations
1. **Minimize test setup**: Use beforeAll for expensive operations
2. **Parallel execution**: Run tests in parallel where possible
3. **Selective testing**: Use focused tests during development
4. **Cleanup resources**: Properly clean up after tests

### Security Testing
1. **Test all input points**: Validate all user inputs
2. **Test edge cases**: Empty strings, null values, extreme lengths
3. **Test malicious inputs**: XSS, SQL injection, script injection
4. **Test authentication**: Verify proper access controls

## Next Steps / TODO

- [ ] Add visual regression testing with Percy/Chromatic
- [ ] Implement contract testing between frontend and API
- [ ] Add performance regression testing
- [x] ✅ **COMPLETED**: Implement accessibility testing with axe-core (v15.7.5)
- [ ] Add mobile-specific E2E tests
- [ ] Implement load testing with k6
- [ ] Add database performance testing
- [ ] Implement mutation testing for code quality
- [ ] Add API documentation testing
- [ ] Implement security penetration testing automation

### Revision History
- **[07 AUG 2025]**: Added Accessibility Testing section with WCAG AA touch target compliance testing suite for v15.7.5.