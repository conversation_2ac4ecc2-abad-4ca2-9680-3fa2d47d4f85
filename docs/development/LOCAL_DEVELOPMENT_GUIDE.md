# Local Development Guide

*Complete guide for setting up and working with the Cashback Deals v2 codebase locally. Last updated: July 21, 2025*

## 🚀 Quick Start Commands

### **Daily Development Workflow**
```bash
# Fresh start for development (most common)
npm run clean && npm run dev

# When you need to test build changes during development
npm run clean && NODE_ENV=test npm run build && npm run start

# Complete cache clear and fresh development start
rm -rf .next node_modules package-lock.json && npm install && npm run dev
```

### **Production Testing Locally**
```bash
# Standard production build (requires proper authentication setup)
npm run clean:build && npm run start

# Quick production preview
npm run preview

# Production build bypassing authentication (for testing UI/functionality)
npm run clean && NODE_ENV=test npm run build && npm run start
```

## 📋 Complete Command Reference

### **Development Commands**

| Command | Purpose | When to Use |
|---------|---------|-------------|
| `npm run dev` | Start development server | Daily development work |
| `npm run clean && npm run dev` | Clean build cache + dev | After dependency changes or build issues |
| `rm -rf .next && npm run dev` | Hard cache clear + dev | When experiencing weird build issues |

### **Build Commands**

| Command | Purpose | When to Use |
|---------|---------|-------------|
| `npm run build` | Production build (with auth) | Testing production-ready builds |
| `NODE_ENV=test npm run build` | Build bypassing security guard-rails | Testing without authentication setup |
| `npm run clean:build` | Clean + build | After major changes or dependency updates |
| `CI=true npm run build` | CI-style build (bypasses guard-rails) | Simulating CI environment locally |

### **Server Commands**

| Command | Purpose | When to Use |
|---------|---------|-------------|
| `npm run start` | Start production server | After `npm run build` |
| `npm run preview` | Build + start production | Quick production testing |
| `NODE_ENV=production npm run start` | Force production mode | Testing production environment variables |

### **Cache Management Commands**

| Command | Purpose | When to Use |
|---------|---------|-------------|
| `npm run clean` | Remove .next directory | Build issues or stale cache |
| `rm -rf .next` | Hard remove build cache | Severe caching issues |
| `rm -rf node_modules package-lock.json && npm install` | Complete dependency reset | Package conflicts or corruption |
| `rm -rf .next node_modules package-lock.json && npm ci` | Nuclear reset | When nothing else works |

### **Testing Commands**

| Command | Purpose | When to Use |
|---------|---------|-------------|
| `npm run test` | Run all tests | Before committing changes |
| `npm run test:watch` | Run tests in watch mode | During test development |
| `npm run test:coverage` | Run tests with coverage | Checking test coverage |
| `npm run test:ci` | Run tests in CI mode | CI-compatible test run |

### **Quality Commands**

| Command | Purpose | When to Use |
|---------|---------|-------------|
| `npm run lint` | Run ESLint | Before committing |
| `npx eslint --fix` | Auto-fix ESLint issues | Fixing linting errors |
| `npm audit --audit-level=high --production` | Security audit | Checking for vulnerabilities |

### **Advanced Commands**

| Command | Purpose | When to Use |
|---------|---------|-------------|
| `npm run seo:test` | Run SEO tests | SEO optimization work |
| `npm run audit:seo` | Run Lighthouse SEO audit | Performance testing |
| `npm run audit:performance` | Run performance audit | Performance optimization |
| `npm run performance:check` | Check Web Vitals | Performance monitoring |

## ⚙️ Environment Configuration

### **Understanding Security Guard-Rails**

The application has built-in security protection that prevents test-only bypass flags from being used in production builds. This is by design to prevent security vulnerabilities in production.

**How it works:**
- During `npm run build`, Next.js sets `NODE_ENV=production`
- Security guard-rails check for unsafe environment variables
- Build fails if test-only bypass flags are detected

**Bypass methods for local development:**
```bash
# Method 1: Use test environment
NODE_ENV=test npm run build

# Method 2: Use CI environment flag
CI=true npm run build

# Method 3: Use development mode
npm run dev  # Never triggers security guard-rails
```

### **Local Environment Setup (.env.local)**

Create `.env.local` with these recommended settings for local development:

```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# Authentication Configuration for Local Development
# Production-safe build with CAPTCHA disabled for better DX
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
ENABLE_CAPTCHA=false

# Secrets (32+ characters required)
JWT_SECRET=dev-jwt-secret-minimum-32-characters-for-local-development
PARTNER_SECRET_DEFAULT=dev-default-secret-minimum-32-characters
PARTNER_SECRET_TEST_PARTNER=test-secret-minimum-32-characters-long

# Cloudflare Turnstile (test keys that always pass)
NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA

# IP Allowlist Configuration
ENABLE_IP_ALLOWLIST=true
IP_ALLOWLIST_CIDRS=10.0.0.0/8,**********/12,***********/16,127.0.0.1/32

# Development Settings
NEXT_PUBLIC_DEBUG_ENABLED=true
ENABLE_SENTRY=false
ENABLE_SENTRY_LOCAL=false
```

### **Environment Variables for Different Scenarios**

#### **Scenario 1: No Authentication Required (Development)**
For smooth development experience:
```bash
# In .env.local
ENABLE_SEARCH_AUTH=false
ENABLE_HMAC_AUTH=false
ENABLE_CAPTCHA=false

# Build command
NODE_ENV=test npm run build && npm run start
```

#### **Scenario 2: Full Authentication (Production-like)**
For production testing:
```bash
# In .env.local
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
ENABLE_CAPTCHA=true

# Build command
npm run build && npm run start
```

#### **Scenario 3: Partial Authentication (Recommended)**
Best balance for local development:
```bash
# In .env.local
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
ENABLE_CAPTCHA=false  # No annoying CAPTCHAs during development

# Build command
npm run build && npm run start
```

## 🔧 Troubleshooting Common Issues

### **Build Failures**

#### **Security Guard-Rail Violations**
```bash
🚨 SECURITY VIOLATION: Test-only bypass flags detected in production environment!
```

**Solution:**
```bash
# Use test environment for build
NODE_ENV=test npm run build

# Or update .env.local to use production-safe values
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
```

#### **Out of Memory Errors**
```bash
# Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build
```

#### **TypeScript Errors**
```bash
# Check types without building
npx tsc --noEmit

# Skip lib check for faster builds
npx tsc --noEmit --skipLibCheck
```

### **Server Issues**

#### **Port Already in Use**
```bash
# Kill process using port 3000
lsof -ti:3000 | xargs kill -9

# Or use different port
PORT=3001 npm run dev
```

#### **Authentication Errors**
```bash
# Check if authentication is properly configured
curl -f http://localhost:3000/api/health/sentry
```

### **Cache Issues**

#### **Stale Build Cache**
```bash
# Progressive cache clearing
npm run clean                    # Soft clear
rm -rf .next                    # Hard clear
rm -rf .next node_modules       # Nuclear clear
```

#### **Dependency Issues**
```bash
# Clear npm cache
npm cache clean --force

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install
```

### **Performance Issues**

#### **Slow Build Times**
```bash
# Analyze bundle size
ANALYZE=true npm run build

# Use faster development builds
npm run dev  # Much faster than build
```

#### **Memory Leaks During Development**
```bash
# Restart development server
# Kill with Ctrl+C and run:
npm run clean && npm run dev
```

## 🎯 Development Workflows

### **Feature Development Workflow**
```bash
# 1. Start development
npm run clean && npm run dev

# 2. Make changes and test
# Development server auto-reloads

# 3. Test production build (optional)
NODE_ENV=test npm run build && npm run start

# 4. Run quality checks
npm run lint
npm run test

# 5. Commit changes
git add .
git commit -m "Your change description"
```

### **Debugging Workflow**
```bash
# 1. Clear all caches
rm -rf .next node_modules package-lock.json

# 2. Fresh install
npm install

# 3. Check for issues
npm run lint
npm run test

# 4. Start development
npm run dev

# 5. If still issues, check environment
cat .env.local
```

### **Production Testing Workflow**
```bash
# 1. Test with authentication
npm run clean:build && npm run start

# 2. Test without authentication barriers
NODE_ENV=test npm run build && npm run start

# 3. Run audits
npm run audit:seo
npm run audit:performance

# 4. Check security
npm audit --audit-level=high --production
```

## 🚀 Performance Tips

### **Faster Development**

1. **Use Development Mode**: `npm run dev` is much faster than build + start
2. **Selective Cache Clearing**: Use `npm run clean` instead of `rm -rf node_modules`
3. **Parallel Development**: Use `--turbo` flag when available
4. **Memory Management**: Set `NODE_OPTIONS="--max-old-space-size=4096"`

### **Faster Builds**

1. **Use CI Environment**: `CI=true npm run build` (faster, less strict)
2. **Skip Type Checking**: Use `SKIP_TYPE_CHECK=true` for faster builds
3. **Incremental Builds**: Don't clear cache unnecessarily
4. **Build Optimization**: Use `npm run build` only when needed

### **Development Server Optimization**

1. **Port Management**: Use consistent ports to avoid conflicts
2. **File Watching**: Ensure file watchers aren't hitting limits
3. **Hot Reload**: Keep changes small for faster hot reload
4. **Resource Management**: Close unused browser tabs during development

---

## 📞 Quick Reference

**Most Common Commands:**
- Daily development: `npm run dev`
- Clean development: `npm run clean && npm run dev`
- Production test: `npm run preview`
- Auth-free test: `NODE_ENV=test npm run build && npm run start`
- Nuclear reset: `rm -rf .next node_modules && npm install && npm run dev`

**Emergency Commands:**
- Kill port 3000: `lsof -ti:3000 | xargs kill -9`
- Clear everything: `rm -rf .next node_modules package-lock.json && npm install`
- Skip security: `NODE_ENV=test npm run build`
- Memory boost: `NODE_OPTIONS="--max-old-space-size=4096" npm run build`