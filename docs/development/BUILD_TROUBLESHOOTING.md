# Build Troubleshooting Guide

*Comprehensive troubleshooting guide for build issues, deployment problems, and common errors. Last updated: July 21, 2025*

## 🎯 Overview

This guide covers all common build and deployment issues you might encounter, with step-by-step solutions and prevention strategies. It's organized by problem type for quick navigation.

## 🚨 Security Guard-Rail Issues

### **Security Violation Errors**

#### **Problem: Build Fails with Security Violation**
```bash
🚨 SECURITY VIOLATION: Test-only bypass flags detected in production environment!

The following unsafe environment variables are set:
  • ENABLE_SEARCH_AUTH=false is not allowed in production (NODE_ENV=production)
  • ENABLE_HMAC_AUTH=false is not allowed in production (NODE_ENV=production)
```

**Root Cause:** Security guard-rails preventing unsafe configurations in production builds.

**Solutions (in order of preference):**

**Option 1: Fix Environment Variables (Recommended)**
```bash
# Update .env.local with production-safe values
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
ENABLE_CAPTCHA=false  # Optional for local development

# Then build normally
npm run build
```

**Option 2: Use Test Environment**
```bash
# Single command bypass
NODE_ENV=test npm run build && npm run start

# Or set environment variable
export NODE_ENV=test
npm run build
npm run start
```

**Option 3: Use CI Flag**
```bash
# Bypass guard-rails with CI flag
CI=true npm run build && npm run start
```

**Option 4: Use Development Mode**
```bash
# No build required, fastest for development
npm run dev
```

#### **Problem: Can't Determine Which Variables Are Causing Issues**

**Debug Commands:**
```bash
# Check current environment
echo "NODE_ENV: $NODE_ENV"
echo "CI: $CI"
env | grep -E "(ENABLE_|TEST_MODE_|BYPASS_)"

# Check .env.local content (be careful with secrets)
grep -E "(ENABLE_|TEST_MODE_)" .env.local
```

**Systematic Fix:**
```bash
# 1. Create backup of current .env.local
cp .env.local .env.local.backup

# 2. Create minimal production-safe .env.local
cat > .env.local << 'EOF'
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
ENABLE_CAPTCHA=false
JWT_SECRET=dev-jwt-secret-minimum-32-characters-for-local-development
PARTNER_SECRET_DEFAULT=dev-default-secret-minimum-32-characters
NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA
EOF

# 3. Test build
npm run build

# 4. If successful, gradually add back other variables
```

### **Missing Required Variables**

#### **Problem: Required Environment Variable Missing**
```bash
Error: Required environment variable SUPABASE_SERVICE_ROLE_KEY is not set
```

**Solutions:**
```bash
# 1. Check if .env.local exists
ls -la .env.local

# 2. Create .env.local if missing
touch .env.local

# 3. Add required variables (minimum set)
cat >> .env.local << 'EOF'
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
JWT_SECRET=dev-jwt-secret-minimum-32-characters-for-local-development
EOF

# 4. Test
npm run build
```

#### **Problem: Secrets Too Short**
```bash
Error: JWT_SECRET must be at least 32 characters
```

**Solutions:**
```bash
# Generate secure secrets
JWT_SECRET=$(openssl rand -base64 32)
PARTNER_SECRET_DEFAULT=$(openssl rand -base64 32)

# Or use manual 32+ character strings
JWT_SECRET="dev-jwt-secret-minimum-32-characters-for-local-development"
PARTNER_SECRET_DEFAULT="dev-partner-secret-minimum-32-characters"

# Add to .env.local
echo "JWT_SECRET=$JWT_SECRET" >> .env.local
echo "PARTNER_SECRET_DEFAULT=$PARTNER_SECRET_DEFAULT" >> .env.local
```

## 🏗️ Build Process Issues

### **AWS Amplify Deployment Errors**

#### **Problem: "Cannot find module 'autoprefixer'" or Similar Dependencies**
```bash
Error: Cannot find module 'autoprefixer'
Require stack:
- /codebuild/output/src[...]/node_modules/next/dist/build/webpack/config/blocks/css/plugins.js
```

**Root Cause:** AWS Amplify using `npm install` (production-only) instead of `npm ci --production=false`

**✅ SOLUTION: Updated amplify.yml Configuration**
```yaml
# In amplify.yml preBuild phase (already fixed)
preBuild:
  commands:
    - npm ci --production=false --prefer-offline
```

**Why This Happens:**
- autoprefixer, postcss, and other CSS build tools are in devDependencies
- AWS Amplify defaults to production-only dependency installation
- Next.js requires these tools during the build process
- PostCSS configuration in `postcss.config.mjs` references autoprefixer

**Manual Fix (if not using updated amplify.yml):**
```yaml
# Update your amplify.yml file
version: 1
frontend:
  phases:
    preBuild:
      commands:
        - npm ci --production=false  # Include devDependencies
    build:
      commands:
        - npm run build
```

**Local Testing:**
```bash
# Test the same scenario locally
rm -rf node_modules
npm ci --production  # This will fail
npm run build        # Will get autoprefixer error

# Fix it
npm ci --production=false  # This works
npm run build             # Should succeed
```

#### **Problem: AWS Amplify Build Environment Issues**
```bash
Build failed with exit code 1
Node.js version mismatch
Environment variables not loading
```

**Comprehensive Solutions (implemented in updated amplify.yml):**
```yaml
# Node.js version management
- nvm install 20.10.0
- nvm use 20.10.0

# Environment variable validation
- echo "Validating critical environment variables..."
- |
  if [ -z "$NEXT_PUBLIC_SUPABASE_URL" ]; then
    echo "ERROR: NEXT_PUBLIC_SUPABASE_URL is required"
    exit 1
  fi

# Build optimization
- export NODE_OPTIONS="--max-old-space-size=8192"
- timeout 25m npm run build
```

**Debugging AWS Amplify Builds:**
1. Check build logs in AWS Amplify Console
2. Look for the **first** error (not cascading errors)
3. Verify environment variables are set correctly
4. Ensure Node.js version matches local development (20.10.0)

### **Out of Memory Errors**

#### **Problem: Build Runs Out of Memory**
```bash
FATAL ERROR: Reached heap limit Allocation failed - JavaScript heap out of memory
```

**Solutions:**

**Option 1: Increase Node.js Memory**
```bash
# Temporary fix
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build

# Or single command
NODE_OPTIONS="--max-old-space-size=4096" npm run build
```

**Option 2: Add to package.json**
```json
{
  "scripts": {
    "build": "NODE_OPTIONS='--max-old-space-size=4096' next build",
    "build:memory": "NODE_OPTIONS='--max-old-space-size=8192' next build"
  }
}
```

**Option 3: Use Development Mode**
```bash
# Development mode uses less memory
npm run dev
```

### **TypeScript Errors**

#### **Problem: TypeScript Compilation Errors**
```bash
Type error: Property 'xyz' does not exist on type 'ABC'
```

**Quick Fixes:**
```bash
# Check types without building
npx tsc --noEmit

# Skip lib check for faster validation
npx tsc --noEmit --skipLibCheck

# Skip type checking in build (temporary)
SKIP_TYPE_CHECK=true npm run build
```

**Systematic Fix:**
```bash
# 1. Clean TypeScript cache
rm -rf .next/types

# 2. Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# 3. Check TypeScript config
npx tsc --showConfig

# 4. Fix specific errors
npx tsc --noEmit --listFiles | grep error
```

#### **Problem: Type Declaration Files Missing**
```bash
Cannot find type declaration file for package 'xyz'
```

**Solutions:**
```bash
# Install type definitions
npm install --save-dev @types/node @types/react @types/react-dom

# For specific packages
npm install --save-dev @types/package-name

# If types don't exist, create declaration
echo "declare module 'package-name'" > src/types/package-name.d.ts
```

### **Dependency Issues**

#### **Problem: Package Resolution Errors**
```bash
Module not found: Can't resolve 'xyz'
```

**Solutions:**
```bash
# 1. Clear npm cache
npm cache clean --force

# 2. Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install

# 3. Check if package is installed
npm list package-name

# 4. Install missing package
npm install package-name

# 5. Nuclear option - complete reset
rm -rf node_modules package-lock.json .next
npm install
```

#### **Problem: Version Conflicts**
```bash
ERESOLVE unable to resolve dependency tree
```

**Solutions:**
```bash
# 1. Check conflicting dependencies
npm ls

# 2. Force resolution (use with caution)
npm install --legacy-peer-deps

# 3. Override specific versions in package.json
"overrides": {
  "problematic-package": "^1.0.0"
}

# 4. Use exact versions
npm install package-name@1.0.0 --save-exact
```

### **Cache Issues**

#### **Problem: Stale Build Cache**
```bash
# Symptoms: Old code running, changes not reflected, weird errors
```

**Progressive Cache Clearing:**
```bash
# Level 1: Soft clear
npm run clean

# Level 2: Hard clear
rm -rf .next

# Level 3: Dependency clear
rm -rf .next node_modules
npm install

# Level 4: Nuclear clear
rm -rf .next node_modules package-lock.json .next/cache
npm cache clean --force
npm install
```

#### **Problem: Next.js Cache Issues**
```bash
# Symptoms: Hot reload not working, pages not updating
```

**Solutions:**
```bash
# 1. Clear Next.js cache
rm -rf .next/cache

# 2. Restart development server
pkill -f "next dev"
npm run dev

# 3. Force refresh browser cache
# In browser: Cmd+Shift+R (Mac) or Ctrl+Shift+R (Windows)

# 4. Disable caching during development
# Add to next.config.js:
webpack: (config) => {
  config.cache = false
  return config
}
```

## 🖥️ Server Issues

### **Port Issues**

#### **Problem: Port Already in Use**
```bash
Error: listen EADDRINUSE :::3000
```

**Solutions:**
```bash
# 1. Kill process using port
lsof -ti:3000 | xargs kill -9

# 2. Find what's using the port
lsof -i :3000
ps aux | grep :3000

# 3. Use different port
PORT=3001 npm run dev

# 4. Kill all Node processes (nuclear option)
pkill -f node
```

#### **Problem: Permission Denied on Port**
```bash
Error: listen EACCES 0.0.0.0:80
```

**Solutions:**
```bash
# 1. Use unprivileged port
PORT=3000 npm run dev

# 2. If you need port 80, use sudo (not recommended)
sudo npm run dev

# 3. Use port forwarding
# Forward port 80 to 3000
sudo iptables -t nat -A PREROUTING -p tcp --dport 80 -j REDIRECT --to-port 3000
```

### **Authentication and API Issues**

#### **Problem: 401 Unauthorized Errors**
```bash
GET /api/search/more 401 (Unauthorized)
```

**Debug Steps:**
```bash
# 1. Check authentication settings
echo "ENABLE_SEARCH_AUTH: $ENABLE_SEARCH_AUTH"
echo "ENABLE_HMAC_AUTH: $ENABLE_HMAC_AUTH"

# 2. Check API endpoint
curl -I http://localhost:3000/api/health

# 3. Check authentication tokens
# In browser console:
localStorage.getItem('jwt_token')
```

**Solutions:**
```bash
# Option 1: Disable authentication for testing
ENABLE_SEARCH_AUTH=false
ENABLE_HMAC_AUTH=false
NODE_ENV=test npm run build && npm run start

# Option 2: Fix authentication setup
# Ensure proper JWT_SECRET and auth configuration

# Option 3: Use development mode
npm run dev  # Has better auth handling
```

#### **Problem: CAPTCHA Issues**
```bash
# Problem: CAPTCHA appearing on every Load More click
```

**Solutions:**
```bash
# 1. Disable CAPTCHA for development
ENABLE_CAPTCHA=false

# 2. Use test Turnstile keys
NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA

# 3. Check if keys are correctly configured
echo "Site key: $NEXT_PUBLIC_TURNSTILE_SITE_KEY"
echo "Secret key: ${TURNSTILE_SECRET_KEY:0:10}..."
```

### **Database Connection Issues**

#### **Problem: Supabase Connection Errors**
```bash
Error: connect ECONNREFUSED
```

**Debug Steps:**
```bash
# 1. Check Supabase URL
echo "Supabase URL: $NEXT_PUBLIC_SUPABASE_URL"
curl -I "$NEXT_PUBLIC_SUPABASE_URL/rest/v1/"

# 2. Test connection
curl -H "Authorization: Bearer $NEXT_PUBLIC_SUPABASE_ANON_KEY" \
     "$NEXT_PUBLIC_SUPABASE_URL/rest/v1/health"

# 3. Check if keys are valid
# In browser: Go to your Supabase project settings
```

**Solutions:**
```bash
# 1. Update Supabase configuration
# Get new URL and keys from Supabase dashboard

# 2. Use mock configuration for testing
NEXT_PUBLIC_SUPABASE_URL=https://mock-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=mock-anon-key
SUPABASE_SERVICE_ROLE_KEY=mock-service-key

# 3. Test with different Supabase project
# Use development/staging project for testing
```

## 🚀 Deployment Issues

### **AWS Amplify Issues**

#### **Problem: Amplify Build Failing**
```bash
Build failed with exit code 1
```

**Debug Steps:**
```bash
# 1. Check Amplify build logs
# Go to AWS Amplify Console → App → Build history

# 2. Check environment variables in Amplify
# Ensure all required variables are set

# 3. Test build locally with same configuration
export NODE_ENV=production
# Set all production environment variables
npm run build
```

**Solutions:**
```bash
# 1. Check amplify.yml configuration
# Ensure correct Node.js version and build commands

# 2. Add debug output to build
preBuild:
  commands:
    - echo "NODE_ENV: $NODE_ENV"
    - echo "Build starting..."
    - npm --version
    - node --version

# 3. Increase build timeout
build:
  commands:
    - timeout 30m npm run build
```

#### **Problem: Environment Variables Not Loading**
```bash
# Build succeeds but app doesn't work
```

**Solutions:**
```bash
# 1. Check variable names match exactly (case-sensitive)
NEXT_PUBLIC_SUPABASE_URL  # ✅ Correct
next_public_supabase_url  # ❌ Wrong

# 2. Ensure NEXT_PUBLIC_ prefix for client-side variables
NEXT_PUBLIC_SITE_URL      # ✅ Available in browser
SUPABASE_SERVICE_ROLE_KEY # ✅ Server-only

# 3. Check for special characters in values
# Escape or quote values with spaces/special chars
JWT_SECRET="value with spaces"
```

### **Domain and SSL Issues**

#### **Problem: SSL Certificate Errors**
```bash
NET::ERR_CERT_AUTHORITY_INVALID
```

**Solutions:**
```bash
# 1. Check certificate status
openssl s_client -connect yourdomain.com:443 -servername yourdomain.com

# 2. Check DNS propagation
dig yourdomain.com
nslookup yourdomain.com

# 3. Check Cloudflare SSL settings
# Ensure SSL/TLS mode is "Full (strict)"
```

#### **Problem: Domain Not Resolving**
```bash
# Site not accessible at custom domain
```

**Solutions:**
```bash
# 1. Check DNS records
dig +short yourdomain.com

# 2. Check Cloudflare proxy status
# Orange cloud = Proxied (recommended)
# Gray cloud = DNS only

# 3. Check Amplify domain configuration
# AWS Console → Amplify → Domain management
```

## 🔄 Performance Issues

### **Slow Build Times**

#### **Problem: Build Takes Too Long**
```bash
# Build taking 5+ minutes
```

**Solutions:**
```bash
# 1. Enable caching
# In package.json scripts:
"build": "next build --cache-dir=.next/cache"

# 2. Use parallel processing
npm run build -- --max-workers=4

# 3. Analyze bundle size
ANALYZE=true npm run build

# 4. Skip unnecessary steps during development
NODE_ENV=test npm run build  # Faster than production build
```

### **Runtime Performance Issues**

#### **Problem: App Running Slowly**
```bash
# Slow page loads, high CPU usage
```

**Debug Steps:**
```bash
# 1. Check Web Vitals in browser
# Chrome DevTools → Lighthouse

# 2. Check for memory leaks
# Chrome DevTools → Memory tab

# 3. Profile the application
# Chrome DevTools → Performance tab
```

**Solutions:**
```bash
# 1. Optimize images
# Ensure Next.js Image optimization is enabled

# 2. Check for infinite loops
grep -r "useEffect\|useState" src/ --include="*.tsx" --include="*.ts"

# 3. Optimize bundle size
ANALYZE=true npm run build
# Review bundle analyzer output

# 4. Enable production optimizations
NODE_ENV=production npm run start
```

## 🛠️ Development Environment Issues

### **IDE and Editor Issues**

#### **Problem: TypeScript Errors in IDE**
```bash
# IDE showing errors but build works
```

**Solutions:**
```bash
# 1. Restart TypeScript server
# VS Code: Cmd+Shift+P → "TypeScript: Restart TS Server"

# 2. Clear TypeScript cache
rm -rf node_modules/.cache/typescript

# 3. Check TypeScript version
npx tsc --version
npm ls typescript

# 4. Ensure IDE uses workspace TypeScript
# VS Code settings: "typescript.preferences.includePackageJsonAutoImports"
```

#### **Problem: Hot Reload Not Working**
```bash
# Changes not reflecting in development mode
```

**Solutions:**
```bash
# 1. Check file watchers limit (Linux/Mac)
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 2. Restart development server
pkill -f "next dev"
npm run dev

# 3. Clear browser cache
# Hard refresh: Cmd+Shift+R (Mac) or Ctrl+Shift+R (Windows)

# 4. Check .gitignore isn't excluding important files
cat .gitignore | grep -v "^#"
```

### **Git and Version Control Issues**

#### **Problem: Merge Conflicts in Generated Files**
```bash
# Conflicts in package-lock.json or .next files
```

**Solutions:**
```bash
# 1. For package-lock.json conflicts
rm package-lock.json
npm install

# 2. For .next conflicts (shouldn't happen if in .gitignore)
rm -rf .next
npm run build

# 3. Update .gitignore to prevent future conflicts
echo ".next/" >> .gitignore
echo "node_modules/" >> .gitignore
```

#### **Problem: Accidental Secret Commits**
```bash
# Committed .env.local or secrets to git
```

**Immediate Actions:**
```bash
# 1. Remove from current commit (if not pushed)
git reset --soft HEAD~1
git reset HEAD .env.local

# 2. Remove from git history (if pushed)
git filter-branch --force --index-filter \
  'git rm --cached --ignore-unmatch .env.local' \
  --prune-empty --tag-name-filter cat -- --all

# 3. Force push (dangerous - coordinate with team)
git push --force-with-lease

# 4. Rotate all exposed secrets immediately
# Update all API keys, tokens, and secrets
```

**Prevention:**
```bash
# 1. Ensure .gitignore is correct
echo ".env.local" >> .gitignore
echo ".env*.local" >> .gitignore

# 2. Use git hooks to prevent commits
# Install pre-commit hooks that scan for secrets
```

## 📊 Monitoring and Debugging

### **Logging and Debugging**

#### **Enable Debug Logging**
```bash
# In .env.local
NEXT_PUBLIC_DEBUG_ENABLED=true
NEXT_PUBLIC_DEBUG_LEVEL=verbose

# Runtime debugging
DEBUG=* npm run dev
```

#### **Check Application Health**
```bash
# Test API endpoints
curl http://localhost:3000/api/health
curl http://localhost:3000/api/health/sentry
curl http://localhost:3000/api/health/ip-allowlist

# Check build artifacts
ls -la .next/
ls -la .next/static/

# Monitor resource usage
top -pid $(pgrep -f "next")
```

### **Error Tracking**

#### **Sentry Integration**
```bash
# Test Sentry connection
curl -X POST "https://sentry.io/api/0/projects/$SENTRY_PROJECT_ID/events/" \
  -H "Authorization: Bearer $SENTRY_AUTH_TOKEN" \
  -d '{"message": "Test error"}'

# Check Sentry configuration
echo "SENTRY_DSN: ${SENTRY_DSN:0:50}..."
echo "SENTRY_ENVIRONMENT: $SENTRY_ENVIRONMENT"
```

## 📞 Emergency Procedures

### **Complete Reset (Nuclear Option)**

When all else fails:

```bash
# 1. Stop all processes
pkill -f node
pkill -f npm

# 2. Complete cleanup
rm -rf node_modules package-lock.json .next .next/cache
npm cache clean --force

# 3. Fresh install
npm install

# 4. Test basic functionality
npm run dev

# 5. If still broken, check environment
cp .env.local .env.local.backup
rm .env.local
npm run dev  # Test without any environment variables
```

### **Quick Diagnosis Commands**

```bash
# System information
node --version
npm --version
echo $NODE_ENV
pwd

# Project status
git status
git log --oneline -5
npm ls --depth=0

# Environment check
env | grep -E "(NODE_|NEXT_|SUPABASE_)" | head -10

# File system check
ls -la
du -sh node_modules .next 2>/dev/null

# Process check
ps aux | grep -E "(node|npm)"
netstat -tulpn | grep :3000
```

---

## 📞 Quick Reference

**Emergency Commands:**
- Nuclear reset: `rm -rf node_modules .next && npm install`
- Kill port 3000: `lsof -ti:3000 | xargs kill -9`
- Bypass security: `NODE_ENV=test npm run build`
- Memory boost: `NODE_OPTIONS="--max-old-space-size=4096" npm run build`
- Clear cache: `npm cache clean --force`

**Diagnosis Commands:**
- Check environment: `env | grep NODE_ENV`
- Test connection: `curl -I http://localhost:3000`
- Check dependencies: `npm ls --depth=0`
- Memory usage: `ps aux | grep node`

**Common Fixes:**
- Security violations → Set `NODE_ENV=test`
- Port conflicts → `lsof -ti:3000 | xargs kill -9`
- Memory errors → `NODE_OPTIONS="--max-old-space-size=4096"`
- Cache issues → `rm -rf .next && npm run dev`
- Auth errors → Check `ENABLE_*_AUTH` variables