<!--
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: MOVED from root directory to docs/security/
📁 ORIGINAL LOCATION: /security.md  
📁 NEW LOCATION: /docs/security/SECURITY_POLICY.md
🎯 REASON: Security policy belongs in dedicated security documentation section
📝 STATUS: Content preserved and enhanced with proper formatting
👥 REVIEW REQUIRED: Security team should verify contact details and response times before production
🏷️ CATEGORY: Security - Vulnerability Disclosure Policy
📅 IMPORTANCE: Critical for production security incident response
-->

# Security Policy

## Vulnerability Disclosure

If you find a security vulnerability in this project, please report it responsibly by emailing: **<EMAIL>**

## Response Times

- **Critical issues**: Fixed within 24 hours
- **High severity issues**: Fixed within 72 hours  
- **Medium/Low severity**: Addressed according to regular development cycle

## Automated Security Tools

This project uses the following automated security scanning tools:

- **Dependabot**: Automated dependency vulnerability scanning
- **npm audit**: Node.js package vulnerability detection
- **Snyk**: Advanced vulnerability scanning (if enabled)

## Security Best Practices

Please follow responsible disclosure practices:

1. **Do not** publicly disclose vulnerabilities before they are fixed
2. **Provide** detailed information about the vulnerability
3. **Include** steps to reproduce the issue
4. **Allow** reasonable time for fixes before public disclosure

## Scope

This security policy covers the main application and its dependencies. Out of scope items include:

- Issues in third-party services (Supabase, AWS, etc.)
- Social engineering attacks
- Physical security issues

Thank you for helping keep our application secure.