<!--
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: ARCHIVED from docs/UPDATES/ to docs/archive/development_notes/misc_implementations/
📁 ORIGINAL LOCATION: /docs/UPDATES/SimilarProductsCashbackInvestigation.md
📁 NEW LOCATION: /docs/archive/development_notes/misc_implementations/SimilarProductsCashbackInvestigation.md
🎯 REASON: Historical development documentation for cashback display investigation
📝 STATUS: Content preserved unchanged, archived as development implementation note
👥 REVIEW REQUIRED: Development team can reference for similar products component debugging and cashback data flow methodology
🏷️ CATEGORY: Archive - Development Notes (Component Implementation)
📅 PURPOSE: Historical record of investigation into missing cashback amounts and brand names in similar products section
-->

# Investigation and Plan for Missing Cashback Amounts in Similar Products Section

## Background

When loading the product detail page, the "Similar Products" section does not display cashback amounts for the products shown or the brand name (instead is renderes "unknow brand). The main product's cashback amount and brand name displays correctly, but the similar products lack this information.

## Root Cause Analysis

- The `SimilarProducts` component receives a list of products as props and passes each product to the `ProductCard` component.
- The `ProductCard` component correctly renders the cashback amount if the `cashbackAmount` field is present on the product object.
- The `SimilarProducts` component ensures a default cashbackAmount of 0 if missing, but this does not solve the issue because the source data lacks the cashbackAmount.
- The product detail page fetches product data and similar products data via the `getProductPageData` function in `src/lib/data/products.ts`.
- The `getProductPageData` function calls `getSimilarProducts` to fetch similar products.
- The `getSimilarProducts` function fetches products by category but **does not fetch or transform the `cashback_amount` or promotion fields**.
- As a result, the similar products passed down to the UI components lack the `cashbackAmount` field.
- The `ProductPageClient` component passes similar products directly to `SimilarProducts` without adding or transforming cashback data.

## Brand Name Missing Hypothesis

- It has been observed that the brand name is also missing in the similar products carousel on the product details page.
- The hypothesis is that the missing cashback amounts may be related to the brand name not being fetched or rendered correctly.
- The brand name is a key part of the product data and is used in the `ProductCard` component to display the brand.
- If the brand data is missing or incomplete, it may indicate that the similar products data is not fully populated from the backend.
- This incomplete data could also explain why cashback amounts are missing, as cashback is often linked to promotions and brand-specific offers.

## Current Logic for Brand Rendering from Backend

- The `getSimilarProducts` function fetches similar products but only selects `retailer_offers` and does not include brand or promotion data.
- In contrast, functions like `getProduct` and `getProductBySlug` fetch brand data with fields like `id`, `name`, `slug`, `logo_url`, and `description`.
- The lack of brand data in similar products means the UI components receive incomplete product objects.
- The `ProductCard` component attempts to render `product.brand?.name` but if `brand` is null or undefined, the brand name will not show.
- This missing brand data is consistent with the missing cashback amounts, both stemming from incomplete data fetching and transformation.

## Why Include Cashback Amounts in Similar Products?

- Cashback amounts are a key value proposition for users comparing deals.
- Displaying cashback on similar products improves user experience and engagement.
- Consistency in UI: all product cards should show cashback if available.
- Missing cashback amounts may reduce user trust or perceived value.

## Plan for Investigation and Fix

1. **Investigate and Update Data Fetching:**
   - Modify `getSimilarProducts` to fetch `cashback_amount`, brand, and relevant promotion fields from the database.
   - Ensure the query includes these fields alongside existing data.

2. **Transform Similar Products Data:**
   - Update the transformation logic in `getSimilarProducts` to map `cashback_amount` to `cashbackAmount` on the product object.
   - Include brand and promotion data transformation similar to `getProduct` and `getProductBySlug`.

3. **Verify Data Flow in Components:**
   - Confirm that `ProductPageClient` passes the transformed similar products correctly to `SimilarProducts`.
   - Confirm that `SimilarProducts` passes products with cashbackAmount and brand to `ProductCard`.

4. **Testing:**
   - Add or update tests to verify cashbackAmount and brand presence in similar products data.
   - Test UI rendering to confirm cashback amounts and brand names display correctly in the similar products section.

## Riskiest Assumptions

- Assuming the database schema supports fetching `cashback_amount`, brand, and promotion fields for similar products.
- Assuming the transformation logic for cashbackAmount, brand, and promotion can be reused or adapted without side effects.
- Assuming no performance degradation occurs due to additional fields fetched for similar products.
- Assuming UI components handle the cashbackAmount and brand fields consistently.

## Next Steps

- Implement the data fetching and transformation changes in `getSimilarProducts`.
- Verify and update component props and rendering as needed.
- Thoroughly test the changes in both unit tests and UI.
- Monitor for any performance or data consistency issues post-deployment.

---

This document serves as a comprehensive update on the investigation and planned resolution for the missing cashback amounts and brand names in the similar products section on the product detail page.
