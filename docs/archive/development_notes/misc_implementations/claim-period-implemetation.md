<!--
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: ARCHIVED from docs/UPDATES/ to docs/archive/development_notes/misc_implementations/
📁 ORIGINAL LOCATION: /docs/UPDATES/claim-period-implementation.md
📁 NEW LOCATION: /docs/archive/development_notes/misc_implementations/claim-period-implemetation.md
🎯 REASON: Historical development documentation for claim period implementation (NOTE: File appears corrupted with incomplete XML-like content)
📝 STATUS: Content preserved unchanged, archived as development implementation note - REQUIRES CLEANUP
👥 REVIEW REQUIRED: Development team should review and clean up corrupted content, extract valuable implementation details
🏷️ CATEGORY: Archive - Development Notes (Implementation Planning - CORRUPTED)
📅 PURPOSE: Historical record of claim period refactoring implementation plan - FILE NEEDS RESTORATION
⚠️ ISSUE: File contains incomplete XML-like invoke tags and may need restoration from backup
-->

<invoke name="propose_code">
<parameter name="TargetFile">/Users/<USER>/cashback-deals-v2 copy/docs/UPDATES/claim-period-implementation.md</parameter>
<parameter name="Instruction">Improve the formatting and organization of the implementation document with proper markdown structure, code blocks, and consistent spacing.</parameter>
<parameter name="CodeMarkdownLanguage">markdown</parameter>
<parameter name="CodeEdit"># Implementation Plan: Claim Period Refactoring

## Overview
This document outlines the plan to refactor the claim period implementation, centralize data transformation, and fix type safety issues across the codebase.

## Impact Analysis

### Breaking Changes

#### Type Changes
- `params` type in page components will be updated to non-Promise
- `product` prop in [ProductInfo](cci:1://file:///Users/<USER>/cashback-deals-v2%20copy/src/app/products/components/ProductInfo.tsx:17:0-284:1) will be strictly typed
- All product-fetching functions will return consistent data structure

#### Data Flow
- Claim period calculation moves from client to server
- Transformation logic consolidated in [products.ts](cci:7://file:///Users/<USER>/cashback-deals-v2%20copy/src/lib/data/products.ts:0:0-0:0)

## Implementation Tasks

### Phase 1: Centralize Data Transformation
- [ ] **Refactor [products.ts](cci:7://file:///Users/<USER>/cashback-deals-v2%20copy/src/lib/data/products.ts:0:0-0:0)**
  - [ ] Ensure all product-fetching functions use [transformProduct](cci:1://file:///Users/<USER>/cashback-deals-v2%20copy/src/lib/data/products.ts:473:0-572:1)
  - [ ] Remove duplicate transformation logic
  - [ ] Add comprehensive JSDoc comments

- [ ] **Enhance [transformProduct](cci:1://file:///Users/<USER>/cashback-deals-v2%20copy/src/lib/data/products.ts:473:0-572:1)**
  - [ ] Handle both `snake_case` and `camelCase` fields
  - [ ] Add claim period calculation
  - [ ] Include input validation

### Phase 2: Server-Side Implementation
- [ ] **Update Page Components**
  - [ ] Fix `params` type in `[id]/page.tsx`
  - [ ] Remove `Promise` wrapper from params type
  - [ ] Remove `await` when destructuring params

- [ ] **Enhance Data Fetching**
  - [ ] Add claim period calculation to server-side queries
  - [ ] Ensure proper error handling
  - [ ] Add input validation

### Phase 3: Client-Side Updates
- [ ] **Update [ProductInfo](cci:1://file:///Users/<USER>/cashback-deals-v2%20copy/src/app/products/components/ProductInfo.tsx:17:0-284:1) Component**
  - [ ] Add proper TypeScript types
  - [ ] Remove client-side claim period calculation
  - [ ] Display pre-calculated claim period

- [ ] **Testing**
  - [ ] Unit tests for [transformProduct](cci:1://file:///Users/<USER>/cashback-deals-v2%20copy/src/lib/data/products.ts:473:0-572:1)
  - [ ] Integration tests for product pages
  - [ ] E2E tests for claim period display

## Technical Implementation

### Data Transformation Example
```typescript
// In src/lib/data/products.ts
export function transformProduct(product: any): TransformedProduct {
  if (!product) return null;

  const promo = product.promotion || {};
  const claimStartOffsetDays = 
    promo.claimStartOffsetDays ?? promo.claim_start_offset_days;
  const claimWindowDays = 
    promo.claimWindowDays ?? promo.claim_window_days;
  const purchaseEndDate = 
    promo.purchaseEndDate ?? promo.purchase_end_date;

  // Calculate claim period
  let claimPeriod = null;
  if (purchaseEndDate && claimStartOffsetDays != null && claimWindowDays) {
    const startDate = new Date(purchaseEndDate);
    startDate.setDate(startDate.getDate() + claimStartOffsetDays);
    
    const endDate = new Date(startDate);
    endDate.setDate(endDate.getDate() + claimWindowDays);
    
    claimPeriod = { 
      startDate: startDate.toISOString(), 
      endDate: endDate.toISOString(),
      formatted: `${formatDate(startDate)} - ${formatDate(endDate)}`
    };
  }

  return {
    ...product,
    promotion: {
      ...promo,
      claimPeriod
    }
  };
}


// In src/lib/data/types.ts
interface ClaimPeriod {
  startDate: string;  // ISO format
  endDate: string;    // ISO format
  formatted: string;  // Human-readable
}

interface Promotion {
  // Existing fields...
  claimPeriod?: ClaimPeriod | null;
}

interface TransformedProduct {
  // Existing fields...
  promotion?: Promotion | null;
}



