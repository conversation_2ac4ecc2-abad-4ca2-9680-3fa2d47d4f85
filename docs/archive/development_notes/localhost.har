{"log": {"version": "1.2", "creator": {"name": "WebInspector", "version": "537.36"}, "pages": [], "entries": [{"_connectionId": "326", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "createFetch", "scriptId": "99", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js", "lineNumber": 153, "columnNumber": 11}, {"functionName": "fetchServerResponse", "scriptId": "99", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js", "lineNumber": 81, "columnNumber": 26}, {"functionName": "eval", "scriptId": "127", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js", "lineNumber": 190, "columnNumber": 105}, {"functionName": "task", "scriptId": "124", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js", "lineNumber": 29, "columnNumber": 37}, {"functionName": "processNext", "scriptId": "124", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js", "lineNumber": 80, "columnNumber": 185}, {"functionName": "enqueue", "scriptId": "124", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js", "lineNumber": 44, "columnNumber": 75}, {"functionName": "createLazyPrefetchEntry", "scriptId": "127", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js", "lineNumber": 190, "columnNumber": 48}, {"functionName": "getOrCreatePrefetchCacheEntry", "scriptId": "127", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js", "lineNumber": 137, "columnNumber": 11}, {"functionName": "navigateReducer", "scriptId": "98", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js", "lineNumber": 143, "columnNumber": 81}, {"functionName": "clientReducer", "scriptId": "97", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js", "lineNumber": 24, "columnNumber": 60}, {"functionName": "action", "scriptId": "96", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js", "lineNumber": 129, "columnNumber": 54}, {"functionName": "runAction", "scriptId": "96", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js", "lineNumber": 41, "columnNumber": 37}, {"functionName": "dispatchAction", "scriptId": "96", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js", "lineNumber": 95, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "96", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js", "lineNumber": 127, "columnNumber": 39}, {"functionName": "eval", "scriptId": "131", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/use-reducer.js", "lineNumber": 36, "columnNumber": 20}, {"functionName": "eval", "scriptId": "128", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js", "lineNumber": 133, "columnNumber": 15}, {"functionName": "eval", "scriptId": "128", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js", "lineNumber": 231, "columnNumber": 20}, {"functionName": "exports.startTransition", "scriptId": "58", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react.development.js", "lineNumber": 1426, "columnNumber": 26}, {"functionName": "push", "scriptId": "128", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js", "lineNumber": 229, "columnNumber": 43}, {"functionName": "navigate", "scriptId": "208", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "lineNumber": 67, "columnNumber": 48}, {"functionName": "exports.startTransition", "scriptId": "58", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react.development.js", "lineNumber": 1426, "columnNumber": 26}, {"functionName": "linkClicked", "scriptId": "208", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "lineNumber": 72, "columnNumber": 19}, {"functionName": "onClick", "scriptId": "208", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "lineNumber": 303, "columnNumber": 12}, {"functionName": "processDispatchQueue", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 16145, "columnNumber": 16}, {"functionName": "eval", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 16748, "columnNumber": 8}, {"functionName": "batchedUpdates$1", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 3129, "columnNumber": 39}, {"functionName": "dispatchEventForPluginEventSystem", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 16304, "columnNumber": 6}, {"functionName": "dispatchEvent", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 20399, "columnNumber": 10}, {"functionName": "dispatchDiscreteEvent", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 20367, "columnNumber": 10}]}}, "_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "3001", "request": {"method": "GET", "url": "http://localhost:3001/brands?_rsc=rxx9e", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "localhost:3001"}, {"name": "Next-Router-State-Tree", "value": "%5B%22%22%2C%7B%22children%22%3A%5B%22__PAGE__%22%2C%7B%7D%2C%22%2F%22%2C%22refresh%22%5D%7D%2Cnull%2Cnull%2Ctrue%5D"}, {"name": "Next-Url", "value": "/"}, {"name": "Pragma", "value": "no-cache"}, {"name": "RSC", "value": "1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://localhost:3001/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"135\", \"Not:A Brand\";v=\"99\", \"Google Chrome\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"macOS\""}], "queryString": [{"name": "_rsc", "value": "rxx9e"}], "cookies": [], "headersSize": 740, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Cache-Control", "value": "no-store, must-revalidate"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Type", "value": "text/x-component"}, {"name": "Date", "value": "Fri, 27 Jun 2025 14:50:23 GMT"}, {"name": "Keep-Alive", "value": "timeout=5"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Vary", "value": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch, Accept-Encoding"}, {"name": "X-DNS-Prefetch-Control", "value": "on"}, {"name": "X-Frame-Options", "value": "DENY"}], "cookies": [], "content": {"size": 26030, "mimeType": "text/x-component", "compression": 19068}, "redirectURL": "", "headersSize": 384, "bodySize": 6962, "_transferSize": 7346, "_error": "net::ERR_ABORTED", "_fetchedViaServiceWorker": false}, "serverIPAddress": "[::1]", "startedDateTime": "2025-06-27T14:50:23.524Z", "time": 608.3809999981895, "timings": {"blocked": 2.075999955892563, "dns": -1, "ssl": -1, "connect": -1, "send": 0.04600000000000001, "wait": 457.7179999526888, "receive": 148.54100008960813, "_blocked_queueing": 1.8899999558925629, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "342", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "__webpack_require__.hmrM", "scriptId": "19", "url": "http://localhost:3001/_next/static/chunks/webpack.js?v=1751035806004", "lineNumber": 1366, "columnNumber": 19}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "hotCheck", "scriptId": "19", "url": "http://localhost:3001/_next/static/chunks/webpack.js?v=1751035806004", "lineNumber": 552, "columnNumber": 14}, {"functionName": "tryApplyUpdates", "scriptId": "563", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js", "lineNumber": 160, "columnNumber": 15}, {"functionName": "handleHotUpdate", "scriptId": "563", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js", "lineNumber": 199, "columnNumber": 12}, {"functionName": "processMessage", "scriptId": "563", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js", "lineNumber": 291, "columnNumber": 20}, {"functionName": "handler", "scriptId": "563", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js", "lineNumber": 507, "columnNumber": 16}]}}}, "_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "3001", "request": {"method": "GET", "url": "http://localhost:3001/_next/static/webpack/39c6fbbbd9c80a50.webpack.hot-update.json", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "localhost:3001"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://localhost:3001/"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"135\", \"Not:A Brand\";v=\"99\", \"Google Chrome\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"macOS\""}], "queryString": [], "cookies": [], "headersSize": 621, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept-Ranges", "value": "bytes"}, {"name": "Cache-Control", "value": "no-store, must-revalidate"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "31"}, {"name": "Content-Type", "value": "application/json; charset=UTF-8"}, {"name": "Date", "value": "Fri, 27 Jun 2025 14:50:23 GMT"}, {"name": "ETag", "value": "W/\"1f-197b1de4708\""}, {"name": "Keep-Alive", "value": "timeout=5"}, {"name": "Last-Modified", "value": "Fri, 27 Jun 2025 14:50:23 GMT"}, {"name": "Vary", "value": "Accept-Encoding"}, {"name": "X-DNS-Prefetch-Control", "value": "on"}, {"name": "X-Frame-Options", "value": "DENY"}], "cookies": [], "content": {"size": 31, "mimeType": "application/json", "compression": 0, "text": "{\"c\":[\"webpack\"],\"r\":[],\"m\":[]}"}, "redirectURL": "", "headersSize": 380, "bodySize": 31, "_transferSize": 411, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "[::1]", "startedDateTime": "2025-06-27T14:50:23.884Z", "time": 9.876999958366156, "timings": {"blocked": 2.439999992318451, "dns": 0.006000000000000005, "ssl": -1, "connect": 0.17200000000000001, "send": 0.056999999999999995, "wait": 6.899999994777143, "receive": 0.3019999712705612, "_blocked_queueing": 2.2769999923184514, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "527", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "createFetch", "scriptId": "99", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js", "lineNumber": 153, "columnNumber": 11}, {"functionName": "fetchServerResponse", "scriptId": "99", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js", "lineNumber": 81, "columnNumber": 26}, {"functionName": "eval", "scriptId": "127", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js", "lineNumber": 190, "columnNumber": 105}, {"functionName": "task", "scriptId": "124", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js", "lineNumber": 29, "columnNumber": 37}, {"functionName": "processNext", "scriptId": "124", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js", "lineNumber": 80, "columnNumber": 185}, {"functionName": "enqueue", "scriptId": "124", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js", "lineNumber": 44, "columnNumber": 75}, {"functionName": "createLazyPrefetchEntry", "scriptId": "127", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js", "lineNumber": 190, "columnNumber": 48}, {"functionName": "getOrCreatePrefetchCacheEntry", "scriptId": "127", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js", "lineNumber": 137, "columnNumber": 11}, {"functionName": "navigateReducer", "scriptId": "98", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js", "lineNumber": 143, "columnNumber": 81}, {"functionName": "clientReducer", "scriptId": "97", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js", "lineNumber": 24, "columnNumber": 60}, {"functionName": "action", "scriptId": "96", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js", "lineNumber": 129, "columnNumber": 54}, {"functionName": "runAction", "scriptId": "96", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js", "lineNumber": 41, "columnNumber": 37}, {"functionName": "dispatchAction", "scriptId": "96", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js", "lineNumber": 95, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "96", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js", "lineNumber": 127, "columnNumber": 39}, {"functionName": "eval", "scriptId": "131", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/use-reducer.js", "lineNumber": 36, "columnNumber": 20}, {"functionName": "eval", "scriptId": "128", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js", "lineNumber": 133, "columnNumber": 15}, {"functionName": "eval", "scriptId": "128", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js", "lineNumber": 231, "columnNumber": 20}, {"functionName": "exports.startTransition", "scriptId": "58", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react.development.js", "lineNumber": 1426, "columnNumber": 26}, {"functionName": "push", "scriptId": "128", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js", "lineNumber": 229, "columnNumber": 43}, {"functionName": "navigate", "scriptId": "208", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "lineNumber": 67, "columnNumber": 48}, {"functionName": "exports.startTransition", "scriptId": "58", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react.development.js", "lineNumber": 1426, "columnNumber": 26}, {"functionName": "linkClicked", "scriptId": "208", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "lineNumber": 72, "columnNumber": 19}, {"functionName": "onClick", "scriptId": "208", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "lineNumber": 303, "columnNumber": 12}, {"functionName": "processDispatchQueue", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 16145, "columnNumber": 16}, {"functionName": "eval", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 16748, "columnNumber": 8}, {"functionName": "batchedUpdates$1", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 3129, "columnNumber": 39}, {"functionName": "dispatchEventForPluginEventSystem", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 16304, "columnNumber": 6}, {"functionName": "dispatchEvent", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 20399, "columnNumber": 10}, {"functionName": "dispatchDiscreteEvent", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 20367, "columnNumber": 10}]}}, "_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "3001", "request": {"method": "GET", "url": "http://localhost:3001/brands/test?_rsc=70ep1", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "localhost:3001"}, {"name": "Next-Router-State-Tree", "value": "%5B%22%22%2C%7B%22children%22%3A%5B%22brands%22%2C%7B%22children%22%3A%5B%22__PAGE__%22%2C%7B%7D%2C%22%2Fbrands%22%2C%22refresh%22%5D%7D%5D%7D%2Cnull%2Cnull%2Ctrue%5D"}, {"name": "Next-Url", "value": "/brands"}, {"name": "Pragma", "value": "no-cache"}, {"name": "RSC", "value": "1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://localhost:3001/brands"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"135\", \"Not:A Brand\";v=\"99\", \"Google Chrome\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"macOS\""}], "queryString": [{"name": "_rsc", "value": "70ep1"}], "cookies": [], "headersSize": 807, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Cache-Control", "value": "no-store, must-revalidate"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Type", "value": "text/x-component"}, {"name": "Date", "value": "Fri, 27 Jun 2025 14:50:42 GMT"}, {"name": "Keep-Alive", "value": "timeout=5"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Vary", "value": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch, Accept-Encoding"}, {"name": "X-DNS-Prefetch-Control", "value": "on"}, {"name": "X-Frame-Options", "value": "DENY"}], "cookies": [], "content": {"size": 27387, "mimeType": "text/x-component", "compression": 19406, "text": "1:\"$Sreact.fragment\"\n2:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"\"]\n3:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"\"]\n6:I[\"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"OutletBoundary\"]\na:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"\"]\nb:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"ClientPageRoot\"]\nc:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"ClientSegmentRoot\"]\nd:I[\"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"HTTPAccessFallbackBoundary\"]\ne:I[\"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"MetadataBoundary\"]\nf:I[\"(app-pages-browser)/./node_modules/next/dist/lib/metadata/metadata-boundary.js\",[\"app-pages-internals\",\"static/chunks/app-pages-internals.js\"],\"ViewportBoundary\"]\n5:{\"name\":\"BrandPage\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"props\":{\"params\":\"$@\",\"searchParams\":\"$@\"}}\n4:D\"$5\"\n8:{\"name\":\"__next_outlet_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"props\":{\"ready\":\"$E(async function getMetadataAndViewportReady() {\\n        await viewport();\\n        await metadata();\\n        return undefined;\\n    })\"}}\n7:D\"$8\"\n10:{\"name\":\"NonIndex\",\"env\":\"Server\",\"key\":null,\"owner\":null,\"props\":{\"ctx\":{\"componentMod\":{\"GlobalError\":\"$a\",\"__next_app__\":{\"require\":\"$E(function __webpack_require__(moduleId) {\\n/******/ \\t\\t// Check if module is in cache\\n/******/ \\t\\tvar cachedModule = __webpack_module_cache__[moduleId];\\n/******/ \\t\\tif (cachedModule !== undefined) {\\n/******/ \\t\\t\\treturn cachedModule.exports;\\n/******/ \\t\\t}\\n/******/ \\t\\t// Create a new module (and put it into the cache)\\n/******/ \\t\\tvar module = __webpack_module_cache__[moduleId] = {\\n/******/ \\t\\t\\tid: moduleId,\\n/******/ \\t\\t\\tloaded: false,\\n/******/ \\t\\t\\texports: {}\\n/******/ \\t\\t};\\n/******/ \\t\\n/******/ \\t\\t// Execute the module function\\n/******/ \\t\\tvar threw = true;\\n/******/ \\t\\ttry {\\n/******/ \\t\\t\\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\\n/******/ \\t\\t\\tthrew = false;\\n/******/ \\t\\t} finally {\\n/******/ \\t\\t\\tif(threw) delete __webpack_module_cache__[moduleId];\\n/******/ \\t\\t}\\n/******/ \\t\\n/******/ \\t\\t// Flag the module as loaded\\n/******/ \\t\\tmodule.loaded = true;\\n/******/ \\t\\n/******/ \\t\\t// Return the exports of the module\\n/******/ \\t\\treturn module.exports;\\n/******/ \\t})\",\"loadChunk\":\"$E(() => Promise.resolve())\"},\"pages\":[\"/Users/<USER>/cashback-deals-v2 copy/src/app/brands/[id]/page.tsx\"],\"routeModule\":{\"userland\":{\"loaderTree\":[\"\",{\"children\":\"$Y\"},\"$Y\"]},\"definition\":\"$Y\"},\"tree\":\"$Y\",\"ClientPageRoot\":\"$b\",\"ClientSegmentRoot\":\"$c\",\"HTTPAccessFallbackBoundary\":\"$d\",\"LayoutRouter\":\"$2\",\"MetadataBoundary\":\"$e\",\"OutletBoundary\":\"$6\",\"Postpone\":\"$E(function Postpone({ reason, route }) {\\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    const dynamicTracking = prerenderStore && prerenderStore.type === 'prerender-ppr' ? prerenderStore.dynamicTracking : null;\\n    postponeWithTracking(route, reason, dynamicTracking);\\n})\",\"RenderFromTemplateContext\":\"$3\",\"ViewportBoundary\":\"$f\",\"actionAsyncStorage\":\"$Y\",\"collectSegmentData\":\"$E(async function collectSegmentData(fullPageDataBuffer, staleTime, clientModules, serverConsumerManifest) {\\n    // Traverse the router tree and generate a prefetch response for each segment.\\n    // A mutable map to collect the results as we traverse the route tree.\\n    const resultMap = new Map();\\n    // Before we start, warm up the module cache by decoding the page data once.\\n    // Then we can assume that any remaining async tasks that occur the next time\\n    // are due to hanging promises caused by dynamic data access. Note we only\\n    // have to do this once per page, not per individual segment.\\n    //\\n    try {\\n        await (0, _clientedge.createFromReadableStream)((0, _nodewebstreamshelper.streamFromBuffer)(fullPageDataBuffer), {\\n            serverConsumerManifest\\n        });\\n        await (0, _scheduler.waitAtLeastOneReactRenderTask)();\\n    } catch  {}\\n    // Create an abort controller that we'll use to stop the stream.\\n    const abortController = new AbortController();\\n    const onCompletedProcessingRouteTree = async ()=>{\\n        // Since all we're doing is decoding and re-encoding a cached prerender, if\\n        // serializing the stream takes longer than a microtask, it must because of\\n        // hanging promises caused by dynamic data.\\n        await (0, _scheduler.waitAtLeastOneReactRenderTask)();\\n        abortController.abort();\\n    };\\n    // Generate a stream for the route tree prefetch. While we're walking the\\n    // tree, we'll also spawn additional tasks to generate the segment prefetches.\\n    // The promises for these tasks are pushed to a mutable array that we will\\n    // await once the route tree is fully rendered.\\n    const segmentTasks = [];\\n    const { prelude: treeStream } = await (0, _staticedge.prerender)(// RootTreePrefetch is not a valid return type for a React component, but\\n    // we need to use a component so that when we decode the original stream\\n    // inside of it, the side effects are transferred to the new stream.\\n    // @ts-expect-error\\n    /*#__PURE__*/ (0, _jsxruntime.jsx)(PrefetchTreeData, {\\n        fullPageDataBuffer: fullPageDataBuffer,\\n        serverConsumerManifest: serverConsumerManifest,\\n        clientModules: clientModules,\\n        staleTime: staleTime,\\n        segmentTasks: segmentTasks,\\n        onCompletedProcessingRouteTree: onCompletedProcessingRouteTree\\n    }), clientModules, {\\n        signal: abortController.signal,\\n        onError () {\\n        // Ignore any errors. These would have already been reported when\\n        // we created the full page data.\\n        }\\n    });\\n    // Write the route tree to a special `/_tree` segment.\\n    const treeBuffer = await (0, _nodewebstreamshelper.streamToBuffer)(treeStream);\\n    resultMap.set('/_tree', treeBuffer);\\n    // Now that we've finished rendering the route tree, all the segment tasks\\n    // should have been spawned. Await them in parallel and write the segment\\n    // prefetches to the result map.\\n    for (const [segmentPath, buffer] of (await Promise.all(segmentTasks))){\\n        resultMap.set(segmentPath, buffer);\\n    }\\n    return resultMap;\\n})\",\"createMetadataComponents\":\"$E(function createMetadataComponents({ tree, searchParams, metadataContext, getDynamicParamFromSegment, appUsingSizeAdjustment, errorType, createServerParamsForMetadata, workStore, MetadataBoundary, ViewportBoundary }) {\\n    function MetadataRoot() {\\n        return /*#__PURE__*/ (0, _jsxruntime.jsxs)(_jsxruntime.Fragment, {\\n            children: [\\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(MetadataBoundary, {\\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Metadata, {})\\n                }),\\n                /*#__PURE__*/ (0, _jsxruntime.jsx)(ViewportBoundary, {\\n                    children: /*#__PURE__*/ (0, _jsxruntime.jsx)(Viewport, {})\\n                }),\\n                appUsingSizeAdjustment ? /*#__PURE__*/ (0, _jsxruntime.jsx)(\\\"meta\\\", {\\n                    name: \\\"next-size-adjust\\\",\\n                    content: \\\"\\\"\\n                }) : null\\n            ]\\n        });\\n    }\\n    async function viewport() {\\n        return getResolvedViewport(tree, searchParams, getDynamicParamFromSegment, createServerParamsForMetadata, workStore, errorType);\\n    }\\n    async function Viewport() {\\n        try {\\n            return await viewport();\\n        } catch (error) {\\n            if (!errorType && (0, _httpaccessfallback.isHTTPAccessFallbackError)(error)) {\\n                try {\\n                    return await getNotFoundViewport(tree, searchParams, getDynamicParamFromSegment, createServerParamsForMetadata, workStore);\\n                } catch  {}\\n            }\\n            // We don't actually want to error in this component. We will\\n            // also error in the MetadataOutlet which causes the error to\\n            // bubble from the right position in the page to be caught by the\\n            // appropriate boundaries\\n            return null;\\n        }\\n    }\\n    Viewport.displayName = _metadataconstants.VIEWPORT_BOUNDARY_NAME;\\n    async function metadata() {\\n        return getResolvedMetadata(tree, searchParams, getDynamicParamFromSegment, metadataContext, createServerParamsForMetadata, workStore, errorType);\\n    }\\n    async function Metadata() {\\n        try {\\n            return await metadata();\\n        } catch (error) {\\n            if (!errorType && (0, _httpaccessfallback.isHTTPAccessFallbackError)(error)) {\\n                try {\\n                    return await getNotFoundMetadata(tree, searchParams, getDynamicParamFromSegment, metadataContext, createServerParamsForMetadata, workStore);\\n                } catch  {}\\n            }\\n            // We don't actually want to error in this component. We will\\n            // also error in the MetadataOutlet which causes the error to\\n            // bubble from the right position in the page to be caught by the\\n            // appropriate boundaries\\n            return null;\\n        }\\n    }\\n    Metadata.displayName = _metadataconstants.METADATA_BOUNDARY_NAME;\\n    async function getMetadataAndViewportReady() {\\n        await viewport();\\n        await metadata();\\n        return undefined;\\n    }\\n    return [\\n        MetadataRoot,\\n        getMetadataAndViewportReady\\n    ];\\n})\",\"createPrerenderParamsForClientSegment\":\"$E(function createPrerenderParamsForClientSegment(underlyingParams, workStore) {\\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (prerenderStore && prerenderStore.type === 'prerender') {\\n        const fallbackParams = workStore.fallbackRouteParams;\\n        if (fallbackParams) {\\n            for(let key in underlyingParams){\\n                if (fallbackParams.has(key)) {\\n                    // This params object has one of more fallback params so we need to consider\\n                    // the awaiting of this params object \\\"dynamic\\\". Since we are in dynamicIO mode\\n                    // we encode this as a promise that never resolves\\n                    return (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`params`');\\n                }\\n            }\\n        }\\n    }\\n    // We're prerendering in a mode that does not abort. We resolve the promise without\\n    // any tracking because we're just transporting a value from server to client where the tracking\\n    // will be applied.\\n    return Promise.resolve(underlyingParams);\\n})\",\"createPrerenderSearchParamsForClientPage\":\"$E(function createPrerenderSearchParamsForClientPage(workStore) {\\n    if (workStore.forceStatic) {\\n        // When using forceStatic we override all other logic and always just return an empty\\n        // dictionary object.\\n        return Promise.resolve({});\\n    }\\n    const prerenderStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (prerenderStore && prerenderStore.type === 'prerender') {\\n        // dynamicIO Prerender\\n        // We're prerendering in a mode that aborts (dynamicIO) and should stall\\n        // the promise to ensure the RSC side is considered dynamic\\n        return (0, _dynamicrenderingutils.makeHangingPromise)(prerenderStore.renderSignal, '`searchParams`');\\n    }\\n    // We're prerendering in a mode that does not aborts. We resolve the promise without\\n    // any tracking because we're just transporting a value from server to client where the tracking\\n    // will be applied.\\n    return Promise.resolve({});\\n})\",\"createServerParamsForMetadata\":\"$E(function createServerParamsForServerSegment(underlyingParams, workStore) {\\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (workUnitStore) {\\n        switch(workUnitStore.type){\\n            case 'prerender':\\n            case 'prerender-ppr':\\n            case 'prerender-legacy':\\n                return createPrerenderParams(underlyingParams, workStore, workUnitStore);\\n            default:\\n        }\\n    }\\n    return createRenderParams(underlyingParams, workStore);\\n})\",\"createServerParamsForServerSegment\":\"$E(function createServerParamsForServerSegment(underlyingParams, workStore) {\\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (workUnitStore) {\\n        switch(workUnitStore.type){\\n            case 'prerender':\\n            case 'prerender-ppr':\\n            case 'prerender-legacy':\\n                return createPrerenderParams(underlyingParams, workStore, workUnitStore);\\n            default:\\n        }\\n    }\\n    return createRenderParams(underlyingParams, workStore);\\n})\",\"createServerSearchParamsForMetadata\":\"$E(function createServerSearchParamsForServerPage(underlyingSearchParams, workStore) {\\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (workUnitStore) {\\n        switch(workUnitStore.type){\\n            case 'prerender':\\n            case 'prerender-ppr':\\n            case 'prerender-legacy':\\n                return createPrerenderSearchParams(workStore, workUnitStore);\\n            default:\\n        }\\n    }\\n    return createRenderSearchParams(underlyingSearchParams, workStore);\\n})\",\"createServerSearchParamsForServerPage\":\"$E(function createServerSearchParamsForServerPage(underlyingSearchParams, workStore) {\\n    const workUnitStore = _workunitasyncstorageexternal.workUnitAsyncStorage.getStore();\\n    if (workUnitStore) {\\n        switch(workUnitStore.type){\\n            case 'prerender':\\n            case 'prerender-ppr':\\n            case 'prerender-legacy':\\n                return createPrerenderSearchParams(workStore, workUnitStore);\\n            default:\\n        }\\n    }\\n    return createRenderSearchParams(underlyingSearchParams, workStore);\\n})\",\"createTemporaryReferenceSet\":\"$E(function(){return new WeakMap})\",\"decodeAction\":\"$E(function(body,serverManifest){var formData=new FormData,action=null;return body.forEach(function(value1,key){key.startsWith(\\\"$ACTION_\\\")?key.startsWith(\\\"$ACTION_REF_\\\")?(value1=decodeBoundActionMetaData(body,serverManifest,value1=\\\"$ACTION_\\\"+key.slice(12)+\\\":\\\"),action=loadServerReference(serverManifest,value1.id,value1.bound)):key.startsWith(\\\"$ACTION_ID_\\\")&&(action=loadServerReference(serverManifest,value1=key.slice(11),null)):formData.append(key,value1)}),null===action?null:action.then(function(fn){return fn.bind(null,formData)})})\",\"decodeFormState\":\"$E(function(actionResult,body,serverManifest){var keyPath=body.get(\\\"$ACTION_KEY\\\");if(\\\"string\\\"!=typeof keyPath)return Promise.resolve(null);var metaData=null;if(body.forEach(function(value1,key){key.startsWith(\\\"$ACTION_REF_\\\")&&(metaData=decodeBoundActionMetaData(body,serverManifest,\\\"$ACTION_\\\"+key.slice(12)+\\\":\\\"))}),null===metaData)return Promise.resolve(null);var referenceId=metaData.id;return Promise.resolve(metaData.bound).then(function(bound){return null===bound?null:[actionResult,keyPath,referenceId,bound.length-1]})})\",\"decodeReply\":\"$E(function(body,webpackMap,options){if(\\\"string\\\"==typeof body){var form=new FormData;form.append(\\\"0\\\",body),body=form}return webpackMap=getChunk(body=createResponse(webpackMap,\\\"\\\",options?options.temporaryReferences:void 0,body),0),close(body),webpackMap})\",\"patchFetch\":\"$E(function patchFetch() {\\n    return (0, _patchfetch.patchFetch)({\\n        workAsyncStorage: _workasyncstorageexternal.workAsyncStorage,\\n        workUnitAsyncStorage: _workunitasyncstorageexternal.workUnitAsyncStorage\\n    });\\n})\",\"preconnect\":\"$E(function preconnect(href, crossOrigin, nonce) {\\n    const opts = {};\\n    if (typeof crossOrigin === 'string') {\\n        opts.crossOrigin = crossOrigin;\\n    }\\n    if (typeof nonce === 'string') {\\n        opts.nonce = nonce;\\n    }\\n    ;\\n    _reactdom.default.preconnect(href, opts);\\n})\",\"preloadFont\":\"$E(function preloadFont(href, type, crossOrigin, nonce) {\\n    const opts = {\\n        as: 'font',\\n        type\\n    };\\n    if (typeof crossOrigin === 'string') {\\n        opts.crossOrigin = crossOrigin;\\n    }\\n    if (typeof nonce === 'string') {\\n        opts.nonce = nonce;\\n    }\\n    _reactdom.default.preload(href, opts);\\n})\",\"preloadStyle\":\"$E(function preloadStyle(href, crossOrigin, nonce) {\\n    const opts = {\\n        as: 'style'\\n    };\\n    if (typeof crossOrigin === 'string') {\\n        opts.crossOrigin = crossOrigin;\\n    }\\n    if (typeof nonce === 'string') {\\n        opts.nonce = nonce;\\n    }\\n    _reactdom.default.preload(href, opts);\\n})\",\"prerender\":\"$undefined\",\"renderToReadableStream\":\"$E(function(model,webpackMap,options){var request=new RequestInstance(20,model,webpackMap,options?options.onError:void 0,options?options.identifierPrefix:void 0,options?options.onPostpone:void 0,options?options.temporaryReferences:void 0,options?options.environmentName:void 0,options?options.filterStackFrame:void 0,noop,noop);if(options&&options.signal){var signal=options.signal;if(signal.aborted)abort(request,signal.reason);else{var listener=function(){abort(request,signal.reason),signal.removeEventListener(\\\"abort\\\",listener)};signal.addEventListener(\\\"abort\\\",listener)}}return new ReadableStream({type:\\\"bytes\\\",start:function(){request.flushScheduled=null!==request.destination,supportsRequestStorage?scheduleMicrotask(function(){requestStorage.run(request,performWork,request)}):scheduleMicrotask(function(){return performWork(request)}),setTimeoutOrImmediate(function(){request.status===OPENING&&(request.status=11)},0)},pull:function(controller){if(request.status===CLOSING)request.status=CLOSED,closeWithError(controller,request.fatalError);else if(request.status!==CLOSED&&null===request.destination){request.destination=controller;try{flushCompletedChunks(request,controller)}catch(error){logRecoverableError(request,error,null),fatalError(request,error)}}},cancel:function(reason){request.destination=null,abort(request,reason)}},{highWaterMark:0})})\",\"serverHooks\":\"$Y\",\"taintObjectReference\":\"$E(function notImplemented() {\\n    throw new Error('Taint can only be used with the taint flag.');\\n})\",\"workAsyncStorage\":\"$Y\",\"workUnitAsyncStorage\":\"$Y\"},\"url\":\"$Y\",\"renderOpts\":\"$Y\",\"workStore\":\"$Y\",\"parsedRequestHeaders\":\"$Y\",\"getDynamicParamFromSegment\":\"$E(function(segment){let segmentParam=getSegmentParam(segment);if(!segmentParam)return null;let key=segmentParam.param,value1=params[key];if(fallbackRouteParams&&fallbackRouteParams.has(segmentParam.param)?value1=fallbackRouteParams.get(segmentParam.param):Array.isArray(value1)?value1=value1.map(i=>encodeURIComponent(i)):\\\"string\\\"==typeof value1&&(value1=encodeURIComponent(value1)),!value1){let isCatchall=\\\"catchall\\\"===segmentParam.type,isOptionalCatchall=\\\"optional-catchall\\\"===segmentParam.type;if(isCatchall||isOptionalCatchall){let dynamicParamType=dynamicParamTypes[segmentParam.type];return isOptionalCatchall?{param:key,value:null,type:dynamicParamType,treeSegment:[key,\\\"\\\",dynamicParamType]}:{param:key,value:value1=pagePath.split(\\\"/\\\").slice(1).flatMap(pathSegment=>{let param=function(param){let match=param.match(PARAMETER_PATTERN);return match?parseMatchedParameter(match[1]):parseMatchedParameter(param)}(pathSegment);return params[param.key]??param.key}),type:dynamicParamType,treeSegment:[key,value1.join(\\\"/\\\"),dynamicParamType]}}}let type=function(type){let short=dynamicParamTypes[type];if(!short)throw Error(\\\"Unknown dynamic param type\\\");return short}(segmentParam.type);return{param:key,value:value1,treeSegment:[key,Array.isArray(value1)?value1.join(\\\"/\\\"):value1,type],type:type}})\",\"query\":\"$Y\",\"isPrefetch\":false,\"isAction\":false,\"requestTimestamp\":1751035842682,\"appUsingSizeAdjustment\":true,\"flightRouterState\":\"$Y\",\"requestId\":\"SHVmLnBMZBy6cjnLT8U_f\",\"pagePath\":\"/brands/[id]\",\"clientReferenceManifest\":\"$Y\",\"assetPrefix\":\"\",\"isNotFoundPath\":false,\"nonce\":\"$undefined\",\"res\":\"$Y\"}}}\n9:D\"$10\"\n9:null\n12:{\"name\":\"MetadataRoot\",\"env\":\"Server\",\"key\":\"SHVmLnBMZBy6cjnLT8U_f\",\"owner\":null,\"props\":{}}\n11:D\"$12\"\n14:{\"name\":\"__next_metadata_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":\"$12\",\"props\":{}}\n13:D\"$14\"\n16:{\"name\":\"__next_viewport_boundary__\",\"env\":\"Server\",\"key\":null,\"owner\":\"$12\",\"props\":{}}\n15:D\"$16\"\n11:[\"$\",\"$1\",\"SHVmLnBMZBy6cjnLT8U_f\",{\"children\":[[\"$\",\"$Le\",null,{\"children\":\"$L13\"},\"$12\"],[\"$\",\"$Lf\",null,{\"children\":\"$L15\"},\"$12\"],[\"$\",\"meta\",null,{\"name\":\"next-size-adjust\",\"content\":\"\"},\"$12\"]]},null]\n0:{\"b\":\"development\",\"f\":[[\"children\",\"brands\",\"children\",[\"id\",\"test\",\"d\"],[[\"id\",\"test\",\"d\"],{\"children\":[\"__PAGE__\",{}]}],[[\"id\",\"test\",\"d\"],[\"$\",\"$1\",\"c\",{\"children\":[null,[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"segmentPath\":[\"children\",\"brands\",\"children\",\"$0:f:0:5:0\",\"children\"],\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{},null],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":\"$undefined\",\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"},null]]},null],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[\"$L4\",null,[\"$\",\"$L6\",null,{\"children\":\"$L7\"},null]]},null],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[\"$9\",\"$11\"]},null],false]],\"S\":false}\n15:[[\"$\",\"meta\",\"0\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"},\"$8\"]]\n17:I[\"(app-pages-browser)/./src/app/brands/[id]/BrandClient.tsx\",[\"app/brands/[id]/page\",\"static/chunks/app/brands/%5Bid%5D/page.js\"],\"default\"]\n:W[\"log\",[[\"_getBrandPromotions\",\"webpack-internal:///(rsc)/./src/lib/data/brands.ts\",376,17],[\"_getBrandPageData\",\"webpack-internal:///(rsc)/./src/lib/data/brands.ts\",443,26],[\"async BrandPage\",\"webpack-internal:///(rsc)/./src/app/brands/[id]/page.tsx\",76,18]],\"$5\",\"Server\",\"Fetching promotions for brand ID: 3387809e-f848-414d-ba7e-3e86defaac1d (test)\"]\n:W[\"log\",[[\"_getBrandPromotions\",\"webpack-internal:///(rsc)/./src/lib/data/brands.ts\",376,17],[\"_getBrandPageData\",\"webpack-internal:///(rsc)/./src/lib/data/brands.ts\",443,26],[\"async Module.generateMetadata\",\"webpack-internal:///(rsc)/./src/app/brands/[id]/page.tsx\",21,18]],\"$14\",\"Server\",\"Fetching promotions for brand ID: 3387809e-f848-414d-ba7e-3e86defaac1d (test)\"]\n:W[\"log\",[[\"_getBrandPromotions\",\"webpack-internal:///(rsc)/./src/lib/data/brands.ts\",387,17],[\"async _getBrandPageData\",\"webpack-internal:///(rsc)/./src/lib/data/brands.ts\",443,20],[\"async BrandPage\",\"webpack-internal:///(rsc)/./src/app/brands/[id]/page.tsx\",76,18]],\"$5\",\"Server\",\"Promotions query status:\",{\"status\":200,\"statusText\":\"OK\"}]\n:W[\"log\",[[\"_getBrandPromotions\",\"webpack-internal:///(rsc)/./src/lib/data/brands.ts\",402,17],[\"async _getBrandPageData\",\"webpack-internal:///(rsc)/./src/lib/data/brands.ts\",443,20],[\"async BrandPage\",\"webpack-internal:///(rsc)/./src/app/brands/[id]/page.tsx\",76,18]],\"$5\",\"Server\",\"Found 1 promotions for brand 3387809e-f848-414d-ba7e-3e86defaac1d\"]\n:W[\"log\",[[\"_getBrandPromotions\",\"webpack-internal:///(rsc)/./src/lib/data/brands.ts\",387,17],[\"async _getBrandPageData\",\"webpack-internal:///(rsc)/./src/lib/data/brands.ts\",443,20],[\"async Module.generateMetadata\",\"webpack-internal:///(rsc)/./src/app/brands/[id]/page.tsx\",21,18]],\"$14\",\"Server\",\"Promotions query status:\",{\"status\":200,\"statusText\":\"OK\"}]\n:W[\"log\",[[\"_getBrandPromotions\",\"webpack-internal:///(rsc)/./src/lib/data/brands.ts\",402,17],[\"async _getBrandPageData\",\"webpack-internal:///(rsc)/./src/lib/data/brands.ts\",443,20],[\"async Module.generateMetadata\",\"webpack-internal:///(rsc)/./src/app/brands/[id]/page.tsx\",21,18]],\"$14\",\"Server\",\"Found 1 promotions for brand 3387809e-f848-414d-ba7e-3e86defaac1d\"]\n4:[\"$\",\"$L17\",null,{\"brand\":{\"id\":\"3387809e-f848-414d-ba7e-3e86defaac1d\",\"name\":\"test\",\"slug\":\"test\",\"logoUrl\":\"https://supabase.com/dashboard/img/supabase-logo.svg\",\"description\":\"tests\",\"featured\":true,\"sponsored\":true,\"createdAt\":\"2025-02-05T14:43:39.167774\",\"updatedAt\":\"2025-02-05T14:43:39.167774\",\"productsCount\":0,\"activePromotions\":[]},\"promotions\":[{\"id\":\"12b981f5-73f3-4d2a-98d9-52245c1a552a\",\"title\":\"TEST BRAND\",\"description\":\"Get amazing cashback on Apple products\",\"maxCashbackAmount\":200,\"purchaseStartDate\":\"2025-01-20\",\"purchaseEndDate\":\"2026-02-19\",\"claimStartOffsetDays\":14,\"claimWindowDays\":30,\"termsUrl\":\"https://example.com/terms\",\"termsDescription\":\"Terms and conditions apply. Valid on selected products only.\",\"status\":\"active\",\"isFeatured\":false,\"brand\":{\"id\":\"3387809e-f848-414d-ba7e-3e86defaac1d\",\"name\":\"test\",\"slug\":\"test\",\"logo_url\":\"https://supabase.com/dashboard/img/supabase-logo.svg\"},\"category\":{\"id\":\"f40c6438-e320-4ca2-9dbb-224e4860af68\",\"name\":\"Audio & Headphones\",\"slug\":\"audio-headphones\"},\"isActive\":true,\"isExpired\":false,\"timeRemaining\":{\"days\":236,\"hours\":9,\"minutes\":9,\"seconds\":17},\"formattedPurchaseDateRange\":\"20 Jan 2025 - 19 Feb 2026\",\"formattedExpiryDate\":\"Thursday 19 February 2026 at 12:00 am\"}],\"activePromotions\":[\"$4:props:promotions:0\"],\"expiredPromotions\":[],\"promotionCount\":1,\"activePromotionCount\":1,\"expiredPromotionCount\":0},\"$5\"]\n13:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"},\"$14\"],[\"$\",\"title\",\"1\",{\"children\":\"test Promotions & Cashback Deals\"},\"$14\"],[\"$\",\"meta\",\"2\",{\"name\":\"description\",\"content\":\"tests\"},\"$14\"],[\"$\",\"meta\",\"3\",{\"name\":\"robots\",\"content\":\"index, follow\"},\"$14\"],[\"$\",\"meta\",\"4\",{\"name\":\"script:ld+json\",\"content\":\"{\\\"@context\\\":\\\"https://schema.org\\\",\\\"@type\\\":\\\"Brand\\\",\\\"name\\\":\\\"test\\\",\\\"description\\\":\\\"tests\\\",\\\"logo\\\":\\\"https://supabase.com/dashboard/img/supabase-logo.svg\\\",\\\"url\\\":\\\"http://localhost:3000/brands/test\\\",\\\"identifier\\\":\\\"3387809e-f848-414d-ba7e-3e86defaac1d\\\"}\"},\"$14\"],[\"$\",\"link\",\"5\",{\"rel\":\"canonical\",\"href\":\"http://localhost:3000\"},\"$14\"],[\"$\",\"meta\",\"6\",{\"property\":\"og:title\",\"content\":\"test Promotions & Cashback Deals\"},\"$14\"],[\"$\",\"meta\",\"7\",{\"property\":\"og:description\",\"content\":\"tests\"},\"$14\"],[\"$\",\"meta\",\"8\",{\"property\":\"og:url\",\"content\":\"http://localhost:3000/brands/test\"},\"$14\"],[\"$\",\"meta\",\"9\",{\"property\":\"og:image\",\"content\":\"https://supabase.com/dashboard/img/supabase-logo.svg\"},\"$14\"],[\"$\",\"meta\",\"10\",{\"name\":\"twitter:card\",\"content\":\"summary_large_image\"},\"$14\"],[\"$\",\"meta\",\"11\",{\"name\":\"twitter:title\",\"content\":\"test Promotions & Cashback Deals\"},\"$14\"],[\"$\",\"meta\",\"12\",{\"name\":\"twitter:description\",\"content\":\"tests\"},\"$14\"],[\"$\",\"meta\",\"13\",{\"name\":\"twitter:image\",\"content\":\"https://supabase.com/dashboard/img/supabase-logo.svg\"},\"$14\"],[\"$\",\"link\",\"14\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"},\"$14\"]]\n7:null\n"}, "redirectURL": "", "headersSize": 384, "bodySize": 7981, "_transferSize": 8365, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "[::1]", "startedDateTime": "2025-06-27T14:50:41.818Z", "time": 1093.7210000294372, "timings": {"blocked": 6.29200000166893, "dns": 0.0129999999999999, "ssl": -1, "connect": 0.22299999999999986, "send": 0.14800000000000013, "wait": 883.1479999701455, "receive": 203.8970000576228, "_blocked_queueing": 4.06700000166893, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "534", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "__webpack_require__.hmrM", "scriptId": "19", "url": "http://localhost:3001/_next/static/chunks/webpack.js?v=1751035806004", "lineNumber": 1366, "columnNumber": 19}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "hotCheck", "scriptId": "19", "url": "http://localhost:3001/_next/static/chunks/webpack.js?v=1751035806004", "lineNumber": 552, "columnNumber": 14}, {"functionName": "tryApplyUpdates", "scriptId": "563", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js", "lineNumber": 160, "columnNumber": 15}, {"functionName": "handleHotUpdate", "scriptId": "563", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js", "lineNumber": 199, "columnNumber": 12}, {"functionName": "processMessage", "scriptId": "563", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js", "lineNumber": 291, "columnNumber": 20}, {"functionName": "handler", "scriptId": "563", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js", "lineNumber": 507, "columnNumber": 16}]}}}, "_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "3001", "request": {"method": "GET", "url": "http://localhost:3001/_next/static/webpack/b37424ec212da695.webpack.hot-update.json", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "localhost:3001"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://localhost:3001/brands"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"135\", \"Not:A Brand\";v=\"99\", \"Google Chrome\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"macOS\""}], "queryString": [], "cookies": [], "headersSize": 627, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept-Ranges", "value": "bytes"}, {"name": "Cache-Control", "value": "no-store, must-revalidate"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "31"}, {"name": "Content-Type", "value": "application/json; charset=UTF-8"}, {"name": "Date", "value": "Fri, 27 Jun 2025 14:50:42 GMT"}, {"name": "ETag", "value": "W/\"1f-197b1de8e80\""}, {"name": "Keep-Alive", "value": "timeout=5"}, {"name": "Last-Modified", "value": "Fri, 27 Jun 2025 14:50:42 GMT"}, {"name": "Vary", "value": "Accept-Encoding"}, {"name": "X-DNS-Prefetch-Control", "value": "on"}, {"name": "X-Frame-Options", "value": "DENY"}], "cookies": [], "content": {"size": 31, "mimeType": "application/json", "compression": 0, "text": "{\"c\":[\"webpack\"],\"r\":[],\"m\":[]}"}, "redirectURL": "", "headersSize": 380, "bodySize": 31, "_transferSize": 411, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "[::1]", "startedDateTime": "2025-06-27T14:50:42.179Z", "time": 5.259999974906444, "timings": {"blocked": 1.404999916560948, "dns": 0.0040000000000000036, "ssl": -1, "connect": 0.158, "send": 0.05099999999999999, "wait": 3.375000004336238, "receive": 0.26700005400925875, "_blocked_queueing": 1.257999916560948, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "677", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "createFetch", "scriptId": "99", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js", "lineNumber": 153, "columnNumber": 11}, {"functionName": "fetchServerResponse", "scriptId": "99", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/fetch-server-response.js", "lineNumber": 81, "columnNumber": 26}, {"functionName": "eval", "scriptId": "127", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js", "lineNumber": 190, "columnNumber": 105}, {"functionName": "task", "scriptId": "124", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js", "lineNumber": 29, "columnNumber": 37}, {"functionName": "processNext", "scriptId": "124", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js", "lineNumber": 80, "columnNumber": 185}, {"functionName": "enqueue", "scriptId": "124", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/promise-queue.js", "lineNumber": 44, "columnNumber": 75}, {"functionName": "createLazyPrefetchEntry", "scriptId": "127", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js", "lineNumber": 190, "columnNumber": 48}, {"functionName": "getOrCreatePrefetchCacheEntry", "scriptId": "127", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/prefetch-cache-utils.js", "lineNumber": 137, "columnNumber": 11}, {"functionName": "navigateReducer", "scriptId": "98", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/reducers/navigate-reducer.js", "lineNumber": 143, "columnNumber": 81}, {"functionName": "clientReducer", "scriptId": "97", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/router-reducer/router-reducer.js", "lineNumber": 24, "columnNumber": 60}, {"functionName": "action", "scriptId": "96", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js", "lineNumber": 129, "columnNumber": 54}, {"functionName": "runAction", "scriptId": "96", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js", "lineNumber": 41, "columnNumber": 37}, {"functionName": "dispatchAction", "scriptId": "96", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js", "lineNumber": 95, "columnNumber": 8}, {"functionName": "dispatch", "scriptId": "96", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/shared/lib/router/action-queue.js", "lineNumber": 127, "columnNumber": 39}, {"functionName": "eval", "scriptId": "131", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/use-reducer.js", "lineNumber": 36, "columnNumber": 20}, {"functionName": "eval", "scriptId": "128", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js", "lineNumber": 133, "columnNumber": 15}, {"functionName": "eval", "scriptId": "128", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js", "lineNumber": 231, "columnNumber": 20}, {"functionName": "exports.startTransition", "scriptId": "58", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react.development.js", "lineNumber": 1426, "columnNumber": 26}, {"functionName": "push", "scriptId": "128", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js", "lineNumber": 229, "columnNumber": 43}, {"functionName": "navigate", "scriptId": "208", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "lineNumber": 67, "columnNumber": 48}, {"functionName": "exports.startTransition", "scriptId": "58", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react.development.js", "lineNumber": 1426, "columnNumber": 26}, {"functionName": "linkClicked", "scriptId": "208", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "lineNumber": 72, "columnNumber": 19}, {"functionName": "onClick", "scriptId": "208", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js", "lineNumber": 303, "columnNumber": 12}, {"functionName": "processDispatchQueue", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 16145, "columnNumber": 16}, {"functionName": "eval", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 16748, "columnNumber": 8}, {"functionName": "batchedUpdates$1", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 3129, "columnNumber": 39}, {"functionName": "dispatchEventForPluginEventSystem", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 16304, "columnNumber": 6}, {"functionName": "dispatchEvent", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 20399, "columnNumber": 10}, {"functionName": "dispatchDiscreteEvent", "scriptId": "80", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js", "lineNumber": 20367, "columnNumber": 10}]}}, "_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "3001", "request": {"method": "GET", "url": "http://localhost:3001/products?promotion_id=12b981f5-73f3-4d2a-98d9-52245c1a552a&_rsc=1tpyn", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "localhost:3001"}, {"name": "Next-Router-State-Tree", "value": "%5B%22%22%2C%7B%22children%22%3A%5B%22brands%22%2C%7B%22children%22%3A%5B%5B%22id%22%2C%22test%22%2C%22d%22%5D%2C%7B%22children%22%3A%5B%22__PAGE__%22%2C%7B%7D%2C%22%2Fbrands%2Ftest%22%2C%22refresh%22%5D%7D%5D%7D%2Cnull%2Cnull%5D%7D%2Cnull%2Cnull%2Ctrue%5D"}, {"name": "Next-Url", "value": "/brands/test"}, {"name": "Pragma", "value": "no-cache"}, {"name": "RSC", "value": "1"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://localhost:3001/brands/test"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"135\", \"Not:A Brand\";v=\"99\", \"Google Chrome\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"macOS\""}], "queryString": [{"name": "promotion_id", "value": "12b981f5-73f3-4d2a-98d9-52245c1a552a"}, {"name": "_rsc", "value": "1tpyn"}], "cookies": [], "headersSize": 954, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Cache-Control", "value": "no-store, must-revalidate"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Encoding", "value": "gzip"}, {"name": "Content-Type", "value": "text/x-component"}, {"name": "Date", "value": "Fri, 27 Jun 2025 14:50:50 GMT"}, {"name": "Keep-Alive", "value": "timeout=5"}, {"name": "Transfer-Encoding", "value": "chunked"}, {"name": "Vary", "value": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch, Accept-Encoding"}, {"name": "X-DNS-Prefetch-Control", "value": "on"}, {"name": "X-Frame-Options", "value": "DENY"}], "cookies": [], "content": {"size": 174492, "mimeType": "text/x-component", "compression": 151933}, "redirectURL": "", "headersSize": 384, "bodySize": 22559, "_transferSize": 22943, "_error": "net::ERR_ABORTED", "_fetchedViaServiceWorker": false}, "serverIPAddress": "[::1]", "startedDateTime": "2025-06-27T14:50:50.044Z", "time": 475.62599990183116, "timings": {"blocked": 3.83899995354563, "dns": 0.009000000000000008, "ssl": -1, "connect": 0.18900000000000006, "send": 0.17099999999999993, "wait": 331.51000005277245, "receive": 139.90799989551306, "_blocked_queueing": 3.49499995354563, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}, {"_connectionId": "684", "_initiator": {"type": "script", "stack": {"callFrames": [{"functionName": "__webpack_require__.hmrM", "scriptId": "19", "url": "http://localhost:3001/_next/static/chunks/webpack.js?v=1751035806004", "lineNumber": 1366, "columnNumber": 19}], "parent": {"description": "Promise.then", "callFrames": [{"functionName": "hotCheck", "scriptId": "19", "url": "http://localhost:3001/_next/static/chunks/webpack.js?v=1751035806004", "lineNumber": 552, "columnNumber": 14}, {"functionName": "tryApplyUpdates", "scriptId": "563", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js", "lineNumber": 160, "columnNumber": 15}, {"functionName": "handleHotUpdate", "scriptId": "563", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js", "lineNumber": 199, "columnNumber": 12}, {"functionName": "processMessage", "scriptId": "563", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js", "lineNumber": 291, "columnNumber": 20}, {"functionName": "handler", "scriptId": "563", "url": "webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/react-dev-overlay/app/hot-reloader-client.js", "lineNumber": 507, "columnNumber": 16}]}}}, "_priority": "High", "_resourceType": "fetch", "cache": {}, "connection": "3001", "request": {"method": "GET", "url": "http://localhost:3001/_next/static/webpack/a3f09ea3fde26d7f.webpack.hot-update.json", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept", "value": "*/*"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br, zstd"}, {"name": "Accept-Language", "value": "en-US,en;q=0.9"}, {"name": "Cache-Control", "value": "no-cache"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Host", "value": "localhost:3001"}, {"name": "Pragma", "value": "no-cache"}, {"name": "<PERSON><PERSON><PERSON>", "value": "http://localhost:3001/brands/test"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "same-origin"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}, {"name": "sec-ch-ua", "value": "\"Chromium\";v=\"135\", \"Not:A Brand\";v=\"99\", \"Google Chrome\";v=\"135\""}, {"name": "sec-ch-ua-mobile", "value": "?0"}, {"name": "sec-ch-ua-platform", "value": "\"macOS\""}], "queryString": [], "cookies": [], "headersSize": 632, "bodySize": 0}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/1.1", "headers": [{"name": "Accept-Ranges", "value": "bytes"}, {"name": "Cache-Control", "value": "no-store, must-revalidate"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Content-Length", "value": "31"}, {"name": "Content-Type", "value": "application/json; charset=UTF-8"}, {"name": "Date", "value": "Fri, 27 Jun 2025 14:50:50 GMT"}, {"name": "ETag", "value": "W/\"1f-197b1deae1d\""}, {"name": "Keep-Alive", "value": "timeout=5"}, {"name": "Last-Modified", "value": "Fri, 27 Jun 2025 14:50:50 GMT"}, {"name": "Vary", "value": "Accept-Encoding"}, {"name": "X-DNS-Prefetch-Control", "value": "on"}, {"name": "X-Frame-Options", "value": "DENY"}], "cookies": [], "content": {"size": 31, "mimeType": "application/json", "compression": 0, "text": "{\"c\":[\"webpack\"],\"r\":[],\"m\":[]}"}, "redirectURL": "", "headersSize": 380, "bodySize": 31, "_transferSize": 411, "_error": null, "_fetchedViaServiceWorker": false}, "serverIPAddress": "[::1]", "startedDateTime": "2025-06-27T14:50:50.272Z", "time": 4.948000036291779, "timings": {"blocked": 1.7470000455379486, "dns": 0.0050000000000000044, "ssl": -1, "connect": 0.19299999999999998, "send": 0.05500000000000005, "wait": 2.8349999452233314, "receive": 0.11300004553049803, "_blocked_queueing": 1.5960000455379486, "_workerStart": -1, "_workerReady": -1, "_workerFetchStart": -1, "_workerRespondWithSettled": -1}}]}}