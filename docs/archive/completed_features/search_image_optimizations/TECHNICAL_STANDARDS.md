# Technical Standards: Performance & Resilience Architecture

## Overview

This document outlines the technical standards and architectural patterns implemented for performance optimization and resilience in the Cashback Deals application. These utilities provide a centralized, reusable foundation for handling external dependencies, monitoring performance, and ensuring consistent user experience.

## Architecture Principles

### 1. Centralized Configuration
All timeout, performance, and resilience settings are managed through centralized configuration files to ensure consistency across the application.

### 2. Circuit Breaker Pattern
Implements automatic failure detection and recovery for external services, preventing cascade failures and improving system resilience.

### 3. Performance Monitoring
Real-time monitoring and analytics for all critical operations, providing insights for optimization and debugging.

### 4. Graceful Degradation
Intelligent fallback mechanisms ensure users always receive meaningful content, even when external services fail.

## Core Utilities

### 1. ResilientImage Component (`src/components/ui/ResilientImage.tsx`)

**Purpose**: Handles external image loading with timeout protection, retry logic, and intelligent fallbacks.

**Usage**:
```typescript
import { ResilientImage } from '@/components/ui/ResilientImage';

<ResilientImage
  src={imageUrl}
  alt="Product image"
  width={600}
  height={600}
  productName={product.name}
  brandName={product.brand?.name}
  enableValidation={true}
  showLoadingState={true}
  retryOnError={true}
/>
```

**Features**:
- Circuit breaker for Samsung image servers
- Exponential backoff retry (max 2 attempts)
- Product-specific placeholder generation
- Performance monitoring integration
- Timeout protection (8 seconds)

### 2. Search Cache System (`src/lib/cache/searchCache.ts`)

**Purpose**: Intelligent caching for search results with dynamic TTL based on performance.

**Usage**:
```typescript
import { cachedSearchProducts } from '@/lib/cache/searchCache';

const results = await cachedSearchProducts(
  searchFunction,
  query,
  page,
  pageSize
);
```

**Features**:
- LRU eviction policy
- Dynamic TTL (2-10 minutes based on query performance)
- Cache warming for popular searches
- Performance analytics and recommendations

### 3. Timeout Configuration (`src/lib/timeoutConfig.ts`)

**Purpose**: Centralized timeout management with environment-aware settings.

**Usage**:
```typescript
import { TIMEOUT_CONFIG, withTimeout } from '@/lib/timeoutConfig';

const result = await withTimeout(
  operation(),
  TIMEOUT_CONFIG.DATABASE.QUERY,
  'Operation description'
);
```

**Configuration Categories**:
- Database operations (5-15 seconds)
- External APIs (2-8 seconds)
- Image operations (5-30 seconds)
- Search operations (0.5-8 seconds)

### 4. Performance Monitoring (`src/lib/monitoring/imagePerformance.ts`)

**Purpose**: Real-time performance tracking and analytics for image loading operations.

**Usage**:
```typescript
import { startImageLoad, recordImageSuccess, recordImageFailure } from '@/lib/monitoring/imagePerformance';

const loadId = startImageLoad(imageUrl);
// ... image loading logic
recordImageSuccess(imageUrl, { width, height, retryCount });
```

**Metrics Tracked**:
- Load times and success rates
- Samsung server performance
- Circuit breaker states
- Fallback usage statistics

### 5. Query Optimizer (`src/lib/optimization/queryOptimizer.ts`)

**Purpose**: Database query performance monitoring and optimization recommendations.

**Usage**:
```typescript
import { monitorQuery } from '@/lib/optimization/queryOptimizer';

const result = await monitorQuery(
  'query-name',
  async () => await databaseOperation(),
  { timeout: 5000, logSlowQueries: true }
);
```

## Development Standards

### 1. Image Handling Standards

**ALWAYS USE**: `ResilientImage` component for external images
**NEVER USE**: Direct Next.js `Image` component for external URLs

```typescript
// ✅ Correct
<ResilientImage src={externalUrl} alt="..." />

// ❌ Incorrect
<Image src={externalUrl} alt="..." />
```

### 2. Timeout Standards

**ALWAYS IMPORT**: Timeout constants from centralized config
**NEVER HARDCODE**: Timeout values in components

```typescript
// ✅ Correct
import { TIMEOUT_CONFIG } from '@/lib/timeoutConfig';
const timeout = TIMEOUT_CONFIG.EXTERNAL_API.GENERAL;

// ❌ Incorrect
const timeout = 5000; // Hardcoded value
```

### 3. Error Handling Standards

**ALWAYS IMPLEMENT**: Circuit breaker pattern for external services
**ALWAYS PROVIDE**: Meaningful fallbacks for users

```typescript
// ✅ Correct - with circuit breaker
const circuitBreaker = getCircuitBreaker(serviceUrl);
if (!circuitBreaker.canAttempt()) {
  return fallbackResponse;
}

// ❌ Incorrect - no resilience
const response = await fetch(serviceUrl);
```

### 4. Performance Monitoring Standards

**ALWAYS MONITOR**: Critical operations and external dependencies
**ALWAYS LOG**: Performance metrics in development

```typescript
// ✅ Correct
const result = await monitorQuery('operation-name', operation);

// ❌ Incorrect
const result = await operation(); // No monitoring
```

## Performance Dashboard

### Accessing the Dashboard

The Image Performance Dashboard is available in development mode only:

1. **Keyboard Shortcut**: `Ctrl+Shift+I` (Windows/Linux) or `Cmd+Shift+I` (Mac)
2. **Manual Access**: Click "Image Performance" button in bottom-right corner
3. **Features**:
   - Real-time metrics display
   - Circuit breaker status indicators
   - Performance recommendations
   - Data export functionality

### Dashboard Metrics

- **Total Loads**: Number of image load attempts
- **Success Rate**: Percentage of successful loads
- **Samsung Image Stats**: Specific metrics for Samsung servers
- **Circuit Breaker Status**: Current protection states
- **Cache Performance**: Hit rates and optimization suggestions

## Cloudflare CDN Integration

### Recommended Cache Rules

```javascript
// Page Rules Configuration
{
  "/_next/image*": {
    "cache_level": "cache_everything",
    "edge_cache_ttl": 2592000, // 30 days
    "browser_cache_ttl": 86400  // 1 day
  },
  "/api/search/*": {
    "cache_level": "cache_everything", 
    "edge_cache_ttl": 300,      // 5 minutes
    "browser_cache_ttl": 60     // 1 minute
  },
  "*.samsung.com/*": {
    "cache_level": "cache_everything",
    "edge_cache_ttl": 86400,    // 24 hours
    "browser_cache_ttl": 3600   // 1 hour
  }
}
```

### Performance Optimizations

1. **Enable Cloudflare Polish**: Automatic image optimization
2. **WebP/AVIF Support**: Modern format delivery
3. **Mirage**: Lazy loading for mobile devices
4. **Argo Smart Routing**: Optimal path selection

### Cache Strategy

- **Static Assets**: Long TTL (30 days) with versioning
- **API Responses**: Short TTL (5 minutes) for freshness
- **External Images**: Medium TTL (24 hours) with fallbacks
- **Search Results**: Very short TTL (1 minute) for accuracy

## Testing Standards

### Unit Testing

```typescript
// Test resilient components
describe('ResilientImage', () => {
  it('should fallback on Samsung image failure', async () => {
    // Test circuit breaker activation
    // Test fallback image generation
    // Test performance monitoring
  });
});
```

### Integration Testing

```typescript
// Test complete user flows
describe('Product Search Flow', () => {
  it('should handle Samsung image timeouts gracefully', async () => {
    // Test search with Samsung products
    // Verify fallback images display
    // Check performance metrics
  });
});
```

### Performance Testing

```typescript
// Monitor performance thresholds
describe('Performance Benchmarks', () => {
  it('should load search results within 3 seconds', async () => {
    // Test with cache miss
    // Test with cache hit
    // Verify timeout handling
  });
});
```

## Deployment Considerations

### Environment Configuration

```bash
# Production Environment Variables
NEXT_PUBLIC_SITE_URL=https://yourdomain.com
NODE_ENV=production

# Timeout Multipliers (optional)
TIMEOUT_MULTIPLIER=1.0  # Production standard
```

### Monitoring Setup

1. **Enable Performance Dashboard**: Development only
2. **Configure Logging**: Production error tracking
3. **Set Up Alerts**: Circuit breaker state changes
4. **Monitor Metrics**: Cache hit rates and response times

### Rollback Procedures

1. **Circuit Breaker Reset**: Manual recovery for stuck states
2. **Cache Invalidation**: Clear problematic cached responses
3. **Fallback Activation**: Force fallback mode for external services
4. **Configuration Rollback**: Revert timeout and performance settings

## Future Enhancements

### Planned Improvements

1. **Machine Learning**: Predictive circuit breaker thresholds
2. **Advanced Caching**: Multi-tier cache with Redis integration
3. **Real-time Analytics**: Live performance dashboards
4. **Auto-scaling**: Dynamic timeout adjustment based on load

### Extension Points

1. **Custom Circuit Breakers**: Service-specific failure detection
2. **Performance Plugins**: Modular monitoring extensions
3. **Cache Strategies**: Pluggable caching algorithms
4. **Fallback Providers**: Multiple fallback service options

## Support and Maintenance

### Documentation Updates

- Update this document when adding new utilities
- Document performance thresholds and SLAs
- Maintain troubleshooting guides

### Code Reviews

- Verify proper utility usage in all PRs
- Check performance impact of changes
- Ensure fallback mechanisms are tested

### Performance Audits

- Monthly review of performance metrics
- Quarterly optimization of timeout values
- Annual architecture review and updates
