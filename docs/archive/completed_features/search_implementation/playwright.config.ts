/**
 * 📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
 * ===========================================
 * 🔄 ACTION: ARCHIVED from docs/UPDATES/SEARCH/ to docs/archive/completed_features/search_implementation/
 * 📁 ORIGINAL LOCATION: /docs/UPDATES/SEARCH/playwright.config.ts  
 * 📁 NEW LOCATION: /docs/archive/completed_features/search_implementation/playwright.config.ts
 * 🎯 REASON: Completed search functionality Playwright test configuration implementation
 * 📝 STATUS: Content preserved unchanged, archived as completed feature for test configuration reference
 * 👥 REVIEW REQUIRED: Development team can reference for search-specific Playwright configuration patterns and testing setup
 * 🏷️ CATEGORY: Archive - Completed Features (Search Implementation)
 * 📅 PURPOSE: Historical record of search functionality test configuration and Playwright setup procedures
 */

import { defineConfig, devices } from '@playwright/test';

/**
 * @file Playwright configuration for running tests related to the search functionality.
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  // Look for test files in the current directory.
  testDir: './',

  // Run all tests in parallel.
  fullyParallel: true,

  // Fail the build on CI if you accidentally left test.only in the source code.
  forbidOnly: !!process.env.CI,

  // Retry on CI only.
  retries: process.env.CI ? 2 : 0,

  // Opt out of parallel tests on CI.
  workers: process.env.CI ? 1 : undefined,

  // Reporter to use. See https://playwright.dev/docs/test-reporters
  reporter: 'html',

  use: {
    // Base URL to use in actions like `await page.goto('/')`.
    baseURL: 'http://localhost:3000',

    // Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer
    trace: 'on-first-retry',

    // Capture screenshot on failure.
    screenshot: 'only-on-failure',
  },

  // Configure projects for major browsers.
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],

  // Folder for test artifacts such as screenshots, videos, traces, etc.
  outputDir: 'test-results/',
});
