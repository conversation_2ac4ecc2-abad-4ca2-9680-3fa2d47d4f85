<!--
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: ARCHIVED from docs/UPDATES/FILTER FEATURE/ to docs/archive/completed_features/filter_system/
📁 ORIGINAL LOCATION: /docs/UPDATES/FILTER FEATURE/TECHNICAL_DESIGN.md  
📁 NEW LOCATION: /docs/archive/completed_features/filter_system/TECHNICAL_DESIGN.md
🎯 REASON: Completed filter system technical architecture - universal filter utility design and system integration patterns
📝 STATUS: Content preserved unchanged, archived as completed feature technical design documentation
👥 REVIEW REQUIRED: Development team can reference for filter implementation patterns and system architecture methodology
🏷️ CATEGORY: Archive - Completed Features (Filter System Technical Design)
📅 PURPOSE: Historical record of comprehensive filter system architecture with performance optimization and mobile-first design patterns
-->

# Universal Filter Utility - Technical Design Document

## Architecture Overview

### Design Principles

1. **Universal Compatibility**: Single codebase works across products, search, and brands pages
2. **Performance First**: Real-time filtering without sacrificing API response times
3. **Mobile-First UX**: Thumb-friendly drawer pattern with desktop sidebar fallback
4. **Type Safety**: Comprehensive TypeScript interfaces and runtime validation
5. **SEO Preservation**: Server-side rendering with clean, crawlable URLs

### System Architecture

```mermaid
graph TB
    A[Page Component] --> B[useUniversalFilter Hook]
    B --> C[URL State Management]
    B --> D[Filter Validation]
    B --> E[API Integration]
    
    F[UniversalFilterPanel] --> G[Individual Filter Components]
    G --> H[PriceFilter]
    G --> I[RatingFilter]
    G --> J[MerchantFilter]
    G --> K[AvailabilityFilter]
    
    L[FilterAPI] --> M[Database Queries]
    M --> N[Redis Cache]
    
    B --> F
    E --> L
```

## Core Components Design

### 1. useUniversalFilter Hook

**File**: `/src/hooks/useUniversalFilter.ts`

```typescript
interface UniversalFilterConfig {
  pageType: 'products' | 'search' | 'brands';
  initialFilters?: Partial<UniversalFilterState>;
  enabledFacets?: FilterFacetType[];
  cacheStrategy?: 'short' | 'medium' | 'long';
}

interface UniversalFilterState {
  // Core filters (Phase 1)
  priceRange?: { min: number; max: number };
  cashbackRange?: { min: number; max: number };
  merchants?: string[];
  rating?: { min: number; minReviews?: number };
  availability?: AvailabilityFilter[];
  shippingCost?: ShippingCostFilter;
  
  // Applied filter metadata
  appliedFilters: AppliedFilter[];
  resultCount: number;
  isLoading: boolean;
  hasError: boolean;
}

interface UniversalFilterHook {
  // State
  filterState: UniversalFilterState;
  
  // Actions
  updateFilter: (facet: FilterFacetType, value: any) => Promise<void>;
  removeFilter: (facet: FilterFacetType, value?: any) => Promise<void>;
  clearAllFilters: () => Promise<void>;
  
  // Utilities
  getFilterOptions: () => Promise<FilterOptions>;
  validateFilters: (filters: Partial<UniversalFilterState>) => ValidationResult;
  
  // URL Integration
  syncToUrl: () => void;
  parseFromUrl: () => UniversalFilterState;
}
```

**Key Features**:
- Wraps existing pagination hooks for backward compatibility
- Debounced API calls (300ms) to prevent excessive requests
- Automatic URL synchronization with clean parameters
- Real-time result count updates
- Filter validation and sanitization

### 2. UniversalFilterPanel Component

**File**: `/src/components/filters/UniversalFilterPanel.tsx`

```typescript
interface UniversalFilterPanelProps {
  config: UniversalFilterConfig;
  className?: string;
  variant?: 'drawer' | 'sidebar' | 'inline';
  onFilterChange?: (state: UniversalFilterState) => void;
}

// Mobile: Bottom drawer with sticky CTA
// Desktop: Left sidebar with collapsible sections
// Inline: Embedded in page content (for future use)
```

**Responsive Behavior**:
- **Mobile (< 768px)**: Bottom sheet drawer with backdrop
- **Tablet (768px - 1024px)**: Collapsible sidebar
- **Desktop (> 1024px)**: Fixed sidebar with filter categories

### 3. Individual Filter Components

#### PriceRangeFilter
```typescript
interface PriceRangeFilterProps {
  min: number;
  max: number;
  step?: number;
  currency?: 'GBP' | 'USD' | 'EUR';
  onChange: (range: { min: number; max: number }) => void;
}
```

#### RatingFilter
```typescript
interface RatingFilterProps {
  minRating: number;
  showReviewCount?: boolean;
  onChange: (rating: { min: number; minReviews?: number }) => void;
}
```

#### MerchantFilter
```typescript
interface MerchantFilterProps {
  merchants: Merchant[];
  selectedMerchants: string[];
  searchable?: boolean;
  showLogos?: boolean;
  onChange: (merchantIds: string[]) => void;
}
```

#### AvailabilityFilter
```typescript
interface AvailabilityFilterProps {
  options: AvailabilityOption[];
  selected: string[];
  onChange: (availability: string[]) => void;
}

type AvailabilityOption = 
  | 'in-stock'
  | 'same-day'
  | 'next-day'
  | '2-3-days'
  | 'free-shipping';
```

## Data Layer Design

### 1. Enhanced Filter Types

**File**: `/src/lib/data/types.ts`

```typescript
// Core filter interfaces
interface UniversalFilterState {
  priceRange?: PriceRange;
  cashbackRange?: CashbackRange;
  merchants?: string[];
  rating?: RatingFilter;
  availability?: AvailabilityFilter[];
  shippingCost?: ShippingCostFilter;
  
  // Metadata
  appliedFilters: AppliedFilter[];
  resultCount: number;
  facetCounts: Record<string, number>;
}

interface FilterOptions {
  priceRange: { min: number; max: number };
  merchants: Merchant[];
  availabilityOptions: AvailabilityOption[];
  categories?: Category[]; // For search page
}

interface AppliedFilter {
  id: string;
  facet: FilterFacetType;
  label: string;
  value: any;
  removable: boolean;
}
```

### 2. Filter Query Builder

**File**: `/src/lib/data/filterQueryBuilder.ts`

```typescript
class FilterQueryBuilder {
  private query: any;
  private facetCounts: Record<string, number> = {};
  
  constructor(private supabase: any, private baseQuery: any) {
    this.query = baseQuery;
  }
  
  applyPriceFilter(range: PriceRange): this {
    if (range.min > 0 || range.max < Number.MAX_VALUE) {
      this.query = this.query.gte('price', range.min).lte('price', range.max);
    }
    return this;
  }
  
  applyMerchantFilter(merchantIds: string[]): this {
    if (merchantIds.length > 0) {
      this.query = this.query.in('merchant_id', merchantIds);
    }
    return this;
  }
  
  applyRatingFilter(rating: RatingFilter): this {
    this.query = this.query.gte('average_rating', rating.min);
    if (rating.minReviews) {
      this.query = this.query.gte('review_count', rating.minReviews);
    }
    return this;
  }
  
  async execute(): Promise<{ data: any[], facetCounts: Record<string, number> }> {
    // Execute main query
    const { data, error } = await this.query;
    
    // Execute facet count queries in parallel
    const facetCounts = await this.calculateFacetCounts();
    
    return { data: data || [], facetCounts };
  }
  
  private async calculateFacetCounts(): Promise<Record<string, number>> {
    // Parallel facet count queries for zero-result prevention
    // Implementation details...
  }
}
```

### 3. Caching Strategy

**File**: `/src/lib/cache/filterCache.ts`

```typescript
interface CacheConfig {
  products: { ttl: 3600; prefix: 'pf:' }; // 1 hour
  search: { ttl: 300; prefix: 'sf:' }; // 5 minutes
  facets: { ttl: 1800; prefix: 'fc:' }; // 30 minutes
}

class FilterCache {
  async get<T>(key: string, pageType: keyof CacheConfig): Promise<T | null> {
    // Redis/memory cache implementation
  }
  
  async set<T>(key: string, value: T, pageType: keyof CacheConfig): Promise<void> {
    // Cache with appropriate TTL
  }
  
  generateCacheKey(filters: UniversalFilterState, pageType: string): string {
    // Create deterministic cache key from filter state
    const sortedFilters = this.canonicalizeFilters(filters);
    return `${pageType}:${hashObject(sortedFilters)}`;
  }
}
```

## API Integration Design

### 1. Enhanced API Routes

**File**: `/src/app/api/products/route.ts` (Enhanced)

```typescript
interface ProductsAPIRequest {
  // Existing parameters
  page?: number;
  limit?: number;
  search?: string;
  category_id?: string;
  
  // New filter parameters
  price_min?: number;
  price_max?: number;
  cashback_min?: number;
  cashback_max?: number;
  merchants?: string; // Comma-separated IDs
  rating_min?: number;
  rating_min_reviews?: number;
  availability?: string; // Comma-separated options
  shipping_cost_max?: number;
  
  // Facet count requests
  include_facet_counts?: boolean;
}

async function GET(request: NextRequest) {
  // Parse and validate filter parameters
  const filters = await parseFilterParams(request);
  
  // Build and execute query with caching
  const cacheKey = generateCacheKey(filters);
  const cached = await cache.get(cacheKey);
  
  if (cached) {
    return NextResponse.json(cached);
  }
  
  const queryBuilder = new FilterQueryBuilder(supabase, baseQuery);
  const result = await queryBuilder
    .applyPriceFilter(filters.priceRange)
    .applyMerchantFilter(filters.merchants)
    .applyRatingFilter(filters.rating)
    .execute();
  
  await cache.set(cacheKey, result);
  return NextResponse.json(result);
}
```

### 2. Filter Options API

**File**: `/src/app/api/filters/options/route.ts` (New)

```typescript
// Provides filter option data for dropdowns and ranges
async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const pageType = searchParams.get('page_type') || 'products';
  const categoryId = searchParams.get('category_id');
  
  const cacheKey = `filter_options:${pageType}:${categoryId || 'all'}`;
  const cached = await cache.get(cacheKey);
  
  if (cached) {
    return NextResponse.json(cached);
  }
  
  const options = await getFilterOptions(pageType, categoryId);
  await cache.set(cacheKey, options);
  
  return NextResponse.json(options);
}

async function getFilterOptions(pageType: string, categoryId?: string): Promise<FilterOptions> {
  const [priceRange, merchants, categories] = await Promise.all([
    getPriceRange(categoryId),
    getMerchants(categoryId),
    pageType === 'search' ? getCategories() : Promise.resolve([])
  ]);
  
  return {
    priceRange,
    merchants,
    categories,
    availabilityOptions: getAvailabilityOptions()
  };
}
```

## Performance Optimizations

### 1. Database Indexing Strategy

```sql
-- Core filter indexes
CREATE INDEX CONCURRENTLY idx_products_price ON products (price);
CREATE INDEX CONCURRENTLY idx_products_rating ON products (average_rating, review_count);
CREATE INDEX CONCURRENTLY idx_products_merchant ON products (merchant_id);
CREATE INDEX CONCURRENTLY idx_products_availability ON products (availability_status);

-- Composite indexes for common filter combinations
CREATE INDEX CONCURRENTLY idx_products_price_merchant 
ON products (price, merchant_id) WHERE price IS NOT NULL;

CREATE INDEX CONCURRENTLY idx_products_rating_price 
ON products (average_rating, price) WHERE average_rating >= 3.0;

-- Full-text search with filters
CREATE INDEX CONCURRENTLY idx_products_search_filters 
ON products USING gin(search_vector, merchant_id, price);
```

### 2. Client-Side Optimizations

```typescript
// Debounced filter updates
const debouncedUpdateFilter = useMemo(
  () => debounce(async (facet: FilterFacetType, value: any) => {
    setIsLoading(true);
    try {
      const result = await updateFilterAPI(facet, value);
      setFilterState(result);
    } finally {
      setIsLoading(false);
    }
  }, 300),
  []
);

// Optimistic updates for instant feedback
const optimisticUpdateFilter = useCallback((facet: FilterFacetType, value: any) => {
  // Update UI immediately
  setFilterState(prev => ({
    ...prev,
    [facet]: value,
    isLoading: true
  }));
  
  // Then sync with server
  debouncedUpdateFilter(facet, value);
}, []);
```

## Security Considerations

### 1. Input Validation

```typescript
const FilterParamsSchema = z.object({
  price_min: z.coerce.number().min(0).max(100000).optional(),
  price_max: z.coerce.number().min(0).max(100000).optional(),
  merchants: z.string().regex(/^[a-zA-Z0-9,-]+$/).optional(),
  rating_min: z.coerce.number().min(1).max(5).optional(),
  // Additional validation schemas...
});

// Sanitization for search terms
function sanitizeSearchTerm(term: string): string {
  return DOMPurify.sanitize(term.trim().slice(0, 100));
}
```

### 2. Rate Limiting

```typescript
// Apply rate limiting to filter API endpoints
const filterRateLimit = new RateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500, // Limit each IP to 500 requests per minute
});
```

## Testing Strategy

### 1. Unit Tests

```typescript
// Hook testing
describe('useUniversalFilter', () => {
  it('should update filter state correctly', async () => {
    const { result } = renderHook(() => useUniversalFilter({
      pageType: 'products'
    }));
    
    await act(async () => {
      await result.current.updateFilter('priceRange', { min: 10, max: 100 });
    });
    
    expect(result.current.filterState.priceRange).toEqual({ min: 10, max: 100 });
  });
});

// Component testing
describe('PriceRangeFilter', () => {
  it('should call onChange when range changes', () => {
    const onChange = jest.fn();
    render(<PriceRangeFilter min={0} max={1000} onChange={onChange} />);
    
    // Simulate range slider interaction
    fireEvent.change(screen.getByRole('slider'), { target: { value: 500 } });
    
    expect(onChange).toHaveBeenCalledWith({ min: 0, max: 500 });
  });
});
```

### 2. Integration Tests

```typescript
describe('Filter Integration', () => {
  it('should filter products correctly', async () => {
    // Test full filter flow from UI to API
    render(<ProductsPage />);
    
    // Open filter panel
    fireEvent.click(screen.getByText('Filters'));
    
    // Apply price filter
    fireEvent.change(screen.getByRole('slider'), { target: { value: 100 } });
    
    // Wait for results
    await waitFor(() => {
      expect(screen.getByText(/\d+ products found/)).toBeInTheDocument();
    });
    
    // Verify URL updated
    expect(window.location.search).toContain('price_max=100');
  });
});
```

### 3. Performance Tests

```typescript
describe('Filter Performance', () => {
  it('should respond within 300ms', async () => {
    const start = Date.now();
    
    const response = await fetch('/api/products?price_min=10&price_max=100');
    const data = await response.json();
    
    const duration = Date.now() - start;
    expect(duration).toBeLessThan(300);
    expect(data.products).toBeDefined();
  });
});
```

## Monitoring & Analytics

### 1. Performance Metrics

```typescript
// Track filter performance
interface FilterMetrics {
  filterType: FilterFacetType;
  responseTime: number;
  resultCount: number;
  cacheHit: boolean;
  errorRate: number;
}

// Analytics events
const trackFilterUsage = (facet: FilterFacetType, value: any) => {
  analytics.track('Filter Applied', {
    facet,
    value,
    page: window.location.pathname,
    sessionId: getSessionId(),
    timestamp: Date.now()
  });
};
```

### 2. Error Monitoring

```typescript
// Sentry integration for filter errors
try {
  await updateFilter(facet, value);
} catch (error) {
  Sentry.withScope((scope) => {
    scope.setTag('filterFacet', facet);
    scope.setContext('filterValue', value);
    Sentry.captureException(error);
  });
  
  // Fallback UI
  showErrorMessage('Filter update failed. Please try again.');
}
```

---

**Document Version**: 1.0  
**Last Updated**: 10 JUL 2025  
**Review Status**: Ready for Technical Review  
**Implementation Target**: Sprint 1 Start