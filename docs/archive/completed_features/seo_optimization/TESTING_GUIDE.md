<!--
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: ARCHIVED from docs/SEO/ to docs/archive/completed_features/seo_optimization/
📁 ORIGINAL LOCATION: /docs/SEO/TESTING_GUIDE.md  
📁 NEW LOCATION: /docs/archive/completed_features/seo_optimization/TESTING_GUIDE.md
🎯 REASON: Completed SEO optimization implementation - SEO testing and quality assurance guide for optimization validation
📝 STATUS: Content preserved unchanged, archived as completed feature testing documentation for SEO optimization quality assurance
👥 REVIEW REQUIRED: SEO and development teams can reference for SEO testing patterns and quality assurance procedures
🏷️ CATEGORY: Archive - Completed Features (SEO Optimization & SSR Migration)
📅 PURPOSE: Historical record of SEO testing guide, quality assurance strategies, and optimization validation procedures
-->

# SEO Testing and Quality Assurance Guide

## Testing Strategy Overview

### Testing Pyramid for SEO
```
                    E2E SEO Tests
                   /              \
              Integration Tests
             /                    \
        Unit Tests (Metadata, Utils)
       /                            \
  Static Analysis (Lighthouse, Schema)
```

## Unit Testing

### Metadata Generation Tests
```typescript
// tests/lib/metadata-utils.test.ts
import { constructMetadata } from '@/lib/metadata-utils'

describe('Metadata Utils', () => {
  it('should generate correct metadata for products', () => {
    const metadata = constructMetadata({
      title: 'Test Product',
      description: 'A test product description',
      image: 'https://example.com/image.jpg',
      pathname: '/products/test'
    })

    expect(metadata.title).toBe('Test Product | CashbackDeals')
    expect(metadata.description).toBe('A test product description')
    expect(metadata.openGraph?.title).toBe('Test Product | CashbackDeals')
    expect(metadata.openGraph?.images?.[0]?.url).toBe('https://example.com/image.jpg')
    expect(metadata.alternates?.canonical).toBe('https://your-domain.com/products/test')
  })

  it('should handle missing optional fields', () => {
    const metadata = constructMetadata({
      title: 'Test Page'
    })

    expect(metadata.title).toBe('Test Page | CashbackDeals')
    expect(metadata.description).toBe('Discover and compare cashback deals and rebates from top brands in the UK.')
    expect(metadata.openGraph?.images).toBeUndefined()
  })

  it('should set noIndex for error pages', () => {
    const metadata = constructMetadata({
      title: 'Page Not Found',
      noIndex: true
    })

    expect(metadata.robots?.index).toBe(false)
    expect(metadata.robots?.follow).toBe(false)
  })
})
```

### Structured Data Tests
```typescript
// tests/lib/structured-data.test.ts
import { generateProductSchema, generateBrandSchema } from '@/lib/structured-data'

describe('Structured Data', () => {
  const mockProduct = {
    id: '1',
    name: 'Test Product',
    description: 'Test description',
    price: 99.99,
    image_url: 'https://example.com/image.jpg',
    brand: { name: 'Test Brand', logo_url: 'https://example.com/logo.jpg' },
    category: { name: 'Electronics' },
    retailerOffers: [
      {
        price: 99.99,
        stock_status: 'in_stock',
        url: 'https://retailer.com/product',
        retailer: { name: 'Test Retailer' }
      }
    ]
  }

  it('should generate valid Product schema', () => {
    const schema = generateProductSchema(mockProduct)

    expect(schema['@context']).toBe('https://schema.org')
    expect(schema['@type']).toBe('Product')
    expect(schema.name).toBe('Test Product')
    expect(schema.brand.name).toBe('Test Brand')
    expect(schema.offers).toHaveLength(1)
    expect(schema.offers[0]['@type']).toBe('Offer')
    expect(schema.offers[0].price).toBe(99.99)
    expect(schema.offers[0].priceCurrency).toBe('GBP')
  })

  it('should handle products without offers', () => {
    const productWithoutOffers = { ...mockProduct, retailerOffers: [] }
    const schema = generateProductSchema(productWithoutOffers)

    expect(schema.offers).toHaveLength(0)
  })
})
```

### SEO Utils Tests
```typescript
// tests/lib/seo-utils.test.ts
import { generateCanonicalUrl, validateMetaDescription, generateSlug } from '@/lib/seo-utils'

describe('SEO Utils', () => {
  describe('generateCanonicalUrl', () => {
    it('should generate correct canonical URLs', () => {
      expect(generateCanonicalUrl('/products/test')).toBe('https://your-domain.com/products/test')
      expect(generateCanonicalUrl('/products/test/')).toBe('https://your-domain.com/products/test')
    })
  })

  describe('validateMetaDescription', () => {
    it('should validate description length', () => {
      const shortDesc = 'Short description'
      const longDesc = 'A'.repeat(200)
      
      expect(validateMetaDescription(shortDesc)).toEqual({
        isValid: false,
        message: 'Description too short (minimum 120 characters)'
      })
      
      expect(validateMetaDescription(longDesc)).toEqual({
        isValid: false,
        message: 'Description too long (maximum 160 characters)'
      })
    })
  })

  describe('generateSlug', () => {
    it('should generate SEO-friendly slugs', () => {
      expect(generateSlug('Test Product Name')).toBe('test-product-name')
      expect(generateSlug('Product with Special Characters!')).toBe('product-with-special-characters')
    })
  })
})
```

## Integration Testing

### Page Metadata Integration Tests
```typescript
// tests/integration/metadata.test.ts
import { generateMetadata } from '@/app/products/[id]/page'
import { getProduct } from '@/lib/data/products'

// Mock the data fetching
jest.mock('@/lib/data/products')
const mockGetProduct = getProduct as jest.MockedFunction<typeof getProduct>

describe('Product Page Metadata Integration', () => {
  beforeEach(() => {
    mockGetProduct.mockResolvedValue({
      id: '1',
      name: 'Test Product',
      description: 'A comprehensive test product description',
      image_url: 'https://example.com/image.jpg',
      brand: { name: 'Test Brand' }
    })
  })

  it('should generate metadata for existing product', async () => {
    const metadata = await generateMetadata({ params: { id: '1' } })

    expect(metadata.title).toBe('Test Product - Test Brand | CashbackDeals')
    expect(metadata.description).toContain('Test Product')
    expect(metadata.openGraph?.images?.[0]?.url).toBe('https://example.com/image.jpg')
  })

  it('should handle product not found', async () => {
    mockGetProduct.mockRejectedValue(new Error('Product not found'))

    const metadata = await generateMetadata({ params: { id: 'invalid' } })

    expect(metadata.title).toBe('Product Not Found | CashbackDeals')
    expect(metadata.robots?.index).toBe(false)
  })
})
```

### API Route Testing
```typescript
// tests/integration/api.test.ts
import { GET } from '@/app/api/products/[id]/route'
import { NextRequest } from 'next/server'

describe('Product API Route', () => {
  it('should return product data with proper headers', async () => {
    const request = new NextRequest('http://localhost:3000/api/products/1')
    const response = await GET(request, { params: { id: '1' } })
    
    expect(response.status).toBe(200)
    expect(response.headers.get('Cache-Control')).toContain('s-maxage=3600')
    
    const data = await response.json()
    expect(data.product).toBeDefined()
    expect(data.product.id).toBe('1')
  })

  it('should handle invalid product ID', async () => {
    const request = new NextRequest('http://localhost:3000/api/products/invalid')
    const response = await GET(request, { params: { id: 'invalid' } })
    
    expect(response.status).toBe(404)
  })
})
```

## E2E Testing with Playwright

### SEO E2E Tests
```typescript
// tests/e2e/seo.spec.ts
import { test, expect } from '@playwright/test'

test.describe('SEO E2E Tests', () => {
  test('homepage should have proper SEO elements', async ({ page }) => {
    await page.goto('/')

    // Check title
    await expect(page).toHaveTitle(/CashbackDeals - Find the Best Rebates/)

    // Check meta description
    const metaDescription = page.locator('meta[name="description"]')
    await expect(metaDescription).toHaveAttribute('content', /Discover and compare cashback deals/)

    // Check structured data
    const structuredData = page.locator('script[type="application/ld+json"]')
    await expect(structuredData).toBeVisible()

    // Check canonical URL
    const canonical = page.locator('link[rel="canonical"]')
    await expect(canonical).toHaveAttribute('href', /https:\/\//)
  })

  test('product page should have dynamic metadata', async ({ page }) => {
    await page.goto('/products/test-product')

    // Check dynamic title
    await expect(page).toHaveTitle(/Test Product/)

    // Check OpenGraph tags
    const ogTitle = page.locator('meta[property="og:title"]')
    await expect(ogTitle).toHaveAttribute('content', /Test Product/)

    const ogImage = page.locator('meta[property="og:image"]')
    await expect(ogImage).toHaveAttribute('content', /https:\/\//)

    // Check structured data
    const productSchema = page.locator('script[type="application/ld+json"]')
    const schemaContent = await productSchema.textContent()
    const schema = JSON.parse(schemaContent || '{}')
    
    expect(schema['@type']).toBe('Product')
    expect(schema.name).toBe('Test Product')
  })

  test('search page should handle query parameters', async ({ page }) => {
    await page.goto('/search?q=laptop')

    // Check dynamic title
    await expect(page).toHaveTitle(/Search results for "laptop"/)

    // Check that results are displayed
    const results = page.locator('[data-testid="search-results"]')
    await expect(results).toBeVisible()

    // Check URL state management
    await page.fill('[data-testid="search-input"]', 'phone')
    await page.press('[data-testid="search-input"]', 'Enter')
    
    await expect(page).toHaveURL(/q=phone/)
    await expect(page).toHaveTitle(/Search results for "phone"/)
  })
})
```

### Performance E2E Tests
```typescript
// tests/e2e/performance.spec.ts
import { test, expect } from '@playwright/test'

test.describe('Performance Tests', () => {
  test('homepage should meet Core Web Vitals', async ({ page }) => {
    await page.goto('/')

    // Measure LCP
    const lcp = await page.evaluate(() => {
      return new Promise((resolve) => {
        new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          resolve(lastEntry.startTime)
        }).observe({ entryTypes: ['largest-contentful-paint'] })
      })
    })

    expect(lcp).toBeLessThan(2500) // LCP should be < 2.5s

    // Check for layout shifts
    const cls = await page.evaluate(() => {
      return new Promise((resolve) => {
        let clsValue = 0
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          }
          resolve(clsValue)
        }).observe({ entryTypes: ['layout-shift'] })
        
        setTimeout(() => resolve(clsValue), 5000)
      })
    })

    expect(cls).toBeLessThan(0.1) // CLS should be < 0.1
  })

  test('images should load with proper optimization', async ({ page }) => {
    await page.goto('/products/test-product')

    // Check that images use Next.js Image component
    const images = page.locator('img')
    const imageCount = await images.count()

    for (let i = 0; i < imageCount; i++) {
      const img = images.nth(i)
      
      // Check for proper loading attribute
      const loading = await img.getAttribute('loading')
      if (i === 0) {
        // First image should be eager (priority)
        expect(loading).toBe('eager')
      } else {
        // Other images should be lazy
        expect(loading).toBe('lazy')
      }

      // Check for proper sizes attribute
      const sizes = await img.getAttribute('sizes')
      expect(sizes).toBeTruthy()
    }
  })
})
```

## Automated SEO Auditing

### Lighthouse CI Configuration
```javascript
// lighthouserc.js
module.exports = {
  ci: {
    collect: {
      url: [
        'http://localhost:3000/',
        'http://localhost:3000/products/test-product',
        'http://localhost:3000/brands/test-brand',
        'http://localhost:3000/search?q=laptop'
      ],
      numberOfRuns: 3
    },
    assert: {
      assertions: {
        'categories:seo': ['error', { minScore: 0.95 }],
        'categories:performance': ['error', { minScore: 0.8 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['error', { minScore: 0.9 }]
      }
    },
    upload: {
      target: 'temporary-public-storage'
    }
  }
}
```

### Schema Validation Script
```javascript
// scripts/validate-structured-data.js
const { JSDOM } = require('jsdom')
const fetch = require('node-fetch')

async function validateStructuredData(url) {
  try {
    const response = await fetch(url)
    const html = await response.text()
    const dom = new JSDOM(html)
    
    const scripts = dom.window.document.querySelectorAll('script[type="application/ld+json"]')
    
    for (const script of scripts) {
      try {
        const data = JSON.parse(script.textContent)
        console.log(`✅ Valid JSON-LD found: ${data['@type']}`)
        
        // Basic validation
        if (!data['@context'] || !data['@type']) {
          console.error('❌ Missing required @context or @type')
        }
        
        // Product-specific validation
        if (data['@type'] === 'Product') {
          if (!data.name || !data.description) {
            console.error('❌ Product missing required fields')
          }
        }
        
      } catch (error) {
        console.error('❌ Invalid JSON-LD:', error.message)
      }
    }
  } catch (error) {
    console.error('❌ Failed to fetch page:', error.message)
  }
}

// Run validation
const urls = [
  'http://localhost:3000/',
  'http://localhost:3000/products/test-product',
  'http://localhost:3000/brands/test-brand'
]

urls.forEach(validateStructuredData)
```

### Performance Monitoring Script
```javascript
// scripts/monitor-performance.js
const lighthouse = require('lighthouse')
const chromeLauncher = require('chrome-launcher')

async function runLighthouse(url) {
  const chrome = await chromeLauncher.launch({ chromeFlags: ['--headless'] })
  
  const options = {
    logLevel: 'info',
    output: 'json',
    onlyCategories: ['performance', 'seo'],
    port: chrome.port
  }
  
  const runnerResult = await lighthouse(url, options)
  await chrome.kill()
  
  const { lhr } = runnerResult
  
  console.log(`Performance Score: ${lhr.categories.performance.score * 100}`)
  console.log(`SEO Score: ${lhr.categories.seo.score * 100}`)
  
  // Check Core Web Vitals
  const lcp = lhr.audits['largest-contentful-paint'].numericValue
  const fid = lhr.audits['max-potential-fid'].numericValue
  const cls = lhr.audits['cumulative-layout-shift'].numericValue
  
  console.log(`LCP: ${lcp}ms (target: <2500ms)`)
  console.log(`FID: ${fid}ms (target: <100ms)`)
  console.log(`CLS: ${cls} (target: <0.1)`)
  
  return {
    performance: lhr.categories.performance.score * 100,
    seo: lhr.categories.seo.score * 100,
    lcp,
    fid,
    cls
  }
}

// Monitor multiple pages
const pages = [
  'http://localhost:3000/',
  'http://localhost:3000/products/test-product'
]

Promise.all(pages.map(runLighthouse))
  .then(results => {
    console.log('Performance monitoring complete:', results)
  })
  .catch(console.error)
```

## Testing Checklist

### Pre-Deployment Testing
- [ ] All unit tests pass
- [ ] Integration tests pass
- [ ] E2E tests pass
- [ ] Lighthouse SEO score > 95
- [ ] Core Web Vitals meet thresholds
- [ ] Structured data validates
- [ ] Meta tags are correct
- [ ] Canonical URLs are set
- [ ] Sitemap is accessible
- [ ] robots.txt is correct

### Post-Deployment Monitoring
- [ ] Google Search Console setup
- [ ] Performance monitoring active
- [ ] Error tracking configured
- [ ] SEO metrics tracking
- [ ] Core Web Vitals monitoring
- [ ] Structured data monitoring

This comprehensive testing guide ensures that SEO optimizations are properly implemented and maintained throughout the development lifecycle.
