/*
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: ARCHIVED from docs/SEO/db/ to docs/archive/completed_features/seo_optimization/db/
📁 ORIGINAL LOCATION: /docs/SEO/db/db_schema_products_brands_promotions.json  
📁 NEW LOCATION: /docs/archive/completed_features/seo_optimization/db/db_schema_products_brands_promotions.json
🎯 REASON: Completed SEO optimization implementation - Database schema definition for products, brands, and promotions data structure
📝 STATUS: Content preserved unchanged, archived as completed feature database documentation for SEO optimization data layer
👥 REVIEW REQUIRED: SEO and development teams can reference for database schema patterns and data structure optimization procedures
🏷️ CATEGORY: Archive - Completed Features (SEO Optimization & SSR Migration)
📅 PURPOSE: Historical record of database schema definition, data structure specifications, and SEO optimization data layer architecture
*/
[
  {
    "column_name": "is_featured",
    "data_type": "boolean"
  },
  {
    "column_name": "brand_id",
    "data_type": "uuid"
  },
  {
    "column_name": "category_id",
    "data_type": "uuid"
  },
  {
    "column_name": "last_validated_at",
    "data_type": "timestamp without time zone"
  },
  {
    "column_name": "created_at",
    "data_type": "timestamp without time zone"
  },
  {
    "column_name": "version",
    "data_type": "bigint"
  },
  {
    "column_name": "id",
    "data_type": "uuid"
  },
  {
    "column_name": "max_cashback_amount",
    "data_type": "numeric"
  },
  {
    "column_name": "purchase_start_date",
    "data_type": "date"
  },
  {
    "column_name": "purchase_end_date",
    "data_type": "date"
  },
  {
    "column_name": "claim_start_offset_days",
    "data_type": "integer"
  },
  {
    "column_name": "claim_window_days",
    "data_type": "integer"
  },
  {
    "column_name": "status",
    "data_type": "USER-DEFINED"
  },
  {
    "column_name": "title",
    "data_type": "character varying"
  },
  {
    "column_name": "description",
    "data_type": "text"
  },
  {
    "column_name": "terms_description",
    "data_type": "text"
  },
  {
    "column_name": "terms_url",
    "data_type": "character varying"
  }
]