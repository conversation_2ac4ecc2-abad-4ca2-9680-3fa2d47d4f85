<!--
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: ARCHIVED from docs/SEO/ to docs/archive/completed_features/seo_optimization/
📁 ORIGINAL LOCATION: /docs/SEO/TECHNICAL_ARCHITECTURE.md  
📁 NEW LOCATION: /docs/archive/completed_features/seo_optimization/TECHNICAL_ARCHITECTURE.md
🎯 REASON: Completed SEO optimization implementation - Technical architecture design and implementation for SEO optimization
📝 STATUS: Content preserved unchanged, archived as completed feature technical documentation for SEO optimization architecture
👥 REVIEW REQUIRED: SEO and development teams can reference for technical architecture patterns and SEO optimization implementation procedures
🏷️ CATEGORY: Archive - Completed Features (SEO Optimization & SSR Migration)
📅 PURPOSE: Historical record of technical architecture design, SEO optimization implementation strategy, and application structure analysis
-->

# NOTICE - MOST OF THIS SCOPE IS NOT OUT OF DATE


# Technical Architecture for SEO Optimization

## Current Architecture Analysis

### Application Structure
```
cashback-deals-v2/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── layout.tsx         # Root layout (SERVER)
│   │   ├── page.tsx           # Homepage (CLIENT) ❌
│   │   ├── products/
│   │   │   ├── page.tsx       # Products listing (CLIENT) ❌
│   │   │   └── [id]/page.tsx  # Product details (CLIENT) ❌
│   │   ├── brands/
│   │   │   ├── page.tsx       # Brands listing (CLIENT) ❌
│   │   │   └── [id]/page.tsx  # Brand details (CLIENT) ❌
│   │   ├── search/page.tsx    # Search page (CLIENT) ❌
│   │   └── api/               # API routes (SERVER)
│   ├── components/            # UI components
│   ├── lib/                   # Utilities and configurations
│   └── types/                 # TypeScript definitions
```

### Current Rendering Strategy
- **Root Layout**: Server Component ✅
- **Main Pages**: Client-Side Rendering ❌
- **API Routes**: Server-side ✅
- **Data Fetching**: Client-side with React Query ❌

## Target Architecture

### Hybrid Rendering Strategy
```
Page Type          | Current    | Target     | Reason
-------------------|------------|------------|------------------
Homepage           | CSR        | SSR        | SEO + Performance
Product Details    | CSR        | SSR        | SEO + Metadata
Brand Details      | CSR        | SSR        | SEO + Metadata
Product Listing    | CSR        | Hybrid     | SEO + Interactivity
Brand Listing      | CSR        | SSG        | Performance
Search Results     | CSR        | Hybrid     | SEO + UX
Static Pages       | SSR        | SSG        | Performance
```

### Component Architecture Pattern

#### Server Component (Data + SEO)
```typescript
// src/app/products/[id]/page.tsx
export async function generateMetadata({ params }): Promise<Metadata> {
  const product = await getProduct(params.id)
  return constructMetadata({
    title: `${product.name} - ${product.brand.name}`,
    description: product.description,
    image: product.image_url
  })
}

export default async function ProductPage({ params }) {
  const product = await getProduct(params.id)
  const similarProducts = await getSimilarProducts(product.category_id)
  
  return (
    <>
      <ProductStructuredData product={product} />
      <ProductPageClient 
        product={product} 
        similarProducts={similarProducts} 
      />
    </>
  )
}
```

#### Client Component (Interactivity)
```typescript
// src/components/products/ProductPageClient.tsx
'use client'
export function ProductPageClient({ product, similarProducts }) {
  return (
    <motion.div>
      <ProductInfo product={product} />
      <PriceComparison offers={product.retailerOffers} />
      <SimilarProducts products={similarProducts} />
    </motion.div>
  )
}
```

## Data Fetching Strategy

### Server-Side Data Fetching
```typescript
// src/lib/data/products.ts
import { createServerClient } from '@/lib/supabase/server'

export async function getProduct(id: string) {
  const supabase = createServerClient()
  
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      brands:brand_id (*),
      categories:category_id (*),
      product_retailer_offers (
        *,
        retailers:retailer_id (*)
      )
    `)
    .eq('id', id)
    .eq('status', 'active')
    .single()
    
  if (error) throw new Error(`Product not found: ${error.message}`)
  return data
}
```

### Client-Side Data Fetching (for updates)
```typescript
// src/hooks/useProductUpdates.ts
'use client'
export function useProductUpdates(productId: string) {
  return useQuery({
    queryKey: ['product-updates', productId],
    queryFn: () => fetchProductUpdates(productId),
    refetchInterval: 30000, // 30 seconds
    staleTime: 25000
  })
}
```

## SEO Implementation Architecture

### Metadata Generation System
```typescript
// src/lib/seo/metadata.ts
export interface SEOConfig {
  title: string
  description: string
  image?: string
  type?: 'website' | 'product' | 'organization'
  pathname?: string
}

export function generateSEOMetadata(config: SEOConfig): Metadata {
  return {
    title: `${config.title} | CashbackDeals`,
    description: config.description,
    openGraph: {
      title: config.title,
      description: config.description,
      images: config.image ? [{ url: config.image }] : undefined,
      type: config.type || 'website'
    },
    twitter: {
      card: 'summary_large_image',
      title: config.title,
      description: config.description
    }
  }
}
```

### Structured Data System
```typescript
// src/lib/seo/structured-data.ts
export function generateProductSchema(product: Product) {
  return {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": product.name,
    "description": product.description,
    "brand": {
      "@type": "Brand",
      "name": product.brand.name
    },
    "offers": {
      "@type": "Offer",
      "price": product.price,
      "priceCurrency": "GBP",
      "availability": "https://schema.org/InStock"
    }
  }
}

export function StructuredData({ data }: { data: object }) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(data) }}
    />
  )
}
```

## Performance Optimization Architecture

### Caching Strategy
```typescript
// API Route Caching
export const revalidate = 3600 // 1 hour
export const runtime = 'edge'

// Component-level caching
export async function getStaticProps() {
  return {
    props: { data },
    revalidate: 3600 // ISR
  }
}

// Database query caching
const cachedQuery = unstable_cache(
  async (id: string) => getProduct(id),
  ['product'],
  { revalidate: 3600 }
)
```

### Image Optimization
```typescript
// src/components/ui/OptimizedImage.tsx
import Image from 'next/image'

interface OptimizedImageProps {
  src: string
  alt: string
  priority?: boolean
  sizes?: string
}

export function OptimizedImage({ 
  src, 
  alt, 
  priority = false,
  sizes = "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
}: OptimizedImageProps) {
  return (
    <Image
      src={src}
      alt={alt}
      priority={priority}
      sizes={sizes}
      className="object-cover"
      placeholder="blur"
      blurDataURL="data:image/jpeg;base64,..."
    />
  )
}
```

### Loading States Architecture
```typescript
// src/app/products/[id]/loading.tsx
export default function ProductLoading() {
  return <ProductPageSkeleton />
}

// src/components/ui/ProductPageSkeleton.tsx
export function ProductPageSkeleton() {
  return (
    <div className="animate-pulse">
      <div className="h-8 bg-gray-200 rounded mb-4"></div>
      <div className="h-64 bg-gray-200 rounded mb-4"></div>
      <div className="h-4 bg-gray-200 rounded mb-2"></div>
    </div>
  )
}
```

## Error Handling Architecture

### Error Boundaries
```typescript
// src/app/products/[id]/error.tsx
'use client'
export default function ProductError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  return (
    <div className="text-center py-12">
      <h2>Something went wrong!</h2>
      <button onClick={reset}>Try again</button>
    </div>
  )
}
```

### Not Found Pages
```typescript
// src/app/products/[id]/not-found.tsx
export default function ProductNotFound() {
  return (
    <div className="text-center py-12">
      <h1>Product Not Found</h1>
      <p>The product you're looking for doesn't exist.</p>
      <Link href="/products">Browse Products</Link>
    </div>
  )
}
```

## Database Architecture Considerations

### Query Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_products_brand_category ON products(brand_id, category_id);
CREATE INDEX idx_products_status_created ON products(status, created_at);
CREATE INDEX idx_promotions_featured_status ON promotions(is_featured, status);
```

### Data Relationships
```typescript
// Optimized query structure
const productQuery = `
  *,
  brands:brand_id (id, name, logo_url),
  categories:category_id (id, name),
  product_retailer_offers (
    price,
    stock_status,
    url,
    retailers:retailer_id (name, logo_url)
  )
`
```

## Monitoring and Analytics Architecture

### Performance Monitoring
```typescript
// src/lib/analytics/web-vitals.ts
export function reportWebVitals(metric: NextWebVitalsMetric) {
  switch (metric.name) {
    case 'LCP':
      gtag('event', 'LCP', { value: metric.value })
      break
    case 'FID':
      gtag('event', 'FID', { value: metric.value })
      break
    case 'CLS':
      gtag('event', 'CLS', { value: metric.value })
      break
  }
}
```

### Error Tracking
```typescript
// src/lib/monitoring/error-tracking.ts
export function trackError(error: Error, context?: object) {
  console.error('Application Error:', error, context)
  
  // Send to monitoring service
  if (process.env.NODE_ENV === 'production') {
    // Sentry, LogRocket, etc.
  }
}
```

## Security Considerations

### Environment Variables
```bash
# Required for SEO optimization
NEXT_PUBLIC_SUPABASE_URL=
NEXT_PUBLIC_SUPABASE_ANON_KEY=
SUPABASE_SERVICE_ROLE_KEY=
NEXT_PUBLIC_SITE_URL=
NEXT_PUBLIC_GA_MEASUREMENT_ID=
```

### Rate Limiting
```typescript
// src/lib/rate-limiting.ts
export async function rateLimit(identifier: string) {
  const limit = 100 // requests per hour
  const window = 3600 // 1 hour in seconds
  
  // Implementation depends on your infrastructure
  // Redis, Vercel KV, etc.
}
```

This technical architecture provides a solid foundation for implementing SEO optimizations while maintaining performance and user experience standards.
