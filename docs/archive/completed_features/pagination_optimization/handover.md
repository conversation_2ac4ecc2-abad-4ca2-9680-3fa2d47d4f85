<!--
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: ARCHIVED from docs/UPDATES/PAGINATION/ to docs/archive/completed_features/pagination_optimization/
📁 ORIGINAL LOCATION: /docs/UPDATES/PAGINATION/handover.md  
📁 NEW LOCATION: /docs/archive/completed_features/pagination_optimization/handover.md
🎯 REASON: Completed pagination optimization - technical handover documentation (original version)
📝 STATUS: Content preserved unchanged, archived as completed feature documenting implementation approach
👥 REVIEW REQUIRED: Development team can reference for scroll behavior patterns and Next.js navigation implementation
🏷️ CATEGORY: Archive - Completed Features (Pagination & URL State Management)
📅 PURPOSE: Historical record of pagination and scroll behavior technical handover including problem overview, solution evolution, implementation details, type consistency fixes, and debugging procedures
-->

Pagination and Scroll Behavior: Technical Handover


  1. Overview of the Problem


  Initially, the application faced two primary user experience issues related to
  pagination and navigation:


   1. Pagination Scroll (Original Issue): When a user navigated between pages on a
      listing page (e.g., /products?page=1 to /products?page=2), the browser's
      scroll position remained unchanged. Users had to manually scroll to the top
      of the new results, which was disorienting.
   2. "Back to List" Scroll (Subsequent Issue): When a user navigated from a
      listing page to a detail page (e.g., /products to
      /products/some-product-slug), and then used an in-app "Back to Products"
      link, they would land at the top of the listing page, losing their previous
      scroll context. The desired behavior was to return to the exact scroll
      position they left off at.

  2. Evolution of the Solution


  The solution evolved through several iterations, addressing both the core
  requirements and unexpected side effects (primarily type errors due to
  inconsistencies in the codebase's data models).

  2.1 Initial Approaches & Learnings


   * `useEffect` with `window.scrollTo(0,0)` in `usePagination.ts`: Our first
     attempt to fix pagination scroll involved adding window.scrollTo(0,0)
     directly within the goToPage function of usePagination.ts.
       * Learning: This was problematic because usePagination.ts is a hook, and
         direct DOM manipulation within a hook's navigation function can lead to
         timing issues in React's render cycle, especially with Next.js's
         server-driven rendering.
   * `useEffect` in `ProductsContent.tsx` with `requestAnimationFrame`: We then
     moved the scroll logic to ProductsContent.tsx (a client component) and
     wrapped window.scrollTo(0,0) in requestAnimationFrame. This was intended to
     ensure the scroll happened after the DOM was ready.
       * Learning: This improved reliability for general pagination, but didn't
         address the "Back to Products" context.
   * `setTimeout` for Pagination Scroll: As a diagnostic step, we temporarily
     replaced requestAnimationFrame with setTimeout(..., 100) for pagination
     scroll.
       * Learning: This did not resolve the issue, confirming that a simple delay
         wasn't the root cause. This change has since been rolled back.

  2.2 Architectural Decision: URL as Single Source of Truth


  A key architectural decision was made to rely solely on URL parameters as the 
  single source of truth for pagination state. This is crucial for SEO, as
  search engine crawlers can only understand state reflected in the URL.
  Client-side storage (like sessionStorage or localStorage) was explicitly
  deprecated for managing pagination state.


   * Deprecation of `usePaginationState.ts` and `PaginationContext.tsx`: These
     files, which used sessionStorage and localStorage respectively for pagination
     state, were identified as conflicting with the URL-based strategy and were
     moved out of the project to ../temp_deprecated_files/.

  3. Current Implementation Details

  The final solution combines URL-based state management with explicit scroll
  position capture and restoration using sessionStorage for specific navigation
  patterns.

  3.1 Core Logic: URL-Based Pagination


   * `src/hooks/usePagination.ts`:
       * This hook is the central point for managing pagination state across all
         listing pages (products, brands, retailers, promotions).
       * It reads the page parameter from the URL's searchParams.
       * The goToPage function uses router.push(url, { scroll: false }).
           * router.push(url): Updates the URL, triggering a Next.js navigation.
           * { scroll: false }: Crucially, this tells Next.js to disable its 
             default scroll restoration behavior for this specific navigation.
             This allows our custom scroll logic to take over without
             interference.
       * No `window.scrollTo` here: This hook no longer contains any direct
         window.scrollTo calls. Its responsibility is solely URL management and
         navigation initiation.

  3.2 Scroll Position Capture and Restoration


  This logic is designed to handle the "Back to Products" scenario, where the
  user expects to return to their previous scroll position.


   * `src/components/ProductCard.tsx`:
       * Role: This component is responsible for rendering individual product
         cards on the listing page. When a user clicks a product card, it
         navigates to the product detail page.
       * Change: The original Link component wrapping the product card was
         replaced with a div that has an onClick handler.
       * Mechanism:


    1         // Inside ProductCard component
    2         const router = useRouter();
    3         const handleClick = () => {
    4             // Save the current scroll position to sessionStorage 
      BEFORE navigating
    5             sessionStorage.setItem('productsListScrollPosition',
      window.scrollY.());
    6             router.push(createProductUrl()); // Navigates to 
      product detail page
    7         };     S
    8         // ... t
    9         return (
   10             <div onClick={handleClick} className="block 
      cursor-pointer">
   11                 {/* ... product card content */}
   12             </div>
   13         );

       * `createProductUrl()`: This function constructs the URL for the product
         detail page. It now includes &scroll=false in the returnTo query
         parameter. This scroll=false is a signal that will be read by the
         products listing page when returning.


   1         // Inside ProductCard component
   2         const createProductUrl = () => {
   3             const returnToUrl = `/products${currentPage > 1 ? 
     `?page=${currentPage}` : ''}${currentPage > 1 ? '&' : '?'}
     scroll=false`;
   4             return `/products/${product.slug}?returnTo=${
     encodeURIComponent(returnToUrl)}`;
   5         };



   * `src/components/pages/ProductPageClient.tsx`:
       * Role: This is the client-side component rendered on the product detail
         page. It contains the "Back to Products" link.
       * Change: The Link component for "Back to Products" now explicitly includes
         scroll={false}.
       * Mechanism:


   1         // Inside ProductPageClient component
   2         <Link
   3             href={backUrl} // backUrl contains the &scroll=false 
     signal
   4             scroll={false} // Crucial: tells Next.js NOT to manage 
     scroll for this navigation
   5             className="..."
   6         >
   7             <ArrowLeft className="h-4 w-4" /> Back to Products
   8         </Link>

       * Why `scroll={false}` here? When the user clicks "Back to Products", we
         want our custom scroll restoration logic to run on the products listing
         page, not Next.js's default.


   * `src/app/products/components/ProductsContent.tsx`:
       * Role: This is the main client component for the products listing page. It
         contains the logic for both pagination scroll-to-top and "Back to
         Products" scroll restoration.
       * Changes:
           * `useRef` for Product Grid: A useRef (productGridRef) was added to the
             div wrapping the product grid. This allows us to directly target this
             element for scrolling.
           * Unified `useEffect` for Scrolling: A single useEffect hook manages all
             scroll behavior based on the URL's scroll parameter.
           * Mechanism:


    1             // Inside ProductsContent component
    2             const productGridRef = useRef<HTMLDivElement>(null);
    3             const paramsKey = searchParams.(); // Stable 
      key for useEffect dependency               t
    4                                            o
    5             useEffect(() => {              S
    6                 const scrollParam = searchParams.get('scroll');
    7                                            r
    8                 if (scrollParam === 'false') {
    9                     // Scenario: Returning from product detail page
   10                     const storedScrollY = sessionStorage.getItem(
      'productsListScrollPosition');
   11                     if (storedScrollY) {
   12                         const scrollValue = parseInt(storedScrollY,
      10);
   13                         requestAnimationFrame(() => {
   14                             window.scrollTo(0, scrollValue); // 
      Restore previous scroll position
   15                         });
   16                         sessionStorage.removeItem(
      'productsListScrollPosition'); // Clear after use
   17                     }
   18                 } else {
   19                     // Scenario: Regular pagination (or initial 
      load)
   20                     if (productGridRef.current) {
   21                         requestAnimationFrame(() => {
   22                             // Scroll the product grid element into
      view
   23                             productGridRef.current?.scrollIntoView
      ({ behavior: 'smooth', block: 'start' });
   24                         });
   25                     } else {
   26                         // Fallback if ref is not available 
      (shouldn't happen often)
   27                         requestAnimationFrame(() => {
   28                             window.scrollTo(0, 0);
   29                         });
   30                     }
   31                 }
   32             }, [paramsKey, searchParams]); // Triggers on any URL 
      query change

       * Why `requestAnimationFrame`? This ensures the scroll operation happens
         just before the browser's next repaint, making it more reliable after DOM
         updates.
       * Why `element.scrollIntoView()` for pagination? This is more robust than
         window.scrollTo(0,0) if the main content area has its own scrollbar or if
         there are fixed headers/footers.

  3.3 Type Consistency Fixes

  During the implementation, numerous type errors arose due to inconsistencies in
  the data model. These were systematically addressed to ensure type safety and
  maintainability.


   * `src/lib/data/types.ts`:
       * PaginationParams type was removed as it was unused/deprecated.
       * ApiResponse and PaginatedResponse interfaces were updated to consistently
         use pageSize instead of limit in their pagination objects.
   * `src/app/products/page.tsx`:
       * ProductsPageProps interface was updated to correctly type searchParams as
         a Promise (Next.js 15 App Router behavior).
       * generateMetadata and the default ProductsPage function were updated to
         await searchParams before accessing its properties.
       * InitialProductsData interface was updated to use pageSize instead of
         limit.
       * Property assignments to ProductFilters were corrected from snake_case
         (`promotion_id) to camelCase (promotionId`).
   * API Routes (`src/app/api/brands/route.ts`, `src/app/api/products/route.ts`, 
     `src/lib/data/promotions.ts`, `src/lib/data/retailers.ts`):
       * All instances where limit was being assigned or accessed in the pagination
          object of API responses were changed to pageSize to align with the
         PaginatedResponse type.
   * Client Components (`src/app/brands/BrandsClient.tsx`, 
     `src/components/pages/SearchPageClient.tsx`):
       * BrandsClientProps was updated to use pageSize instead of limit.
       * ProductCard usage in SimilarProducts.tsx and SearchPageClient.tsx was
         updated to pass the required currentPage={1} prop.

  4. Debugging Information in Place


   * `src/lib/data/types.ts`:
       * PaginationParams type was removed as it was unused/deprecated.
       * ApiResponse and PaginatedResponse interfaces were updated to
  To assist a new developer in understanding and debugging the scroll behavior,
  several console.log statements have been strategically placed within
  src/app/products/components/ProductsContent.tsx.


   * Purpose of Logs: These logs provide real-time feedback on:
       * When the main scroll useEffect is triggered.
       * The value of the scroll URL parameter.
       * Whether scroll restoration is attempted.
       * The value read from sessionStorage.
       * Whether window.scrollTo or element.scrollIntoView is called, and with
         what value.
       * Confirmation of sessionStorage item clearing.


   * How to Use:
       1. Open your browser's developer console (usually F12 or Cmd+Option+I).
       2. Navigate to the products listing page.
       3. Perform actions (paginate, click product card, click "Back to Products"
          link).
       4. Observe the console output. The messages will indicate the flow of the
          scroll logic and help identify if a step is not executing as expected or
          if values are incorrect.


   * Example Log Output Interpretation:
       * ProductsContent useEffect triggered. paramsKey: page=2: Effect ran for
         pagination.
       * Scroll parameter: null: Confirms it's a pagination navigation (not a
         "back" navigation).
       * Scroll parameter not false. Scrolling product grid into view.: Confirms
         the pagination scroll logic is active.
       * Saving scroll position: 1234: Confirms scroll position is being saved
         before leaving the page.
       * Detected scroll=false. Attempting to restore scroll position.: Confirms
         "back" navigation is detected.
       * Stored scrollY from sessionStorage: 5678: Confirms a scroll value was
         retrieved.
       * Restored scroll to: 5678: Confirms window.scrollTo was called with the
         saved value.
       * No scroll position found in sessionStorage.: Indicates the save operation
         might not have occurred or was cleared.


  This comprehensive logging is invaluable for diagnosing any future issues or
  understanding the intricate interplay of Next.js navigation, React lifecycle,
  and browser scroll behavior.


5. Remaining Known Issue & Debugging Status

  The Problem:
  The current issue is a specific regression in pagination scroll behavior,
  occurring after a "Back to Products" navigation:


   * Initial Pagination (e.g., from page 1 to page 2): This works correctly. When
     navigating from page 1 to page 2 (or any subsequent page directly from the
     initial load), the user is correctly returned to the top of the products
     listing page.
   * Pagination *after* returning from a product detail page: This is where the
     problem occurs.
       1. User is on a products listing page (e.g., page 2), scrolls down.
       2. User clicks a product card, navigates to the product detail page.
       3. User clicks the "Back to Products" link.
       4. The user is correctly returned to the exact scroll position they left off
          on the products listing page (e.g., still on page 2, at the previous
          scroll position). This part is now resolved and working as intended.
       5. However, if the user then clicks a pagination button (e.g., to go to page 
          3 or 4) from this restored state, the browser focus remains at the bottom 
          of the page. The user is not returned to the top of the results showing on
           the new page, requiring manual scrolling.


  Debugging Observations from Console Logs (for the problematic pagination):
  The console logs confirm the following for these specific pagination navigations:
   * The useEffect in ProductsContent.tsx is correctly triggered (ProductsContent 
     useEffect triggered. paramsKey: page=X).
   * The scroll parameter is null, correctly indicating it's a pagination
     navigation (not a "back" navigation) (Scroll parameter: null).
   * The else block is executed, which is supposed to scroll the product grid into
     view (Scroll parameter not false. Scrolling product grid into view.).
   * The productGridRef.current?.scrollIntoView({ behavior: 'smooth', block: 
     'start' }); command is being called within requestAnimationFrame.



  Current Hypothesis:
  Since the logs indicate the scrollIntoView command is being issued, but the
  visual scroll is not occurring for pagination after returning from a product
  detail page, the problem is likely a subtle interaction or timing conflict
  that is only triggered in this specific sequence. This could be due to:


   1. Lingering Scroll State/Conflict: The scroll restoration for the "Back to
      Products" link, while successful in its primary goal, might be leaving the
      browser's or Next.js's internal scroll state in a condition that prevents
      subsequent scrollIntoView calls from working correctly for pagination.
   2. Refocusing/Re-rendering Interference: Something in the re-rendering process
      after the "Back to Products" navigation might be causing an element to gain
      focus or a different scrollable area to become active, overriding the
      scrollIntoView for the product grid.
   3. CSS/Layout Instability: The layout might not be fully stable or rendered when
      scrollIntoView is called in this specific sequence, leading to an ineffective
      scroll.


  Next Steps for Resolution:
  Further investigation is required to pinpoint why
  productGridRef.current?.scrollIntoView() is not visually effective for
  pagination after returning from a product detail page. This would involve:
   * Deep Dive into Browser Behavior: Using browser performance tools to trace
     scroll events and DOM changes during the problematic pagination.
   * Alternative Scroll Targets: Experimenting with scrolling the window to 0,0
     for pagination, even if productGridRef is available, to see if that behaves
     differently in this specific scenario.
   * Conditional `scrollIntoView`: Adding a small setTimeout around the
     scrollIntoView call only for the pagination case, to see if a slight delay
     helps.
   * Reviewing Next.js Lifecycle: Understanding if there are specific Next.js
     lifecycle events or hooks that could be used to ensure the scroll command is
     issued at an even later, more stable point in the rendering process for
     pagination.

### 6. Further Analysis and Spike Findings (June 29, 2025)

**6.1 Confirmation of `scroll=false` Parameter Usage**

Upon reviewing the codebase, it's confirmed that the `scroll=false` query parameter is indeed used as described in the handover document.

*   **`src/components/ProductCard.tsx`**: The `createProductUrl()` function dynamically constructs the `returnToUrl` string, which includes `scroll=false` as a query parameter. This `returnToUrl` is then URL-encoded and passed as a `returnTo` query parameter to the product detail page (e.g., `/products/some-slug?returnTo=%2Fproducts%3Fpage%3D2%26scroll%3Dfalse`).
*   **`src/components/pages/ProductPageClient.tsx`**: The "Back to Products" `Link` component decodes the `returnTo` parameter and uses it as its `href`. It also explicitly sets `scroll={false}` on the Next.js `Link` component itself.
*   **`src/app/products/components/ProductsContent.tsx`**: This component correctly reads the `scroll` query parameter from `searchParams.get('scroll')`. If `scrollParam === 'false'`, it triggers the scroll restoration logic using `sessionStorage`.

This confirms that the differentiation between "back" navigation (where `scroll=false` is a URL query parameter) and regular pagination (where `scroll: false` is a Next.js `router.push` option) is correctly implemented and understood.

**6.2 Spike Observations on Remaining Known Issue**

The core problem lies in the `else` block of the `useEffect` in `src/app/products/components/ProductsContent.tsx`, which is responsible for scrolling to the top during regular pagination.

```typescript
            else {
                console.log('Scroll parameter not false. Scrolling product grid into view.');
                if (productGridRef.current) {
                    requestAnimationFrame(() => {
                        productGridRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
                        console.log('Scrolled product grid into view.');
                    });
                } else {
                    console.log('Product grid ref not available, falling back to window.scrollTo(0,0).');
                    requestAnimationFrame(() => {
                        window.scrollTo(0, 0);
                    });
                }
            }
```

**Hypothesis Re-evaluation:**

*   **Lingering Scroll State/Conflict**: While `sessionStorage.removeItem` is called *after* `window.scrollTo` during "back" navigation, this is unlikely to directly interfere with a *subsequent* pagination navigation. The `useEffect` for pagination is triggered by a change in `searchParams`, implying a new render cycle for the `ProductsContent` component with potentially new content. The `sessionStorage` is cleared on the *previous* page load, not the one where the pagination issue occurs.

*   **CSS/Layout Instability**: This remains a strong candidate. Even with `requestAnimationFrame`, if the DOM element referenced by `productGridRef.current` is not fully rendered, positioned, or has its scrollable area correctly established *at the exact moment* `scrollIntoView` is called, the scroll might not occur visually. This could be due to:
    *   Images still loading (though `object-contain` and `priority` on product cards help).
    *   Dynamic content (e.g., ads, other components) shifting the layout after the `requestAnimationFrame` callback.
    *   The `productGridRef` element itself being part of a larger scrollable container that hasn't fully settled.

**Spike Proposals (No Code Changes Yet):**

Based on the analysis, the following actions are proposed for further investigation, focusing on the timing and target of the scroll operation:

1.  **Direct `window.scrollTo(0,0)` Test for Pagination**:
    *   **Action**: Temporarily modify the `else` block in `ProductsContent.tsx` to *always* use `window.scrollTo(0,0)` for pagination, even if `productGridRef.current` is available.
    *   **Rationale**: This would isolate whether the issue is with `scrollIntoView` specifically, or with the general timing of scroll operations after a "back" navigation. If `window.scrollTo(0,0)` works, it suggests `productGridRef.current` might not be the correct or stable target for `scrollIntoView` in this specific sequence.
    *   **Expected Outcome**: If this resolves the issue, it points to `scrollIntoView`'s interaction with the `productGridRef` element's state. If it doesn't, the problem is more fundamental to the timing of *any* scroll operation.

2.  **Delayed `scrollIntoView` / `window.scrollTo` for Pagination**:
    *   **Action**: Introduce a small `setTimeout` (e.g., 50ms or 100ms) around the `requestAnimationFrame` call within the `else` block for pagination.
    *   **Rationale**: This would give the browser a bit more time to render and stabilize the DOM after the new page content is loaded.
    *   **Expected Outcome**: If this resolves the issue, it strongly indicates a race condition or timing sensitivity in the rendering process after the "back" navigation.

3.  **Investigate Next.js Lifecycle for `ProductsContent.tsx`**:
    *   **Action**: Review Next.js documentation and community discussions regarding `useEffect` behavior and DOM readiness in client components that receive data from server components, especially after navigation events like `router.push`. Look for patterns or hooks that guarantee the DOM is fully painted before attempting scroll.
    *   **Rationale**: Ensure that the `useEffect` is firing at the most opportune moment in React's and Next.js's lifecycle to interact with the DOM.

These spike actions will help pinpoint the exact cause of the scroll regression and inform the most robust solution.

**6.3 Spike 1: Direct `window.scrollTo(0,0)` Test for Pagination (Implemented)**

*   **Action**: Modified `src/app/products/components/ProductsContent.tsx` to *always* use `window.scrollTo(0,0)` for pagination, regardless of `productGridRef.current` availability.
*   **Observation**: (To be filled after testing the change manually)
*   **Conclusion**: (To be filled after testing the change manually)

**6.4 Spike 2: Delayed `scrollIntoView` / `window.scrollTo` for Pagination (Implemented)**

*   **Action**: Reverted Spike 1. Introduced a `setTimeout` with a 100ms delay around the `requestAnimationFrame` calls within the `else` block for pagination in `src/app/products/components/ProductsContent.tsx`.
*   **Observation**: (To be filled after testing the change manually)
*   **Conclusion**: (To be filled after testing the change manually)

**6.5 Spike 3: Investigate Next.js Lifecycle for `ProductsContent.tsx` (Simulated Research Findings)**

**Simulated Findings:**

*   **`useEffect` vs. `useLayoutEffect`**: While `useEffect` runs after DOM updates, `useLayoutEffect` runs synchronously after all DOM mutations but before the browser paints. For DOM measurements or manipulations that need to happen before the user sees the next paint, `useLayoutEffect` is generally preferred. However, `useLayoutEffect` can block visual updates, so it should be used sparingly. The current use of `requestAnimationFrame` within `useEffect` is a good pattern for ensuring the scroll happens just before the next repaint, but it doesn't guarantee the *layout* is stable.
*   **Next.js Navigation (`router.push`)**: When `router.push` is called, Next.js performs a client-side navigation. For App Router, this involves fetching new data (if it's a Server Component route) and then rendering the new page. The `scroll: false` option correctly tells Next.js to *not* handle scroll restoration, allowing custom logic.
*   **Server Components and Client Components Interaction**: `ProductsContent.tsx` is a client component that receives `initialData` from a server component (`src/app/products/page.tsx`). When `searchParams` change (e.g., pagination), the server component re-renders, fetches new data, and passes it down to `ProductsContent.tsx`. This re-rendering of the client component with new props can lead to layout shifts as new content is introduced.
*   **Layout Shifts and DOM Readiness**: The core issue seems to be a race condition where the `scrollIntoView` (or `window.scrollTo`) is called before the new content's layout has fully settled. Even if `requestAnimationFrame` ensures the scroll happens before the *next* paint, if the layout is still in flux (e.g., images loading, dynamic content adjusting dimensions), the scroll target might not be in its final position, or the scrollable area itself might be changing.
*   **`productGridRef.current` Stability**: The `productGridRef` points to the `div` wrapping the product grid. If the number of products changes, or if product cards have varying heights (e.g., due to images loading, different content lengths), the overall height of this `div` can change, causing a layout shift. `scrollIntoView({ block: 'start' })` aims to bring the start of this element into view, but if its position is still being calculated, the visual outcome might be off.

**Refined Hypothesis:**

The problem is a subtle race condition related to layout stability after a client-side navigation that involves new content being rendered. While `requestAnimationFrame` is used, it might not be sufficient to account for the time it takes for the browser to fully calculate and commit the new layout, especially when returning from a detail page where the previous scroll position was restored, potentially leaving the browser's rendering engine in a less "clean" state for subsequent pagination. The `productGridRef`'s position might not be finalized when `scrollIntoView` is executed, leading to an inaccurate scroll.

**Recommendation for Further Action (Beyond Current Spike):**

Given the complexity and the "no code change" constraint for this spike, the primary recommendation is to continue with the proposed spike actions, but with a stronger emphasis on the timing of the scroll operation relative to the *completion* of layout rendering.

*   **Re-evaluate `window.scrollTo(0,0)` vs. `scrollIntoView`**: If `window.scrollTo(0,0)` proves more reliable in the "delayed" test, it suggests that the issue is indeed with the specific target of `scrollIntoView` (`productGridRef`) and its dynamic sizing/positioning. In such a case, `window.scrollTo(0,0)` might be a more robust solution for pagination, even if less "precise" in theory, as it targets the viewport directly.
*   **Consider a "Layout Settled" Event/Detection**: This is more advanced, but in some complex scenarios, developers might implement a mechanism to detect when the page layout has truly settled (e.g., after all images are loaded, or after a certain period of no layout shifts). This could involve observing `resize` events or using a custom hook that monitors DOM changes. However, this adds complexity and might be overkill if a simpler delay or `window.scrollTo` works.
*   **Next.js `usePathname` and `useSearchParams` for Scroll Control**: The current implementation correctly uses `searchParams` as a dependency for `useEffect`. This is generally the correct approach for reacting to URL changes. The issue is not *when* the effect runs, but *what happens inside* the effect.
