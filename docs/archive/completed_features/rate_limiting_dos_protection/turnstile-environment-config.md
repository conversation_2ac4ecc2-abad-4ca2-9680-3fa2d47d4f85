# Cloudflare Turnstile Environment Configuration

## Overview
This document outlines the environment variable configuration for Cloudflare Turnstile CAPTCHA integration across development, staging, and production environments.

## Environment Variables

### Required Variables

#### Frontend (Public)
```bash
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_site_key_here
```
- **Purpose**: Public site key for rendering Turnstile widget
- **Visibility**: Client-side accessible (prefixed with NEXT_PUBLIC_)
- **Security**: Safe to expose in browser

#### Backend (Private)
```bash
TURNSTILE_SECRET_KEY=your_secret_key_here
```
- **Purpose**: Secret key for server-side token verification
- **Visibility**: Server-side only
- **Security**: Must be kept confidential

## Environment-Specific Configuration

### Development Environment (.env.local)
```bash
# Cloudflare Turnstile - Development (Test Keys)
NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA
```

**Test Key Behavior:**
- Always passes verification
- No actual CAPTCHA challenge shown
- Allows development without real Cloudflare setup
- Should NOT be used in production

### Staging Environment
```bash
# Cloudflare Turnstile - Staging (Production Keys)
NEXT_PUBLIC_TURNSTILE_SITE_KEY=0x4AAAAAAA... (your staging site key)
TURNSTILE_SECRET_KEY=0x4AAAAAAA... (your staging secret key)
```

**Staging Key Behavior:**
- Real CAPTCHA challenges
- Same keys as production for realistic testing
- Allows testing of actual user experience

### Production Environment
```bash
# Cloudflare Turnstile - Production
NEXT_PUBLIC_TURNSTILE_SITE_KEY=0x4AAAAAAA... (your production site key)
TURNSTILE_SECRET_KEY=0x4AAAAAAA... (your production secret key)
```

**Production Key Behavior:**
- Full CAPTCHA protection
- Real bot detection and blocking
- Optimized for security and user experience

## Obtaining Turnstile Keys

### Step 1: Cloudflare Dashboard Setup
1. Log into Cloudflare Dashboard
2. Navigate to "Turnstile" section
3. Click "Add Site"
4. Configure site settings:
   - **Site Name**: Cashback Deals Contact Form
   - **Domain**: your-domain.com
   - **Widget Mode**: Managed (recommended)

### Step 2: Key Generation
- **Site Key**: Automatically generated (public)
- **Secret Key**: Automatically generated (private)
- **Test Keys**: Available for development

### Step 3: Domain Configuration
- Add all domains where Turnstile will be used:
  - `localhost:3000` (development)
  - `your-staging-domain.com` (staging)
  - `your-production-domain.com` (production)

## Security Best Practices

### Key Management
- ✅ **Never commit real keys to version control**
- ✅ **Use test keys for development only**
- ✅ **Store production keys in secure environment secrets**
- ✅ **Rotate keys periodically**
- ✅ **Use different keys for staging and production**

### Environment Secrets Storage

#### AWS Amplify
```bash
# Add to Amplify Environment Variables
NEXT_PUBLIC_TURNSTILE_SITE_KEY=0x4AAAAAAA...
TURNSTILE_SECRET_KEY=0x4AAAAAAA...
```

#### Vercel
```bash
# Add to Vercel Environment Variables
NEXT_PUBLIC_TURNSTILE_SITE_KEY=0x4AAAAAAA...
TURNSTILE_SECRET_KEY=0x4AAAAAAA...
```

#### Other Platforms
- Store in platform-specific secure environment variable systems
- Never use plain text files for production keys

## Validation and Testing

### Development Testing
```bash
# Test with development keys
npm run dev
# Visit contact form and verify CAPTCHA widget appears
# Submit form and verify it processes without real challenge
```

### Staging Testing
```bash
# Test with production keys in staging environment
# Verify real CAPTCHA challenges appear
# Test various scenarios (success, failure, timeout)
```

### Production Verification
```bash
# Monitor logs for Turnstile verification results
# Check Cloudflare Analytics for CAPTCHA metrics
# Verify legitimate users can complete forms
```

## Troubleshooting

### Common Issues

#### Widget Not Appearing
- Check `NEXT_PUBLIC_TURNSTILE_SITE_KEY` is set correctly
- Verify domain is configured in Cloudflare
- Check browser console for errors

#### Verification Failing
- Verify `TURNSTILE_SECRET_KEY` is correct
- Check server logs for detailed error messages
- Ensure IP address is being passed correctly

#### Test Keys in Production
- **Critical**: Never use test keys in production
- Always use real Cloudflare-generated keys
- Monitor for unexpected pass rates

### Debug Commands
```bash
# Check environment variables are loaded
echo $NEXT_PUBLIC_TURNSTILE_SITE_KEY
echo $TURNSTILE_SECRET_KEY

# Test API endpoint
curl -X POST http://localhost:3000/api/contact \
  -H "Content-Type: application/json" \
  -d '{"cf-turnstile-response": "test-token"}'
```

## Monitoring and Alerts

### Cloudflare Analytics
- Monitor CAPTCHA solve rates
- Track blocked vs. allowed requests
- Analyze geographic patterns

### Application Logs
- Log all Turnstile verification attempts
- Monitor for unusual failure patterns
- Alert on high failure rates

### Success Metrics
- ✅ **>95% legitimate user success rate**
- ✅ **<5% false positive rate**
- ✅ **Significant reduction in spam submissions**
- ✅ **No impact on conversion rates**

## Rollback Plan

### Emergency Disable
If Turnstile causes issues:
1. Set environment variable: `TURNSTILE_DISABLED=true`
2. Update code to skip verification when disabled
3. Deploy immediately
4. Investigate and fix issues
5. Re-enable when resolved

### Gradual Rollback
1. Switch back to test keys temporarily
2. Investigate specific issues
3. Fix configuration problems
4. Re-deploy with correct keys