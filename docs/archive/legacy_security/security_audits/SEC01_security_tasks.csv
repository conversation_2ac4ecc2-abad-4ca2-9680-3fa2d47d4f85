Issue Id,Project Key,Issue Type,Summary,Description,Priority,Labels,Epic Name,Epic Link,Parent Id
E1,CAS,Epic,SEC01 Security Hardening,Goal: Close launch‑blocking security issues before first production release.,High,security,SEC01 Security Hardening,,
S1,CAS,Story,[SEC] Remove service_role key,,Critical,secrets,,E1,
S1-T1,CAS,Sub-task,Local – [SEC] Remove service_role key,"Environment: **Local**

Pages/Files affected:
- `src/lib/supabase/admin.ts` (new server‑only client)
- `src/lib/supabase.ts` (remove direct service_role key usage)
- Verify bundles in `.next/` and Amplify artefacts

Acceptance Criteria:
- service_role key loaded only via `process.env.SUPABASE_SERVICE_ROLE_KEY`
- No `service_role` string present in client JS bundle
- App pages (`/search`, `/product/[slug]`) still load successfully

AI agent prompt:
```
Remove all occurrences of the Supabase service_role key from the codebase for the **Local** environment. Create `src/lib/supabase/admin.ts` that imports the key via process.env. Add a Jest test that fails if the key appears in any built JS asset.
```",Critical,"secrets,local",,,S1
S1-T2,CAS,Sub-task,Staging – [SEC] Remove service_role key,"Environment: **Staging**

Pages/Files affected:
- `src/lib/supabase/admin.ts` (new server‑only client)
- `src/lib/supabase.ts` (remove direct service_role key usage)
- Verify bundles in `.next/` and Amplify artefacts

Acceptance Criteria:
- service_role key loaded only via `process.env.SUPABASE_SERVICE_ROLE_KEY`
- No `service_role` string present in client JS bundle
- App pages (`/search`, `/product/[slug]`) still load successfully

AI agent prompt:
```
Remove all occurrences of the Supabase service_role key from the codebase for the **Staging** environment. Create `src/lib/supabase/admin.ts` that imports the key via process.env. Add a Jest test that fails if the key appears in any built JS asset.
```",Critical,"secrets,staging",,,S1
S1-T3,CAS,Sub-task,Production – [SEC] Remove service_role key,"Environment: **Production**

Pages/Files affected:
- `src/lib/supabase/admin.ts` (new server‑only client)
- `src/lib/supabase.ts` (remove direct service_role key usage)
- Verify bundles in `.next/` and Amplify artefacts

Acceptance Criteria:
- service_role key loaded only via `process.env.SUPABASE_SERVICE_ROLE_KEY`
- No `service_role` string present in client JS bundle
- App pages (`/search`, `/product/[slug]`) still load successfully

AI agent prompt:
```
Remove all occurrences of the Supabase service_role key from the codebase for the **Production** environment. Create `src/lib/supabase/admin.ts` that imports the key via process.env. Add a Jest test that fails if the key appears in any built JS asset.
```",Critical,"secrets,production",,,S1
S2,CAS,Story,[SEC] Implement Supabase Auth (email link),,High,authentication,,E1,
S2-T1,CAS,Sub-task,Local – [SEC] Implement Supabase Auth (email link),"Environment: **Local**

Pages/Files affected:
- `/pages/login.tsx`
- `/pages/signup.tsx`
- `/pages/search/index.tsx` (SSR)
- `/pages/product/[slug].tsx` (ISR)
- `src/context/Auth.tsx`

Acceptance Criteria:
- Email link login & signup render and complete successfully
- Auth context provides `user` object to pages
- Protected pages redirect unauthenticated users to `/login`
- Playwright test proves flow

AI agent prompt:
```
Implement Supabase email‑link auth for the **Local** environment. Scaffold login and signup pages, wrap `/search` and `/product/[slug]` with auth guard, supply Playwright test that logs in and verifies redirection.
```",High,"authentication,local",,,S2
S2-T2,CAS,Sub-task,Staging – [SEC] Implement Supabase Auth (email link),"Environment: **Staging**

Pages/Files affected:
- `/pages/login.tsx`
- `/pages/signup.tsx`
- `/pages/search/index.tsx` (SSR)
- `/pages/product/[slug].tsx` (ISR)
- `src/context/Auth.tsx`

Acceptance Criteria:
- Email link login & signup render and complete successfully
- Auth context provides `user` object to pages
- Protected pages redirect unauthenticated users to `/login`
- Playwright test proves flow

AI agent prompt:
```
Implement Supabase email‑link auth for the **Staging** environment. Scaffold login and signup pages, wrap `/search` and `/product/[slug]` with auth guard, supply Playwright test that logs in and verifies redirection.
```",High,"authentication,staging",,,S2
S2-T3,CAS,Sub-task,Production – [SEC] Implement Supabase Auth (email link),"Environment: **Production**

Pages/Files affected:
- `/pages/login.tsx`
- `/pages/signup.tsx`
- `/pages/search/index.tsx` (SSR)
- `/pages/product/[slug].tsx` (ISR)
- `src/context/Auth.tsx`

Acceptance Criteria:
- Email link login & signup render and complete successfully
- Auth context provides `user` object to pages
- Protected pages redirect unauthenticated users to `/login`
- Playwright test proves flow

AI agent prompt:
```
Implement Supabase email‑link auth for the **Production** environment. Scaffold login and signup pages, wrap `/search` and `/product/[slug]` with auth guard, supply Playwright test that logs in and verifies redirection.
```",High,"authentication,production",,,S2
S3,CAS,Story,[SEC] Enforce RLS policies,,High,authorization,,E1,
S3-T1,CAS,Sub-task,Local – [SEC] Enforce RLS policies,"Environment: **Local**

Database Objects:
- Tables: users, reminders, user_favorites, user_cashback_claims

Acceptance Criteria:
- RLS policies present (`auth.uid() = user_id`) and enforced
- Anonymous key cannot read protected rows via API
- Playwright or Jest integration test with two users passes

AI agent prompt:
```
Write and apply SQL migrations that add strict RLS to all user‑scoped tables in the **Local** Supabase project, then write a test that shows user A cannot access user B's data.
```",High,"authorization,local",,,S3
S3-T2,CAS,Sub-task,Staging – [SEC] Enforce RLS policies,"Environment: **Staging**

Database Objects:
- Tables: users, reminders, user_favorites, user_cashback_claims

Acceptance Criteria:
- RLS policies present (`auth.uid() = user_id`) and enforced
- Anonymous key cannot read protected rows via API
- Playwright or Jest integration test with two users passes

AI agent prompt:
```
Write and apply SQL migrations that add strict RLS to all user‑scoped tables in the **Staging** Supabase project, then write a test that shows user A cannot access user B's data.
```",High,"authorization,staging",,,S3
S3-T3,CAS,Sub-task,Production – [SEC] Enforce RLS policies,"Environment: **Production**

Database Objects:
- Tables: users, reminders, user_favorites, user_cashback_claims

Acceptance Criteria:
- RLS policies present (`auth.uid() = user_id`) and enforced
- Anonymous key cannot read protected rows via API
- Playwright or Jest integration test with two users passes

AI agent prompt:
```
Write and apply SQL migrations that add strict RLS to all user‑scoped tables in the **Production** Supabase project, then write a test that shows user A cannot access user B's data.
```",High,"authorization,production",,,S3
S4,CAS,Story,[SEC] Protect API routes with middleware,,High,backend,,E1,
S4-T1,CAS,Sub-task,Local – [SEC] Protect API routes with middleware,"Environment: **Local**

Files:
- `middleware.ts`
- `/pages/api/*` routes
- `/pages/search/index.tsx` (SSR serverProps)

Acceptance Criteria:
- Middleware denies unauth'd requests to API & SSR pages
- Unit tests cover 401 vs 200 scenarios

AI agent prompt:
```
Create Next.js middleware for **Local** that checks Supabase session cookie; return 401 for unauthenticated calls to /api/* and SSR pages. Provide Jest tests.
```",High,"backend,local",,,S4
S4-T2,CAS,Sub-task,Staging – [SEC] Protect API routes with middleware,"Environment: **Staging**

Files:
- `middleware.ts`
- `/pages/api/*` routes
- `/pages/search/index.tsx` (SSR serverProps)

Acceptance Criteria:
- Middleware denies unauth'd requests to API & SSR pages
- Unit tests cover 401 vs 200 scenarios

AI agent prompt:
```
Create Next.js middleware for **Staging** that checks Supabase session cookie; return 401 for unauthenticated calls to /api/* and SSR pages. Provide Jest tests.
```",High,"backend,staging",,,S4
S4-T3,CAS,Sub-task,Production – [SEC] Protect API routes with middleware,"Environment: **Production**

Files:
- `middleware.ts`
- `/pages/api/*` routes
- `/pages/search/index.tsx` (SSR serverProps)

Acceptance Criteria:
- Middleware denies unauth'd requests to API & SSR pages
- Unit tests cover 401 vs 200 scenarios

AI agent prompt:
```
Create Next.js middleware for **Production** that checks Supabase session cookie; return 401 for unauthenticated calls to /api/* and SSR pages. Provide Jest tests.
```",High,"backend,production",,,S4
S5,CAS,Story,[SEC] Implement security headers,,Medium,headers,,E1,
S5-T1,CAS,Sub-task,Local – [SEC] Implement security headers,"Environment: **Local**

Files:
- `amplify.yml` custom_headers (staging/prod)
- For local: `next.config.js` headers()

Acceptance Criteria:
- CSP, HSTS, X‑Frame‑Options, Referrer‑Policy headers present
- Curl test shows headers
- Datadog synthetic monitor created (staging/prod)

AI agent prompt:
```
Add required security headers for the **Local** environment. Fail CI if headers missing. Provide CURL‑based Jest test.
```",Medium,"headers,local",,,S5
S5-T2,CAS,Sub-task,Staging – [SEC] Implement security headers,"Environment: **Staging**

Files:
- `amplify.yml` custom_headers (staging/prod)
- For local: `next.config.js` headers()

Acceptance Criteria:
- CSP, HSTS, X‑Frame‑Options, Referrer‑Policy headers present
- Curl test shows headers
- Datadog synthetic monitor created (staging/prod)

AI agent prompt:
```
Add required security headers for the **Staging** environment. Fail CI if headers missing. Provide CURL‑based Jest test.
```",Medium,"headers,staging",,,S5
S5-T3,CAS,Sub-task,Production – [SEC] Implement security headers,"Environment: **Production**

Files:
- `amplify.yml` custom_headers (staging/prod)
- For local: `next.config.js` headers()

Acceptance Criteria:
- CSP, HSTS, X‑Frame‑Options, Referrer‑Policy headers present
- Curl test shows headers
- Datadog synthetic monitor created (staging/prod)

AI agent prompt:
```
Add required security headers for the **Production** environment. Fail CI if headers missing. Provide CURL‑based Jest test.
```",Medium,"headers,production",,,S5
S6,CAS,Story,[SEC] Rate‑limit API & Search endpoints,,Medium,rate-limit,,E1,
S6-T1,CAS,Sub-task,Local – [SEC] Rate‑limit API & Search endpoints,"Environment: **Local**

Resources:
- Cloudflare rule on zone
- k6 load test script in CI (local/staging)

Acceptance Criteria:
- Requests >10 req/s to /api/* receive 429
- Datadog monitor on 429 rate (staging/prod)

AI agent prompt:
```
Configure Cloudflare rate‑limiting for /api/* and /search/* on **Local**. Add k6 load test that confirms 429s trigger and create Datadog monitor where applicable.
```",Medium,"rate-limit,local",,,S6
S6-T2,CAS,Sub-task,Staging – [SEC] Rate‑limit API & Search endpoints,"Environment: **Staging**

Resources:
- Cloudflare rule on zone
- k6 load test script in CI (local/staging)

Acceptance Criteria:
- Requests >10 req/s to /api/* receive 429
- Datadog monitor on 429 rate (staging/prod)

AI agent prompt:
```
Configure Cloudflare rate‑limiting for /api/* and /search/* on **Staging**. Add k6 load test that confirms 429s trigger and create Datadog monitor where applicable.
```",Medium,"rate-limit,staging",,,S6
S6-T3,CAS,Sub-task,Production – [SEC] Rate‑limit API & Search endpoints,"Environment: **Production**

Resources:
- Cloudflare rule on zone
- k6 load test script in CI (local/staging)

Acceptance Criteria:
- Requests >10 req/s to /api/* receive 429
- Datadog monitor on 429 rate (staging/prod)

AI agent prompt:
```
Configure Cloudflare rate‑limiting for /api/* and /search/* on **Production**. Add k6 load test that confirms 429s trigger and create Datadog monitor where applicable.
```",Medium,"rate-limit,production",,,S6
