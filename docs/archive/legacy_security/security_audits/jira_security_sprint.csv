Issue Id,Project Key,Issue Type,Summary,Description,Priority,Labels,Parent Id,Epic Link
1,CAS,Epic,"SEC01 – Pre-Prod Security Hardening","Security hardening epic to address all critical and high findings from the security audits before production launch.","Highest","security,hardening",,
2,CA<PERSON>,Story,"[SEC] Harden Environment and Secrets Management","User Story: As a developer, I want to ensure all secrets and environment variables are managed securely, removed from version control, and accessed safely to prevent leaks.","Highest","security,secrets,amplify",,1
3,CAS,Sub-task,"[Secrets] Remove exposed credentials from Git history and rotate them","User Story: Remove all hardcoded secrets from the Git repository, including history, and rotate any compromised keys to prevent unauthorized access.
Acceptance Criteria:
- Run a tool like `git filter-repo` to purge `.env.example` and any files containing `SUPABASE_SERVICE_ROLE_KEY` or `EMAIL_PASSWORD` from the entire Git history.
- Confirm no sensitive keys are present in the code base by searching for key patterns.
- Add `.env*` and `*.txt` to the `.gitignore` file to prevent future commits of sensitive text files.
- Generate new Supabase `service_role` and `anon` keys in the Supabase dashboard.
- Generate a new application password for the email account.
- Update all deployment environments (Local, Staging, Production) with the new, rotated keys using AWS Amplify Environment Secrets.
- Document the new process for secret management.
Files Impacted:
- /.gitignore
- /.env.example
- /cashbackdeals-prd.md
- /cashbackdeals-prd copy.txt
Environment Differences:
- Local: Developers must create their own `.env.local` file from `.env.example` and populate it with keys from a secure vault.
- Staging/Production: Secrets must be configured in the AWS Amplify Console for each respective environment.
Prompt for AI Coding Agent:
Write a bash script using `git filter-repo` to completely remove the files `.env.example`, `cashbackdeals-prd.md`, and `cashbackdeals-prd copy.txt` from the entire git history of the repository. The script should then force-push the changes to all branches. Also, provide the `detect-secrets` command to scan the repository for any remaining secrets to verify the cleanup was successful.","Critical","secrets,git",2,1
4,CAS,Sub-task,"[Secrets] Implement secret management in AWS Amplify","User Story: Centralize all secrets and environment-specific configurations within AWS Amplify's secret management system to ensure they are not hardcoded and are securely provided to the application at build and runtime.
Acceptance Criteria:
- Identify all required environment variables (`NEXT_PUBLIC_SUPABASE_URL`, `NEXT_PUBLIC_SUPABASE_ANON_KEY`, `SUPABASE_SERVICE_ROLE_KEY`).
- For each Amplify environment (Staging, Production), define these variables in the Amplify Console under 'Environment variables > Secrets'.
- Use the `secret()` function from `@aws-amplify/backend` for all sensitive values.
- Update the application code in `/src/lib/supabase.ts` to read these variables from `process.env`.
- Modify the `check-env.js` script to validate that all required environment variables are present at build time.
Files Impacted:
- /check-env.js
- /src/lib/supabase.ts
Environment Differences:
- Local: Developers will continue to use a `.env.local` file.
- Staging & Production: Variables are injected by Amplify. No `.env` files should be present.
Prompt for AI Coding Agent:
Generate a TypeScript function for an AWS Amplify Gen 2 backend that defines a secret for `SUPABASE_SERVICE_ROLE_KEY` using the `secret()` function. Then, show how to configure `defineFunction` for a Lambda handler to securely access this secret as an environment variable. Reference the official AWS Amplify Gen 2 documentation for secret management.","Critical","secrets,amplify",2,1
5,CAS,Story,"[SEC] Implement Authentication and Authorization","User Story: As a user, I want to securely log in and access only the data and pages I am authorized to see, preventing unauthorized access to other users' information or admin areas.","Highest","security,auth,supabase",,1
6,CAS,Sub-task,"[Auth] Implement robust user authentication flow","User Story: Implement a complete, secure password-based authentication flow (signup, login, logout) using Supabase Auth to protect user accounts and application data.
Acceptance Criteria:
- Utilize `@supabase/ssr` library (v0.6.4 or latest stable) to handle server-side authentication.
- Create a server client in `/src/lib/supabase/server.ts`.
- Implement a Next.js middleware in a new file `/src/middleware.ts` to refresh session cookies.
- Create dedicated pages for login and signup.
- Use Supabase's `signInWithPassword` and `signUp` methods in Server Actions.
- Implement a logout mechanism that calls `signOut`.
- JWTs must be stored securely in HttpOnly cookies, managed by the `@supabase/ssr` library.
- Protect sensitive routes using server-side checks in page components and API routes.
Files Impacted:
- /src/middleware.ts (new)
- /src/lib/supabase/server.ts
- /src/components/layout/header.tsx
- /src/app/page.tsx
Environment Differences: None
Prompt for AI Coding Agent:
Using `@supabase/ssr` version 0.6.4, generate the code for a Next.js App Router project. Provide the complete code for:
1.  `/src/lib/supabase/server.ts` creating a server client.
2.  A new `/src/middleware.ts` to handle session refreshing.
3.  A login form component that uses a Server Action to call `signInWithPassword`.
Ensure all code follows the latest official Supabase documentation for server-side rendering.","Critical","auth,supabase,ssr",5,1
7,CAS,Sub-task,"[Auth] Enforce secure Supabase auth settings","User Story: Configure Supabase project settings to enforce stronger security policies for user accounts, including email verification and protection against account takeover.
Acceptance Criteria:
- In the Supabase project dashboard, enable the ""Enable email confirmations"" setting.
- In the Supabase project dashboard, enable the ""Enable secure password change"" setting.
- Implement frontend logic to handle the user flow for unconfirmed emails.
- Enforce a minimum password complexity policy on the signup form using a library like `zod`.
Files Impacted:
- This is primarily a Supabase project configuration.
- The signup form component (e.g., within `/src/app/page.tsx` or a new signup page).
Environment Differences:
- These settings are environment-agnostic but must be applied to both Staging and Production Supabase projects.
Prompt for AI Coding Agent:
Provide a Zod schema for a user signup form that enforces the following password policy: minimum 8 characters, at least one uppercase letter, one lowercase letter, one number, and one special character. Then, show how to use this schema within a Next.js Server Action to validate form data before calling the Supabase `signUp` function.","High","auth,supabase,password-policy",5,1
8,CAS,Story,"[SEC] Harden Supabase Configuration and RLS","User Story: As a database administrator, I want to configure Supabase with strict Row-Level Security (RLS) policies and avoid using over-privileged roles to ensure data is properly isolated and secure.","Highest","security,database,supabase,rls",,1
9,CAS,Sub-task,"[RLS] Implement and Enforce Row-Level Security","User Story: Create and apply Row-Level Security (RLS) policies to all tables containing user data to ensure users can only access their own information.
Acceptance Criteria:
- Connect to the Supabase database and enable RLS on all tables in the `public` schema.
- Create a `DENY BY DEFAULT` policy on all tables.
- Create permissive `SELECT`, `INSERT`, `UPDATE`, `DELETE` policies as needed, with specific `USING` and `WITH CHECK` clauses based on `auth.uid()`.
- For public data tables like `products` and `brands`, create a policy that allows read-only access for `anon` and `authenticated` roles.
- Write tests to verify that one user cannot access another user's data.
Files Impacted:
- Database schema only. This is managed via the Supabase SQL editor or migrations. No application files are directly impacted by the policy creation itself.
Environment Differences:
- RLS policies must be identical across Staging and Production Supabase projects.
Prompt for AI Coding Agent:
Provide the SQL statements for a Supabase database to:
1. Enable Row-Level Security on a table named `products`.
2. Create a policy that allows a user to `UPDATE` only the products they own, assuming a `user_id` column exists.
3. Create a policy that allows ANYONE (public) to `SELECT` from the `products` table.","Critical","rls,supabase,database",8,1
10,CAS,Sub-task,"[RLS] Eliminate service_role key misuse","User Story: Refactor all server-side data access logic to use the user's JWT for queries, instead of the `service_role` key, to ensure RLS policies are always enforced.
Acceptance Criteria:
- Audit all server-side code (API routes, Server Components) for usage of the Supabase admin client initialized with the `service_role` key.
- Refactor all queries in `/src/lib/data/products.ts` and `/src/lib/data/brands.ts` to use the per-request Supabase client created by `@supabase/ssr`.
- Restrict the use of the `service_role` key to only essential administrative tasks in the `/scripts` directory.
Files Impacted:
- /src/lib/data/products.ts
- /src/lib/data/brands.ts
- /src/lib/data/retailers.ts
- /src/lib/data/search.ts
- /scripts/test-supabase.mjs
Environment Differences: None
Prompt for AI Coding Agent:
I am using `@supabase/ssr`. My data fetching function in `/src/lib/data/products.ts` currently uses the Supabase admin client (with the service_role key). Rewrite this function to accept a Supabase client instance as an argument, so that I can pass the user-specific client created by `@supabase/ssr` from a Server Component. This will ensure RLS policies are applied.","Critical","rls,supabase,service-role",8,1
11,CAS,Story,"[SEC] Prevent Injection and XSS Vulnerabilities","User Story: As a developer, I want to sanitize and validate all user inputs and encode all outputs to prevent Cross-Site Scripting (XSS), SQL injection, and other injection attacks.","Highest","security,xss,injection",,1
12,CAS,Sub-task,"[Input] Implement comprehensive input validation","User Story: Implement strict, schema-based validation for all user-controllable input, including API request bodies, URL parameters, and form data.
Acceptance Criteria:
- Install and configure `zod`.
- Create Zod schemas for all API endpoints and Server Actions (search, contact form).
- In each API route/action (`/src/app/api/search/route.ts`), parse and validate the incoming request against the schema.
- Reject any request that fails validation with a `400 Bad Request`.
- Ensure validation covers data types, formats, and lengths.
Files Impacted:
- /src/app/api/search/route.ts
- /src/app/api/contact/route.ts
- /src/app/api/search/more/route.ts
Environment Differences: None
Prompt for AI Coding Agent:
Using `zod`, create a validation schema for a search API endpoint (`/api/search`). The schema should validate the query parameter `q` (string, required, min 3 chars, max 50 chars). Show how to use this schema in the Next.js Route Handler at `/src/app/api/search/route.ts` to validate the request's search parameters.","Critical","validation,xss,zod",11,1
13,CAS,Sub-task,"[Output] Prevent XSS with output encoding","User Story: Prevent Cross-Site Scripting (XSS) by ensuring all user-generated content is properly escaped before being rendered in the browser.
Acceptance Criteria:
- Audit the entire frontend codebase for any usage of `dangerouslySetInnerHTML`.
- Replace all instances of `dangerouslySetInnerHTML` with safe alternatives. React's default JSX encoding is sufficient for most cases.
- If rich text rendering is a hard requirement, sanitize the HTML with `isomorphic-dompurify` before rendering.
- Implement a strict Content Security Policy (CSP) as a defense-in-depth measure (handled in another task).
Files Impacted:
- /src/app/brands/[id]/BrandClient.tsx
- /src/app/products/[id]/components/ProductInfo.tsx
- Any other component that might render database content directly.
Environment Differences: None
Prompt for AI Coding Agent:
My React component at `/src/app/products/[id]/components/ProductInfo.tsx` needs to render a product description that is currently passed to `dangerouslySetInnerHTML`. Remove this usage. Instead, sanitize the HTML string using `isomorphic-dompurify` to allow only `<b>` and `<i>` tags, and then render it safely. Include installation instructions for the library.","High","xss,react,dompurify",11,1
14,CAS,Story,"[SEC] Implement Robust Security Headers and CSP","User Story: As a security engineer, I want to configure strong HTTP security headers to protect the application from XSS, clickjacking, and protocol downgrade attacks.","High","security,headers,csp",,1
15,CAS,Sub-task,"[Headers] Configure security headers in next.config.js","User Story: Implement a comprehensive set of HTTP security headers through the Next.js configuration to enforce security best practices in the browser.
Acceptance Criteria:
- Modify `next.config.js` to add a `headers` function.
- Add `Strict-Transport-Security` with `max-age=63072000; includeSubDomains; preload`.
- Add `X-Frame-Options` with the value `SAMEORIGIN`.
- Add `X-Content-Type-Options` with `nosniff`.
- Add `Referrer-Policy` with `strict-origin-when-cross-origin`.
- Add `Permissions-Policy` to disable unused features (e.g., `camera=(), microphone=()`).
Files Impacted:
- /next.config.js
Environment Differences:
- The `Strict-Transport-Security` header should only be applied in production. Use a conditional in the config.
Prompt for AI Coding Agent:
Provide the complete `async headers()` function for a `next.config.js` file. The function should return an array of security headers including `Content-Security-Policy`, `Strict-Transport-Security`, `X-Frame-Options`, and `X-Content-Type-Options`. The CSP should be moderately strict, allowing 'self' and Supabase URLs. Ensure the configuration is for Next.js 14+.","High","headers,nextjs",14,1
16,CAS,Sub-task,"[CSP] Implement a strict Content Security Policy (CSP)","User Story: Define and implement a Content Security Policy (CSP) to mitigate XSS attacks by restricting the sources from which resources can be loaded.
Acceptance Criteria:
- Define a CSP in the `headers` section of `next.config.js`.
- `default-src` should be `'self'`.
- `script-src` should be `'self'`. Avoid `'unsafe-inline'` and `'unsafe-eval'`.
- `connect-src` must include `https://*.supabase.co` and `wss://*.supabase.co`.
- `img-src` should be `'self'`, `data:`, and any required image CDNs.
- `frame-ancestors` should be `'none'`.
- Use a `report-uri` or `report-to` directive to log violations during rollout.
Files Impacted:
- /next.config.js
Environment Differences:
- The CSP can be more lenient in development but should be strict in Staging and Production. Use `process.env.NODE_ENV` to create different policies.
Prompt for AI Coding Agent:
Generate a Content Security Policy (CSP) string for a Next.js application that uses Supabase. The policy should be configured within `next.config.js`. It needs to: allow scripts/styles from 'self'; allow connections to `https://*.supabase.co` and `wss://*.supabase.co`; allow images from 'self' and `data:`; disallow framing; and use `'unsafe-eval'` for `script-src` only in development.","High","csp,headers,nextjs",14,1
17,CAS,Story,"[SEC] Implement Rate Limiting and DoS Protection","User Story: As a site operator, I want to protect my application from denial-of-service and brute-force attacks by implementing effective rate limiting on sensitive and expensive endpoints.","High","security,rate-limiting,dos",,1
18,CAS,Sub-task,"[Rate Limit] Implement rate limiting on API endpoints","User Story: Apply IP-based rate limiting to all public API endpoints, especially for authentication and search, to prevent abuse and ensure service availability.
Acceptance Criteria:
- Configure rate limiting rules in Cloudflare for all API routes under `/api/*`.
- Create a specific, stricter rule for `/api/contact` and any new auth endpoints to prevent brute-force attacks (e.g., 10 requests per minute per IP).
- Create a more lenient rule for search endpoints (`/api/search/*`, `/api/search/more/*`) to prevent scraping (e.g., 100 requests per minute per IP).
- The action for exceeding the rate limit should be to block the request for a defined period.
Files Impacted:
- Cloudflare configuration only.
Environment Differences:
- Rate limiting rules should be applied to both Staging and Production environments.
Prompt for AI Coding Agent:
Provide the configuration details for a Cloudflare Rate Limiting Rule to protect a contact form API. The rule should: match requests to `/api/contact` with the POST method; count requests based on IP address; trigger when the rate exceeds 5 requests in 10 minutes; and block the client for 1 hour. Reference the fields and values in the Cloudflare WAF documentation.","High","rate-limiting,cloudflare,api",17,1
19,CAS,Sub-task,"[Bot Protection] Implement CAPTCHA on public forms","User Story: Protect public-facing forms, like the contact and signup forms, from spam and automated abuse by integrating a user-friendly CAPTCHA service.
Acceptance Criteria:
- Sign up for Cloudflare Turnstile and get site/secret keys.
- Store the keys securely in AWS Amplify Environment Secrets.
- Add the Turnstile widget to the Contact form (`/src/app/contact/ContactPageContent.tsx`).
- In the backend logic for the contact form (`/src/app/api/contact/route.ts`), verify the Turnstile token by calling Cloudflare's `siteverify` endpoint.
- Reject any form submission that does not have a valid Turnstile token.
Files Impacted:
- /src/app/contact/ContactPageContent.tsx
- /src/app/api/contact/route.ts
Environment Differences:
- Use test keys for local/staging and production keys for production.
Prompt for AI Coding Agent:
Provide a Next.js Server Action that handles a contact form submission. It should receive form data and a Cloudflare Turnstile token, make a `fetch` request to the Cloudflare `siteverify` endpoint, and only proceed if the token is valid. Show how to read the secret key from environment variables.","High","captcha,cloudflare,turnstile",17,1
20,CAS,Story,"[SEC] Integrate Automated Security Scanning","User Story: As a DevSecOps engineer, I want to integrate automated security scanning tools into our CI/CD pipeline to proactively identify vulnerabilities.","Medium","security,automation,ci-cd",,1
21,CAS,Sub-task,"[CI/CD] Implement secret scanning in the CI pipeline","User Story: Add a secret scanning step to the CI/CD pipeline to automatically detect hardcoded credentials on every commit and pull request.
Acceptance Criteria:
- Install and configure `detect-secrets`.
- Create a baseline file (`.secrets.baseline`) by running `detect-secrets scan` and auditing the results to mark false positives.
- Add `.secrets.baseline` to version control.
- Add a script to `package.json` to run the scanner.
- Integrate this script into the existing GitHub Actions workflow (`/.github/workflows/seo-testing.yml`).
- The CI step must fail if `detect-secrets` finds any new, un-baselined secrets.
Files Impacted:
- /package.json
- /.secrets.baseline (new)
- /.github/workflows/seo-testing.yml
Environment Differences: None
Prompt for AI Coding Agent:
Provide the YAML configuration for a GitHub Actions workflow step. This step should run after dependency installation. It must execute a `detect-secrets scan` command and fail the build if any new secrets are found that are not in the `.secrets.baseline` file. Reference the official `detect-secrets` documentation for the correct command.","Medium","secret-scanning,detect-secrets,ci-cd",20,1
22,CAS,Sub-task,"[CI/CD] Implement dependency vulnerability scanning","User Story: Add a step to the CI/CD pipeline to automatically scan for known vulnerabilities in third-party dependencies.
Acceptance Criteria:
- Add a script to `package.json`: `"audit:deps": "npm audit --audit-level=high"`.
- Integrate this script into the `/.github/workflows/seo-testing.yml` workflow.
- The workflow step must fail the build if `npm audit` finds any high or critical severity vulnerabilities.
- Document a process for regularly reviewing and updating dependencies.
Files Impacted:
- /package.json
- /.github/workflows/seo-testing.yml
Environment Differences: None
Prompt for AI Coding Agent:
Provide the YAML configuration for a GitHub Actions job named `security-audit`. This job should run on every push, check out the code, set up Node.js, install dependencies using `npm ci`, and then run `npm audit --audit-level=high`, failing the job if vulnerabilities are found.","Medium","vulnerability-scanning,npm-audit,ci-cd",20,1