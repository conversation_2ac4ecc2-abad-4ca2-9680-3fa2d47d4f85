<!--
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: ARCHIVED from docs/UPDATES/AUTH-SPRINT/PR2/ to docs/archive/historical/auth_sprint_implementations/PR2/
📁 ORIGINAL LOCATION: /docs/UPDATES/AUTH-SPRINT/PR2/HMAC-AUTHENTICATION.md  
📁 NEW LOCATION: /docs/archive/historical/auth_sprint_implementations/PR2/HMAC-AUTHENTICATION.md
🎯 REASON: Historical PR2 HMAC authentication implementation specification and technical details
📝 STATUS: Content preserved unchanged, archived as authentication architecture reference
👥 REVIEW REQUIRED: Security and API teams can reference for HMAC authentication implementation patterns
🏷️ CATEGORY: Archive - Historical (PR2 HMAC Implementation)
📅 PURPOSE: Historical record of Phase 2 HMAC authentication system design and implementation specifications
-->

# HMAC Authentication Implementation

## Overview

This document describes the implementation of HMAC (Hash-based Message Authentication Code) authentication for the search API endpoints. This implementation provides secure API access for external partners while maintaining backward compatibility with existing JWT authentication for frontend users.

## Architecture

### Dual Authentication System

The system supports two authentication methods:

1. **JWT Authentication** - For frontend/browser users
2. **HMAC Authentication** - For API partners and external integrations

### Authentication Flow

```
Request → Auth Middleware → JWT Check → HMAC Check → Route Handler
                              ↓           ↓
                           Success    Success
                              ↓           ↓
                           Continue   Continue
                              ↓           ↓
                           Fail       Fail
                              ↓           ↓
                           Try HMAC   Return 401
```

## Implementation Details

### Core Files

- `src/lib/security/hmac.ts` - HMAC signature generation and verification
- `src/lib/security/jwt.ts` - JWT token handling
- `src/lib/security/auth-middleware.ts` - Unified authentication middleware
- `src/lib/security/utils.ts` - Security utilities and validation

### Protected Endpoints

All search endpoints now require authentication:

- `GET /api/search` - Main search endpoint
- `GET /api/search/suggestions` - Search suggestions
- `GET /api/search/more` - Load more results

### HMAC Signature Process

#### 1. Signature Generation

```typescript
const signature = generateHMACSignature(
  method,      // HTTP method (GET, POST, etc.)
  path,        // Request path with query parameters
  timestamp,   // Unix timestamp
  body,        // Request body (empty string for GET)
  secret       // Partner secret key
)
```

#### 2. Required Headers

```
X-Signature: sha256=<signature>
X-Timestamp: <unix_timestamp>
X-Partner-ID: <partner_identifier>
```

#### 3. Signature Verification

The server verifies:
- Signature validity using partner's secret key
- Timestamp freshness (within 5-minute window)
- Request body integrity (for POST requests)
- Replay attack prevention

## Security Features

### 1. Timestamp Validation
- Requests must include current timestamp
- 5-minute tolerance window for clock skew
- Prevents replay attacks

### 2. Body Hash Verification
- POST requests include body hash in signature
- Ensures request integrity
- Prevents tampering

### 3. Replay Protection
- In-memory cache of recent signatures
- Prevents duplicate request processing
- Automatic cleanup of expired entries

### 4. Partner Management
- Environment-based secret configuration
- Support for multiple partners
- Secure secret storage

### 5. Error Handling
- Structured error responses
- Trace ID for debugging
- Security event logging

## Configuration

### Environment Variables

```bash
# Authentication Control
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true

# JWT Configuration
JWT_SECRET=your-jwt-secret-key

# HMAC Configuration
HMAC_TIMESTAMP_WINDOW=300  # 5 minutes in seconds

# Partner Secrets
PARTNER_SECRET_DEFAULT=default-partner-secret
PARTNER_SECRET_PARTNER1=partner1-secret-key
PARTNER_SECRET_PARTNER2=partner2-secret-key
```

### Feature Flags

- `ENABLE_SEARCH_AUTH` - Master switch for all authentication
- `ENABLE_HMAC_AUTH` - Specific control for HMAC authentication

## Usage Examples

### JavaScript/Node.js Client

```javascript
import crypto from 'crypto'

function generateHMACSignature(method, path, timestamp, body, secret) {
  const message = `${method}\n${path}\n${timestamp}\n${body}`
  return crypto.createHmac('sha256', secret).update(message).digest('hex')
}

// Example request
const method = 'GET'
const path = '/api/search?q=laptop'
const timestamp = Math.floor(Date.now() / 1000)
const body = ''
const secret = 'your-partner-secret'

const signature = generateHMACSignature(method, path, timestamp, body, secret)

const response = await fetch(`https://api.example.com${path}`, {
  headers: {
    'X-Signature': `sha256=${signature}`,
    'X-Timestamp': timestamp.toString(),
    'X-Partner-ID': 'your-partner-id'
  }
})
```

### Python Client

```python
import hmac
import hashlib
import time
import requests

def generate_hmac_signature(method, path, timestamp, body, secret):
    message = f"{method}\n{path}\n{timestamp}\n{body}"
    return hmac.new(
        secret.encode('utf-8'),
        message.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()

# Example request
method = 'GET'
path = '/api/search?q=laptop'
timestamp = int(time.time())
body = ''
secret = 'your-partner-secret'

signature = generate_hmac_signature(method, path, timestamp, body, secret)

response = requests.get(
    f'https://api.example.com{path}',
    headers={
        'X-Signature': f'sha256={signature}',
        'X-Timestamp': str(timestamp),
        'X-Partner-ID': 'your-partner-id'
    }
)
```

## Testing

### Unit Tests

- `src/__tests__/security/hmac.test.ts` - HMAC functionality tests
- `src/__tests__/security/jwt.test.ts` - JWT functionality tests
- `src/__tests__/api/search-auth.test.ts` - API endpoint authentication tests
- `src/__tests__/integration/jwt-hmac-compatibility.test.ts` - Integration tests

### E2E Tests

- `tests/e2e/auth-hmac.spec.ts` - End-to-end authentication tests

### Running Tests

```bash
# Unit tests
npm test

# E2E tests (requires browser installation)
npx playwright install
npm run test:e2e

# Specific test suites
npm test -- src/__tests__/security/hmac.test.ts
npm test -- src/__tests__/api/search-auth.test.ts
```

## Deployment Checklist

### Pre-deployment

- [ ] All tests passing
- [ ] Environment variables configured
- [ ] Partner secrets securely stored
- [ ] Feature flags properly set
- [ ] Build successful

### Post-deployment

- [ ] Verify authentication endpoints respond correctly
- [ ] Test with sample HMAC requests
- [ ] Monitor error logs for authentication failures
- [ ] Validate performance metrics
- [ ] Confirm backward compatibility with existing JWT users

## Monitoring & Debugging

### Security Events

All authentication events are logged with structured data:

```json
{
  "level": "INFO|WARN|ERROR",
  "message": "Security Event: HMAC_AUTH_SUCCESS",
  "traceId": "hmac-get-abc123-def456",
  "endpoint": "/api/search",
  "method": "GET",
  "ip": "***********",
  "timestamp": "2025-01-13T12:00:00.000Z",
  "partnerId": "partner1"
}
```

### Error Codes

- `MISSING_AUTH` - No valid authentication found
- `MISSING_SIGNATURE` - X-Signature header missing
- `MISSING_TIMESTAMP` - X-Timestamp header missing
- `MISSING_PARTNER_ID` - X-Partner-ID header missing
- `INVALID_SIGNATURE` - HMAC signature verification failed
- `EXPIRED_TIMESTAMP` - Request timestamp too old
- `UNKNOWN_PARTNER` - Partner ID not recognized
- `REPLAY_DETECTED` - Duplicate request detected
- `BODY_HASH_MISMATCH` - Request body hash doesn't match

### Troubleshooting

1. **Authentication Failures**
   - Check partner secret configuration
   - Verify timestamp is current
   - Ensure signature generation matches server algorithm

2. **Performance Issues**
   - Monitor HMAC verification times
   - Check replay cache size
   - Validate timestamp window settings

3. **Integration Issues**
   - Verify header format and naming
   - Check URL encoding in path
   - Validate body hash for POST requests

## Security Considerations

### Best Practices

1. **Secret Management**
   - Store secrets in secure environment variables
   - Rotate secrets regularly
   - Use different secrets per partner

2. **Network Security**
   - Always use HTTPS in production
   - Implement rate limiting
   - Monitor for suspicious patterns

3. **Operational Security**
   - Log authentication events
   - Set up alerts for failures
   - Regular security audits

### Known Limitations

1. **Clock Synchronization**
   - Requires synchronized clocks between client and server
   - 5-minute tolerance window may need adjustment

2. **Replay Protection**
   - In-memory cache doesn't persist across server restarts
   - Consider Redis for distributed deployments

3. **Partner Management**
   - Manual secret configuration
   - No automated partner onboarding

## Future Enhancements

1. **Enhanced Partner Management**
   - Database-backed partner configuration
   - API for partner management
   - Automated secret rotation

2. **Advanced Security**
   - Rate limiting per partner
   - Geographic restrictions
   - Advanced threat detection

3. **Monitoring Improvements**
   - Real-time dashboards
   - Automated alerting
   - Performance analytics

## Support

For technical support or questions about HMAC authentication:

1. Check this documentation first
2. Review test files for implementation examples
3. Check application logs for error details
4. Contact the development team with trace IDs for specific issues
