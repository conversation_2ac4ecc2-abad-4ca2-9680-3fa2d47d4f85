<!--
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: ARCHIVED from docs/UPDATES/AUTH-SPRINT/PR2/ to docs/archive/historical/auth_sprint_implementations/PR2/
📁 ORIGINAL LOCATION: /docs/UPDATES/AUTH-SPRINT/PR2/feature-flags-guide.md  
📁 NEW LOCATION: /docs/archive/historical/auth_sprint_implementations/PR2/feature-flags-guide.md
🎯 REASON: Historical PR2 feature flags guide for HMAC authentication operational control
📝 STATUS: Content preserved unchanged, archived as feature flag methodology reference
👥 REVIEW REQUIRED: DevOps and platform teams can reference for feature flag implementation patterns
🏷️ CATEGORY: Archive - Historical (PR2 Feature Flags)
📅 PURPOSE: Historical record of Phase 2 HMAC authentication feature flag strategy and rollback procedures
-->

# Feature Flags Guide - HMAC Authentication

**Date:** January 12, 2025  
**Component:** PR 2 - HMAC Authentication & Search Routes  
**Purpose:** Operational control and rollback capability  

## Overview

The HMAC authentication system includes comprehensive feature flags for safe deployment, gradual rollout, and emergency rollback scenarios. This guide provides complete documentation for operators and developers.

## Feature Flags Reference

### Primary Authentication Control

#### `ENABLE_SEARCH_AUTH`
- **Type**: Boolean (`true` | `false`)
- **Default**: `false` (authentication disabled)
- **Scope**: Master switch for ALL search endpoint authentication
- **Impact**: When `false`, bypasses both JWT and HMAC authentication

```bash
# Enable authentication (production)
ENABLE_SEARCH_AUTH=true

# Disable authentication (emergency rollback)
ENABLE_SEARCH_AUTH=false
```

#### `ENABLE_HMAC_AUTH`
- **Type**: Boolean (`true` | `false`)
- **Default**: `false` (HMAC disabled)
- **Scope**: Controls HMAC authentication only (JWT unaffected)
- **Impact**: When `false`, only JWT authentication is available

```bash
# Enable HMAC for API partners
ENABLE_HMAC_AUTH=true

# Disable HMAC (keep JWT only)
ENABLE_HMAC_AUTH=false
```

### Configuration Parameters

#### `HMAC_TIMESTAMP_WINDOW`
- **Type**: Integer (seconds)
- **Default**: `300` (5 minutes)
- **Purpose**: Maximum allowed time difference between request timestamp and server time
- **Range**: `60` - `1800` (1 minute to 30 minutes)

```bash
# Standard window (recommended)
HMAC_TIMESTAMP_WINDOW=300

# Tighter security (shorter window)
HMAC_TIMESTAMP_WINDOW=120

# Looser for clock skew tolerance
HMAC_TIMESTAMP_WINDOW=600
```

#### `HMAC_ALGORITHM`
- **Type**: String
- **Default**: `sha256`
- **Purpose**: HMAC algorithm specification
- **Values**: `sha256` (only supported value)

```bash
HMAC_ALGORITHM=sha256
```

## Deployment Scenarios

### Scenario 1: Initial Deployment (Gradual Rollout)

```bash
# Step 1: Deploy with authentication disabled
ENABLE_SEARCH_AUTH=false
ENABLE_HMAC_AUTH=false

# Step 2: Enable JWT only (existing users)
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=false

# Step 3: Enable HMAC for partners
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
```

### Scenario 2: Emergency Rollback

```bash
# Complete rollback (no authentication)
ENABLE_SEARCH_AUTH=false
ENABLE_HMAC_AUTH=false

# Partial rollback (JWT only)
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=false
```

### Scenario 3: Partner-Specific Issues

```bash
# Disable HMAC while investigating partner issues
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=false
# Partners temporarily lose access, browsers continue working
```

### Scenario 4: Maintenance Mode

```bash
# Disable all authentication during maintenance
ENABLE_SEARCH_AUTH=false
# Allows internal monitoring/testing without auth
```

## Environment-Specific Configuration

### Development Environment
```bash
# .env.local
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
HMAC_TIMESTAMP_WINDOW=300
PARTNER_SECRET_DEFAULT=dev-default-secret-minimum-32-characters
PARTNER_SECRET_TEST_PARTNER=test-secret-minimum-32-characters-long
```

### Staging Environment
```bash
# AWS Amplify Environment Variables
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
HMAC_TIMESTAMP_WINDOW=300
PARTNER_SECRET_DEFAULT=staging-default-secret-minimum-32-chars
PARTNER_SECRET_TEST_PARTNER=staging-test-secret-minimum-32-chars
```

### Production Environment
```bash
# AWS Amplify Environment Secrets
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
HMAC_TIMESTAMP_WINDOW=300
PARTNER_SECRET_DEFAULT=prod-default-secret-minimum-32-characters
PARTNER_SECRET_ACME_CORP=prod-acme-secret-minimum-32-characters
```

## How-To Guide

### How to Enable Authentication

1. **Set Environment Variables**
   ```bash
   ENABLE_SEARCH_AUTH=true
   ENABLE_HMAC_AUTH=true
   ```

2. **Deploy Application**
   ```bash
   git push origin main
   # AWS Amplify auto-deploys
   ```

3. **Verify Deployment**
   ```bash
   curl -X GET "https://api.cashback-deals.com/api/search?q=test"
   # Should return 401 Unauthorized
   ```

### How to Disable Authentication (Emergency)

1. **Update Environment Variables**
   ```bash
   # In AWS Amplify Console or CLI
   ENABLE_SEARCH_AUTH=false
   ```

2. **Trigger Redeploy**
   ```bash
   # Option 1: Push empty commit
   git commit --allow-empty -m "Trigger redeploy"
   git push origin main
   
   # Option 2: Use AWS Amplify Console
   # Go to App > Actions > Redeploy this version
   ```

3. **Verify Rollback**
   ```bash
   curl -X GET "https://api.cashback-deals.com/api/search?q=test"
   # Should return 200 OK with search results
   ```

### How to Test Feature Flags

```bash
# Test authentication disabled
ENABLE_SEARCH_AUTH=false npm test -- src/__tests__/api/search-auth.test.ts

# Test HMAC disabled
ENABLE_SEARCH_AUTH=true ENABLE_HMAC_AUTH=false npm test

# Test full authentication
ENABLE_SEARCH_AUTH=true ENABLE_HMAC_AUTH=true npm test
```

## Monitoring and Alerts

### Key Metrics to Monitor

```bash
# Authentication success rate
aws logs filter-log-events \
  --log-group-name "/aws/lambda/cashback-deals" \
  --filter-pattern "HMAC_AUTH_SUCCESS"

# Authentication failure rate
aws logs filter-log-events \
  --log-group-name "/aws/lambda/cashback-deals" \
  --filter-pattern "HMAC_AUTH_FAILURE"
```

### Recommended Alerts

```yaml
# CloudWatch Alarms
AuthFailureRateHigh:
  MetricName: AuthFailureRate
  Threshold: 25  # 25% failure rate
  ComparisonOperator: GreaterThanThreshold
  
AuthLatencyHigh:
  MetricName: AuthLatency
  Threshold: 100  # 100ms
  ComparisonOperator: GreaterThanThreshold
```

## Troubleshooting

### Common Issues

#### Authentication Not Working After Enabling
```bash
# Check environment variables are set
echo $ENABLE_SEARCH_AUTH
echo $ENABLE_HMAC_AUTH

# Check application logs
aws logs tail /aws/lambda/cashback-deals --follow
```

#### Partners Can't Authenticate
```bash
# Temporarily disable HMAC
ENABLE_HMAC_AUTH=false

# Check partner secrets are configured
env | grep PARTNER_SECRET_
```

#### High Authentication Latency
```bash
# Check timestamp window
echo $HMAC_TIMESTAMP_WINDOW

# Reduce window if too large
HMAC_TIMESTAMP_WINDOW=120
```

## Security Considerations

### Feature Flag Security
- **Never commit feature flags to code** - Use environment variables only
- **Audit flag changes** - Log all environment variable modifications
- **Principle of least privilege** - Only ops team should modify production flags

### Rollback Security
- **Test rollback procedures** - Regularly test emergency rollback
- **Monitor during rollback** - Watch for abuse during authentication-disabled periods
- **Time-bound rollbacks** - Re-enable authentication as soon as issues are resolved

## Best Practices

1. **Gradual Rollout**: Always enable JWT before HMAC
2. **Monitor Closely**: Watch metrics for 24 hours after flag changes
3. **Document Changes**: Log all flag modifications with reasons
4. **Test Thoroughly**: Verify flag behavior in staging before production
5. **Communicate**: Notify team and partners of planned flag changes

---

**Maintained by:** DevOps Team  
**Last Updated:** January 12, 2025  
**Next Review:** February 12, 2025
