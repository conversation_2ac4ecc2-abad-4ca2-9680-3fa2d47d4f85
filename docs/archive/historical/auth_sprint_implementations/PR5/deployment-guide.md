<!--
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: ARCHIVED from docs/UPDATES/AUTH-SPRINT/PR5/ to docs/archive/historical/auth_sprint_implementations/PR5/
📁 ORIGINAL LOCATION: /docs/UPDATES/AUTH-SPRINT/PR5/deployment-guide.md  
📁 NEW LOCATION: /docs/archive/historical/auth_sprint_implementations/PR5/deployment-guide.md
🎯 REASON: Historical PR5 deployment guide for CORS tightening and automated security testing
📝 STATUS: Content preserved unchanged, archived as deployment procedure reference
👥 REVIEW REQUIRED: DevOps team can reference for PR5 CORS configuration and security testing deployment procedures
🏷️ CATEGORY: Archive - Historical (PR5 Deployment Guide)
📅 PURPOSE: Historical record of Phase 5 CORS tightening and automated security testing deployment on AWS Amplify and Cloudflare
-->

# PR5: CORS Tightening + Automated Security Tests - Deployment Guide

**Date:** July 13, 2025  
**Version:** v14.9.0  
**Environment:** AWS Amplify + Cloudflare Proxy  
**Risk Level:** Low (Feature Flag Controlled)  

## 🚀 Deployment Overview

### **Phased Rollout Strategy**
1. **Phase 0**: Deploy with CORS disabled (monitoring only)
2. **Phase 1**: Enable CORS for catalog endpoint only  
3. **Phase 2**: Full enforcement across all protected routes
4. **Phase 3**: Production optimization and monitoring

### **Safety Mechanisms**
- ✅ Feature flag controls (`ENABLE_CORS_STRICT`)
- ✅ Route-specific configuration
- ✅ Instant rollback capability (< 1 minute)
- ✅ Comprehensive monitoring and alerting

## 📋 Pre-Deployment Checklist

### **Code Verification**
- [ ] All tests passing (59/59 test cases)
- [ ] TypeScript compilation successful
- [ ] No security vulnerabilities in dependencies
- [ ] Feature flag controls validated
- [ ] Server-to-server detection working

### **Environment Configuration**
- [ ] Environment variables documented in `.env.example`
- [ ] AWS Amplify environment variables configured
- [ ] Cloudflare proxy settings verified
- [ ] Monitoring and logging endpoints tested

### **Security Validation**
- [ ] CORS policy enforcement tested with real browsers
- [ ] Rate limiting thresholds validated
- [ ] Authentication flows verified
- [ ] Error responses consistent across routes

## 🔧 Environment Variables Configuration

### **Required Variables**
```bash
# CORS Protection Configuration (PR5)
ENABLE_CORS_STRICT=false  # Start with false for safe deployment
CORS_PROTECTED_ROUTES=/api/catalog,/api/search/more,/api/products,/api/brands,/api/retailers
CORS_ALLOWED_AMPLIFY_DOMAINS=https://4-2.d3q274urye85k3.amplifyapp.com,https://main.d3q274urye85k3.amplifyapp.com

# Authentication Configuration (Required for protected routes)
JWT_SECRET=your-production-jwt-secret
PARTNER_SECRET=your-production-partner-secret
ENABLE_HMAC_AUTH=true

# Rate Limiting Configuration
ENABLE_RATE_LIMITING=true

# Monitoring Configuration
ENABLE_SECURITY_LOGGING=true
```

### **AWS Amplify Console Setup**
1. **Navigate to**: AWS Amplify Console → App Settings → Environment Variables
2. **Add Variables**: Copy all required variables from above
3. **Verify Settings**: Ensure all variables are marked as non-secret (except JWT/PARTNER secrets)
4. **Save Configuration**: Trigger environment rebuild

## 🎯 Phase 0: Initial Deployment (CORS Monitoring)

### **Objective**: Deploy with CORS disabled to validate infrastructure and monitoring

### **Step 1: Deploy Code**
```bash
# Verify environment variables
aws amplify get-app --app-id your-app-id

# Deploy via Git push (automatic trigger)
git push origin main

# Monitor build progress
aws amplify list-jobs --app-id your-app-id --branch-name main
```

### **Step 2: Verify Infrastructure**
```bash
# Test all endpoints respond normally
curl https://your-app.amplifyapp.com/api/catalog
curl https://your-app.amplifyapp.com/api/search/more?q=test
curl https://your-app.amplifyapp.com/api/products/123
curl https://your-app.amplifyapp.com/api/brands/abc
curl https://your-app.amplifyapp.com/api/retailers/xyz

# Expected: Normal responses (200/401 depending on auth)
```

### **Step 3: Monitoring Validation**
```bash
# Check CloudWatch logs for structured logging
aws logs filter-log-events --log-group-name /aws/amplify/your-app \
  --filter-pattern "CORS_VIOLATION"

# Should show: No CORS violations (feature disabled)
```

### **Step 4: Feature Flag Test**
```bash
# Temporarily enable CORS for testing
aws amplify update-app --app-id your-app-id \
  --environment-variables ENABLE_CORS_STRICT=true

# Redeploy and test
curl -H "Origin: https://evil.com" https://your-app.amplifyapp.com/api/catalog
# Expected: 403 CORS blocked

# Disable for Phase 1 preparation
aws amplify update-app --app-id your-app-id \
  --environment-variables ENABLE_CORS_STRICT=false
```

## 🔒 Phase 1: Catalog Endpoint Protection

### **Objective**: Enable CORS for catalog endpoint only, monitor for 24 hours

### **Step 1: Selective CORS Enablement**
```bash
# Update environment to enable CORS with catalog-only protection
aws amplify update-app --app-id your-app-id \
  --environment-variables \
  ENABLE_CORS_STRICT=true \
  CORS_PROTECTED_ROUTES=/api/catalog
```

### **Step 2: Verification Testing**
```bash
# Test catalog endpoint blocking
curl -H "Origin: https://unauthorized-site.com" \
     https://your-app.amplifyapp.com/api/catalog
# Expected: 403 CORS blocked

# Test catalog endpoint allowing
curl -H "Origin: https://cashback-deals.com" \
     -H "Authorization: Bearer valid-jwt" \
     https://your-app.amplifyapp.com/api/catalog
# Expected: 200 with data

# Test other endpoints unaffected
curl -H "Origin: https://unauthorized-site.com" \
     https://your-app.amplifyapp.com/api/products/123
# Expected: 200 (not protected yet)
```

### **Step 3: Monitoring Setup**
```bash
# Create CloudWatch alarm for CORS violations
aws cloudwatch put-metric-alarm \
  --alarm-name "CORS-Violations-Catalog" \
  --alarm-description "Monitor CORS violations on catalog endpoint" \
  --metric-name "cors_violations" \
  --namespace "CashbackDeals/Security" \
  --statistic "Sum" \
  --period 300 \
  --threshold 10 \
  --comparison-operator "GreaterThanThreshold"

# Monitor logs for 24 hours
aws logs filter-log-events --log-group-name /aws/amplify/your-app \
  --filter-pattern "CORS_VIOLATION" \
  --start-time $(date -d '24 hours ago' +%s)000
```

### **Step 4: 24-Hour Validation**
After 24 hours, verify:
- [ ] No legitimate user complaints
- [ ] CORS violations logged only for unauthorized origins
- [ ] Catalog endpoint performance maintained
- [ ] Authentication flows working correctly
- [ ] Rate limiting functioning as expected

## 🌐 Phase 2: Full CORS Enforcement

### **Objective**: Enable CORS for all protected routes after successful Phase 1

### **Step 1: Full Route Protection**
```bash
# Enable full CORS protection
aws amplify update-app --app-id your-app-id \
  --environment-variables \
  ENABLE_CORS_STRICT=true \
  CORS_PROTECTED_ROUTES=/api/catalog,/api/search/more,/api/products,/api/brands,/api/retailers
```

### **Step 2: Comprehensive Testing**
```bash
# Test all protected routes
PROTECTED_ROUTES=(
  "/api/catalog"
  "/api/search/more?q=test" 
  "/api/products/123"
  "/api/brands/abc"
  "/api/retailers/xyz"
)

for route in "${PROTECTED_ROUTES[@]}"; do
  echo "Testing $route"
  
  # Test blocking
  curl -H "Origin: https://malicious-site.com" \
       "https://your-app.amplifyapp.com$route"
  echo "Expected: 403 CORS blocked"
  
  # Test allowing
  curl -H "Origin: https://cashback-deals.com" \
       "https://your-app.amplifyapp.com$route"
  echo "Expected: 200 or 401 (auth required)"
  
  echo "---"
done
```

### **Step 3: Browser Compatibility Testing**
```javascript
// Run in browser console from https://cashback-deals.com
const testEndpoints = [
  '/api/catalog',
  '/api/search/more?q=test',
  '/api/products/123',
  '/api/brands/abc',
  '/api/retailers/xyz'
];

testEndpoints.forEach(endpoint => {
  fetch(`https://your-app.amplifyapp.com${endpoint}`)
    .then(response => console.log(`${endpoint}: ${response.status}`))
    .catch(error => console.error(`${endpoint}: ${error.message}`));
});
```

### **Step 4: OPTIONS Preflight Validation**
```bash
# Test preflight requests for each endpoint
for route in "${PROTECTED_ROUTES[@]}"; do
  curl -X OPTIONS \
       -H "Origin: https://cashback-deals.com" \
       -H "Access-Control-Request-Method: GET" \
       -H "Access-Control-Request-Headers: authorization" \
       "https://your-app.amplifyapp.com$route"
  echo "Expected: 200 with CORS headers"
done
```

## 📊 Phase 3: Production Optimization

### **Objective**: Optimize performance and monitoring based on real usage data

### **Step 1: Performance Monitoring**
```bash
# Monitor response times with CORS enabled
aws logs filter-log-events --log-group-name /aws/amplify/your-app \
  --filter-pattern "X-Response-Time" \
  --start-time $(date -d '1 hour ago' +%s)000

# Analyze CORS overhead impact
aws logs insights start-query \
  --log-group-name "/aws/amplify/your-app" \
  --start-time $(date -d '24 hours ago' +%s) \
  --end-time $(date +%s) \
  --query-string '
    fields @timestamp, responseTime
    | filter @message like /CATALOG_ACCESS/
    | stats avg(responseTime), max(responseTime), min(responseTime)
  '
```

### **Step 2: Security Analytics**
```bash
# Analyze CORS violation patterns
aws logs insights start-query \
  --log-group-name "/aws/amplify/your-app" \
  --start-time $(date -d '7 days ago' +%s) \
  --end-time $(date +%s) \
  --query-string '
    fields @timestamp, origin
    | filter @message like /CORS_VIOLATION/
    | stats count() by origin
    | sort count desc
  '

# Monitor server-to-server access patterns
aws logs insights start-query \
  --log-group-name "/aws/amplify/your-app" \
  --start-time $(date -d '24 hours ago' +%s) \
  --end-time $(date +%s) \
  --query-string '
    fields @timestamp, userAgent, pathname
    | filter @message like /SERVER_TO_SERVER_ACCESS/
    | stats count() by userAgent
  '
```

### **Step 3: Optimization Opportunities**
Based on monitoring data, consider:

#### **Domain Allowlist Refinement**
```bash
# If specific preview domains are identified
aws amplify update-app --app-id your-app-id \
  --environment-variables \
  CORS_ALLOWED_AMPLIFY_DOMAINS="https://4-2.d3q274urye85k3.amplifyapp.com,https://main.d3q274urye85k3.amplifyapp.com,https://feature-xyz.d3q274urye85k3.amplifyapp.com"
```

#### **Rate Limit Tuning**
```bash
# Adjust catalog rate limits based on usage patterns
# (Requires code deployment)
```

## 🚨 Emergency Rollback Procedures

### **Level 1: Feature Flag Disable** (< 1 minute)
```bash
# Immediate CORS disable
aws amplify update-app --app-id your-app-id \
  --environment-variables ENABLE_CORS_STRICT=false

# Verify disable
curl -H "Origin: https://any-site.com" \
     https://your-app.amplifyapp.com/api/catalog
# Expected: Normal response (not blocked)
```

### **Level 2: Selective Route Protection** (< 5 minutes)
```bash
# Remove problematic routes from protection
aws amplify update-app --app-id your-app-id \
  --environment-variables \
  ENABLE_CORS_STRICT=true \
  CORS_PROTECTED_ROUTES=/api/catalog  # Remove other routes
```

### **Level 3: Full Deployment Rollback** (< 5 minutes)
```bash
# Revert to previous deployment
aws amplify start-job \
  --app-id your-app-id \
  --branch-name main \
  --job-type RELEASE \
  --job-id previous-successful-job-id

# Monitor rollback progress
aws amplify get-job --app-id your-app-id --branch-name main --job-id new-job-id
```

## 🔍 Monitoring & Alerting Setup

### **CloudWatch Dashboards**
```json
{
  "widgets": [
    {
      "type": "metric",
      "properties": {
        "metrics": [
          ["CashbackDeals/Security", "cors_violations"],
          ["CashbackDeals/Performance", "response_time_p95"],
          ["CashbackDeals/API", "request_count"]
        ],
        "period": 300,
        "stat": "Sum",
        "region": "us-east-1",
        "title": "CORS Security Metrics"
      }
    }
  ]
}
```

### **Slack Integration**
```bash
# Create SNS topic for alerts
aws sns create-topic --name cors-security-alerts

# Subscribe Slack webhook
aws sns subscribe \
  --topic-arn arn:aws:sns:us-east-1:account:cors-security-alerts \
  --protocol https \
  --notification-endpoint https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK
```

### **Alert Thresholds**
- **CORS Violations**: > 10 per 5 minutes
- **Response Time Degradation**: > 2x baseline
- **Authentication Failures**: > 100 per minute
- **Rate Limit Triggers**: > 50 per minute

## ✅ Success Criteria

### **Performance Targets**
- [ ] Response time impact < 2ms (95th percentile)
- [ ] No increase in error rates for legitimate traffic
- [ ] Memory usage increase < 10MB per instance
- [ ] CPU impact < 1% additional utilization

### **Security Validation**
- [ ] 100% blocking of unauthorized cross-origin requests
- [ ] 0% false positive blocking of legitimate requests
- [ ] All preflight requests handled correctly
- [ ] Server-to-server tools working without origin headers

### **Operational Requirements**
- [ ] Feature flag controls working reliably
- [ ] Monitoring and alerting functioning
- [ ] Emergency rollback procedures tested
- [ ] Documentation complete and accessible

## 📚 Post-Deployment Documentation

### **Runbook Updates**
- [ ] Update incident response procedures
- [ ] Document CORS troubleshooting steps
- [ ] Create partner integration guidelines
- [ ] Update security review checklist

### **Team Training**
- [ ] Security team briefing on new controls
- [ ] DevOps team training on monitoring tools
- [ ] Support team guidance on CORS-related issues
- [ ] Partner integration team updated on new requirements

This deployment guide ensures a safe, monitored, and reversible rollout of the CORS security enhancements while maintaining system stability and user experience.