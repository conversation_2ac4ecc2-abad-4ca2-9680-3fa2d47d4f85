<!--
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: ARCHIVED from docs/UPDATES/AUTH-SPRINT/ to docs/archive/historical/auth_sprint_implementations/
📁 ORIGINAL LOCATION: /docs/UPDATES/AUTH-SPRINT/README-jwt.md  
📁 NEW LOCATION: /docs/archive/historical/auth_sprint_implementations/README-jwt.md
🎯 REASON: Historical JWT authentication implementation guide and best practices
📝 STATUS: Content preserved unchanged, archived as authentication reference
👥 REVIEW REQUIRED: Security and development teams can reference for JWT implementation patterns
🏷️ CATEGORY: Archive - Historical (JWT Authentication Guide)
📅 PURPOSE: Historical record of JWT authentication system implementation guidelines and security considerations
-->

# JWT Authentication Implementation Guide

## Overview

This document provides detailed implementation guidance for the JWT authentication system used in the Cashback Deals platform.

## Architecture

### Two-Step Authentication Flow

#### Step 1: Turnstile Verification → JWT Issuance
1. User completes contact form with Turnstile CAPTCHA
2. Server verifies CAPTCHA with Cloudflare
3. On success, server creates JWT token (5-minute expiry)
4. JWT set as HttpOnly cookie AND returned in response

#### Step 2: Form Submission with JWT
1. Client submits actual form data
2. Server validates JWT (from cookie or Authorization header)
3. On valid JWT, process form submission
4. Send email and return success response

### Dual Transport Support

**Browser Requests (Automatic)**:
- JWT stored as HttpOnly cookie
- Automatically sent with same-origin requests
- Secure, SameSite=Lax configuration

**API Clients (Manual)**:
- JWT sent in Authorization header: `Bearer <token>`
- Suitable for server-to-server or mobile clients
- No cookie dependency

### Fallback Order
1. Check `Authorization: Bearer` header first
2. If not found, check `auth-token` cookie
3. If neither found, return 401 Unauthorized

## Security Features

### Token Specifications
- **Algorithm**: HS256 (HMAC SHA-256)
- **Expiry**: 5 minutes (300 seconds)
- **Payload**: `{ sub: 'frontend', iat: timestamp, exp: timestamp }`
- **Secret**: Environment-based (32+ characters required)

### Production Security
- JWT_SECRET environment variable required in production
- Fallback secret only allowed in development
- Automatic secret validation on module load
- Secure cookie flags in production (HttpOnly, Secure, SameSite=Lax)

### Rate Limiting Integration
- Contact API protected by existing rate limiter (5 requests/10 minutes)
- JWT verification happens after rate limiting check
- Prevents JWT brute force attacks

## Environment Configuration

### Required Variables
```bash
# Production (required)
JWT_SECRET=your-secure-32-char-minimum-secret-key

# Development (optional - has fallback)
JWT_SECRET=dev-secret-for-testing
```

### Cookie Configuration
```javascript
// Development
HttpOnly; SameSite=Lax; Path=/; Max-Age=300

// Production  
HttpOnly; SameSite=Lax; Path=/; Max-Age=300; Secure
```

## API Usage Examples

### Frontend (Browser)
```javascript
// Step 1: Submit with Turnstile
const response1 = await fetch('/api/contact', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    'cf-turnstile-response': turnstileToken,
    name: 'User',
    email: '<EMAIL>',
    enquiryType: 'general',
    message: 'Hello'
  })
});

// JWT now set as cookie automatically

// Step 2: Submit form data (JWT sent automatically via cookie)
const response2 = await fetch('/api/contact', {
  method: 'POST', 
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'User',
    email: '<EMAIL>',
    enquiryType: 'general', 
    message: 'Hello'
  })
});
```

### Server/Mobile Client
```javascript
// Step 1: Get JWT from Turnstile verification
const response1 = await fetch('/api/contact', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    'cf-turnstile-response': turnstileToken,
    // ... form data
  })
});

const { token } = await response1.json();

// Step 2: Use JWT in Authorization header
const response2 = await fetch('/api/contact', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    // ... form data (no Turnstile token)
  })
});
```

## Error Handling

### Common Error Scenarios

#### 401 Unauthorized Responses
```json
{
  "error": "Unauthorized",
  "message": "Invalid or missing authentication token", 
  "code": "AUTH_TOKEN_REQUIRED"
}
```

#### Turnstile Verification Failures
```json
{
  "error": "CAPTCHA verification failed",
  "message": "Invalid CAPTCHA token. Please try again."
}
```

#### Rate Limiting
```json
{
  "error": "Too many requests",
  "message": "Rate limit exceeded. Try again in 600 seconds."
}
```

## Testing Guidelines

### Unit Testing
- Test JWT creation and verification functions
- Test token extraction from various request formats
- Test production secret validation
- Test cookie and header fallback order

### Integration Testing  
- Test complete two-step flow
- Test error scenarios (invalid tokens, expired tokens)
- Test rate limiting integration
- Test CORS header handling

### Manual Testing
- Browser form submission with CAPTCHA
- API client requests with Authorization headers
- Invalid token scenarios
- Production environment secret validation

## Security Considerations

### Token Rotation
- Tokens expire automatically after 5 minutes
- No refresh token mechanism (intentionally simple)
- Quarterly JWT_SECRET rotation recommended

### Attack Mitigation
- Rate limiting prevents brute force attacks
- Short expiry limits replay attack window  
- HttpOnly cookies prevent XSS token theft
- Secure cookies prevent MITM in production
- Input validation prevents injection attacks

### Monitoring
- Log all authentication failures
- Monitor rate limiting triggers
- Alert on production secret validation failures
- Track token creation/verification metrics

## Future Enhancements

### Planned Improvements
- Redis-backed rate limiting for horizontal scaling
- Partner-specific HMAC secrets for bulk API access
- Enhanced security event logging and alerting
- Automated secret rotation via AWS Parameter Store

### Integration Points
- Search API protection (PR 2)
- Catalog page rate limiting (PR 4)  
- CORS origin restrictions (PR 5)
- Advanced bot detection integration