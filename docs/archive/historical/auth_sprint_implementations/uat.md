<!--
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: ARCHIVED from docs/UPDATES/AUTH-SPRINT/ to docs/archive/historical/auth_sprint_implementations/
📁 ORIGINAL LOCATION: /docs/UPDATES/AUTH-SPRINT/uat.md  
📁 NEW LOCATION: /docs/archive/historical/auth_sprint_implementations/uat.md
🎯 REASON: Historical User Acceptance Testing (UAT) guide for JWT authentication implementation
📝 STATUS: Content preserved unchanged, archived as testing methodology reference
👥 REVIEW REQUIRED: QA and security teams can reference for authentication testing procedures
🏷️ CATEGORY: Archive - Historical (UAT Testing Guide)
📅 PURPOSE: Historical record of JWT authentication UI testing procedures and validation steps
-->

🧪 How to Test JWT Authentication in the UI - Simple Guide

  🎯 What We're Testing

  Your JWT authentication protects the contact form with a 2-step process:
  1. Step 1: Solve CAPTCHA → Get JWT token
  2. Step 2: Submit form with JW<PERSON> → Form processes successfully

  🚀 Simple Testing Steps

  1. Start Your Development Server

  npm run dev
  Wait for: Ready - started server on 0.0.0.0:3000

  2. Open the Contact Page

  Go to: http://localhost:3000/contact

  3. Test the Authentication Flow

  Normal Flow (Should Work):

  1. Fill out the contact form:
    - Name: "Test User"
    - Email: "<EMAIL>"
    - Message: "Testing JWT authentication"
  2. Solve the CAPTCHA (click the checkbox)
  3. Click "Send Message"
  4. ✅ Expected: Success message appears

  Bypass Attempt (Should Fail):

  1. Open browser Developer Tools (F12)
  2. Go to Network tab
  3. Fill out the form but DON'T solve CAPTCHA
  4. Click "Send Message"
  5. ✅ Expected: Error message about missing authentication

  🕵️ What to Look For

  In the Browser Network Tab:

  1. First Request to /api/contact/verify (CAPTCHA step)
    - Should return JWT token
  2. Second Request to /api/contact (form submission)
    - Should include Authorization: Bearer <token> header
    - Should succeed with JWT

  In the Browser Console:

  - No errors about missing JWT
  - Success messages for form submission

  Visual Feedback:

  - CAPTCHA checkbox works
  - Form shows loading state
  - Success/error messages display properly

  🔧 If Something Goes Wrong

  Common Issues & Fixes:

  ❌ "Missing authentication token" error:
  - Make sure you solved the CAPTCHA first
  - Check Network tab for the verify request

  ❌ CAPTCHA not loading:
  - Check your .env.local has NEXT_PUBLIC_TURNSTILE_SITE_KEY
  - Use the test key: 1x00000000000000000000AA

  ❌ Form submission fails:
  - Check server logs in terminal
  - Verify JWT_SECRET is set in environment

  🎪 Quick Test Script

  Want to test programmatically? Open browser console and run:

  // Test 1: Try to submit without JWT (should fail)
  fetch('/api/contact', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      name: 'Test',
      email: '<EMAIL>',
      message: 'Test message'
    })
  }).then(r => r.json()).then(console.log)
  // Expected: 401 Unauthorized

  // Test 2: Get JWT first, then submit (should work)
  // This requires solving CAPTCHA in UI first

  ✅ Success Indicators

  You'll know it's working when:
  - ✅ Contact form requires CAPTCHA completion
  - ✅ Form submits successfully after CAPTCHA
  - ✅ Direct API calls without JWT are rejected
  - ✅ Network tab shows JWT tokens being passed
  - ✅ No console errors about authentication

  That's it! Your JWT authentication is now protecting the contact form from bots and unauthorized
  submissions. 🎉
