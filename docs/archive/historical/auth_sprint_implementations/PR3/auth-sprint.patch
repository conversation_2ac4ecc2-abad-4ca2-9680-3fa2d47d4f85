diff --git a/CLAUDE.md b/CLAUDE.md
index d371a35..3111ec4 100644
--- a/CLAUDE.md
+++ b/CLAUDE.md
@@ -40,7 +40,7 @@ This file provides guidance to <PERSON> (claude.ai/code) when working with co
 - **State Management**: React Query for server state
 - **Authentication**: Supabase Auth
 - **Testing**: Jest with React Testing Library
-- **Deployment**: Vercel with Cloudflare security
+- **Deployment**: AWS Amplify with Cloudflare security
 
 ### Key Architectural Patterns
 
@@ -165,11 +165,13 @@ Key environment variables (see `docs/ENVIRONMENT_VARIABLES.md`):
 - **Performance tests**: Web Vitals and Lighthouse audits
 
 ### Deployment and Infrastructure
-- **Hosting**: Vercel with automatic deployments
+- **Hosting**: Our production build is deployed via AWS Amplify Console
 - **Database**: Supabase with migrations in `supabase/migrations/`
 - **CDN**: Cloudflare for security headers and bot protection
 - **Images**: Next.js Image optimization with remote patterns
 - **Monitoring**: Web Vitals and error tracking
+- # INFRASTRUCTURE:
+Our production build is deployed via AWS Amplify Console. A Cloudflare proxy terminates TLS and routes traffic to the Amplify distribution. No Vercel services are used in this pipeline. The refereces to Vercel are incorrect and out of date. 
 
 ## Important Notes
 
@@ -213,6 +215,8 @@ I have created a complete documentation suite for the Cashback Deals v2 codebase
 - Component architecture with server/client component strategies
 - Rendering strategies and performance optimization patterns
 - Data flow diagrams and system interaction patterns
+- - # INFRASTRUCTURE update:
+Our production build is deployed via AWS Amplify Console. A Cloudflare proxy terminates TLS and routes traffic to the Amplify distribution. No Vercel services are used in this pipeline. The refereces to Vercel are incorrect and out of date. 
 
 #### [docs/DATA_MODEL.md](docs/DATA_MODEL.md)
 **Database Schema & Data Management**
diff --git a/auth-sprint.patch b/auth-sprint.patch
index 5463961..e69de29 100644
--- a/auth-sprint.patch
+++ b/auth-sprint.patch
@@ -1,6675 +0,0 @@
-diff --git a/docs/UPDATES/AUTH-SPRINT/PR2/deployment-guide.md b/docs/UPDATES/AUTH-SPRINT/PR2/deployment-guide.md
-new file mode 100644
-index 0000000..9296b87
---- /dev/null
-+++ b/docs/UPDATES/AUTH-SPRINT/PR2/deployment-guide.md
-@@ -0,0 +1,290 @@
-+# PR 2 Deployment Guide - HMAC Authentication & Search Routes
-+
-+**Date:** January 12, 2025  
-+**Sprint:** Auth Layer Implementation  
-+**Component:** HMAC Helper + Search Routes Protection  
-+**Deployment Type:** Incremental with Rollback Capability  
-+
-+## Pre-Deployment Checklist
-+
-+### Code Quality Gates
-+- [ ] **All Tests Passing**: 50/50 tests (HMAC + Search Auth + Security + Performance + Compatibility)
-+- [ ] **Code Coverage**: >95% for new HMAC functionality
-+- [ ] **Lint Checks**: ESLint passing with no errors
-+- [ ] **Build Success**: Production build completes without errors
-+- [ ] **Security Scan**: No new vulnerabilities introduced
-+
-+### Environment Preparation
-+- [ ] **Partner Secrets**: Configured in AWS Amplify Environment Secrets
-+- [ ] **Test Partners**: Development/staging test accounts ready
-+- [ ] **Monitoring**: Authentication metrics dashboard prepared
-+- [ ] **Alerts**: Authentication failure rate alerts configured
-+
-+## Environment Configuration
-+
-+### Development Environment
-+```bash
-+# .env.local (development)
-+PARTNER_SECRET_DEFAULT=dev-default-secret-minimum-32-characters
-+PARTNER_SECRET_TEST_PARTNER=test-secret-minimum-32-characters-long
-+PARTNER_SECRET_ACME_CORP=acme-dev-secret-minimum-32-characters
-+HMAC_TIMESTAMP_WINDOW=300
-+HMAC_ALGORITHM=sha256
-+```
-+
-+### Staging Environment
-+```bash
-+# AWS Amplify Environment Secrets (staging)
-+PARTNER_SECRET_DEFAULT=staging-default-secret-minimum-32-chars
-+PARTNER_SECRET_TEST_PARTNER=staging-test-secret-minimum-32-chars
-+PARTNER_SECRET_ACME_CORP=staging-acme-secret-minimum-32-chars
-+HMAC_TIMESTAMP_WINDOW=300
-+HMAC_ALGORITHM=sha256
-+```
-+
-+### Production Environment
-+```bash
-+# AWS Amplify Environment Secrets (production)
-+PARTNER_SECRET_DEFAULT=prod-default-secret-minimum-32-characters
-+PARTNER_SECRET_ACME_CORP=prod-acme-secret-minimum-32-characters
-+PARTNER_SECRET_BETA_CORP=prod-beta-secret-minimum-32-characters
-+HMAC_TIMESTAMP_WINDOW=300
-+HMAC_ALGORITHM=sha256
-+
-+# Optional: Feature flags for gradual rollout
-+ENABLE_SEARCH_AUTH=true
-+ENABLE_HMAC_AUTH=true
-+```
-+
-+## Deployment Strategy
-+
-+### Phase 1: Staging Deployment (Day 1)
-+```bash
-+# Deploy to staging
-+git checkout feature/pr2-hmac-auth
-+git push origin feature/pr2-hmac-auth
-+
-+# Verify staging deployment
-+curl -X GET "https://staging.cashback-deals.com/api/search?q=test" \
-+  -H "X-Signature: sha256=..." \
-+  -H "X-Timestamp: $(date +%s)" \
-+  -H "X-Partner-ID: test-partner"
-+```
-+
-+#### Staging Validation Checklist
-+- [ ] **HMAC Authentication**: Test partner can authenticate successfully
-+- [ ] **JWT Compatibility**: Existing JWT authentication still works
-+- [ ] **Error Handling**: Proper 401 responses for invalid authentication
-+- [ ] **Rate Limiting**: Rate limits still enforced after authentication
-+- [ ] **Performance**: Response times within acceptable range (<5ms overhead)
-+
-+### Phase 2: Production Deployment (Day 2)
-+```bash
-+# Create production deployment
-+git checkout main
-+git merge feature/pr2-hmac-auth
-+git push origin main
-+
-+# Monitor deployment
-+# AWS Amplify will automatically deploy to production
-+```
-+
-+#### Production Validation Checklist
-+- [ ] **Health Check**: All search endpoints responding correctly
-+- [ ] **Authentication Metrics**: Monitor success/failure rates
-+- [ ] **Error Rates**: No increase in 5xx errors
-+- [ ] **Response Times**: No degradation in API performance
-+- [ ] **Partner Integration**: Test partners can authenticate successfully
-+
-+### Phase 3: Partner Onboarding (Day 3+)
-+- [ ] **Partner Notification**: Inform partners of new authentication requirements
-+- [ ] **Integration Support**: Provide technical assistance for HMAC implementation
-+- [ ] **Gradual Migration**: Allow grace period for partner integration
-+- [ ] **Monitoring**: Track partner adoption and authentication patterns
-+
-+## Monitoring & Alerting
-+
-+### Key Metrics to Monitor
-+```typescript
-+// Authentication success rates
-+const authMetrics = {
-+  jwtSuccessRate: 'percentage of successful JWT authentications',
-+  hmacSuccessRate: 'percentage of successful HMAC authentications',
-+  authFailureRate: 'percentage of authentication failures',
-+  partnerAuthDistribution: 'authentication attempts by partner',
-+  responseTimeP95: '95th percentile response time with authentication'
-+}
-+```
-+
-+### Alert Thresholds
-+```yaml
-+alerts:
-+  - name: "High Authentication Failure Rate"
-+    condition: "auth_failure_rate > 10%"
-+    severity: "warning"
-+    
-+  - name: "Critical Authentication Failure Rate"
-+    condition: "auth_failure_rate > 25%"
-+    severity: "critical"
-+    
-+  - name: "Authentication Performance Degradation"
-+    condition: "auth_response_time_p95 > 100ms"
-+    severity: "warning"
-+    
-+  - name: "Partner Authentication Issues"
-+    condition: "partner_auth_failure_rate > 50%"
-+    severity: "warning"
-+```
-+
-+### Monitoring Dashboard
-+```typescript
-+// CloudWatch/Grafana dashboard metrics
-+const dashboardMetrics = [
-+  'API requests per minute by endpoint',
-+  'Authentication method distribution (JWT vs HMAC)',
-+  'Authentication success/failure rates',
-+  'Response time percentiles (P50, P95, P99)',
-+  'Partner authentication patterns',
-+  'Error rate by endpoint and authentication method'
-+]
-+```
-+
-+## Rollback Procedures
-+
-+### Immediate Rollback (Emergency)
-+If critical issues are detected within first 30 minutes:
-+
-+```bash
-+# Emergency rollback to previous version
-+git revert HEAD --no-edit
-+git push origin main
-+
-+# Or use AWS Amplify console to rollback to previous deployment
-+```
-+
-+#### Emergency Rollback Triggers
-+- **Authentication Failure Rate >50%**: Mass authentication failures
-+- **API Downtime**: Search endpoints returning 5xx errors
-+- **Performance Degradation >200%**: Unacceptable response time increase
-+- **Partner Integration Failures**: Multiple partners unable to authenticate
-+
-+### Gradual Rollback (Planned)
-+If issues are identified during monitoring period:
-+
-+#### Option 1: Feature Flag Rollback
-+```bash
-+# Disable authentication via environment variables
-+ENABLE_SEARCH_AUTH=false
-+ENABLE_HMAC_AUTH=false
-+
-+# Redeploy with authentication disabled
-+```
-+
-+#### Option 2: Endpoint-Specific Rollback
-+```typescript
-+// Temporarily disable authentication on specific endpoints
-+const ROLLBACK_ENDPOINTS = [
-+  '/api/search/suggestions', // If suggestions endpoint has issues
-+  '/api/search/more'         // If pagination endpoint has issues
-+]
-+
-+// Keep /api/search protected, rollback others
-+```
-+
-+#### Option 3: Partner-Specific Rollback
-+```typescript
-+// Temporarily allow specific partners without authentication
-+const ROLLBACK_PARTNERS = ['acme-corp', 'beta-corp']
-+
-+// Allow these partners to access APIs without HMAC temporarily
-+```
-+
-+### Rollback Validation
-+After any rollback:
-+- [ ] **API Functionality**: All search endpoints working correctly
-+- [ ] **Performance**: Response times back to baseline
-+- [ ] **Error Rates**: Error rates back to normal levels
-+- [ ] **Partner Access**: Partners can access APIs as before
-+- [ ] **JWT Functionality**: Contact form JWT authentication still working
-+
-+## Post-Deployment Tasks
-+
-+### Day 1 (Deployment Day)
-+- [ ] **Monitor Metrics**: Watch authentication success rates for first 4 hours
-+- [ ] **Partner Communication**: Notify partners of successful deployment
-+- [ ] **Documentation Update**: Update API documentation with authentication requirements
-+- [ ] **Support Team Brief**: Brief support team on new authentication system
-+
-+### Week 1 (Monitoring Period)
-+- [ ] **Daily Metrics Review**: Review authentication patterns and performance
-+- [ ] **Partner Feedback**: Collect feedback from early adopter partners
-+- [ ] **Performance Analysis**: Analyze impact on API response times
-+- [ ] **Error Pattern Analysis**: Identify common authentication failure patterns
-+
-+### Week 2-4 (Optimization Period)
-+- [ ] **Performance Tuning**: Optimize authentication overhead if needed
-+- [ ] **Partner Onboarding**: Complete onboarding of all partners
-+- [ ] **Documentation Refinement**: Update documentation based on partner feedback
-+- [ ] **Monitoring Refinement**: Adjust alert thresholds based on observed patterns
-+
-+## Success Criteria
-+
-+### Technical Success Metrics
-+- [ ] **Authentication Success Rate**: >95% for both JWT and HMAC
-+- [ ] **API Performance**: <5ms authentication overhead
-+- [ ] **Error Rate**: No increase in 5xx errors
-+- [ ] **Partner Adoption**: >80% of partners successfully integrated within 2 weeks
-+
-+### Business Success Metrics
-+- [ ] **Search API Abuse**: Reduction in unauthorized scraping attempts
-+- [ ] **Partner Satisfaction**: Positive feedback on integration experience
-+- [ ] **System Stability**: No authentication-related outages
-+- [ ] **Security Posture**: Improved protection against API abuse
-+
-+## Troubleshooting Guide
-+
-+### Common Issues and Solutions
-+
-+#### High Authentication Failure Rate
-+```bash
-+# Check partner secret configuration
-+aws amplify get-app --app-id YOUR_APP_ID
-+
-+# Verify environment variables are set correctly
-+# Check CloudWatch logs for authentication errors
-+```
-+
-+#### Performance Degradation
-+```bash
-+# Monitor authentication overhead
-+# Check for memory leaks in HMAC verification
-+# Verify partner secret caching is working
-+```
-+
-+#### Partner Integration Issues
-+```bash
-+# Provide partner with test signature generation
-+# Verify partner's timestamp is within window
-+# Check partner secret configuration
-+```
-+
-+### Emergency Contacts
-+- **Technical Lead**: [email]
-+- **DevOps Team**: [email]
-+- **Partner Support**: [email]
-+- **Security Team**: [email]
-+
-+## Documentation Updates
-+
-+### Files to Update Post-Deployment
-+- [ ] **API Documentation**: Add authentication requirements
-+- [ ] **Partner Onboarding Guide**: Update with HMAC instructions
-+- [ ] **Troubleshooting Guide**: Add HMAC-specific troubleshooting
-+- [ ] **Security Documentation**: Update with new authentication methods
-+
-+---
-+
-+**Deployment Owner**: SecOps-Claude  
-+**Approval Required**: CTO Sign-off  
-+**Rollback Authority**: Technical Lead + DevOps  
-+**Go-Live Date**: TBD after CTO approval
-diff --git a/docs/UPDATES/AUTH-SPRINT/PR2/executive-summary.md b/docs/UPDATES/AUTH-SPRINT/PR2/executive-summary.md
-new file mode 100644
-index 0000000..1dad19f
---- /dev/null
-+++ b/docs/UPDATES/AUTH-SPRINT/PR2/executive-summary.md
-@@ -0,0 +1,191 @@
-+# PR 2 Executive Summary - HMAC Authentication & Search Routes Protection
-+
-+**Date:** January 12, 2025  
-+**Sprint:** Auth Layer Implementation  
-+**Prepared for:** CTO Review  
-+**Prepared by:** SecOps-Claude  
-+
-+## Executive Overview
-+
-+PR 2 represents the second phase of our comprehensive authentication security implementation, building upon the successful JWT authentication system delivered in PR 1. This phase focuses on protecting our search APIs from abuse and scraping while enabling legitimate partner integrations through HMAC authentication.
-+
-+## Business Impact
-+
-+### Security Benefits
-+- **API Abuse Prevention**: Protects search endpoints from systematic scraping and competitive data harvesting
-+- **Partner Integration**: Enables secure, programmatic access for legitimate business partners
-+- **Cost Protection**: Prevents unauthorized bandwidth consumption and server resource abuse
-+- **Competitive Advantage**: Protects proprietary product and pricing data from competitors
-+
-+### Revenue Protection
-+- **Search API Value**: Protects valuable search functionality that drives user engagement
-+- **Partner Revenue**: Enables monetization through secure partner API access
-+- **Infrastructure Costs**: Reduces costs from unauthorized high-volume usage
-+- **Brand Protection**: Maintains control over how our data is accessed and used
-+
-+## Technical Solution
-+
-+### HMAC Authentication System
-+- **Algorithm**: HMAC-SHA256 with timestamp-based replay protection
-+- **Partner Management**: Individual secrets for each partner with rotation capability
-+- **Dual Authentication**: Supports both JWT (browser users) and HMAC (API partners)
-+- **Backward Compatibility**: Maintains existing JWT functionality from PR 1
-+
-+### Protected Endpoints
-+1. **`/api/search`** - Main search API with filtering and pagination
-+2. **`/api/search/suggestions`** - Search suggestions and autocomplete
-+3. **`/api/search/more`** - Additional search results pagination
-+
-+### Security Features
-+- **Replay Attack Protection**: 5-minute timestamp window prevents request replay
-+- **Signature Verification**: Cryptographic proof of request authenticity
-+- **Partner Validation**: Individual secrets for granular access control
-+- **Rate Limiting**: Existing rate limiting maintained post-authentication
-+
-+## Implementation Approach
-+
-+### Development Strategy
-+Following the proven approach from PR 1:
-+- **Incremental Development**: 4 phases over 8-12 hours
-+- **Comprehensive Testing**: 50+ tests covering all scenarios
-+- **Security-First**: Attack scenario testing and edge case validation
-+- **Performance Focus**: <5ms authentication overhead target
-+
-+### Risk Mitigation
-+- **Gradual Rollout**: Staging deployment followed by production
-+- **Feature Flags**: Ability to disable authentication if issues arise
-+- **Rollback Procedures**: Multiple rollback options (immediate, gradual, endpoint-specific)
-+- **Monitoring**: Real-time authentication success/failure rate tracking
-+
-+## Deliverables
-+
-+### Core Implementation
-+1. **HMAC Helper Library** (`src/lib/security/hmac.ts`)
-+   - Signature generation and verification functions
-+   - Request parsing and validation utilities
-+   - Partner secret management and validation
-+
-+2. **Search Endpoint Protection**
-+   - Authentication middleware for all three search endpoints
-+   - Dual authentication support (JWT + HMAC)
-+   - Consistent error handling and logging
-+
-+3. **Comprehensive Testing**
-+   - 50+ tests covering unit, integration, security, and performance scenarios
-+   - Attack scenario validation (replay attacks, signature tampering)
-+   - JWT compatibility testing to ensure no regression
-+
-+### Documentation & Support
-+1. **Partner Integration Guide**
-+   - Complete HMAC implementation examples in JavaScript, Python, PHP
-+   - Test vectors for signature validation
-+   - Troubleshooting guide and best practices
-+
-+2. **Technical Specifications**
-+   - Detailed HMAC algorithm specification
-+   - TypeScript interfaces and error handling
-+   - Security considerations and threat mitigation
-+
-+3. **Deployment Guide**
-+   - Environment configuration and secret management
-+   - Monitoring and alerting setup
-+   - Rollback procedures and emergency contacts
-+
-+## Success Metrics
-+
-+### Technical Targets
-+- **Test Coverage**: >95% for new HMAC functionality
-+- **Performance**: <5ms authentication overhead
-+- **Reliability**: >95% authentication success rate
-+- **Compatibility**: 100% JWT functionality preservation
-+
-+### Security Targets
-+- **Attack Prevention**: 0 successful bypass attempts in testing
-+- **Partner Security**: Individual secret management with rotation capability
-+- **Monitoring**: Real-time detection of authentication anomalies
-+- **Compliance**: Meets enterprise security standards for API protection
-+
-+## Timeline & Resources
-+
-+### Development Timeline
-+- **Phase 1**: HMAC Helper Implementation (4 hours)
-+- **Phase 2**: Search Endpoints Protection (3 hours)
-+- **Phase 3**: Testing & Validation (3 hours)
-+- **Phase 4**: Documentation & Deployment (2 hours)
-+- **Total**: 8-12 hours over 2-3 days
-+
-+### Resource Requirements
-+- **Development**: 1 senior developer (SecOps-Claude)
-+- **Testing**: Automated test suite + manual validation
-+- **Infrastructure**: AWS Amplify Environment Secrets for partner secret storage
-+- **Monitoring**: CloudWatch/Grafana dashboard setup
-+
-+## Risk Assessment
-+
-+### Low Risk Factors
-+- **Proven Foundation**: Building on successful JWT implementation from PR 1
-+- **Incremental Approach**: Small, testable changes with rollback capability
-+- **Comprehensive Testing**: Extensive test coverage including attack scenarios
-+- **Monitoring**: Real-time visibility into authentication performance
-+
-+### Mitigation Strategies
-+- **Rollback Plans**: Multiple rollback options (immediate, gradual, endpoint-specific)
-+- **Feature Flags**: Environment variables to disable authentication if needed
-+- **Partner Support**: Dedicated integration support during rollout
-+- **Performance Monitoring**: Real-time tracking of authentication overhead
-+
-+## Competitive Analysis
-+
-+### Industry Standards
-+- **HMAC Authentication**: Industry-standard approach used by AWS, GitHub, Stripe
-+- **Timestamp Protection**: Standard practice for preventing replay attacks
-+- **Partner Management**: Individual secrets align with enterprise security practices
-+- **Dual Authentication**: Flexible approach supporting both browser and API access
-+
-+### Differentiation
-+- **Comprehensive Protection**: Protects all search functionality, not just premium features
-+- **Partner-Friendly**: Easy integration with clear documentation and examples
-+- **Performance-Optimized**: Minimal overhead while maintaining strong security
-+- **Backward Compatible**: Maintains existing user experience while adding protection
-+
-+## Recommendation
-+
-+### Approval Requested
-+I recommend **immediate approval** for PR 2 implementation based on:
-+
-+1. **Strong Business Case**: Protects valuable search functionality and enables partner revenue
-+2. **Proven Technical Approach**: Builds on successful PR 1 foundation with comprehensive testing
-+3. **Low Risk Profile**: Incremental implementation with multiple rollback options
-+4. **Industry Alignment**: Uses standard HMAC authentication practices
-+
-+### Next Steps Upon Approval
-+1. **Environment Setup**: Configure partner secrets in development environment
-+2. **Implementation Start**: Begin Phase 1 (HMAC Helper) development
-+3. **Partner Notification**: Inform partners of upcoming authentication requirements
-+4. **Monitoring Setup**: Prepare authentication metrics dashboard
-+
-+## Appendix
-+
-+### Related Documents
-+- **Implementation Plan**: `docs/UPDATES/AUTH-SPRINT/PR2/implementation-plan.md`
-+- **Technical Specifications**: `docs/UPDATES/AUTH-SPRINT/PR2/technical-specifications.md`
-+- **Partner Integration Guide**: `docs/UPDATES/AUTH-SPRINT/PR2/partner-integration-guide.md`
-+- **Testing Strategy**: `docs/UPDATES/AUTH-SPRINT/PR2/testing-strategy.md`
-+- **Deployment Guide**: `docs/UPDATES/AUTH-SPRINT/PR2/deployment-guide.md`
-+
-+### Success Story Reference
-+PR 1 (JWT Authentication) delivered:
-+- ✅ **100% Test Success**: 15/15 tests passing
-+- ✅ **Security Validation**: Complete protection against bot attacks
-+- ✅ **Zero Downtime**: Seamless deployment with no user impact
-+- ✅ **Performance**: Minimal overhead with strong security
-+
-+PR 2 follows the same proven methodology for consistent success.
-+
-+---
-+
-+**Prepared by:** SecOps-Claude  
-+**Review Status:** Pending CTO Approval  
-+**Implementation Ready:** Yes  
-+**Risk Level:** Low  
-+**Business Impact:** High
-diff --git a/docs/UPDATES/AUTH-SPRINT/PR2/feature-flags-guide.md b/docs/UPDATES/AUTH-SPRINT/PR2/feature-flags-guide.md
-new file mode 100644
-index 0000000..ce7319e
---- /dev/null
-+++ b/docs/UPDATES/AUTH-SPRINT/PR2/feature-flags-guide.md
-@@ -0,0 +1,297 @@
-+# Feature Flags Guide - HMAC Authentication
-+
-+**Date:** January 12, 2025  
-+**Component:** PR 2 - HMAC Authentication & Search Routes  
-+**Purpose:** Operational control and rollback capability  
-+
-+## Overview
-+
-+The HMAC authentication system includes comprehensive feature flags for safe deployment, gradual rollout, and emergency rollback scenarios. This guide provides complete documentation for operators and developers.
-+
-+## Feature Flags Reference
-+
-+### Primary Authentication Control
-+
-+#### `ENABLE_SEARCH_AUTH`
-+- **Type**: Boolean (`true` | `false`)
-+- **Default**: `false` (authentication disabled)
-+- **Scope**: Master switch for ALL search endpoint authentication
-+- **Impact**: When `false`, bypasses both JWT and HMAC authentication
-+
-+```bash
-+# Enable authentication (production)
-+ENABLE_SEARCH_AUTH=true
-+
-+# Disable authentication (emergency rollback)
-+ENABLE_SEARCH_AUTH=false
-+```
-+
-+#### `ENABLE_HMAC_AUTH`
-+- **Type**: Boolean (`true` | `false`)
-+- **Default**: `false` (HMAC disabled)
-+- **Scope**: Controls HMAC authentication only (JWT unaffected)
-+- **Impact**: When `false`, only JWT authentication is available
-+
-+```bash
-+# Enable HMAC for API partners
-+ENABLE_HMAC_AUTH=true
-+
-+# Disable HMAC (keep JWT only)
-+ENABLE_HMAC_AUTH=false
-+```
-+
-+### Configuration Parameters
-+
-+#### `HMAC_TIMESTAMP_WINDOW`
-+- **Type**: Integer (seconds)
-+- **Default**: `300` (5 minutes)
-+- **Purpose**: Maximum allowed time difference between request timestamp and server time
-+- **Range**: `60` - `1800` (1 minute to 30 minutes)
-+
-+```bash
-+# Standard window (recommended)
-+HMAC_TIMESTAMP_WINDOW=300
-+
-+# Tighter security (shorter window)
-+HMAC_TIMESTAMP_WINDOW=120
-+
-+# Looser for clock skew tolerance
-+HMAC_TIMESTAMP_WINDOW=600
-+```
-+
-+#### `HMAC_ALGORITHM`
-+- **Type**: String
-+- **Default**: `sha256`
-+- **Purpose**: HMAC algorithm specification
-+- **Values**: `sha256` (only supported value)
-+
-+```bash
-+HMAC_ALGORITHM=sha256
-+```
-+
-+## Deployment Scenarios
-+
-+### Scenario 1: Initial Deployment (Gradual Rollout)
-+
-+```bash
-+# Step 1: Deploy with authentication disabled
-+ENABLE_SEARCH_AUTH=false
-+ENABLE_HMAC_AUTH=false
-+
-+# Step 2: Enable JWT only (existing users)
-+ENABLE_SEARCH_AUTH=true
-+ENABLE_HMAC_AUTH=false
-+
-+# Step 3: Enable HMAC for partners
-+ENABLE_SEARCH_AUTH=true
-+ENABLE_HMAC_AUTH=true
-+```
-+
-+### Scenario 2: Emergency Rollback
-+
-+```bash
-+# Complete rollback (no authentication)
-+ENABLE_SEARCH_AUTH=false
-+ENABLE_HMAC_AUTH=false
-+
-+# Partial rollback (JWT only)
-+ENABLE_SEARCH_AUTH=true
-+ENABLE_HMAC_AUTH=false
-+```
-+
-+### Scenario 3: Partner-Specific Issues
-+
-+```bash
-+# Disable HMAC while investigating partner issues
-+ENABLE_SEARCH_AUTH=true
-+ENABLE_HMAC_AUTH=false
-+# Partners temporarily lose access, browsers continue working
-+```
-+
-+### Scenario 4: Maintenance Mode
-+
-+```bash
-+# Disable all authentication during maintenance
-+ENABLE_SEARCH_AUTH=false
-+# Allows internal monitoring/testing without auth
-+```
-+
-+## Environment-Specific Configuration
-+
-+### Development Environment
-+```bash
-+# .env.local
-+ENABLE_SEARCH_AUTH=true
-+ENABLE_HMAC_AUTH=true
-+HMAC_TIMESTAMP_WINDOW=300
-+PARTNER_SECRET_DEFAULT=dev-default-secret-minimum-32-characters
-+PARTNER_SECRET_TEST_PARTNER=test-secret-minimum-32-characters-long
-+```
-+
-+### Staging Environment
-+```bash
-+# AWS Amplify Environment Variables
-+ENABLE_SEARCH_AUTH=true
-+ENABLE_HMAC_AUTH=true
-+HMAC_TIMESTAMP_WINDOW=300
-+PARTNER_SECRET_DEFAULT=staging-default-secret-minimum-32-chars
-+PARTNER_SECRET_TEST_PARTNER=staging-test-secret-minimum-32-chars
-+```
-+
-+### Production Environment
-+```bash
-+# AWS Amplify Environment Secrets
-+ENABLE_SEARCH_AUTH=true
-+ENABLE_HMAC_AUTH=true
-+HMAC_TIMESTAMP_WINDOW=300
-+PARTNER_SECRET_DEFAULT=prod-default-secret-minimum-32-characters
-+PARTNER_SECRET_ACME_CORP=prod-acme-secret-minimum-32-characters
-+```
-+
-+## How-To Guide
-+
-+### How to Enable Authentication
-+
-+1. **Set Environment Variables**
-+   ```bash
-+   ENABLE_SEARCH_AUTH=true
-+   ENABLE_HMAC_AUTH=true
-+   ```
-+
-+2. **Deploy Application**
-+   ```bash
-+   git push origin main
-+   # AWS Amplify auto-deploys
-+   ```
-+
-+3. **Verify Deployment**
-+   ```bash
-+   curl -X GET "https://api.cashback-deals.com/api/search?q=test"
-+   # Should return 401 Unauthorized
-+   ```
-+
-+### How to Disable Authentication (Emergency)
-+
-+1. **Update Environment Variables**
-+   ```bash
-+   # In AWS Amplify Console or CLI
-+   ENABLE_SEARCH_AUTH=false
-+   ```
-+
-+2. **Trigger Redeploy**
-+   ```bash
-+   # Option 1: Push empty commit
-+   git commit --allow-empty -m "Trigger redeploy"
-+   git push origin main
-+   
-+   # Option 2: Use AWS Amplify Console
-+   # Go to App > Actions > Redeploy this version
-+   ```
-+
-+3. **Verify Rollback**
-+   ```bash
-+   curl -X GET "https://api.cashback-deals.com/api/search?q=test"
-+   # Should return 200 OK with search results
-+   ```
-+
-+### How to Test Feature Flags
-+
-+```bash
-+# Test authentication disabled
-+ENABLE_SEARCH_AUTH=false npm test -- src/__tests__/api/search-auth.test.ts
-+
-+# Test HMAC disabled
-+ENABLE_SEARCH_AUTH=true ENABLE_HMAC_AUTH=false npm test
-+
-+# Test full authentication
-+ENABLE_SEARCH_AUTH=true ENABLE_HMAC_AUTH=true npm test
-+```
-+
-+## Monitoring and Alerts
-+
-+### Key Metrics to Monitor
-+
-+```bash
-+# Authentication success rate
-+aws logs filter-log-events \
-+  --log-group-name "/aws/lambda/cashback-deals" \
-+  --filter-pattern "HMAC_AUTH_SUCCESS"
-+
-+# Authentication failure rate
-+aws logs filter-log-events \
-+  --log-group-name "/aws/lambda/cashback-deals" \
-+  --filter-pattern "HMAC_AUTH_FAILURE"
-+```
-+
-+### Recommended Alerts
-+
-+```yaml
-+# CloudWatch Alarms
-+AuthFailureRateHigh:
-+  MetricName: AuthFailureRate
-+  Threshold: 25  # 25% failure rate
-+  ComparisonOperator: GreaterThanThreshold
-+  
-+AuthLatencyHigh:
-+  MetricName: AuthLatency
-+  Threshold: 100  # 100ms
-+  ComparisonOperator: GreaterThanThreshold
-+```
-+
-+## Troubleshooting
-+
-+### Common Issues
-+
-+#### Authentication Not Working After Enabling
-+```bash
-+# Check environment variables are set
-+echo $ENABLE_SEARCH_AUTH
-+echo $ENABLE_HMAC_AUTH
-+
-+# Check application logs
-+aws logs tail /aws/lambda/cashback-deals --follow
-+```
-+
-+#### Partners Can't Authenticate
-+```bash
-+# Temporarily disable HMAC
-+ENABLE_HMAC_AUTH=false
-+
-+# Check partner secrets are configured
-+env | grep PARTNER_SECRET_
-+```
-+
-+#### High Authentication Latency
-+```bash
-+# Check timestamp window
-+echo $HMAC_TIMESTAMP_WINDOW
-+
-+# Reduce window if too large
-+HMAC_TIMESTAMP_WINDOW=120
-+```
-+
-+## Security Considerations
-+
-+### Feature Flag Security
-+- **Never commit feature flags to code** - Use environment variables only
-+- **Audit flag changes** - Log all environment variable modifications
-+- **Principle of least privilege** - Only ops team should modify production flags
-+
-+### Rollback Security
-+- **Test rollback procedures** - Regularly test emergency rollback
-+- **Monitor during rollback** - Watch for abuse during authentication-disabled periods
-+- **Time-bound rollbacks** - Re-enable authentication as soon as issues are resolved
-+
-+## Best Practices
-+
-+1. **Gradual Rollout**: Always enable JWT before HMAC
-+2. **Monitor Closely**: Watch metrics for 24 hours after flag changes
-+3. **Document Changes**: Log all flag modifications with reasons
-+4. **Test Thoroughly**: Verify flag behavior in staging before production
-+5. **Communicate**: Notify team and partners of planned flag changes
-+
-+---
-+
-+**Maintained by:** DevOps Team  
-+**Last Updated:** January 12, 2025  
-+**Next Review:** February 12, 2025
-diff --git a/docs/UPDATES/AUTH-SPRINT/PR2/implementation-plan.md b/docs/UPDATES/AUTH-SPRINT/PR2/implementation-plan.md
-new file mode 100644
-index 0000000..cd40856
---- /dev/null
-+++ b/docs/UPDATES/AUTH-SPRINT/PR2/implementation-plan.md
-@@ -0,0 +1,280 @@
-+# PR 2 Implementation Plan - HMAC Helper + Search Routes Authentication
-+
-+**Date:** January 12, 2025  
-+**Sprint:** Auth Layer Implementation  
-+**PR Scope:** HMAC authentication helper + protect search endpoints  
-+**Estimated Effort:** 8-12 hours development + testing  
-+
-+## Executive Summary
-+
-+This implementation plan details PR 2 of the Auth Sprint, focusing on creating an HMAC authentication system and protecting search endpoints with dual authentication support (JWT or HMAC). Building on the successful JWT implementation from PR 1, this PR will secure search APIs against scraping and abuse while maintaining backward compatibility.
-+
-+## 1. Technical Analysis & Architecture
-+
-+### Current State Assessment
-+
-+#### Search Endpoints Identified
-+- **`/api/search/route.ts`** - Main search API with filtering and pagination
-+- **`/api/search/suggestions/route.ts`** - Search suggestions and autocomplete
-+- **`/api/search/more/route.ts`** - Additional search results pagination (missing from original scope)
-+
-+#### Current Security Status
-+- ✅ **Rate Limiting**: All endpoints have rate limiting implemented
-+- ✅ **Input Validation**: Zod schemas for parameter validation
-+- ❌ **Authentication**: No authentication required (public access)
-+- ❌ **Abuse Protection**: Vulnerable to systematic scraping
-+
-+#### JWT Infrastructure (From PR 1)
-+- **JWT Helper**: `src/lib/security/jwt.ts` with dual transport (cookies + headers)
-+- **Verification Functions**: `verifyRequestJWT()`, `createUnauthorizedResponse()`
-+- **Cookie Management**: Secure HttpOnly cookies with proper expiration
-+- **Test Coverage**: Comprehensive test suite with 15/15 tests passing
-+
-+### HMAC Authentication Design
-+
-+#### Why HMAC for Search APIs?
-+1. **Partner Integration**: Allows API partners to authenticate without browser sessions
-+2. **Stateless**: No server-side session storage required
-+3. **Timestamp Protection**: Prevents replay attacks with time-based validation
-+4. **Signature Verification**: Cryptographic proof of request authenticity
-+
-+#### HMAC vs JWT Decision Matrix
-+| Use Case | Authentication Method | Rationale |
-+|----------|----------------------|-----------|
-+| **Browser Users** | JWT (from PR 1) | Session-based, CAPTCHA-protected |
-+| **API Partners** | HMAC | Stateless, programmatic access |
-+| **Internal Services** | HMAC | Service-to-service authentication |
-+| **Mobile Apps** | JWT or HMAC | Flexible based on implementation |
-+
-+### Architecture Overview
-+
-+```mermaid
-+graph TB
-+    Client[Client Request] --> Auth{Authentication Check}
-+    Auth -->|JWT Present| JWT[Verify JWT Token]
-+    Auth -->|HMAC Present| HMAC[Verify HMAC Signature]
-+    Auth -->|None| Reject[401 Unauthorized]
-+    
-+    JWT -->|Valid| Process[Process Request]
-+    JWT -->|Invalid| Reject
-+    
-+    HMAC -->|Valid| Process
-+    HMAC -->|Invalid| Reject
-+    
-+    Process --> Response[Return Results]
-+    
-+    subgraph "HMAC Verification"
-+        HMAC --> Timestamp[Check Timestamp]
-+        Timestamp --> Signature[Verify Signature]
-+        Signature --> Partner[Validate Partner]
-+    end
-+```
-+
-+## 2. Implementation Specifications
-+
-+### HMAC Helper (`src/lib/security/hmac.ts`)
-+
-+#### Core Functions Required
-+```typescript
-+// Generate HMAC signature for outgoing requests
-+export function generateHMACSignature(
-+  method: string,
-+  path: string,
-+  timestamp: number,
-+  body?: string,
-+  secret?: string
-+): string
-+
-+// Verify incoming HMAC signature
-+export function verifyHMACSignature(
-+  signature: string,
-+  method: string,
-+  path: string,
-+  timestamp: number,
-+  body?: string,
-+  secret?: string
-+): boolean
-+
-+// Extract HMAC data from request
-+export function extractHMACFromRequest(
-+  request: NextRequest
-+): HMACData | null
-+
-+// Verify request with HMAC authentication
-+export function verifyRequestHMAC(
-+  request: NextRequest
-+): Promise<HMACPayload | null>
-+
-+// Create HMAC authentication headers
-+export function createHMACHeaders(
-+  method: string,
-+  path: string,
-+  body?: string
-+): Record<string, string>
-+```
-+
-+#### HMAC Request Format
-+```
-+Headers:
-+  X-Signature: sha256=<signature>
-+  X-Timestamp: <unix_timestamp>
-+  X-Partner-ID: <partner_identifier>
-+
-+Signature Calculation:
-+  message = METHOD + '\n' + PATH + '\n' + TIMESTAMP + '\n' + BODY_HASH
-+  signature = HMAC-SHA256(message, PARTNER_SECRET)
-+```
-+
-+#### Security Parameters
-+- **Algorithm**: HMAC-SHA256
-+- **Timestamp Window**: 300 seconds (5 minutes)
-+- **Partner Secrets**: Environment-based configuration
-+- **Replay Protection**: Timestamp validation + optional nonce tracking
-+
-+### Search Endpoints Protection
-+
-+#### Authentication Middleware Pattern
-+```typescript
-+// Dual authentication check
-+async function authenticateRequest(request: NextRequest): Promise<AuthResult> {
-+  // Try JWT first (for browser users)
-+  const jwtPayload = await verifyRequestJWT(request)
-+  if (jwtPayload) {
-+    return { success: true, method: 'JWT', payload: jwtPayload }
-+  }
-+  
-+  // Try HMAC second (for API partners)
-+  const hmacPayload = await verifyRequestHMAC(request)
-+  if (hmacPayload) {
-+    return { success: true, method: 'HMAC', payload: hmacPayload }
-+  }
-+  
-+  return { success: false, error: 'No valid authentication found' }
-+}
-+```
-+
-+#### Implementation Strategy
-+1. **Minimal Changes**: Add authentication middleware before existing logic
-+2. **Backward Compatibility**: Maintain existing response formats
-+3. **Error Handling**: Consistent 401 responses for authentication failures
-+4. **Logging**: Track authentication method and partner usage
-+
-+## 3. Development Plan
-+
-+### Phase 1: HMAC Helper Implementation (4 hours)
-+- [ ] Create `src/lib/security/hmac.ts` with core functions
-+- [ ] Implement signature generation and verification
-+- [ ] Add request parsing and validation helpers
-+- [ ] Create TypeScript interfaces and types
-+- [ ] Add environment variable validation
-+
-+### Phase 2: Search Endpoints Protection (3 hours)
-+- [ ] Update `/api/search/route.ts` with dual authentication
-+- [ ] Update `/api/search/suggestions/route.ts` with dual authentication  
-+- [ ] Update `/api/search/more/route.ts` with dual authentication
-+- [ ] Implement consistent error handling across endpoints
-+- [ ] Add authentication logging and monitoring
-+
-+### Phase 3: Testing & Validation (3 hours)
-+- [ ] Create HMAC test suite (`src/__tests__/security/hmac.test.ts`)
-+- [ ] Test search endpoint authentication scenarios
-+- [ ] Verify JWT backward compatibility
-+- [ ] Test partner HMAC authentication flows
-+- [ ] Performance testing for authentication overhead
-+
-+### Phase 4: Documentation & Deployment (2 hours)
-+- [ ] Update API documentation with authentication requirements
-+- [ ] Create partner integration guide
-+- [ ] Document environment variable requirements
-+- [ ] Prepare deployment checklist and rollback procedures
-+
-+## 4. Testing Strategy
-+
-+### Test Coverage Requirements
-+- **HMAC Functions**: 100% coverage of signature generation/verification
-+- **Authentication Flows**: JWT, HMAC, and failure scenarios
-+- **Search Endpoints**: All three endpoints with both auth methods
-+- **Edge Cases**: Invalid signatures, expired timestamps, missing headers
-+- **Performance**: Authentication overhead measurement
-+
-+### Test Scenarios
-+1. **Valid JWT Authentication**: Browser user with valid JWT token
-+2. **Valid HMAC Authentication**: API partner with correct signature
-+3. **Invalid JWT**: Expired or malformed JWT tokens
-+4. **Invalid HMAC**: Wrong signature, expired timestamp, missing headers
-+5. **No Authentication**: Requests without any authentication
-+6. **Mixed Authentication**: Requests with both JWT and HMAC (JWT takes precedence)
-+
-+## 5. Security Considerations
-+
-+### Threat Mitigation
-+- **Replay Attacks**: Timestamp validation with 5-minute window
-+- **Signature Forgery**: HMAC-SHA256 with strong partner secrets
-+- **Partner Compromise**: Individual partner secret rotation capability
-+- **Timing Attacks**: Constant-time signature comparison
-+- **Information Disclosure**: Minimal error messages, no secret leakage
-+
-+### Environment Variables Required
-+```bash
-+# Partner HMAC secrets (production)
-+PARTNER_SECRET_DEFAULT=your-default-partner-secret-32-chars-min
-+PARTNER_SECRET_PARTNER1=partner1-specific-secret-32-chars-min
-+PARTNER_SECRET_PARTNER2=partner2-specific-secret-32-chars-min
-+
-+# Development/testing
-+HMAC_TIMESTAMP_WINDOW=300  # 5 minutes
-+HMAC_ALGORITHM=sha256      # HMAC algorithm
-+```
-+
-+## 6. Deployment Considerations
-+
-+### Environment Setup
-+- **Development**: Test partner secrets for local development
-+- **Staging**: Production-like secrets for integration testing
-+- **Production**: Secure partner secrets via AWS Amplify Environment Secrets
-+
-+### Rollback Strategy
-+- **Feature Flags**: Environment variable to disable HMAC authentication
-+- **Gradual Rollout**: Enable authentication per endpoint incrementally
-+- **Monitoring**: Track authentication success/failure rates
-+- **Fallback**: Ability to revert to public access if needed
-+
-+### Performance Impact
-+- **Expected Overhead**: 1-3ms per request for signature verification
-+- **Caching Strategy**: Partner secret caching to reduce environment lookups
-+- **Monitoring**: Track authentication performance metrics
-+
-+## 7. Success Criteria
-+
-+### Functional Requirements
-+- [ ] All search endpoints require JWT or HMAC authentication
-+- [ ] HMAC signature generation and verification working correctly
-+- [ ] JWT authentication from PR 1 continues to work unchanged
-+- [ ] Partner API integration possible with HMAC authentication
-+- [ ] Comprehensive test coverage (>95%) for new authentication code
-+
-+### Security Requirements
-+- [ ] No bypass methods for authentication requirements
-+- [ ] Proper error handling without information disclosure
-+- [ ] Replay attack protection through timestamp validation
-+- [ ] Partner secret security and rotation capability
-+
-+### Performance Requirements
-+- [ ] Authentication overhead <5ms per request
-+- [ ] No degradation in search response times
-+- [ ] Successful handling of concurrent authenticated requests
-+
-+## Next Steps
-+
-+1. **CTO Review**: Present this implementation plan for approval
-+2. **Environment Setup**: Configure partner secrets in development environment
-+3. **Development Start**: Begin Phase 1 implementation
-+4. **Iterative Testing**: Test each phase before proceeding to next
-+5. **Documentation**: Maintain detailed progress documentation
-+
-+---
-+
-+**Prepared by:** SecOps-Claude  
-+**Review Required:** CTO Approval  
-+**Implementation Timeline:** 2-3 days  
-+**Risk Level:** Low (building on proven JWT foundation)
-diff --git a/docs/UPDATES/AUTH-SPRINT/PR2/partner-integration-guide.md b/docs/UPDATES/AUTH-SPRINT/PR2/partner-integration-guide.md
-new file mode 100644
-index 0000000..4d4fb5e
---- /dev/null
-+++ b/docs/UPDATES/AUTH-SPRINT/PR2/partner-integration-guide.md
-@@ -0,0 +1,451 @@
-+# Partner Integration Guide - HMAC Authentication
-+
-+**Date:** January 12, 2025  
-+**API Version:** 1.0  
-+**Authentication Method:** HMAC-SHA256  
-+
-+## Overview
-+
-+This guide provides technical documentation for API partners to integrate with the Cashback Deals search APIs using HMAC authentication. HMAC (Hash-based Message Authentication Code) provides secure, stateless authentication for programmatic access to our search endpoints.
-+
-+## Authentication Method: HMAC-SHA256
-+
-+### Why HMAC?
-+- **Stateless**: No server-side session management required
-+- **Secure**: Cryptographic signature prevents tampering
-+- **Replay Protection**: Timestamp validation prevents replay attacks
-+- **Partner-Specific**: Individual secrets for each partner
-+
-+## Getting Started
-+
-+### 1. Sandbox Credentials (Immediate Testing)
-+Start testing immediately with these sandbox credentials:
-+
-+```bash
-+# Sandbox Environment
-+Base URL: https://staging-api.cashback-deals.com
-+Partner ID: sandbox-partner
-+Partner Secret: sandbox-secret-32-chars-minimum-length-test
-+Rate Limit: 100 requests per minute
-+Valid Until: 30 days from first use
-+```
-+
-+**⚠️ Sandbox Limitations:**
-+- Limited to staging environment only
-+- 30-day expiration from first use
-+- Shared credentials (not for production)
-+- Rate limited to 100 requests/minute
-+
-+### 2. Production Partner Registration
-+For production access, contact our API team to receive:
-+- **Partner ID**: Your unique identifier (e.g., `acme-corp`)
-+- **Partner Secret**: 32+ character secret key for signature generation
-+- **API Endpoints**: List of accessible endpoints
-+- **Rate Limits**: Your specific rate limiting configuration
-+
-+### 3. Required Headers
-+Every authenticated request must include:
-+
-+```http
-+X-Signature: sha256=<hex_signature>
-+X-Timestamp: <unix_timestamp>
-+X-Partner-ID: <your_partner_id>
-+Content-Type: application/json
-+```
-+
-+### 4. Optional Headers
-+For enhanced functionality:
-+
-+```http
-+X-Version: 1.0                    # API version (recommended)
-+X-Nonce: <unique_request_id>      # Additional replay protection
-+```
-+
-+### 3. Signature Generation
-+
-+#### Step 1: Create Message String
-+```
-+message = HTTP_METHOD + '\n' + 
-+          REQUEST_PATH + '\n' + 
-+          TIMESTAMP + '\n' + 
-+          SHA256(REQUEST_BODY)
-+```
-+
-+#### Step 2: Generate HMAC Signature
-+```
-+signature = HMAC-SHA256(message, partner_secret)
-+```
-+
-+#### Step 3: Format Headers
-+```http
-+X-Signature: sha256=<hex_signature>
-+X-Timestamp: <unix_timestamp>
-+X-Partner-ID: <your_partner_id>
-+```
-+
-+## Implementation Examples
-+
-+### JavaScript/Node.js
-+```javascript
-+const crypto = require('crypto');
-+
-+function generateHMACSignature(method, path, timestamp, body, secret) {
-+  // Create body hash (empty string for GET requests)
-+  const bodyHash = crypto.createHash('sha256').update(body || '').digest('hex');
-+  
-+  // Create message string
-+  const message = `${method}\n${path}\n${timestamp}\n${bodyHash}`;
-+  
-+  // Generate HMAC signature
-+  const signature = crypto
-+    .createHmac('sha256', secret)
-+    .update(message)
-+    .digest('hex');
-+    
-+  return signature;
-+}
-+
-+function createAuthHeaders(method, path, partnerId, body, secret) {
-+  const timestamp = Math.floor(Date.now() / 1000);
-+  const signature = generateHMACSignature(method, path, timestamp, body, secret);
-+  
-+  return {
-+    'X-Signature': `sha256=${signature}`,
-+    'X-Timestamp': timestamp.toString(),
-+    'X-Partner-ID': partnerId,
-+    'Content-Type': 'application/json'
-+  };
-+}
-+
-+// Example usage
-+const headers = createAuthHeaders(
-+  'GET',
-+  '/api/search',
-+  'acme-corp',
-+  '',
-+  'your-partner-secret'
-+);
-+
-+// Make authenticated request
-+fetch('https://api.cashback-deals.com/api/search?q=laptop', {
-+  method: 'GET',
-+  headers: headers
-+})
-+.then(response => response.json())
-+.then(data => console.log(data));
-+```
-+
-+### Python
-+```python
-+import hashlib
-+import hmac
-+import time
-+import requests
-+
-+def generate_hmac_signature(method, path, timestamp, body, secret):
-+    # Create body hash
-+    body_hash = hashlib.sha256((body or '').encode()).hexdigest()
-+    
-+    # Create message string
-+    message = f"{method}\n{path}\n{timestamp}\n{body_hash}"
-+    
-+    # Generate HMAC signature
-+    signature = hmac.new(
-+        secret.encode(),
-+        message.encode(),
-+        hashlib.sha256
-+    ).hexdigest()
-+    
-+    return signature
-+
-+def create_auth_headers(method, path, partner_id, body, secret):
-+    timestamp = int(time.time())
-+    signature = generate_hmac_signature(method, path, timestamp, body, secret)
-+    
-+    return {
-+        'X-Signature': f'sha256={signature}',
-+        'X-Timestamp': str(timestamp),
-+        'X-Partner-ID': partner_id,
-+        'Content-Type': 'application/json'
-+    }
-+
-+# Example usage
-+headers = create_auth_headers(
-+    'GET',
-+    '/api/search',
-+    'acme-corp',
-+    '',
-+    'your-partner-secret'
-+)
-+
-+response = requests.get(
-+    'https://api.cashback-deals.com/api/search?q=laptop',
-+    headers=headers
-+)
-+print(response.json())
-+```
-+
-+### PHP
-+```php
-+<?php
-+function generateHMACSignature($method, $path, $timestamp, $body, $secret) {
-+    // Create body hash
-+    $bodyHash = hash('sha256', $body ?: '');
-+    
-+    // Create message string
-+    $message = "$method\n$path\n$timestamp\n$bodyHash";
-+    
-+    // Generate HMAC signature
-+    $signature = hash_hmac('sha256', $message, $secret);
-+    
-+    return $signature;
-+}
-+
-+function createAuthHeaders($method, $path, $partnerId, $body, $secret) {
-+    $timestamp = time();
-+    $signature = generateHMACSignature($method, $path, $timestamp, $body, $secret);
-+    
-+    return [
-+        'X-Signature' => "sha256=$signature",
-+        'X-Timestamp' => (string)$timestamp,
-+        'X-Partner-ID' => $partnerId,
-+        'Content-Type' => 'application/json'
-+    ];
-+}
-+
-+// Example usage
-+$headers = createAuthHeaders(
-+    'GET',
-+    '/api/search',
-+    'acme-corp',
-+    '',
-+    'your-partner-secret'
-+);
-+
-+$context = stream_context_create([
-+    'http' => [
-+        'method' => 'GET',
-+        'header' => implode("\r\n", array_map(
-+            fn($k, $v) => "$k: $v",
-+            array_keys($headers),
-+            $headers
-+        ))
-+    ]
-+]);
-+
-+$response = file_get_contents(
-+    'https://api.cashback-deals.com/api/search?q=laptop',
-+    false,
-+    $context
-+);
-+echo $response;
-+?>
-+```
-+
-+## Available Endpoints
-+
-+### Search API
-+- **Endpoint**: `/api/search`
-+- **Method**: GET
-+- **Description**: Search products with filtering and sorting
-+- **Rate Limit**: 100 requests per minute
-+
-+#### Parameters
-+```
-+q          - Search query string
-+category   - Category filter
-+brand      - Brand filter  
-+sort       - Sort order (relevance, price_asc, price_desc, newest)
-+page       - Page number (default: 1)
-+limit      - Items per page (default: 20, max: 50)
-+```
-+
-+#### Example GET Request
-+```bash
-+curl -X GET "https://api.cashback-deals.com/api/search?q=laptop&brand=samsung&page=1&limit=20" \
-+  -H "X-Signature: sha256=abc123..." \
-+  -H "X-Timestamp: 1705123456" \
-+  -H "X-Partner-ID: acme-corp" \
-+  -H "X-Version: 1.0" \
-+  -H "Content-Type: application/json"
-+```
-+
-+#### Example POST Request with Body
-+```bash
-+# Generate signature for this exact body
-+BODY='{"query":"laptop","filters":{"brand":"samsung","priceRange":{"min":500,"max":1500}}}'
-+TIMESTAMP=$(date +%s)
-+SIGNATURE=$(echo -n "POST\n/api/search\n$TIMESTAMP\n$(echo -n "$BODY" | sha256sum | cut -d' ' -f1)" | \
-+  openssl dgst -sha256 -hmac "your-partner-secret" | cut -d' ' -f2)
-+
-+curl -X POST "https://api.cashback-deals.com/api/search" \
-+  -H "X-Signature: sha256=$SIGNATURE" \
-+  -H "X-Timestamp: $TIMESTAMP" \
-+  -H "X-Partner-ID: acme-corp" \
-+  -H "X-Version: 1.0" \
-+  -H "Content-Type: application/json" \
-+  -d "$BODY"
-+```
-+
-+### Search Suggestions API
-+- **Endpoint**: `/api/search/suggestions`
-+- **Method**: GET
-+- **Description**: Get search suggestions and autocomplete
-+- **Rate Limit**: 100 requests per minute
-+
-+#### Parameters
-+```
-+q     - Search query string (minimum 2 characters)
-+limit - Maximum suggestions (default: 10, max: 20)
-+```
-+
-+### Search More API
-+- **Endpoint**: `/api/search/more`
-+- **Method**: GET
-+- **Description**: Get additional search results for pagination
-+- **Rate Limit**: 100 requests per minute
-+
-+## Error Handling
-+
-+### Complete Error Catalog
-+
-+| Error Code | HTTP Status | Description | Resolution |
-+|------------|-------------|-------------|------------|
-+| `AUTH_MISSING` | 401 | No authentication headers provided | Add X-Signature, X-Timestamp, X-Partner-ID headers |
-+| `MISSING_SIGNATURE` | 401 | X-Signature header missing | Add X-Signature header with sha256= prefix |
-+| `MISSING_TIMESTAMP` | 401 | X-Timestamp header missing | Add X-Timestamp header with Unix timestamp |
-+| `MISSING_PARTNER_ID` | 401 | X-Partner-ID header missing | Add X-Partner-ID header with your partner ID |
-+| `INVALID_SIGNATURE` | 401 | HMAC signature verification failed | Check signature calculation and secret |
-+| `EXPIRED_TIMESTAMP` | 401 | Request timestamp outside 5-minute window | Check system clock and timestamp generation |
-+| `UNKNOWN_PARTNER` | 401 | Partner ID not recognized | Verify partner ID or contact support |
-+| `REPLAY_DETECTED` | 401 | Duplicate request detected | Use unique timestamps or add nonce |
-+| `BODY_HASH_MISMATCH` | 401 | Request body doesn't match signature | Ensure body used for signature matches sent body |
-+| `INVALID_FORMAT` | 400 | Malformed request headers | Check header format and values |
-+| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests | Implement exponential backoff |
-+
-+### Example Error Responses
-+
-+#### Authentication Missing
-+```json
-+{
-+  "error": "Unauthorized",
-+  "message": "Valid authentication required. Use JWT (browser) or HMAC (API) authentication.",
-+  "code": "AUTH_MISSING",
-+  "traceId": "hmac-get-a1b2-c3d4e5f6",
-+  "serverTime": "2025-01-12T10:30:00.000Z",
-+  "supportedMethods": ["JWT", "HMAC"],
-+  "documentation": "https://docs.cashback-deals.com/api/authentication"
-+}
-+```
-+
-+#### Invalid Signature
-+```json
-+{
-+  "error": "Unauthorized",
-+  "message": "Invalid HMAC signature",
-+  "code": "INVALID_SIGNATURE",
-+  "traceId": "hmac-get-a1b2-c3d4e5f6",
-+  "serverTime": "2025-01-12T10:30:00.000Z",
-+  "supportedMethods": ["JWT", "HMAC"],
-+  "documentation": "https://docs.cashback-deals.com/api/authentication"
-+}
-+```
-+
-+#### Timestamp Expired
-+```json
-+{
-+  "error": "Unauthorized",
-+  "message": "Request timestamp expired",
-+  "code": "EXPIRED_TIMESTAMP",
-+  "traceId": "hmac-get-a1b2-c3d4e5f6",
-+  "serverTime": "2025-01-12T10:30:00.000Z",
-+  "supportedMethods": ["JWT", "HMAC"],
-+  "documentation": "https://docs.cashback-deals.com/api/authentication"
-+}
-+```
-+
-+### Rate Limiting
-+```json
-+{
-+  "error": "Too many requests",
-+  "message": "Rate limit exceeded. Try again in 45 seconds."
-+}
-+```
-+
-+Response headers include:
-+- `X-RateLimit-Limit`: Maximum requests allowed
-+- `X-RateLimit-Remaining`: Requests remaining in window
-+- `X-RateLimit-Reset`: Unix timestamp when limit resets
-+- `Retry-After`: Seconds to wait before retrying
-+
-+## Security Best Practices
-+
-+### Secret Management
-+- **Never commit secrets to version control**
-+- **Use environment variables for secret storage**
-+- **Rotate secrets regularly (quarterly recommended)**
-+- **Use different secrets for development/staging/production**
-+
-+### Request Security
-+- **Always use HTTPS in production**
-+- **Validate timestamp is within 5-minute window**
-+- **Implement request timeout handling**
-+- **Log authentication failures for monitoring**
-+
-+### Error Handling
-+- **Don't expose sensitive information in error messages**
-+- **Implement exponential backoff for retries**
-+- **Monitor authentication success/failure rates**
-+- **Set up alerts for unusual authentication patterns**
-+
-+## Testing Your Integration
-+
-+### Test Environment
-+- **Base URL**: `https://staging-api.cashback-deals.com`
-+- **Test Partner ID**: `test-partner`
-+- **Test Secret**: `test-secret-minimum-32-characters-long`
-+
-+### Validation Checklist
-+- [ ] Signature generation produces expected results
-+- [ ] Timestamp is current Unix timestamp
-+- [ ] Headers are properly formatted
-+- [ ] HTTPS is used for all requests
-+- [ ] Error responses are handled gracefully
-+- [ ] Rate limiting is respected
-+
-+### Test Signature Generation
-+Use these test vectors to validate your implementation:
-+
-+```
-+Method: GET
-+Path: /api/search
-+Timestamp: 1705123456
-+Body: (empty)
-+Secret: test-secret-minimum-32-characters-long
-+
-+Expected Message:
-+GET
-+/api/search
-+1705123456
-+e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
-+
-+Expected Signature:
-+a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
-+```
-+
-+## Support
-+
-+### Technical Support
-+- **Email**: <EMAIL>
-+- **Documentation**: https://docs.cashback-deals.com/api
-+- **Status Page**: https://status.cashback-deals.com
-+
-+### Partner Onboarding
-+- **Business Contact**: <EMAIL>
-+- **Technical Contact**: <EMAIL>
-+
-+---
-+
-+**Last Updated:** January 12, 2025  
-+**API Version:** 1.0  
-+**Document Version:** 1.0
-diff --git a/docs/UPDATES/AUTH-SPRINT/PR2/scope-review.md b/docs/UPDATES/AUTH-SPRINT/PR2/scope-review.md
-new file mode 100644
-index 0000000..8ed14a9
---- /dev/null
-+++ b/docs/UPDATES/AUTH-SPRINT/PR2/scope-review.md
-@@ -0,0 +1,63 @@
-+Below is a focused technical review of the PR 2 documentation set (HMAC + search-route protection) with an eye toward shipping an MVP next week. Overall the docs are clear and thorough, but a handful of gaps and “TBDs” still need to be closed before code freeze.
-+
-+1 — Material that already looks solid
-+Area	Why it’s in good shape	Key source
-+Business & risk framing	Executive summary quantifies cost-savings and ties HMAC to partner monetisation.	executive-summary
-+High-level architecture	Dual-auth mermaid diagram & decision matrix lay out JWT→browser / HMAC→API separation cleanly.	implementation-plan
-+Core algorithm spec	Message construction, header format and constant-time comparison rules are explicit.	technical-specifications
-+Partner guide	End-to-end code samples in JS/Python/PHP + test vectors enable self-service onboarding.	partner-integration-gui…
-+Happy-path tests	Unit tests already cover signature generation/verification, header extraction etc.	testing-strategy
-+
-+2 — Issues to resolve for MVP launch
-+2.1 Environment & secret handling
-+Hard-coded / sample secrets are referenced throughout docs; environment-variable names vary (PARTNER_SECRET_DEFAULT, PARTNER_SECRET_ACME, etc.) and the storage mechanism is only “AWS Amplify Env Secrets” in a footnote technical-specifications.
-+▶ Action: add a short “Secret lifecycle” section: creation, rotation, revocation, and where exactly these live in each env (dev/stage/prod).
-+
-+The timestamp window (300 s) is configured via an env var in some docs but hard-coded elsewhere implementation-plan.
-+▶ Action: define one source of truth—HMAC_TIMESTAMP_WINDOW—and update all snippets.
-+
-+2.2 Replay & nonce protection
-+Spec mentions an optional X-Nonce but nothing in tests or helper-interfaces enforces uniqueness technical-specifications.
-+▶ Action: clarify whether nonce support is post-MVP or implement minimal in-memory map (no Redis) to block duplicate (timestamp, signature) pairs within the 5-min window.
-+
-+2.3 Error handling & observability
-+Consistent 401 bodies are defined, but there’s no mapping from HMACError → response payload in code samples technical-specifications.
-+▶ Action: add a short table in Technical Specs mapping each error enum to response & log entry.
-+
-+Security-event logging writes to console.log only technical-specifications.
-+▶ Action: specify which log sink (CloudWatch, Logtail, etc.) and include a trace-ID to allow correlation with search-service logs.
-+
-+
-+
-+Rollback procedures are clear, but feature-flag names (ENABLE_SEARCH_AUTH, ENABLE_HMAC_AUTH) aren’t referenced in code snippets.
-+▶ Action: add a code example showing guards that read those flags.
-+
-+2.5 Testing gaps
-+Test matrix lists performance (<5 ms overhead) but no k6/Artillery script is included to prove it.
-+▶ Action: add a simple perf test in __tests__/performance/auth-performance.test.ts that fires 100 concurrent signed GETs.
-+
-+No negative-path integration test for mismatched body-hash: e.g. sign with empty body, then send a JSON body.
-+▶ Action: extend hmac-security.test.ts accordingly.
-+
-+
-+
-+3 — Post-MVP nice-to-haves (can defer)
-+Item	Rationale
-+Redis/DB cache for nonce deduping	Improves replay protection at scale but not critical if window is short and traffic moderate.
-+OpenAPI / Postman collection	Speeds external integration; low effort but not on critical path.
-+Automated secret rotation playbook	Useful long-term security hygiene.
-+
-+4 — Suggested doc tweaks summary
-+Secret lifecycle & single env-var schema
-+
-+Nonce strategy or explicitly defer
-+
-+Error-code → response table & log sink details
-+
-+Fill feature-flag code sample
-+
-+Add perf + body-hash mismatch tests
-+
-+
-+
-+Addressing the above will leave us with a robust, auditable HMAC MVP that can ship confidently while keeping optional Redis and other enhancements on the backlog.
-\ No newline at end of file
-diff --git a/docs/UPDATES/AUTH-SPRINT/PR2/technical-specifications.md b/docs/UPDATES/AUTH-SPRINT/PR2/technical-specifications.md
-new file mode 100644
-index 0000000..faa3df5
---- /dev/null
-+++ b/docs/UPDATES/AUTH-SPRINT/PR2/technical-specifications.md
-@@ -0,0 +1,650 @@
-+# PR 2 Technical Specifications - HMAC Authentication System
-+
-+**Date:** January 12, 2025  
-+**Sprint:** Auth Layer Implementation  
-+**Component:** HMAC Helper + Search Routes Protection  
-+
-+## 1. HMAC Authentication Specification
-+
-+### Signature Algorithm: HMAC-SHA256
-+
-+#### Message Construction
-+```
-+message = HTTP_METHOD + '\n' + 
-+          REQUEST_PATH + '\n' + 
-+          TIMESTAMP + '\n' + 
-+          SHA256(REQUEST_BODY || '')
-+```
-+
-+#### Example Message
-+```
-+GET
-+/api/search
-+1705123456
-+e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
-+```
-+
-+#### Signature Generation
-+```typescript
-+const signature = crypto
-+  .createHmac('sha256', partnerSecret)
-+  .update(message)
-+  .digest('hex')
-+```
-+
-+### Request Headers Format
-+
-+#### Required Headers
-+```http
-+X-Signature: sha256=<hex_signature>
-+X-Timestamp: <unix_timestamp>
-+X-Partner-ID: <partner_identifier>
-+Content-Type: application/json
-+```
-+
-+#### Optional Headers
-+```http
-+X-Nonce: <unique_request_id>  # For additional replay protection
-+X-Version: 1.0                # API version for future compatibility
-+```
-+
-+### Partner Configuration
-+
-+#### Environment Variables Structure
-+```bash
-+# Default partner secret (fallback)
-+PARTNER_SECRET_DEFAULT=your-default-secret-minimum-32-characters
-+
-+# Named partner secrets
-+PARTNER_SECRET_ACME=acme-corp-secret-minimum-32-characters
-+PARTNER_SECRET_BETA=beta-partner-secret-minimum-32-characters
-+
-+# HMAC configuration
-+HMAC_TIMESTAMP_WINDOW=300     # 5 minutes (single source of truth)
-+HMAC_ALGORITHM=sha256
-+HMAC_ENCODING=hex
-+
-+# Feature flags for rollback capability
-+ENABLE_SEARCH_AUTH=true
-+ENABLE_HMAC_AUTH=true
-+```
-+
-+#### Secret Lifecycle Management
-+
-+##### Secret Creation
-+```bash
-+# Generate cryptographically secure secrets (32+ characters)
-+openssl rand -hex 32  # Generates 64-character hex string
-+```
-+
-+##### Secret Storage by Environment
-+- **Development**: `.env.local` file (not committed to git)
-+- **Staging**: AWS Amplify Environment Variables (staging app)
-+- **Production**: AWS Amplify Environment Secrets (production app)
-+
-+##### Secret Rotation Procedure
-+1. **Generate new secret**: `openssl rand -hex 32`
-+2. **Update environment**: Add new secret alongside old one
-+3. **Deploy with dual support**: Accept both old and new secrets
-+4. **Partner migration**: Notify partners to update to new secret
-+5. **Remove old secret**: After 30-day grace period
-+
-+##### Secret Revocation
-+1. **Immediate**: Remove from environment variables
-+2. **Deploy**: Redeploy application to pick up changes
-+3. **Monitor**: Watch for authentication failures from revoked partner
-+
-+## 2. TypeScript Interfaces
-+
-+### Core Types
-+```typescript
-+interface HMACData {
-+  signature: string
-+  timestamp: number
-+  partnerId: string
-+  nonce?: string
-+  version?: string
-+}
-+
-+interface HMACConfig {
-+  algorithm: 'sha256'
-+  timestampWindow: number
-+  encoding: 'hex'
-+  requireNonce: boolean
-+  enableReplayProtection: boolean
-+}
-+
-+interface HMACPayload {
-+  partnerId: string
-+  timestamp: number
-+  method: string
-+  path: string
-+  isValid: boolean
-+  nonce?: string
-+}
-+
-+interface AuthResult {
-+  success: boolean
-+  method: 'JWT' | 'HMAC' | null
-+  payload: JWTPayload | HMACPayload | null
-+  error?: string
-+}
-+
-+interface HMACConfig {
-+  algorithm: 'sha256'
-+  timestampWindow: number
-+  encoding: 'hex'
-+  requireNonce: boolean
-+}
-+```
-+
-+### Error Types
-+```typescript
-+type HMACError =
-+  | 'MISSING_SIGNATURE'
-+  | 'MISSING_TIMESTAMP'
-+  | 'MISSING_PARTNER_ID'
-+  | 'INVALID_SIGNATURE'
-+  | 'EXPIRED_TIMESTAMP'
-+  | 'UNKNOWN_PARTNER'
-+  | 'INVALID_FORMAT'
-+  | 'REPLAY_DETECTED'
-+  | 'BODY_HASH_MISMATCH'
-+
-+#### Error Code to Response Mapping
-+| Error Code | HTTP Status | Response Message | Log Level |
-+|------------|-------------|------------------|-----------|
-+| `MISSING_SIGNATURE` | 401 | "Missing X-Signature header" | WARN |
-+| `MISSING_TIMESTAMP` | 401 | "Missing X-Timestamp header" | WARN |
-+| `MISSING_PARTNER_ID` | 401 | "Missing X-Partner-ID header" | WARN |
-+| `INVALID_SIGNATURE` | 401 | "Invalid HMAC signature" | WARN |
-+| `EXPIRED_TIMESTAMP` | 401 | "Request timestamp expired" | WARN |
-+| `UNKNOWN_PARTNER` | 401 | "Unknown partner ID" | ERROR |
-+| `INVALID_FORMAT` | 400 | "Invalid request format" | WARN |
-+| `REPLAY_DETECTED` | 401 | "Duplicate request detected" | ERROR |
-+| `BODY_HASH_MISMATCH` | 401 | "Request body hash mismatch" | ERROR |
-+
-+interface HMACValidationResult {
-+  isValid: boolean
-+  error?: HMACError
-+  partnerId?: string
-+  timestamp?: number
-+}
-+```
-+
-+## 3. Implementation Details
-+
-+### HMAC Helper Functions
-+
-+#### Core Signature Functions
-+```typescript
-+// Generate signature for outgoing requests
-+export function generateHMACSignature(
-+  method: string,
-+  path: string,
-+  timestamp: number,
-+  body: string = '',
-+  secret: string
-+): string {
-+  const bodyHash = crypto.createHash('sha256').update(body).digest('hex')
-+  const message = `${method}\n${path}\n${timestamp}\n${bodyHash}`
-+  
-+  return crypto
-+    .createHmac('sha256', secret)
-+    .update(message)
-+    .digest('hex')
-+}
-+
-+// Verify incoming signature
-+export function verifyHMACSignature(
-+  signature: string,
-+  method: string,
-+  path: string,
-+  timestamp: number,
-+  body: string = '',
-+  secret: string
-+): boolean {
-+  const expectedSignature = generateHMACSignature(method, path, timestamp, body, secret)
-+  
-+  // Use constant-time comparison to prevent timing attacks
-+  return crypto.timingSafeEqual(
-+    Buffer.from(signature, 'hex'),
-+    Buffer.from(expectedSignature, 'hex')
-+  )
-+}
-+```
-+
-+#### Request Processing Functions
-+```typescript
-+// Extract HMAC data from request headers
-+export function extractHMACFromRequest(request: NextRequest): HMACData | null {
-+  const signature = request.headers.get('x-signature')
-+  const timestamp = request.headers.get('x-timestamp')
-+  const partnerId = request.headers.get('x-partner-id')
-+  
-+  if (!signature || !timestamp || !partnerId) {
-+    return null
-+  }
-+  
-+  // Remove 'sha256=' prefix if present
-+  const cleanSignature = signature.replace(/^sha256=/, '')
-+  
-+  return {
-+    signature: cleanSignature,
-+    timestamp: parseInt(timestamp, 10),
-+    partnerId,
-+    nonce: request.headers.get('x-nonce') || undefined,
-+    version: request.headers.get('x-version') || undefined
-+  }
-+}
-+
-+// Comprehensive request verification
-+export async function verifyRequestHMAC(request: NextRequest): Promise<HMACPayload | null> {
-+  const hmacData = extractHMACFromRequest(request)
-+  if (!hmacData) {
-+    return null
-+  }
-+  
-+  // Validate timestamp window
-+  const now = Math.floor(Date.now() / 1000)
-+  const timestampWindow = parseInt(process.env.HMAC_TIMESTAMP_WINDOW || '300', 10)
-+  
-+  if (Math.abs(now - hmacData.timestamp) > timestampWindow) {
-+    console.warn(`HMAC timestamp expired: ${hmacData.timestamp}, now: ${now}`)
-+    return null
-+  }
-+  
-+  // Get partner secret
-+  const partnerSecret = getPartnerSecret(hmacData.partnerId)
-+  if (!partnerSecret) {
-+    console.warn(`Unknown partner ID: ${hmacData.partnerId}`)
-+    return null
-+  }
-+  
-+  // Get request body for signature verification
-+  const body = await getRequestBody(request)
-+  const { pathname } = new URL(request.url)
-+  
-+  // Verify signature
-+  const isValid = verifyHMACSignature(
-+    hmacData.signature,
-+    request.method,
-+    pathname,
-+    hmacData.timestamp,
-+    body,
-+    partnerSecret
-+  )
-+  
-+  if (!isValid) {
-+    console.warn(`HMAC signature verification failed for partner: ${hmacData.partnerId}`)
-+    return null
-+  }
-+  
-+  return {
-+    partnerId: hmacData.partnerId,
-+    timestamp: hmacData.timestamp,
-+    method: request.method,
-+    path: pathname,
-+    isValid: true,
-+    nonce: hmacData.nonce
-+  }
-+}
-+```
-+
-+#### Utility Functions
-+```typescript
-+// Get partner secret from environment
-+function getPartnerSecret(partnerId: string): string | null {
-+  const secretKey = `PARTNER_SECRET_${partnerId.toUpperCase()}`
-+  const secret = process.env[secretKey] || process.env.PARTNER_SECRET_DEFAULT
-+  
-+  if (!secret) {
-+    console.error(`No secret found for partner: ${partnerId}`)
-+    return null
-+  }
-+  
-+  if (secret.length < 32) {
-+    console.error(`Partner secret too short for: ${partnerId}`)
-+    return null
-+  }
-+  
-+  return secret
-+}
-+
-+// Get request body (handling different content types)
-+async function getRequestBody(request: NextRequest): Promise<string> {
-+  try {
-+    if (request.method === 'GET' || request.method === 'HEAD') {
-+      return ''
-+    }
-+    
-+    const contentType = request.headers.get('content-type') || ''
-+    
-+    if (contentType.includes('application/json')) {
-+      const body = await request.json()
-+      return JSON.stringify(body)
-+    }
-+    
-+    if (contentType.includes('application/x-www-form-urlencoded')) {
-+      const formData = await request.formData()
-+      return new URLSearchParams(formData as any).toString()
-+    }
-+    
-+    // For other content types, get raw text
-+    return await request.text()
-+  } catch (error) {
-+    console.warn('Failed to parse request body for HMAC verification:', error)
-+    return ''
-+  }
-+}
-+
-+// Create HMAC headers for outgoing requests
-+export function createHMACHeaders(
-+  method: string,
-+  path: string,
-+  partnerId: string,
-+  body: string = '',
-+  secret?: string
-+): Record<string, string> {
-+  const timestamp = Math.floor(Date.now() / 1000)
-+  const partnerSecret = secret || getPartnerSecret(partnerId)
-+  
-+  if (!partnerSecret) {
-+    throw new Error(`No secret available for partner: ${partnerId}`)
-+  }
-+  
-+  const signature = generateHMACSignature(method, path, timestamp, body, partnerSecret)
-+  
-+  return {
-+    'X-Signature': `sha256=${signature}`,
-+    'X-Timestamp': timestamp.toString(),
-+    'X-Partner-ID': partnerId,
-+    'Content-Type': 'application/json'
-+  }
-+}
-+```
-+
-+## 4. Search Endpoint Integration
-+
-+### Feature Flag Implementation
-+```typescript
-+// Check if authentication is enabled via feature flags
-+function isAuthenticationEnabled(): boolean {
-+  return process.env.ENABLE_SEARCH_AUTH === 'true'
-+}
-+
-+function isHMACEnabled(): boolean {
-+  return process.env.ENABLE_HMAC_AUTH === 'true'
-+}
-+
-+// Feature flag guards for rollback capability
-+function shouldEnforceAuthentication(endpoint: string): boolean {
-+  if (!isAuthenticationEnabled()) {
-+    console.log(`Authentication disabled via feature flag for ${endpoint}`)
-+    return false
-+  }
-+  return true
-+}
-+```
-+
-+### Nonce Strategy (MVP: In-Memory Replay Protection)
-+```typescript
-+// Simple in-memory nonce tracking for MVP
-+// Note: For production scale, migrate to Redis
-+const recentRequests = new Map<string, number>()
-+
-+function isReplayRequest(signature: string, timestamp: number): boolean {
-+  const key = `${signature}-${timestamp}`
-+  const now = Date.now() / 1000
-+
-+  // Clean old entries (older than timestamp window)
-+  const timestampWindow = parseInt(process.env.HMAC_TIMESTAMP_WINDOW || '300', 10)
-+  for (const [existingKey, existingTime] of recentRequests.entries()) {
-+    if (now - existingTime > timestampWindow) {
-+      recentRequests.delete(existingKey)
-+    }
-+  }
-+
-+  // Check if this request was seen before
-+  if (recentRequests.has(key)) {
-+    return true // Replay detected
-+  }
-+
-+  // Store this request
-+  recentRequests.set(key, timestamp)
-+  return false
-+}
-+```
-+
-+### Authentication Middleware
-+```typescript
-+// Dual authentication check for search endpoints
-+async function authenticateSearchRequest(request: NextRequest): Promise<AuthResult> {
-+  // Try JWT first (browser users with CAPTCHA)
-+  const jwtPayload = await verifyRequestJWT(request)
-+  if (jwtPayload) {
-+    return {
-+      success: true,
-+      method: 'JWT',
-+      payload: jwtPayload
-+    }
-+  }
-+  
-+  // Try HMAC second (API partners)
-+  const hmacPayload = await verifyRequestHMAC(request)
-+  if (hmacPayload) {
-+    return {
-+      success: true,
-+      method: 'HMAC',
-+      payload: hmacPayload
-+    }
-+  }
-+  
-+  return {
-+    success: false,
-+    method: null,
-+    payload: null,
-+    error: 'No valid authentication found'
-+  }
-+}
-+
-+// Create consistent unauthorized response
-+function createSearchUnauthorizedResponse(): NextResponse {
-+  return NextResponse.json(
-+    {
-+      error: 'Unauthorized',
-+      message: 'Valid authentication required. Use JWT (browser) or HMAC (API) authentication.',
-+      code: 'AUTH_REQUIRED',
-+      supportedMethods: ['JWT', 'HMAC']
-+    },
-+    {
-+      status: 401,
-+      headers: {
-+        'Content-Type': 'application/json',
-+        'WWW-Authenticate': 'Bearer realm="Search API", HMAC realm="Partner API"'
-+      }
-+    }
-+  )
-+}
-+```
-+
-+### Endpoint Implementation Pattern
-+```typescript
-+// Example: /api/search/route.ts integration
-+export async function GET(request: NextRequest): Promise<NextResponse> {
-+  // Apply rate limiting first
-+  const rateLimitResponse = applyRateLimit(request, rateLimits.search)
-+  if (rateLimitResponse) {
-+    return rateLimitResponse
-+  }
-+  
-+  // Apply authentication
-+  const authResult = await authenticateSearchRequest(request)
-+  if (!authResult.success) {
-+    console.warn(`Search API unauthorized access attempt from ${getClientIP(request)}`)
-+    return createSearchUnauthorizedResponse()
-+  }
-+  
-+  // Log successful authentication
-+  console.log(`Search API access: ${authResult.method} authentication successful`)
-+  
-+  // Continue with existing search logic...
-+  const { searchParams } = new URL(request.url)
-+  // ... rest of existing implementation
-+}
-+```
-+
-+## 5. Error Handling & Logging
-+
-+### Security Event Logging
-+```typescript
-+interface SecurityEvent {
-+  type: 'HMAC_AUTH_SUCCESS' | 'HMAC_AUTH_FAILURE' | 'JWT_AUTH_SUCCESS' | 'JWT_AUTH_FAILURE'
-+  endpoint: string
-+  method: string
-+  partnerId?: string
-+  ip: string
-+  timestamp: string
-+  traceId: string
-+  error?: HMACError
-+  errorMessage?: string
-+}
-+
-+function logSecurityEvent(event: SecurityEvent): void {
-+  const logEntry = {
-+    level: event.type.includes('FAILURE') ? 'WARN' : 'INFO',
-+    message: `Security Event: ${event.type}`,
-+    traceId: event.traceId,
-+    endpoint: event.endpoint,
-+    method: event.method,
-+    partnerId: event.partnerId,
-+    ip: event.ip,
-+    timestamp: event.timestamp,
-+    error: event.error,
-+    errorMessage: event.errorMessage
-+  }
-+
-+  // Log to CloudWatch via console (structured logging)
-+  console.log(JSON.stringify(logEntry))
-+
-+  // Optional: Send to additional monitoring services
-+  if (process.env.NODE_ENV === 'production') {
-+    // Could integrate with Datadog, New Relic, etc.
-+  }
-+}
-+
-+// Generate trace ID for request correlation
-+function generateTraceId(): string {
-+  return `hmac-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
-+}
-+```
-+
-+### Error Response Generation
-+```typescript
-+function createHMACErrorResponse(error: HMACError, traceId: string): NextResponse {
-+  const errorMap = {
-+    MISSING_SIGNATURE: { status: 401, message: "Missing X-Signature header" },
-+    MISSING_TIMESTAMP: { status: 401, message: "Missing X-Timestamp header" },
-+    MISSING_PARTNER_ID: { status: 401, message: "Missing X-Partner-ID header" },
-+    INVALID_SIGNATURE: { status: 401, message: "Invalid HMAC signature" },
-+    EXPIRED_TIMESTAMP: { status: 401, message: "Request timestamp expired" },
-+    UNKNOWN_PARTNER: { status: 401, message: "Unknown partner ID" },
-+    INVALID_FORMAT: { status: 400, message: "Invalid request format" },
-+    REPLAY_DETECTED: { status: 401, message: "Duplicate request detected" },
-+    BODY_HASH_MISMATCH: { status: 401, message: "Request body hash mismatch" }
-+  }
-+
-+  const { status, message } = errorMap[error]
-+
-+  return NextResponse.json(
-+    {
-+      error: status === 401 ? 'Unauthorized' : 'Bad Request',
-+      message,
-+      code: error,
-+      traceId,
-+      supportedMethods: ['JWT', 'HMAC']
-+    },
-+    {
-+      status,
-+      headers: {
-+        'Content-Type': 'application/json',
-+        'X-Trace-ID': traceId,
-+        'WWW-Authenticate': 'Bearer realm="Search API", HMAC realm="Partner API"'
-+      }
-+    }
-+  )
-+}
-+```
-+
-+### Error Response Standards
-+```typescript
-+// Consistent error responses across all search endpoints
-+const AUTH_ERROR_RESPONSES = {
-+  MISSING_AUTH: {
-+    error: 'Unauthorized',
-+    message: 'Authentication required. Provide JWT token or HMAC signature.',
-+    code: 'AUTH_MISSING'
-+  },
-+  INVALID_JWT: {
-+    error: 'Unauthorized', 
-+    message: 'Invalid JWT token. Please re-authenticate.',
-+    code: 'JWT_INVALID'
-+  },
-+  INVALID_HMAC: {
-+    error: 'Unauthorized',
-+    message: 'Invalid HMAC signature. Check signature calculation.',
-+    code: 'HMAC_INVALID'
-+  },
-+  EXPIRED_TIMESTAMP: {
-+    error: 'Unauthorized',
-+    message: 'Request timestamp expired. Check system clock.',
-+    code: 'TIMESTAMP_EXPIRED'
-+  }
-+}
-+```
-+
-+## 6. Testing Specifications
-+
-+### Test Data Setup
-+```typescript
-+// Test partner configuration
-+const TEST_PARTNERS = {
-+  'test-partner': 'test-secret-minimum-32-characters-long',
-+  'acme-corp': 'acme-secret-minimum-32-characters-long'
-+}
-+
-+// Test request scenarios
-+const TEST_SCENARIOS = [
-+  {
-+    name: 'Valid HMAC Request',
-+    method: 'GET',
-+    path: '/api/search',
-+    partnerId: 'test-partner',
-+    expectSuccess: true
-+  },
-+  {
-+    name: 'Expired Timestamp',
-+    method: 'GET', 
-+    path: '/api/search',
-+    partnerId: 'test-partner',
-+    timestampOffset: -400, // 400 seconds ago
-+    expectSuccess: false
-+  },
-+  {
-+    name: 'Invalid Signature',
-+    method: 'GET',
-+    path: '/api/search',
-+    partnerId: 'test-partner',
-+    invalidSignature: true,
-+    expectSuccess: false
-+  }
-+]
-+```
-+
-+---
-+
-+**Next Steps:**
-+1. Review technical specifications with development team
-+2. Validate HMAC algorithm choice and parameters
-+3. Confirm partner secret management approach
-+4. Begin implementation of `src/lib/security/hmac.ts`
-diff --git a/docs/UPDATES/AUTH-SPRINT/PR2/testing-strategy.md b/docs/UPDATES/AUTH-SPRINT/PR2/testing-strategy.md
-new file mode 100644
-index 0000000..138a22c
---- /dev/null
-+++ b/docs/UPDATES/AUTH-SPRINT/PR2/testing-strategy.md
-@@ -0,0 +1,678 @@
-+# PR 2 Testing Strategy - HMAC Authentication & Search Routes
-+
-+**Date:** January 12, 2025  
-+**Sprint:** Auth Layer Implementation  
-+**Component:** HMAC Helper + Search Routes Protection  
-+**Test Coverage Target:** >95%  
-+
-+## Testing Overview
-+
-+This document outlines the comprehensive testing strategy for PR 2, focusing on HMAC authentication implementation and search endpoint protection. The testing approach follows the same rigorous standards established in PR 1 (JWT authentication) with 15/15 tests passing.
-+
-+## 1. Test Architecture
-+
-+### Test Categories
-+1. **Unit Tests**: HMAC helper functions and utilities
-+2. **Integration Tests**: Search endpoint authentication flows
-+3. **Security Tests**: Attack scenarios and edge cases
-+4. **Performance Tests**: Authentication overhead measurement
-+5. **Compatibility Tests**: JWT + HMAC coexistence
-+
-+### Test Environment Setup
-+```typescript
-+// Test configuration
-+const TEST_CONFIG = {
-+  partners: {
-+    'test-partner': 'test-secret-minimum-32-characters-long',
-+    'acme-corp': 'acme-secret-minimum-32-characters-long',
-+    'invalid-partner': 'short-secret' // For testing validation
-+  },
-+  timestampWindow: 300, // 5 minutes
-+  testEndpoints: [
-+    '/api/search',
-+    '/api/search/suggestions', 
-+    '/api/search/more'
-+  ]
-+}
-+```
-+
-+## 2. Unit Tests - HMAC Helper Functions
-+
-+### Test File: `src/__tests__/security/hmac.test.ts`
-+
-+#### Core Function Tests (15 tests)
-+```typescript
-+describe('HMAC Helper Functions', () => {
-+  describe('generateHMACSignature', () => {
-+    it('generates correct signature for GET request', () => {
-+      const signature = generateHMACSignature(
-+        'GET',
-+        '/api/search',
-+        1705123456,
-+        '',
-+        'test-secret-minimum-32-characters-long'
-+      )
-+      expect(signature).toBe('expected-signature-hash')
-+    })
-+    
-+    it('generates correct signature for POST request with body', () => {
-+      const body = JSON.stringify({ query: 'laptop' })
-+      const signature = generateHMACSignature(
-+        'POST',
-+        '/api/search',
-+        1705123456,
-+        body,
-+        'test-secret-minimum-32-characters-long'
-+      )
-+      expect(signature).toMatch(/^[a-f0-9]{64}$/)
-+    })
-+    
-+    it('generates different signatures for different methods', () => {
-+      const getSignature = generateHMACSignature('GET', '/api/search', 1705123456, '', 'secret')
-+      const postSignature = generateHMACSignature('POST', '/api/search', 1705123456, '', 'secret')
-+      expect(getSignature).not.toBe(postSignature)
-+    })
-+    
-+    it('generates different signatures for different paths', () => {
-+      const searchSignature = generateHMACSignature('GET', '/api/search', 1705123456, '', 'secret')
-+      const suggestionsSignature = generateHMACSignature('GET', '/api/search/suggestions', 1705123456, '', 'secret')
-+      expect(searchSignature).not.toBe(suggestionsSignature)
-+    })
-+    
-+    it('generates different signatures for different timestamps', () => {
-+      const signature1 = generateHMACSignature('GET', '/api/search', 1705123456, '', 'secret')
-+      const signature2 = generateHMACSignature('GET', '/api/search', 1705123457, '', 'secret')
-+      expect(signature1).not.toBe(signature2)
-+    })
-+  })
-+  
-+  describe('verifyHMACSignature', () => {
-+    it('verifies valid signature', () => {
-+      const signature = generateHMACSignature('GET', '/api/search', 1705123456, '', 'secret')
-+      const isValid = verifyHMACSignature(signature, 'GET', '/api/search', 1705123456, '', 'secret')
-+      expect(isValid).toBe(true)
-+    })
-+    
-+    it('rejects invalid signature', () => {
-+      const isValid = verifyHMACSignature('invalid-signature', 'GET', '/api/search', 1705123456, '', 'secret')
-+      expect(isValid).toBe(false)
-+    })
-+    
-+    it('rejects signature with wrong secret', () => {
-+      const signature = generateHMACSignature('GET', '/api/search', 1705123456, '', 'secret1')
-+      const isValid = verifyHMACSignature(signature, 'GET', '/api/search', 1705123456, '', 'secret2')
-+      expect(isValid).toBe(false)
-+    })
-+  })
-+  
-+  describe('extractHMACFromRequest', () => {
-+    it('extracts valid HMAC data from request', () => {
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': 'sha256=abc123',
-+          'x-timestamp': '1705123456',
-+          'x-partner-id': 'test-partner'
-+        }
-+      })
-+      
-+      const hmacData = extractHMACFromRequest(request)
-+      expect(hmacData).toEqual({
-+        signature: 'abc123',
-+        timestamp: 1705123456,
-+        partnerId: 'test-partner'
-+      })
-+    })
-+    
-+    it('returns null for missing headers', () => {
-+      const request = new NextRequest('http://localhost/api/search')
-+      const hmacData = extractHMACFromRequest(request)
-+      expect(hmacData).toBeNull()
-+    })
-+    
-+    it('handles sha256= prefix in signature', () => {
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': 'sha256=abc123',
-+          'x-timestamp': '1705123456',
-+          'x-partner-id': 'test-partner'
-+        }
-+      })
-+      
-+      const hmacData = extractHMACFromRequest(request)
-+      expect(hmacData?.signature).toBe('abc123')
-+    })
-+  })
-+  
-+  describe('verifyRequestHMAC', () => {
-+    it('verifies valid HMAC request', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'test-secret-minimum-32-characters-long')
-+      
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': `sha256=${signature}`,
-+          'x-timestamp': timestamp.toString(),
-+          'x-partner-id': 'test-partner'
-+        }
-+      })
-+      
-+      const payload = await verifyRequestHMAC(request)
-+      expect(payload).toEqual({
-+        partnerId: 'test-partner',
-+        timestamp,
-+        method: 'GET',
-+        path: '/api/search',
-+        isValid: true
-+      })
-+    })
-+    
-+    it('rejects expired timestamp', async () => {
-+      const expiredTimestamp = Math.floor(Date.now() / 1000) - 400 // 400 seconds ago
-+      const signature = generateHMACSignature('GET', '/api/search', expiredTimestamp, '', 'test-secret-minimum-32-characters-long')
-+      
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': `sha256=${signature}`,
-+          'x-timestamp': expiredTimestamp.toString(),
-+          'x-partner-id': 'test-partner'
-+        }
-+      })
-+      
-+      const payload = await verifyRequestHMAC(request)
-+      expect(payload).toBeNull()
-+    })
-+    
-+    it('rejects unknown partner', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'unknown-secret')
-+      
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': `sha256=${signature}`,
-+          'x-timestamp': timestamp.toString(),
-+          'x-partner-id': 'unknown-partner'
-+        }
-+      })
-+      
-+      const payload = await verifyRequestHMAC(request)
-+      expect(payload).toBeNull()
-+    })
-+  })
-+  
-+  describe('createHMACHeaders', () => {
-+    it('creates valid HMAC headers', () => {
-+      const headers = createHMACHeaders('GET', '/api/search', 'test-partner', '')
-+      
-+      expect(headers).toHaveProperty('X-Signature')
-+      expect(headers).toHaveProperty('X-Timestamp')
-+      expect(headers).toHaveProperty('X-Partner-ID')
-+      expect(headers['X-Partner-ID']).toBe('test-partner')
-+      expect(headers['X-Signature']).toMatch(/^sha256=[a-f0-9]{64}$/)
-+    })
-+  })
-+})
-+```
-+
-+## 3. Integration Tests - Search Endpoint Authentication
-+
-+### Test File: `src/__tests__/api/search-auth.test.ts`
-+
-+#### Search Endpoint Tests (12 tests)
-+```typescript
-+describe('Search API Authentication', () => {
-+  describe('/api/search', () => {
-+    it('allows access with valid JWT', async () => {
-+      const jwt = await createJWT()
-+      const response = await fetch('/api/search?q=laptop', {
-+        headers: { 'Authorization': `Bearer ${jwt}` }
-+      })
-+      expect(response.status).toBe(200)
-+    })
-+    
-+    it('allows access with valid HMAC', async () => {
-+      const headers = createHMACHeaders('GET', '/api/search?q=laptop', 'test-partner', '')
-+      const response = await fetch('/api/search?q=laptop', { headers })
-+      expect(response.status).toBe(200)
-+    })
-+    
-+    it('rejects request without authentication', async () => {
-+      const response = await fetch('/api/search?q=laptop')
-+      expect(response.status).toBe(401)
-+      
-+      const data = await response.json()
-+      expect(data.error).toBe('Unauthorized')
-+      expect(data.supportedMethods).toEqual(['JWT', 'HMAC'])
-+    })
-+    
-+    it('rejects request with invalid HMAC signature', async () => {
-+      const headers = {
-+        'X-Signature': 'sha256=invalid-signature',
-+        'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
-+        'X-Partner-ID': 'test-partner'
-+      }
-+      const response = await fetch('/api/search?q=laptop', { headers })
-+      expect(response.status).toBe(401)
-+    })
-+  })
-+  
-+  describe('/api/search/suggestions', () => {
-+    it('allows access with valid HMAC', async () => {
-+      const headers = createHMACHeaders('GET', '/api/search/suggestions?q=lap', 'test-partner', '')
-+      const response = await fetch('/api/search/suggestions?q=lap', { headers })
-+      expect(response.status).toBe(200)
-+    })
-+    
-+    it('rejects request without authentication', async () => {
-+      const response = await fetch('/api/search/suggestions?q=lap')
-+      expect(response.status).toBe(401)
-+    })
-+  })
-+  
-+  describe('/api/search/more', () => {
-+    it('allows access with valid HMAC', async () => {
-+      const headers = createHMACHeaders('GET', '/api/search/more?q=laptop&page=2', 'test-partner', '')
-+      const response = await fetch('/api/search/more?q=laptop&page=2', { headers })
-+      expect(response.status).toBe(200)
-+    })
-+    
-+    it('rejects request without authentication', async () => {
-+      const response = await fetch('/api/search/more?q=laptop&page=2')
-+      expect(response.status).toBe(401)
-+    })
-+  })
-+})
-+```
-+
-+## 4. Security Tests - Attack Scenarios
-+
-+### Test File: `src/__tests__/security/hmac-security.test.ts`
-+
-+#### Security Scenario Tests (10 tests)
-+```typescript
-+describe('HMAC Security Tests', () => {
-+  describe('Replay Attack Protection', () => {
-+    it('rejects replayed request with old timestamp', async () => {
-+      const oldTimestamp = Math.floor(Date.now() / 1000) - 400
-+      const signature = generateHMACSignature('GET', '/api/search', oldTimestamp, '', 'test-secret-minimum-32-characters-long')
-+      
-+      const headers = {
-+        'X-Signature': `sha256=${signature}`,
-+        'X-Timestamp': oldTimestamp.toString(),
-+        'X-Partner-ID': 'test-partner'
-+      }
-+      
-+      const response = await fetch('/api/search?q=laptop', { headers })
-+      expect(response.status).toBe(401)
-+    })
-+    
-+    it('rejects future timestamp', async () => {
-+      const futureTimestamp = Math.floor(Date.now() / 1000) + 400
-+      const signature = generateHMACSignature('GET', '/api/search', futureTimestamp, '', 'test-secret-minimum-32-characters-long')
-+      
-+      const headers = {
-+        'X-Signature': `sha256=${signature}`,
-+        'X-Timestamp': futureTimestamp.toString(),
-+        'X-Partner-ID': 'test-partner'
-+      }
-+      
-+      const response = await fetch('/api/search?q=laptop', { headers })
-+      expect(response.status).toBe(401)
-+    })
-+  })
-+  
-+  describe('Signature Tampering Protection', () => {
-+    it('rejects modified signature', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const validSignature = generateHMACSignature('GET', '/api/search', timestamp, '', 'test-secret-minimum-32-characters-long')
-+      const tamperedSignature = validSignature.slice(0, -1) + '0' // Change last character
-+
-+      const headers = {
-+        'X-Signature': `sha256=${tamperedSignature}`,
-+        'X-Timestamp': timestamp.toString(),
-+        'X-Partner-ID': 'test-partner'
-+      }
-+
-+      const response = await fetch('/api/search?q=laptop', { headers })
-+      expect(response.status).toBe(401)
-+    })
-+
-+    it('rejects signature for different path', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search/suggestions', timestamp, '', 'test-secret-minimum-32-characters-long')
-+
-+      const headers = {
-+        'X-Signature': `sha256=${signature}`,
-+        'X-Timestamp': timestamp.toString(),
-+        'X-Partner-ID': 'test-partner'
-+      }
-+
-+      // Use signature for /suggestions on /search endpoint
-+      const response = await fetch('/api/search?q=laptop', { headers })
-+      expect(response.status).toBe(401)
-+    })
-+
-+    it('rejects signature with mismatched body hash', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+
-+      // Generate signature with empty body
-+      const signature = generateHMACSignature('POST', '/api/search', timestamp, '', 'test-secret-minimum-32-characters-long')
-+
-+      const headers = {
-+        'X-Signature': `sha256=${signature}`,
-+        'X-Timestamp': timestamp.toString(),
-+        'X-Partner-ID': 'test-partner',
-+        'Content-Type': 'application/json'
-+      }
-+
-+      // Send request with JSON body (should fail because signature was for empty body)
-+      const response = await fetch('/api/search', {
-+        method: 'POST',
-+        headers,
-+        body: JSON.stringify({ query: 'laptop', filters: { brand: 'samsung' } })
-+      })
-+
-+      expect(response.status).toBe(401)
-+
-+      const data = await response.json()
-+      expect(data.code).toBe('BODY_HASH_MISMATCH')
-+    })
-+
-+    it('rejects signature with different method', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+
-+      // Generate signature for GET request
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'test-secret-minimum-32-characters-long')
-+
-+      const headers = {
-+        'X-Signature': `sha256=${signature}`,
-+        'X-Timestamp': timestamp.toString(),
-+        'X-Partner-ID': 'test-partner'
-+      }
-+
-+      // Use GET signature on POST request
-+      const response = await fetch('/api/search', {
-+        method: 'POST',
-+        headers,
-+        body: JSON.stringify({ query: 'laptop' })
-+      })
-+
-+      expect(response.status).toBe(401)
-+    })
-+  })
-+  
-+  describe('Partner Validation', () => {
-+    it('rejects unknown partner ID', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'unknown-secret')
-+      
-+      const headers = {
-+        'X-Signature': `sha256=${signature}`,
-+        'X-Timestamp': timestamp.toString(),
-+        'X-Partner-ID': 'unknown-partner'
-+      }
-+      
-+      const response = await fetch('/api/search?q=laptop', { headers })
-+      expect(response.status).toBe(401)
-+    })
-+    
-+    it('rejects partner with short secret', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'short')
-+      
-+      const headers = {
-+        'X-Signature': `sha256=${signature}`,
-+        'X-Timestamp': timestamp.toString(),
-+        'X-Partner-ID': 'invalid-partner'
-+      }
-+      
-+      const response = await fetch('/api/search?q=laptop', { headers })
-+      expect(response.status).toBe(401)
-+    })
-+  })
-+})
-+```
-+
-+## 5. Performance Tests
-+
-+### Test File: `src/__tests__/performance/auth-performance.test.ts`
-+
-+#### Performance Benchmarks (7 tests)
-+```typescript
-+describe('Authentication Performance', () => {
-+  it('HMAC verification completes within 5ms', async () => {
-+    const timestamp = Math.floor(Date.now() / 1000)
-+    const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'test-secret-minimum-32-characters-long')
-+
-+    const request = new NextRequest('http://localhost/api/search', {
-+      headers: {
-+        'x-signature': `sha256=${signature}`,
-+        'x-timestamp': timestamp.toString(),
-+        'x-partner-id': 'test-partner'
-+      }
-+    })
-+
-+    const startTime = performance.now()
-+    await verifyRequestHMAC(request)
-+    const endTime = performance.now()
-+
-+    expect(endTime - startTime).toBeLessThan(5)
-+  })
-+
-+  it('handles 100 concurrent HMAC verifications under 500ms', async () => {
-+    const requests = Array.from({ length: 100 }, (_, i) => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', `/api/search?q=test${i}`, timestamp, '', 'test-secret-minimum-32-characters-long')
-+
-+      return new NextRequest(`http://localhost/api/search?q=test${i}`, {
-+        headers: {
-+          'x-signature': `sha256=${signature}`,
-+          'x-timestamp': timestamp.toString(),
-+          'x-partner-id': 'test-partner'
-+        }
-+      })
-+    })
-+
-+    const startTime = performance.now()
-+    const results = await Promise.all(requests.map(verifyRequestHMAC))
-+    const endTime = performance.now()
-+
-+    expect(results.every(result => result !== null)).toBe(true)
-+    expect(endTime - startTime).toBeLessThan(500) // 500ms for 100 concurrent requests
-+  })
-+
-+  it('measures authentication overhead on search endpoint', async () => {
-+    // Test without authentication (baseline)
-+    const baselineStart = performance.now()
-+    const baselineResponse = await fetch('/api/search?q=laptop', {
-+      headers: { 'x-test-bypass-auth': 'true' } // Test bypass flag
-+    })
-+    const baselineEnd = performance.now()
-+    const baselineTime = baselineEnd - baselineStart
-+
-+    // Test with HMAC authentication
-+    const headers = createHMACHeaders('GET', '/api/search?q=laptop', 'test-partner', '')
-+    const authStart = performance.now()
-+    const authResponse = await fetch('/api/search?q=laptop', { headers })
-+    const authEnd = performance.now()
-+    const authTime = authEnd - authStart
-+
-+    // Authentication overhead should be less than 5ms
-+    const overhead = authTime - baselineTime
-+    expect(overhead).toBeLessThan(5)
-+    expect(authResponse.status).toBe(200)
-+  })
-+
-+  it('stress test: 1000 sequential HMAC verifications', async () => {
-+    const startTime = performance.now()
-+
-+    for (let i = 0; i < 1000; i++) {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'test-secret-minimum-32-characters-long')
-+
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': `sha256=${signature}`,
-+          'x-timestamp': timestamp.toString(),
-+          'x-partner-id': 'test-partner'
-+        }
-+      })
-+
-+      const result = await verifyRequestHMAC(request)
-+      expect(result).not.toBeNull()
-+    }
-+
-+    const endTime = performance.now()
-+    const totalTime = endTime - startTime
-+    const avgTime = totalTime / 1000
-+
-+    expect(avgTime).toBeLessThan(2) // Average less than 2ms per verification
-+  })
-+
-+  it('memory usage remains stable during extended operation', async () => {
-+    const initialMemory = process.memoryUsage().heapUsed
-+
-+    // Perform 500 HMAC operations
-+    for (let i = 0; i < 500; i++) {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'test-secret-minimum-32-characters-long')
-+
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': `sha256=${signature}`,
-+          'x-timestamp': timestamp.toString(),
-+          'x-partner-id': 'test-partner'
-+        }
-+      })
-+
-+      await verifyRequestHMAC(request)
-+    }
-+
-+    // Force garbage collection if available
-+    if (global.gc) {
-+      global.gc()
-+    }
-+
-+    const finalMemory = process.memoryUsage().heapUsed
-+    const memoryIncrease = finalMemory - initialMemory
-+
-+    // Memory increase should be minimal (less than 10MB)
-+    expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024)
-+  })
-+})
-+```
-+
-+## 6. Compatibility Tests
-+
-+### Test File: `src/__tests__/integration/jwt-hmac-compatibility.test.ts`
-+
-+#### JWT + HMAC Coexistence (8 tests)
-+```typescript
-+describe('JWT + HMAC Compatibility', () => {
-+  it('JWT takes precedence when both are present', async () => {
-+    const jwt = await createJWT()
-+    const hmacHeaders = createHMACHeaders('GET', '/api/search', 'test-partner', '')
-+    
-+    const response = await fetch('/api/search?q=laptop', {
-+      headers: {
-+        'Authorization': `Bearer ${jwt}`,
-+        ...hmacHeaders
-+      }
-+    })
-+    
-+    expect(response.status).toBe(200)
-+    // Should log JWT authentication, not HMAC
-+  })
-+  
-+  it('falls back to HMAC when JWT is invalid', async () => {
-+    const hmacHeaders = createHMACHeaders('GET', '/api/search', 'test-partner', '')
-+    
-+    const response = await fetch('/api/search?q=laptop', {
-+      headers: {
-+        'Authorization': 'Bearer invalid-jwt',
-+        ...hmacHeaders
-+      }
-+    })
-+    
-+    expect(response.status).toBe(200)
-+    // Should log HMAC authentication
-+  })
-+  
-+  it('existing JWT functionality remains unchanged', async () => {
-+    // Test that PR 1 JWT functionality still works
-+    const jwt = await createJWT()
-+    const response = await fetch('/api/contact', {
-+      method: 'POST',
-+      headers: {
-+        'Authorization': `Bearer ${jwt}`,
-+        'Content-Type': 'application/json'
-+      },
-+      body: JSON.stringify({
-+        name: 'Test User',
-+        email: '<EMAIL>',
-+        message: 'Test message'
-+      })
-+    })
-+    
-+    expect(response.status).toBe(200)
-+  })
-+})
-+```
-+
-+## 7. Test Execution Strategy
-+
-+### Test Commands
-+```bash
-+# Run all HMAC tests
-+npm test -- src/__tests__/security/hmac.test.ts
-+
-+# Run search endpoint authentication tests
-+npm test -- src/__tests__/api/search-auth.test.ts
-+
-+# Run security tests
-+npm test -- src/__tests__/security/hmac-security.test.ts
-+
-+# Run performance tests
-+npm test -- src/__tests__/performance/auth-performance.test.ts
-+
-+# Run compatibility tests
-+npm test -- src/__tests__/integration/jwt-hmac-compatibility.test.ts
-+
-+# Run all PR 2 tests
-+npm test -- --testPathPattern="(hmac|search-auth)"
-+
-+# Run with coverage
-+npm run test:coverage -- --testPathPattern="(hmac|search-auth)"
-+```
-+
-+### Success Criteria
-+- [ ] **Unit Tests**: 15/15 HMAC helper function tests passing
-+- [ ] **Integration Tests**: 12/12 search endpoint authentication tests passing
-+- [ ] **Security Tests**: 10/10 attack scenario tests passing
-+- [ ] **Performance Tests**: 5/5 performance benchmark tests passing
-+- [ ] **Compatibility Tests**: 8/8 JWT+HMAC coexistence tests passing
-+- [ ] **Overall Coverage**: >95% code coverage for new HMAC functionality
-+- [ ] **Regression Tests**: All existing JWT tests (15/15) still passing
-+
-+### Test Data Management
-+```typescript
-+// Test environment setup
-+beforeAll(async () => {
-+  // Set test environment variables
-+  process.env.PARTNER_SECRET_TEST_PARTNER = 'test-secret-minimum-32-characters-long'
-+  process.env.PARTNER_SECRET_ACME_CORP = 'acme-secret-minimum-32-characters-long'
-+  process.env.HMAC_TIMESTAMP_WINDOW = '300'
-+})
-+
-+afterAll(async () => {
-+  // Clean up test environment
-+  delete process.env.PARTNER_SECRET_TEST_PARTNER
-+  delete process.env.PARTNER_SECRET_ACME_CORP
-+  delete process.env.HMAC_TIMESTAMP_WINDOW
-+})
-+```
-+
-+---
-+
-+**Target: 50/50 tests passing (100% success rate)**  
-+**Coverage Target: >95%**  
-+**Performance Target: <5ms authentication overhead**
-diff --git a/package.json b/package.json
-index fc2490c..8d14295 100644
---- a/package.json
-+++ b/package.json
-@@ -19,6 +19,9 @@
-     "test:transformations": "jest --testPathPatterns=transformations",
-     "test:api": "jest --testPathPatterns=api",
-     "test:metadata": "jest --testPathPatterns=metadata",
-+    "test:e2e": "playwright test",
-+    "test:e2e:headed": "playwright test --headed",
-+    "test:e2e:ui": "playwright test --ui",
-     "validate:schema": "node scripts/validate-structured-data.js",
-     "audit:performance": "lighthouse --only=performance --output=json --quiet",
-     "audit:seo": "lighthouse --only=seo --output=json --quiet",
-diff --git a/playwright.config.ts b/playwright.config.ts
-new file mode 100644
-index 0000000..976ce8d
---- /dev/null
-+++ b/playwright.config.ts
-@@ -0,0 +1,74 @@
-+import { defineConfig, devices } from '@playwright/test'
-+
-+/**
-+ * @file Playwright configuration for end-to-end testing
-+ * @see https://playwright.dev/docs/test-configuration
-+ */
-+export default defineConfig({
-+  // Look for test files in the tests/e2e directory
-+  testDir: './tests/e2e',
-+
-+  // Run tests in files in parallel
-+  fullyParallel: true,
-+
-+  // Fail the build on CI if you accidentally left test.only in the source code
-+  forbidOnly: !!process.env.CI,
-+
-+  // Retry on CI only
-+  retries: process.env.CI ? 2 : 0,
-+
-+  // Opt out of parallel tests on CI
-+  workers: process.env.CI ? 1 : undefined,
-+
-+  // Reporter to use
-+  reporter: [
-+    ['html'],
-+    ['list'],
-+    ['junit', { outputFile: 'test-results/junit.xml' }]
-+  ],
-+
-+  use: {
-+    // Base URL to use in actions like `await page.goto('/')`
-+    baseURL: process.env.BASE_URL || 'http://localhost:3000',
-+
-+    // Collect trace when retrying the failed test
-+    trace: 'on-first-retry',
-+
-+    // Capture screenshot on failure
-+    screenshot: 'only-on-failure',
-+
-+    // Record video on failure
-+    video: 'retain-on-failure',
-+
-+    // Global test timeout
-+    actionTimeout: 30000,
-+    navigationTimeout: 30000,
-+  },
-+
-+  // Configure projects for major browsers
-+  projects: [
-+    {
-+      name: 'chromium',
-+      use: { ...devices['Desktop Chrome'] },
-+    },
-+    {
-+      name: 'firefox',
-+      use: { ...devices['Desktop Firefox'] },
-+    },
-+    {
-+      name: 'webkit',
-+      use: { ...devices['Desktop Safari'] },
-+    },
-+  ],
-+
-+  // Folder for test artifacts such as screenshots, videos, traces, etc.
-+  outputDir: 'test-results/',
-+
-+  // Run your local dev server before starting the tests
-+  webServer: {
-+    command: 'npm run dev',
-+    url: 'http://localhost:3000',
-+    reuseExistingServer: !process.env.CI,
-+    timeout: 120 * 1000, // 2 minutes
-+  },
-+})
-diff --git a/scripts/smoke-test-auth.sh b/scripts/smoke-test-auth.sh
-new file mode 100755
-index 0000000..86477a5
---- /dev/null
-+++ b/scripts/smoke-test-auth.sh
-@@ -0,0 +1,268 @@
-+#!/bin/bash
-+
-+# Smoke Test Script for HMAC Authentication
-+# Tests authentication functionality after deployment
-+
-+set -e
-+
-+# Configuration
-+BASE_URL="${BASE_URL:-https://api.cashback-deals.com}"
-+PARTNER_ID="${PARTNER_ID:-sandbox-partner}"
-+PARTNER_SECRET="${PARTNER_SECRET:-sandbox-secret-32-chars-minimum-length-test}"
-+VERBOSE="${VERBOSE:-false}"
-+
-+# Colors for output
-+RED='\033[0;31m'
-+GREEN='\033[0;32m'
-+YELLOW='\033[1;33m'
-+BLUE='\033[0;34m'
-+NC='\033[0m' # No Color
-+
-+# Logging functions
-+log_info() {
-+    echo -e "${BLUE}[INFO]${NC} $1"
-+}
-+
-+log_success() {
-+    echo -e "${GREEN}[SUCCESS]${NC} $1"
-+}
-+
-+log_warning() {
-+    echo -e "${YELLOW}[WARNING]${NC} $1"
-+}
-+
-+log_error() {
-+    echo -e "${RED}[ERROR]${NC} $1"
-+}
-+
-+# Generate HMAC signature
-+generate_signature() {
-+    local method="$1"
-+    local path="$2"
-+    local timestamp="$3"
-+    local body="$4"
-+    
-+    # Calculate body hash
-+    local body_hash=$(echo -n "$body" | sha256sum | cut -d' ' -f1)
-+    
-+    # Create message
-+    local message="$method\n$path\n$timestamp\n$body_hash"
-+    
-+    # Generate signature
-+    echo -n -e "$message" | openssl dgst -sha256 -hmac "$PARTNER_SECRET" | cut -d' ' -f2
-+}
-+
-+# Test function
-+test_endpoint() {
-+    local test_name="$1"
-+    local method="$2"
-+    local path="$3"
-+    local body="$4"
-+    local expected_status="$5"
-+    local use_auth="${6:-true}"
-+    
-+    log_info "Testing: $test_name"
-+    
-+    local timestamp=$(date +%s)
-+    local url="$BASE_URL$path"
-+    
-+    # Prepare curl command
-+    local curl_cmd="curl -s -w '%{http_code}' -o /tmp/response.json"
-+    
-+    if [ "$use_auth" = "true" ]; then
-+        local signature=$(generate_signature "$method" "$path" "$timestamp" "$body")
-+        curl_cmd="$curl_cmd -H 'X-Signature: sha256=$signature'"
-+        curl_cmd="$curl_cmd -H 'X-Timestamp: $timestamp'"
-+        curl_cmd="$curl_cmd -H 'X-Partner-ID: $PARTNER_ID'"
-+        curl_cmd="$curl_cmd -H 'X-Version: 1.0'"
-+    fi
-+    
-+    curl_cmd="$curl_cmd -H 'Content-Type: application/json'"
-+    
-+    if [ "$method" = "POST" ] && [ -n "$body" ]; then
-+        curl_cmd="$curl_cmd -d '$body'"
-+    fi
-+    
-+    curl_cmd="$curl_cmd -X $method '$url'"
-+    
-+    if [ "$VERBOSE" = "true" ]; then
-+        log_info "Command: $curl_cmd"
-+    fi
-+    
-+    # Execute request
-+    local status_code=$(eval $curl_cmd)
-+    local response=$(cat /tmp/response.json)
-+    
-+    if [ "$VERBOSE" = "true" ]; then
-+        log_info "Response: $response"
-+    fi
-+    
-+    # Check status code
-+    if [ "$status_code" = "$expected_status" ]; then
-+        log_success "$test_name - Status: $status_code ✓"
-+        return 0
-+    else
-+        log_error "$test_name - Expected: $expected_status, Got: $status_code ✗"
-+        log_error "Response: $response"
-+        return 1
-+    fi
-+}
-+
-+# Main test suite
-+main() {
-+    log_info "Starting HMAC Authentication Smoke Tests"
-+    log_info "Base URL: $BASE_URL"
-+    log_info "Partner ID: $PARTNER_ID"
-+    echo ""
-+    
-+    local failed_tests=0
-+    local total_tests=0
-+    
-+    # Test 1: No authentication (should fail)
-+    total_tests=$((total_tests + 1))
-+    if ! test_endpoint "No Authentication" "GET" "/api/search?q=test" "" "401" "false"; then
-+        failed_tests=$((failed_tests + 1))
-+    fi
-+    
-+    # Test 2: Valid HMAC authentication (should succeed)
-+    total_tests=$((total_tests + 1))
-+    if ! test_endpoint "Valid HMAC Auth" "GET" "/api/search?q=test" "" "200" "true"; then
-+        failed_tests=$((failed_tests + 1))
-+    fi
-+    
-+    # Test 3: Search suggestions with auth
-+    total_tests=$((total_tests + 1))
-+    if ! test_endpoint "Search Suggestions" "GET" "/api/search/suggestions?q=lap" "" "200" "true"; then
-+        failed_tests=$((failed_tests + 1))
-+    fi
-+    
-+    # Test 4: Search more with auth
-+    total_tests=$((total_tests + 1))
-+    if ! test_endpoint "Search More" "GET" "/api/search/more?q=laptop&page=2" "" "200" "true"; then
-+        failed_tests=$((failed_tests + 1))
-+    fi
-+    
-+    # Test 5: Invalid signature (should fail)
-+    total_tests=$((total_tests + 1))
-+    PARTNER_SECRET="wrong-secret" 
-+    if ! test_endpoint "Invalid Signature" "GET" "/api/search?q=test" "" "401" "true"; then
-+        failed_tests=$((failed_tests + 1))
-+    fi
-+    # Restore correct secret
-+    PARTNER_SECRET="${PARTNER_SECRET:-sandbox-secret-32-chars-minimum-length-test}"
-+    
-+    # Test 6: Expired timestamp (should fail)
-+    total_tests=$((total_tests + 1))
-+    local old_timestamp=$(($(date +%s) - 400)) # 400 seconds ago
-+    local old_signature=$(generate_signature "GET" "/api/search?q=test" "$old_timestamp" "")
-+    local curl_cmd="curl -s -w '%{http_code}' -o /tmp/response.json"
-+    curl_cmd="$curl_cmd -H 'X-Signature: sha256=$old_signature'"
-+    curl_cmd="$curl_cmd -H 'X-Timestamp: $old_timestamp'"
-+    curl_cmd="$curl_cmd -H 'X-Partner-ID: $PARTNER_ID'"
-+    curl_cmd="$curl_cmd -H 'Content-Type: application/json'"
-+    curl_cmd="$curl_cmd -X GET '$BASE_URL/api/search?q=test'"
-+    
-+    local status_code=$(eval $curl_cmd)
-+    if [ "$status_code" = "401" ]; then
-+        log_success "Expired Timestamp - Status: $status_code ✓"
-+    else
-+        log_error "Expired Timestamp - Expected: 401, Got: $status_code ✗"
-+        failed_tests=$((failed_tests + 1))
-+    fi
-+    
-+    # Test 7: POST request with body
-+    total_tests=$((total_tests + 1))
-+    local post_body='{"query":"laptop","filters":{"brand":"samsung"}}'
-+    if ! test_endpoint "POST with Body" "POST" "/api/search" "$post_body" "200" "true"; then
-+        failed_tests=$((failed_tests + 1))
-+    fi
-+    
-+    echo ""
-+    log_info "Test Results:"
-+    log_info "Total Tests: $total_tests"
-+    log_info "Passed: $((total_tests - failed_tests))"
-+    
-+    if [ $failed_tests -eq 0 ]; then
-+        log_success "All tests passed! ✓"
-+        exit 0
-+    else
-+        log_error "Failed Tests: $failed_tests ✗"
-+        exit 1
-+    fi
-+}
-+
-+# Help function
-+show_help() {
-+    echo "HMAC Authentication Smoke Test Script"
-+    echo ""
-+    echo "Usage: $0 [OPTIONS]"
-+    echo ""
-+    echo "Options:"
-+    echo "  -h, --help              Show this help message"
-+    echo "  -v, --verbose           Enable verbose output"
-+    echo "  -u, --url URL           Set base URL (default: https://api.cashback-deals.com)"
-+    echo "  -p, --partner-id ID     Set partner ID (default: sandbox-partner)"
-+    echo "  -s, --secret SECRET     Set partner secret"
-+    echo ""
-+    echo "Environment Variables:"
-+    echo "  BASE_URL               Base API URL"
-+    echo "  PARTNER_ID             Partner ID for authentication"
-+    echo "  PARTNER_SECRET         Partner secret for HMAC signing"
-+    echo "  VERBOSE                Enable verbose output (true/false)"
-+    echo ""
-+    echo "Examples:"
-+    echo "  $0                                    # Run with defaults"
-+    echo "  $0 --verbose                          # Run with verbose output"
-+    echo "  $0 --url https://staging.example.com # Test staging environment"
-+    echo "  VERBOSE=true $0                       # Use environment variable"
-+}
-+
-+# Parse command line arguments
-+while [[ $# -gt 0 ]]; do
-+    case $1 in
-+        -h|--help)
-+            show_help
-+            exit 0
-+            ;;
-+        -v|--verbose)
-+            VERBOSE=true
-+            shift
-+            ;;
-+        -u|--url)
-+            BASE_URL="$2"
-+            shift 2
-+            ;;
-+        -p|--partner-id)
-+            PARTNER_ID="$2"
-+            shift 2
-+            ;;
-+        -s|--secret)
-+            PARTNER_SECRET="$2"
-+            shift 2
-+            ;;
-+        *)
-+            log_error "Unknown option: $1"
-+            show_help
-+            exit 1
-+            ;;
-+    esac
-+done
-+
-+# Check dependencies
-+if ! command -v curl &> /dev/null; then
-+    log_error "curl is required but not installed"
-+    exit 1
-+fi
-+
-+if ! command -v openssl &> /dev/null; then
-+    log_error "openssl is required but not installed"
-+    exit 1
-+fi
-+
-+if ! command -v sha256sum &> /dev/null; then
-+    log_error "sha256sum is required but not installed"
-+    exit 1
-+fi
-+
-+# Run tests
-+main
-diff --git a/src/__tests__/api/search-auth.test.ts b/src/__tests__/api/search-auth.test.ts
-new file mode 100644
-index 0000000..7f6eb1b
---- /dev/null
-+++ b/src/__tests__/api/search-auth.test.ts
-@@ -0,0 +1,307 @@
-+// src/__tests__/api/search-auth.test.ts
-+// Integration tests for search endpoint authentication
-+
-+import { NextRequest } from 'next/server'
-+import { createJWT } from '@/lib/security/jwt'
-+import { createHMACHeaders, clearReplayCache } from '@/lib/security/hmac'
-+import { authenticateSearchRequest } from '@/lib/security/auth-middleware'
-+
-+// Test configuration
-+const TEST_SECRET = 'test-secret-minimum-32-characters-long'
-+const TEST_PARTNER_ID = 'test-partner'
-+
-+// Mock environment setup
-+beforeAll(() => {
-+  process.env.JWT_SECRET = 'test-jwt-secret-minimum-32-characters-long'
-+  process.env.PARTNER_SECRET_TEST_PARTNER = TEST_SECRET
-+  process.env.PARTNER_SECRET_DEFAULT = TEST_SECRET
-+  process.env.HMAC_TIMESTAMP_WINDOW = '300'
-+  process.env.ENABLE_SEARCH_AUTH = 'true'
-+  process.env.ENABLE_HMAC_AUTH = 'true'
-+})
-+
-+afterAll(() => {
-+  delete process.env.JWT_SECRET
-+  delete process.env.PARTNER_SECRET_TEST_PARTNER
-+  delete process.env.PARTNER_SECRET_DEFAULT
-+  delete process.env.HMAC_TIMESTAMP_WINDOW
-+  delete process.env.ENABLE_SEARCH_AUTH
-+  delete process.env.ENABLE_HMAC_AUTH
-+})
-+
-+// Clear replay cache before each test to ensure clean state
-+beforeEach(() => {
-+  clearReplayCache()
-+})
-+
-+describe('Search API Authentication', () => {
-+  describe('JWT Authentication', () => {
-+    it('allows access with valid JWT in Authorization header', async () => {
-+      const jwt = await createJWT()
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: { 'Authorization': `Bearer ${jwt}` }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('JWT')
-+      expect(authResult.payload).not.toBeNull()
-+    })
-+
-+    it('allows access with valid JWT in cookie', async () => {
-+      const jwt = await createJWT()
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: { 'Cookie': `auth-token=${jwt}` }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('JWT')
-+    })
-+
-+    it('rejects access with invalid JWT', async () => {
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: { 'Authorization': 'Bearer invalid-jwt-token' }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+      expect(authResult.method).toBeNull()
-+    })
-+
-+    it('rejects access with expired JWT', async () => {
-+      // Create JWT with past expiration (this would need a modified createJWT for testing)
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: { 'Authorization': 'Bearer expired.jwt.token' }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+  })
-+
-+  describe('HMAC Authentication', () => {
-+    it('allows access with valid HMAC signature', async () => {
-+      const headers = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', { headers })
-+
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('HMAC')
-+      expect(authResult.payload).toMatchObject({
-+        partnerId: TEST_PARTNER_ID,
-+        method: 'GET',
-+        path: '/api/search',
-+        isValid: true
-+      })
-+    })
-+
-+    it('rejects access with invalid HMAC signature', async () => {
-+      const headers = {
-+        'X-Signature': 'sha256=invalid-signature',
-+        'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
-+        'X-Partner-ID': TEST_PARTNER_ID
-+      }
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', { headers })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+
-+    it('rejects access with expired HMAC timestamp', async () => {
-+      const expiredTimestamp = Math.floor(Date.now() / 1000) - 400 // 400 seconds ago
-+      const headers = {
-+        'X-Signature': 'sha256=some-signature',
-+        'X-Timestamp': expiredTimestamp.toString(),
-+        'X-Partner-ID': TEST_PARTNER_ID
-+      }
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', { headers })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+
-+    it('rejects access with unknown partner ID', async () => {
-+      const headers = createHMACHeaders('GET', '/api/search?q=laptop', 'unknown-partner', '', 'unknown-secret')
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', { headers })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+  })
-+
-+  describe('Dual Authentication Priority', () => {
-+    it('JWT takes precedence when both JWT and HMAC are present', async () => {
-+      const jwt = await createJWT()
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search?q=laptop', TEST_PARTNER_ID, '')
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'Authorization': `Bearer ${jwt}`,
-+          ...hmacHeaders
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('JWT') // JWT should take precedence
-+    })
-+
-+    it('falls back to HMAC when JWT is invalid but HMAC is valid', async () => {
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
-+
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'Authorization': 'Bearer invalid-jwt',
-+          ...hmacHeaders
-+        }
-+      })
-+
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('HMAC') // Should fall back to HMAC
-+    })
-+
-+    it('rejects when both JWT and HMAC are invalid', async () => {
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'Authorization': 'Bearer invalid-jwt',
-+          'X-Signature': 'sha256=invalid-signature',
-+          'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+  })
-+
-+  describe('Feature Flag Behavior', () => {
-+    it('bypasses authentication when ENABLE_SEARCH_AUTH is false', async () => {
-+      process.env.ENABLE_SEARCH_AUTH = 'false'
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop')
-+      const authResult = await authenticateSearchRequest(request)
-+      
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBeNull()
-+      
-+      process.env.ENABLE_SEARCH_AUTH = 'true'
-+    })
-+
-+    it('only allows JWT when ENABLE_HMAC_AUTH is false', async () => {
-+      process.env.ENABLE_HMAC_AUTH = 'false'
-+      
-+      // HMAC request should fail
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search?q=laptop', TEST_PARTNER_ID, '')
-+      const hmacRequest = new NextRequest('http://localhost/api/search?q=laptop', { headers: hmacHeaders })
-+      const hmacResult = await authenticateSearchRequest(hmacRequest)
-+      expect(hmacResult.success).toBe(false)
-+      
-+      // JWT request should still work
-+      const jwt = await createJWT()
-+      const jwtRequest = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: { 'Authorization': `Bearer ${jwt}` }
-+      })
-+      const jwtResult = await authenticateSearchRequest(jwtRequest)
-+      expect(jwtResult.success).toBe(true)
-+      expect(jwtResult.method).toBe('JWT')
-+      
-+      process.env.ENABLE_HMAC_AUTH = 'true'
-+    })
-+  })
-+
-+  describe('No Authentication', () => {
-+    it('rejects request without any authentication', async () => {
-+      const request = new NextRequest('http://localhost/api/search?q=laptop')
-+      const authResult = await authenticateSearchRequest(request)
-+      
-+      expect(authResult.success).toBe(false)
-+      expect(authResult.method).toBeNull()
-+      expect(authResult.error).toBe('No valid authentication found')
-+      expect(authResult.traceId).toMatch(/^hmac-[a-z]+-[a-z0-9]+-[a-z0-9]+$/)
-+    })
-+
-+    it('rejects request with incomplete HMAC headers', async () => {
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': 'sha256=some-signature',
-+          // Missing X-Timestamp and X-Partner-ID
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+
-+    it('rejects request with malformed Authorization header', async () => {
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'Authorization': 'InvalidFormat token-here'
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+  })
-+
-+  describe('Different Search Endpoints', () => {
-+    it('authenticates /api/search/suggestions endpoint', async () => {
-+      const headers = createHMACHeaders('GET', '/api/search/suggestions', TEST_PARTNER_ID, '')
-+      const request = new NextRequest('http://localhost/api/search/suggestions?q=lap', { headers })
-+
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('HMAC')
-+    })
-+
-+    it('authenticates /api/search/more endpoint', async () => {
-+      const headers = createHMACHeaders('GET', '/api/search/more', TEST_PARTNER_ID, '')
-+      const request = new NextRequest('http://localhost/api/search/more?q=laptop&page=2', { headers })
-+
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('HMAC')
-+    })
-+  })
-+
-+  describe('Request Body Handling', () => {
-+    it('handles POST requests with JSON body', async () => {
-+      const body = JSON.stringify({ query: 'laptop', filters: { brand: 'samsung' } })
-+      const headers = createHMACHeaders('POST', '/api/search', TEST_PARTNER_ID, body)
-+      
-+      const request = new NextRequest('http://localhost/api/search', {
-+        method: 'POST',
-+        headers: {
-+          ...headers,
-+          'Content-Type': 'application/json'
-+        },
-+        body
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('HMAC')
-+    })
-+
-+    it('rejects POST request with body hash mismatch', async () => {
-+      // Generate signature with empty body
-+      const headers = createHMACHeaders('POST', '/api/search', TEST_PARTNER_ID, '')
-+      
-+      // Send request with actual body (should fail)
-+      const request = new NextRequest('http://localhost/api/search', {
-+        method: 'POST',
-+        headers: {
-+          ...headers,
-+          'Content-Type': 'application/json'
-+        },
-+        body: JSON.stringify({ query: 'laptop' })
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+  })
-+})
-diff --git a/src/__tests__/integration/jwt-hmac-compatibility.test.ts b/src/__tests__/integration/jwt-hmac-compatibility.test.ts
-new file mode 100644
-index 0000000..5b64d37
---- /dev/null
-+++ b/src/__tests__/integration/jwt-hmac-compatibility.test.ts
-@@ -0,0 +1,372 @@
-+// src/__tests__/integration/jwt-hmac-compatibility.test.ts
-+// Compatibility tests for JWT + HMAC coexistence
-+
-+import { NextRequest } from 'next/server'
-+import { createJWT, verifyRequestJWT } from '@/lib/security/jwt'
-+import { createHMACHeaders, clearReplayCache } from '@/lib/security/hmac'
-+import { authenticateSearchRequest } from '@/lib/security/auth-middleware'
-+
-+// Test configuration
-+const TEST_SECRET = 'test-secret-minimum-32-characters-long'
-+const TEST_PARTNER_ID = 'test-partner'
-+
-+// Setup test environment
-+beforeAll(() => {
-+  process.env.JWT_SECRET = 'test-jwt-secret-minimum-32-characters-long'
-+  process.env.PARTNER_SECRET_TEST_PARTNER = TEST_SECRET
-+  process.env.PARTNER_SECRET_DEFAULT = TEST_SECRET
-+  process.env.HMAC_TIMESTAMP_WINDOW = '300'
-+  process.env.ENABLE_SEARCH_AUTH = 'true'
-+  process.env.ENABLE_HMAC_AUTH = 'true'
-+})
-+
-+beforeEach(() => {
-+  // Clear replay cache to avoid conflicts between tests
-+  clearReplayCache()
-+})
-+
-+afterAll(() => {
-+  delete process.env.JWT_SECRET
-+  delete process.env.PARTNER_SECRET_TEST_PARTNER
-+  delete process.env.PARTNER_SECRET_DEFAULT
-+  delete process.env.HMAC_TIMESTAMP_WINDOW
-+  delete process.env.ENABLE_SEARCH_AUTH
-+  delete process.env.ENABLE_HMAC_AUTH
-+})
-+
-+describe('JWT + HMAC Compatibility', () => {
-+  describe('Authentication Priority', () => {
-+    it('JWT takes precedence when both are present and valid', async () => {
-+      const jwt = await createJWT()
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'Authorization': `Bearer ${jwt}`,
-+          ...hmacHeaders
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('JWT') // JWT should take precedence
-+      expect(authResult.payload).toHaveProperty('sub')
-+    })
-+    
-+    it('falls back to HMAC when JWT is invalid but HMAC is valid', async () => {
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'Authorization': 'Bearer invalid-jwt',
-+          ...hmacHeaders
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('HMAC') // Should fall back to HMAC
-+      expect(authResult.payload).toHaveProperty('partnerId')
-+    })
-+
-+    it('falls back to HMAC when JWT is missing but HMAC is valid', async () => {
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: hmacHeaders
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('HMAC')
-+    })
-+
-+    it('rejects when both JWT and HMAC are invalid', async () => {
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'Authorization': 'Bearer invalid-jwt',
-+          'X-Signature': 'sha256=invalid-signature',
-+          'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+      expect(authResult.method).toBeNull()
-+    })
-+  })
-+
-+  describe('Backward Compatibility', () => {
-+    it('existing JWT functionality remains unchanged', async () => {
-+      // Test that PR 1 JWT functionality still works exactly as before
-+      const jwt = await createJWT()
-+      const request = new NextRequest('http://localhost/api/contact', {
-+        method: 'POST',
-+        headers: {
-+          'Authorization': `Bearer ${jwt}`,
-+          'Content-Type': 'application/json'
-+        },
-+        body: JSON.stringify({
-+          name: 'Test User',
-+          email: '<EMAIL>',
-+          message: 'Test message'
-+        })
-+      })
-+      
-+      // Verify JWT directly (as it would work in contact endpoint)
-+      const jwtPayload = await verifyRequestJWT(request)
-+      expect(jwtPayload).not.toBeNull()
-+      expect(jwtPayload).toHaveProperty('sub')
-+    })
-+
-+    it('JWT authentication works independently of HMAC', async () => {
-+      // Disable HMAC temporarily
-+      process.env.ENABLE_HMAC_AUTH = 'false'
-+      
-+      const jwt = await createJWT()
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: { 'Authorization': `Bearer ${jwt}` }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('JWT')
-+      
-+      // Re-enable HMAC
-+      process.env.ENABLE_HMAC_AUTH = 'true'
-+    })
-+
-+    it('HMAC authentication works independently of JWT', async () => {
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', { headers: hmacHeaders })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('HMAC')
-+    })
-+  })
-+
-+  describe('Feature Flag Interactions', () => {
-+    it('respects ENABLE_SEARCH_AUTH flag for both methods', async () => {
-+      process.env.ENABLE_SEARCH_AUTH = 'false'
-+      
-+      // Both JWT and HMAC should be bypassed
-+      const jwt = await createJWT()
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
-+      
-+      const jwtRequest = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: { 'Authorization': `Bearer ${jwt}` }
-+      })
-+      
-+      const hmacRequest = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: hmacHeaders
-+      })
-+      
-+      const noAuthRequest = new NextRequest('http://localhost/api/search?q=laptop')
-+      
-+      const jwtResult = await authenticateSearchRequest(jwtRequest)
-+      const hmacResult = await authenticateSearchRequest(hmacRequest)
-+      const noAuthResult = await authenticateSearchRequest(noAuthRequest)
-+      
-+      // All should succeed when authentication is disabled
-+      expect(jwtResult.success).toBe(true)
-+      expect(hmacResult.success).toBe(true)
-+      expect(noAuthResult.success).toBe(true)
-+      
-+      // All should have null method when bypassed
-+      expect(jwtResult.method).toBeNull()
-+      expect(hmacResult.method).toBeNull()
-+      expect(noAuthResult.method).toBeNull()
-+      
-+      process.env.ENABLE_SEARCH_AUTH = 'true'
-+    })
-+
-+    it('ENABLE_HMAC_AUTH only affects HMAC, not JWT', async () => {
-+      process.env.ENABLE_HMAC_AUTH = 'false'
-+      
-+      const jwt = await createJWT()
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
-+      
-+      // JWT should still work
-+      const jwtRequest = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: { 'Authorization': `Bearer ${jwt}` }
-+      })
-+      const jwtResult = await authenticateSearchRequest(jwtRequest)
-+      expect(jwtResult.success).toBe(true)
-+      expect(jwtResult.method).toBe('JWT')
-+      
-+      // HMAC should fail
-+      const hmacRequest = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: hmacHeaders
-+      })
-+      const hmacResult = await authenticateSearchRequest(hmacRequest)
-+      expect(hmacResult.success).toBe(false)
-+      
-+      process.env.ENABLE_HMAC_AUTH = 'true'
-+    })
-+  })
-+
-+  describe('Error Handling Consistency', () => {
-+    it('provides consistent error responses regardless of authentication method', async () => {
-+      // Test invalid JWT
-+      const invalidJwtRequest = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: { 'Authorization': 'Bearer invalid-jwt' }
-+      })
-+      const jwtResult = await authenticateSearchRequest(invalidJwtRequest)
-+      
-+      // Test invalid HMAC
-+      const invalidHmacRequest = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': 'sha256=invalid-signature',
-+          'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      const hmacResult = await authenticateSearchRequest(invalidHmacRequest)
-+      
-+      // Test no authentication
-+      const noAuthRequest = new NextRequest('http://localhost/api/search?q=laptop')
-+      const noAuthResult = await authenticateSearchRequest(noAuthRequest)
-+      
-+      // All should fail with consistent structure
-+      expect(jwtResult.success).toBe(false)
-+      expect(hmacResult.success).toBe(false)
-+      expect(noAuthResult.success).toBe(false)
-+      
-+      expect(jwtResult.traceId).toMatch(/^hmac-[a-z]+-[a-z0-9]+-[a-z0-9]+$/)
-+      expect(hmacResult.traceId).toMatch(/^hmac-[a-z]+-[a-z0-9]+-[a-z0-9]+$/)
-+      expect(noAuthResult.traceId).toMatch(/^hmac-[a-z]+-[a-z0-9]+-[a-z0-9]+$/)
-+    })
-+  })
-+
-+  describe('Performance Impact', () => {
-+    it('dual authentication does not significantly impact performance', async () => {
-+      // Test JWT-only performance
-+      const jwt = await createJWT()
-+      const jwtRequest = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: { 'Authorization': `Bearer ${jwt}` }
-+      })
-+      
-+      const jwtStart = performance.now()
-+      await authenticateSearchRequest(jwtRequest)
-+      const jwtTime = performance.now() - jwtStart
-+      
-+      // Test HMAC-only performance
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
-+      const hmacRequest = new NextRequest('http://localhost/api/search?q=laptop', { headers: hmacHeaders })
-+      
-+      const hmacStart = performance.now()
-+      await authenticateSearchRequest(hmacRequest)
-+      const hmacTime = performance.now() - hmacStart
-+      
-+      // Test dual authentication (JWT + HMAC headers, JWT should win)
-+      const dualRequest = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'Authorization': `Bearer ${jwt}`,
-+          ...hmacHeaders
-+        }
-+      })
-+      
-+      const dualStart = performance.now()
-+      await authenticateSearchRequest(dualRequest)
-+      const dualTime = performance.now() - dualStart
-+      
-+      // Dual authentication should not be significantly slower than JWT alone
-+      expect(dualTime).toBeLessThan(jwtTime * 1.5) // At most 50% slower
-+      expect(jwtTime).toBeLessThan(10) // JWT should be fast
-+      expect(hmacTime).toBeLessThan(10) // HMAC should be fast
-+    })
-+  })
-+
-+  describe('Logging and Tracing', () => {
-+    it('generates unique trace IDs for each authentication attempt', async () => {
-+      const requests = Array.from({ length: 10 }, () => {
-+        return new NextRequest('http://localhost/api/search?q=laptop')
-+      })
-+      
-+      const results = await Promise.all(requests.map(authenticateSearchRequest))
-+      const traceIds = results.map(r => r.traceId)
-+      
-+      // All trace IDs should be unique
-+      const uniqueTraceIds = new Set(traceIds)
-+      expect(uniqueTraceIds.size).toBe(traceIds.length)
-+      
-+      // All should match the expected format
-+      traceIds.forEach(traceId => {
-+        expect(traceId).toMatch(/^hmac-[a-z]+-[a-z0-9]+-[a-z0-9]+$/)
-+      })
-+    })
-+
-+    it('maintains trace ID consistency across authentication methods', async () => {
-+      const jwt = await createJWT()
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
-+      
-+      // Test with valid JWT
-+      const jwtRequest = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: { 'Authorization': `Bearer ${jwt}` }
-+      })
-+      const jwtResult = await authenticateSearchRequest(jwtRequest)
-+      
-+      // Test with valid HMAC
-+      const hmacRequest = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: hmacHeaders
-+      })
-+      const hmacResult = await authenticateSearchRequest(hmacRequest)
-+      
-+      // Both should have valid trace IDs
-+      expect(jwtResult.traceId).toMatch(/^hmac-[a-z]+-[a-z0-9]+-[a-z0-9]+$/)
-+      expect(hmacResult.traceId).toMatch(/^hmac-[a-z]+-[a-z0-9]+-[a-z0-9]+$/)
-+      expect(jwtResult.traceId).not.toBe(hmacResult.traceId)
-+    })
-+  })
-+
-+  describe('Edge Cases', () => {
-+    it('handles malformed Authorization header with valid HMAC', async () => {
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'Authorization': 'Malformed header format',
-+          ...hmacHeaders
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('HMAC') // Should fall back to HMAC
-+    })
-+
-+    it('handles empty Authorization header with valid HMAC', async () => {
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'Authorization': '',
-+          ...hmacHeaders
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('HMAC')
-+    })
-+
-+    it('handles valid JWT with malformed HMAC headers', async () => {
-+      const jwt = await createJWT()
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'Authorization': `Bearer ${jwt}`,
-+          'X-Signature': 'malformed',
-+          'X-Timestamp': 'not-a-number',
-+          'X-Partner-ID': ''
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('JWT') // JWT should work despite malformed HMAC
-+    })
-+  })
-+})
-diff --git a/src/__tests__/performance/auth-performance.test.ts b/src/__tests__/performance/auth-performance.test.ts
-new file mode 100644
-index 0000000..7b00a02
---- /dev/null
-+++ b/src/__tests__/performance/auth-performance.test.ts
-@@ -0,0 +1,350 @@
-+// src/__tests__/performance/auth-performance.test.ts
-+// Performance tests for authentication overhead
-+
-+import { NextRequest } from 'next/server'
-+import { generateHMACSignature, verifyRequestHMAC } from '@/lib/security/hmac'
-+import { createHMACHeaders } from '@/lib/security/hmac'
-+import { authenticateSearchRequest } from '@/lib/security/auth-middleware'
-+import { createJWT, verifyRequestJWT } from '@/lib/security/jwt'
-+
-+// Test configuration
-+const TEST_SECRET = 'test-secret-minimum-32-characters-long'
-+const TEST_PARTNER_ID = 'test-partner'
-+
-+// Setup test environment
-+beforeAll(() => {
-+  process.env.JWT_SECRET = 'test-jwt-secret-minimum-32-characters-long'
-+  process.env.PARTNER_SECRET_TEST_PARTNER = TEST_SECRET
-+  process.env.PARTNER_SECRET_DEFAULT = TEST_SECRET
-+  process.env.HMAC_TIMESTAMP_WINDOW = '300'
-+  process.env.ENABLE_SEARCH_AUTH = 'true'
-+  process.env.ENABLE_HMAC_AUTH = 'true'
-+})
-+
-+afterAll(() => {
-+  delete process.env.JWT_SECRET
-+  delete process.env.PARTNER_SECRET_TEST_PARTNER
-+  delete process.env.PARTNER_SECRET_DEFAULT
-+  delete process.env.HMAC_TIMESTAMP_WINDOW
-+  delete process.env.ENABLE_SEARCH_AUTH
-+  delete process.env.ENABLE_HMAC_AUTH
-+})
-+
-+describe('Authentication Performance', () => {
-+  describe('HMAC Performance', () => {
-+    it('HMAC verification completes within 5ms', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
-+      
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': `sha256=${signature}`,
-+          'x-timestamp': timestamp.toString(),
-+          'x-partner-id': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const startTime = performance.now()
-+      await verifyRequestHMAC(request)
-+      const endTime = performance.now()
-+      
-+      expect(endTime - startTime).toBeLessThan(5)
-+    })
-+
-+    it('HMAC signature generation completes within 2ms', () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      
-+      const startTime = performance.now()
-+      generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
-+      const endTime = performance.now()
-+      
-+      expect(endTime - startTime).toBeLessThan(2)
-+    })
-+
-+    it('handles 100 concurrent HMAC verifications under 500ms', async () => {
-+      const requests = Array.from({ length: 100 }, (_, i) => {
-+        const timestamp = Math.floor(Date.now() / 1000)
-+        const signature = generateHMACSignature('GET', `/api/search?q=test${i}`, timestamp, '', TEST_SECRET)
-+        
-+        return new NextRequest(`http://localhost/api/search?q=test${i}`, {
-+          headers: {
-+            'x-signature': `sha256=${signature}`,
-+            'x-timestamp': timestamp.toString(),
-+            'x-partner-id': TEST_PARTNER_ID
-+          }
-+        })
-+      })
-+      
-+      const startTime = performance.now()
-+      const results = await Promise.all(requests.map(verifyRequestHMAC))
-+      const endTime = performance.now()
-+      
-+      expect(results.every(result => result !== null)).toBe(true)
-+      expect(endTime - startTime).toBeLessThan(500) // 500ms for 100 concurrent requests
-+    })
-+
-+    it('stress test: 1000 sequential HMAC verifications', async () => {
-+      const startTime = performance.now()
-+      
-+      for (let i = 0; i < 1000; i++) {
-+        const timestamp = Math.floor(Date.now() / 1000)
-+        const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
-+        
-+        const request = new NextRequest('http://localhost/api/search', {
-+          headers: {
-+            'x-signature': `sha256=${signature}`,
-+            'x-timestamp': timestamp.toString(),
-+            'x-partner-id': TEST_PARTNER_ID
-+          }
-+        })
-+        
-+        const result = await verifyRequestHMAC(request)
-+        expect(result).not.toBeNull()
-+      }
-+      
-+      const endTime = performance.now()
-+      const totalTime = endTime - startTime
-+      const avgTime = totalTime / 1000
-+      
-+      expect(avgTime).toBeLessThan(2) // Average less than 2ms per verification
-+    })
-+
-+    it('memory usage remains stable during extended operation', async () => {
-+      const initialMemory = process.memoryUsage().heapUsed
-+      
-+      // Perform 500 HMAC operations
-+      for (let i = 0; i < 500; i++) {
-+        const timestamp = Math.floor(Date.now() / 1000)
-+        const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
-+        
-+        const request = new NextRequest('http://localhost/api/search', {
-+          headers: {
-+            'x-signature': `sha256=${signature}`,
-+            'x-timestamp': timestamp.toString(),
-+            'x-partner-id': TEST_PARTNER_ID
-+          }
-+        })
-+        
-+        await verifyRequestHMAC(request)
-+      }
-+      
-+      // Force garbage collection if available
-+      if (global.gc) {
-+        global.gc()
-+      }
-+      
-+      const finalMemory = process.memoryUsage().heapUsed
-+      const memoryIncrease = finalMemory - initialMemory
-+      
-+      // Memory increase should be minimal (less than 10MB)
-+      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024)
-+    })
-+  })
-+
-+  describe('JWT Performance', () => {
-+    it('JWT verification completes within 5ms', async () => {
-+      const jwt = await createJWT()
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'authorization': `Bearer ${jwt}`
-+        }
-+      })
-+      
-+      const startTime = performance.now()
-+      await verifyRequestJWT(request)
-+      const endTime = performance.now()
-+      
-+      expect(endTime - startTime).toBeLessThan(5)
-+    })
-+
-+    it('JWT creation completes within 3ms', async () => {
-+      const startTime = performance.now()
-+      await createJWT()
-+      const endTime = performance.now()
-+      
-+      expect(endTime - startTime).toBeLessThan(3)
-+    })
-+  })
-+
-+  describe('Dual Authentication Performance', () => {
-+    it('dual authentication check completes within 10ms', async () => {
-+      const headers = createHMACHeaders('GET', '/api/search?q=laptop', TEST_PARTNER_ID, '')
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', { headers })
-+      
-+      const startTime = performance.now()
-+      const authResult = await authenticateSearchRequest(request)
-+      const endTime = performance.now()
-+      
-+      expect(authResult.success).toBe(true)
-+      expect(endTime - startTime).toBeLessThan(10)
-+    })
-+
-+    it('JWT fallback to HMAC completes within 15ms', async () => {
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search?q=laptop', TEST_PARTNER_ID, '')
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'authorization': 'Bearer invalid-jwt-token',
-+          ...hmacHeaders
-+        }
-+      })
-+      
-+      const startTime = performance.now()
-+      const authResult = await authenticateSearchRequest(request)
-+      const endTime = performance.now()
-+      
-+      expect(authResult.success).toBe(true)
-+      expect(authResult.method).toBe('HMAC')
-+      expect(endTime - startTime).toBeLessThan(15) // Slightly higher due to JWT attempt first
-+    })
-+
-+    it('authentication failure detection is fast', async () => {
-+      const request = new NextRequest('http://localhost/api/search?q=laptop')
-+      
-+      const startTime = performance.now()
-+      const authResult = await authenticateSearchRequest(request)
-+      const endTime = performance.now()
-+      
-+      expect(authResult.success).toBe(false)
-+      expect(endTime - startTime).toBeLessThan(5) // Should fail fast
-+    })
-+  })
-+
-+  describe('Concurrent Authentication Performance', () => {
-+    it('handles mixed authentication methods concurrently', async () => {
-+      // Create mix of JWT and HMAC requests
-+      const jwtPromises = Array.from({ length: 25 }, async (_, i) => {
-+        const jwt = await createJWT()
-+        const request = new NextRequest(`http://localhost/api/search?q=jwt${i}`, {
-+          headers: { 'authorization': `Bearer ${jwt}` }
-+        })
-+        return authenticateSearchRequest(request)
-+      })
-+
-+      const hmacPromises = Array.from({ length: 25 }, (_, i) => {
-+        const headers = createHMACHeaders('GET', `/api/search?q=hmac${i}`, TEST_PARTNER_ID, '')
-+        const request = new NextRequest(`http://localhost/api/search?q=hmac${i}`, { headers })
-+        return authenticateSearchRequest(request)
-+      })
-+
-+      const startTime = performance.now()
-+      const results = await Promise.all([...jwtPromises, ...hmacPromises])
-+      const endTime = performance.now()
-+
-+      // All should succeed
-+      expect(results.every(result => result.success)).toBe(true)
-+      
-+      // Should complete within reasonable time
-+      expect(endTime - startTime).toBeLessThan(1000) // 1 second for 50 mixed requests
-+    })
-+
-+    it('authentication overhead scales linearly', async () => {
-+      // Test with different batch sizes
-+      const batchSizes = [10, 50, 100]
-+      const timings = []
-+
-+      for (const batchSize of batchSizes) {
-+        const requests = Array.from({ length: batchSize }, (_, i) => {
-+          const headers = createHMACHeaders('GET', `/api/search?q=test${i}`, TEST_PARTNER_ID, '')
-+          return new NextRequest(`http://localhost/api/search?q=test${i}`, { headers })
-+        })
-+
-+        const startTime = performance.now()
-+        const results = await Promise.all(requests.map(authenticateSearchRequest))
-+        const endTime = performance.now()
-+
-+        expect(results.every(result => result.success)).toBe(true)
-+        timings.push({
-+          batchSize,
-+          totalTime: endTime - startTime,
-+          avgTime: (endTime - startTime) / batchSize
-+        })
-+      }
-+
-+      // Average time per request should remain relatively stable
-+      const avgTimes = timings.map(t => t.avgTime)
-+      const maxAvgTime = Math.max(...avgTimes)
-+      const minAvgTime = Math.min(...avgTimes)
-+      
-+      // Variation should be less than 100% (linear scaling)
-+      expect((maxAvgTime - minAvgTime) / minAvgTime).toBeLessThan(1.0)
-+    })
-+  })
-+
-+  describe('Real-world Performance Simulation', () => {
-+    it('simulates realistic API usage patterns', async () => {
-+      // Simulate realistic mix: 70% HMAC (API partners), 30% JWT (browsers)
-+      const totalRequests = 100
-+      const hmacRequests = Math.floor(totalRequests * 0.7)
-+      const jwtRequests = totalRequests - hmacRequests
-+
-+      const startTime = performance.now()
-+
-+      // Create HMAC requests (API partners)
-+      const hmacPromises = Array.from({ length: hmacRequests }, (_, i) => {
-+        const headers = createHMACHeaders('GET', `/api/search?q=partner${i}`, TEST_PARTNER_ID, '')
-+        const request = new NextRequest(`http://localhost/api/search?q=partner${i}`, { headers })
-+        return authenticateSearchRequest(request)
-+      })
-+
-+      // Create JWT requests (browser users)
-+      const jwtPromises = Array.from({ length: jwtRequests }, async (_, i) => {
-+        const jwt = await createJWT()
-+        const request = new NextRequest(`http://localhost/api/search?q=browser${i}`, {
-+          headers: { 'authorization': `Bearer ${jwt}` }
-+        })
-+        return authenticateSearchRequest(request)
-+      })
-+
-+      const results = await Promise.all([...hmacPromises, ...jwtPromises])
-+      const endTime = performance.now()
-+
-+      // All should succeed
-+      expect(results.every(result => result.success)).toBe(true)
-+
-+      // Performance should be acceptable for realistic load
-+      const totalTime = endTime - startTime
-+      const avgTimePerRequest = totalTime / totalRequests
-+      
-+      expect(avgTimePerRequest).toBeLessThan(10) // Less than 10ms average per request
-+      expect(totalTime).toBeLessThan(1000) // Total under 1 second for 100 requests
-+    })
-+
-+    it('measures authentication overhead vs no authentication', async () => {
-+      // Simulate requests without authentication (feature flag disabled)
-+      process.env.ENABLE_SEARCH_AUTH = 'false'
-+      
-+      const noAuthRequests = Array.from({ length: 50 }, (_, i) => {
-+        return new NextRequest(`http://localhost/api/search?q=noauth${i}`)
-+      })
-+
-+      const noAuthStart = performance.now()
-+      const noAuthResults = await Promise.all(noAuthRequests.map(authenticateSearchRequest))
-+      const noAuthEnd = performance.now()
-+      const noAuthTime = noAuthEnd - noAuthStart
-+
-+      // Re-enable authentication
-+      process.env.ENABLE_SEARCH_AUTH = 'true'
-+
-+      // Simulate requests with authentication
-+      const authRequests = Array.from({ length: 50 }, (_, i) => {
-+        const headers = createHMACHeaders('GET', `/api/search?q=auth${i}`, TEST_PARTNER_ID, '')
-+        return new NextRequest(`http://localhost/api/search?q=auth${i}`, { headers })
-+      })
-+
-+      const authStart = performance.now()
-+      const authResults = await Promise.all(authRequests.map(authenticateSearchRequest))
-+      const authEnd = performance.now()
-+      const authTime = authEnd - authStart
-+
-+      // All should succeed
-+      expect(noAuthResults.every(result => result.success)).toBe(true)
-+      expect(authResults.every(result => result.success)).toBe(true)
-+
-+      // Authentication overhead should be minimal
-+      const overhead = authTime - noAuthTime
-+      const overheadPerRequest = overhead / 50
-+      
-+      expect(overheadPerRequest).toBeLessThan(5) // Less than 5ms overhead per request
-+    })
-+  })
-+})
-diff --git a/src/__tests__/security/hmac-security.test.ts b/src/__tests__/security/hmac-security.test.ts
-new file mode 100644
-index 0000000..33e5814
---- /dev/null
-+++ b/src/__tests__/security/hmac-security.test.ts
-@@ -0,0 +1,402 @@
-+// src/__tests__/security/hmac-security.test.ts
-+// Security tests for HMAC authentication - attack scenarios and edge cases
-+
-+import { NextRequest } from 'next/server'
-+import { generateHMACSignature, verifyRequestHMAC } from '@/lib/security/hmac'
-+import { authenticateSearchRequest } from '@/lib/security/auth-middleware'
-+
-+// Test configuration
-+const TEST_SECRET = 'test-secret-minimum-32-characters-long'
-+const TEST_PARTNER_ID = 'test-partner'
-+
-+// Setup test environment
-+beforeAll(() => {
-+  process.env.PARTNER_SECRET_TEST_PARTNER = TEST_SECRET
-+  process.env.PARTNER_SECRET_DEFAULT = TEST_SECRET
-+  process.env.HMAC_TIMESTAMP_WINDOW = '300'
-+  process.env.ENABLE_SEARCH_AUTH = 'true'
-+  process.env.ENABLE_HMAC_AUTH = 'true'
-+})
-+
-+afterAll(() => {
-+  delete process.env.PARTNER_SECRET_TEST_PARTNER
-+  delete process.env.PARTNER_SECRET_DEFAULT
-+  delete process.env.HMAC_TIMESTAMP_WINDOW
-+  delete process.env.ENABLE_SEARCH_AUTH
-+  delete process.env.ENABLE_HMAC_AUTH
-+})
-+
-+describe('HMAC Security Tests', () => {
-+  describe('Replay Attack Protection', () => {
-+    it('rejects replayed request with old timestamp', async () => {
-+      const oldTimestamp = Math.floor(Date.now() / 1000) - 400 // 400 seconds ago
-+      const signature = generateHMACSignature('GET', '/api/search', oldTimestamp, '', TEST_SECRET)
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': `sha256=${signature}`,
-+          'X-Timestamp': oldTimestamp.toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+    
-+    it('rejects future timestamp beyond window', async () => {
-+      const futureTimestamp = Math.floor(Date.now() / 1000) + 400 // 400 seconds in future
-+      const signature = generateHMACSignature('GET', '/api/search', futureTimestamp, '', TEST_SECRET)
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': `sha256=${signature}`,
-+          'X-Timestamp': futureTimestamp.toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+
-+    it('detects and blocks duplicate requests (replay protection)', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': `sha256=${signature}`,
-+          'X-Timestamp': timestamp.toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      // First request should succeed
-+      const result1 = await verifyRequestHMAC(request)
-+      expect(result1).not.toBeNull()
-+      
-+      // Second identical request should be rejected as replay
-+      const result2 = await verifyRequestHMAC(request)
-+      expect(result2).toBeNull()
-+    })
-+
-+    it('allows requests with same timestamp but different signatures', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      
-+      // First request to /api/search
-+      const signature1 = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
-+      const request1 = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': `sha256=${signature1}`,
-+          'X-Timestamp': timestamp.toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      // Second request to /api/search/suggestions (different path, different signature)
-+      const signature2 = generateHMACSignature('GET', '/api/search/suggestions', timestamp, '', TEST_SECRET)
-+      const request2 = new NextRequest('http://localhost/api/search/suggestions?q=lap', {
-+        headers: {
-+          'X-Signature': `sha256=${signature2}`,
-+          'X-Timestamp': timestamp.toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      // Both should succeed since they have different signatures
-+      const result1 = await verifyRequestHMAC(request1)
-+      const result2 = await verifyRequestHMAC(request2)
-+      
-+      expect(result1).not.toBeNull()
-+      expect(result2).not.toBeNull()
-+    })
-+  })
-+  
-+  describe('Signature Tampering Protection', () => {
-+    it('rejects modified signature', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const validSignature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
-+      const tamperedSignature = validSignature.slice(0, -1) + '0' // Change last character
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': `sha256=${tamperedSignature}`,
-+          'X-Timestamp': timestamp.toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+    
-+    it('rejects signature for different path', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search/suggestions', timestamp, '', TEST_SECRET)
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': `sha256=${signature}`,
-+          'X-Timestamp': timestamp.toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      // Use signature for /suggestions on /search endpoint
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+    
-+    it('rejects signature with mismatched body hash', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      
-+      // Generate signature with empty body
-+      const signature = generateHMACSignature('POST', '/api/search', timestamp, '', TEST_SECRET)
-+      
-+      const request = new NextRequest('http://localhost/api/search', {
-+        method: 'POST',
-+        headers: {
-+          'X-Signature': `sha256=${signature}`,
-+          'X-Timestamp': timestamp.toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID,
-+          'Content-Type': 'application/json'
-+        },
-+        body: JSON.stringify({ query: 'laptop', filters: { brand: 'samsung' } })
-+      })
-+      
-+      // Should fail because signature was for empty body but request has JSON body
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+    
-+    it('rejects signature with different method', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      
-+      // Generate signature for GET request
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
-+      
-+      const request = new NextRequest('http://localhost/api/search', {
-+        method: 'POST',
-+        headers: {
-+          'X-Signature': `sha256=${signature}`,
-+          'X-Timestamp': timestamp.toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        },
-+        body: JSON.stringify({ query: 'laptop' })
-+      })
-+      
-+      // Use GET signature on POST request
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+
-+    it('rejects signature with different query parameters', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      
-+      // Generate signature for specific query
-+      const signature = generateHMACSignature('GET', '/api/search?q=laptop', timestamp, '', TEST_SECRET)
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=phone', {
-+        headers: {
-+          'X-Signature': `sha256=${signature}`,
-+          'X-Timestamp': timestamp.toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      // Use signature for different query parameters
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+  })
-+  
-+  describe('Partner Validation', () => {
-+    it('rejects unknown partner ID', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'unknown-secret')
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': `sha256=${signature}`,
-+          'X-Timestamp': timestamp.toString(),
-+          'X-Partner-ID': 'unknown-partner'
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+    
-+    it('rejects partner with short secret', async () => {
-+      // Set up partner with short secret
-+      process.env.PARTNER_SECRET_INVALID_PARTNER = 'short'
-+      
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'short')
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': `sha256=${signature}`,
-+          'X-Timestamp': timestamp.toString(),
-+          'X-Partner-ID': 'invalid-partner'
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+      
-+      // Clean up
-+      delete process.env.PARTNER_SECRET_INVALID_PARTNER
-+    })
-+
-+    it('handles partner ID with special characters', async () => {
-+      // Set up partner with special characters in ID
-+      process.env.PARTNER_SECRET_SPECIAL_PARTNER_123 = TEST_SECRET
-+      
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': `sha256=${signature}`,
-+          'X-Timestamp': timestamp.toString(),
-+          'X-Partner-ID': 'special-partner-123'
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(true)
-+      
-+      // Clean up
-+      delete process.env.PARTNER_SECRET_SPECIAL_PARTNER_123
-+    })
-+  })
-+
-+  describe('Header Injection Attacks', () => {
-+    it('rejects requests with malformed signature header', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': 'malformed-signature-without-sha256-prefix',
-+          'X-Timestamp': timestamp.toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+
-+    it('rejects requests with non-numeric timestamp', async () => {
-+      const signature = generateHMACSignature('GET', '/api/search', 1705123456, '', TEST_SECRET)
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': `sha256=${signature}`,
-+          'X-Timestamp': 'not-a-number',
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+
-+    it('rejects requests with extremely large timestamp', async () => {
-+      const largeTimestamp = '9999999999999999999' // Way beyond reasonable timestamp
-+      const signature = generateHMACSignature('GET', '/api/search', parseInt(largeTimestamp), '', TEST_SECRET)
-+      
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': `sha256=${signature}`,
-+          'X-Timestamp': largeTimestamp,
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+
-+    it('handles empty header values gracefully', async () => {
-+      const request = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': '',
-+          'X-Timestamp': '',
-+          'X-Partner-ID': ''
-+        }
-+      })
-+      
-+      const authResult = await authenticateSearchRequest(request)
-+      expect(authResult.success).toBe(false)
-+    })
-+  })
-+
-+  describe('Timing Attack Protection', () => {
-+    it('signature verification takes consistent time for valid and invalid signatures', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const validSignature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
-+      const invalidSignature = 'a'.repeat(64) // Invalid but same length
-+      
-+      // Measure time for valid signature
-+      const validStart = performance.now()
-+      const validRequest = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': `sha256=${validSignature}`,
-+          'X-Timestamp': timestamp.toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      await verifyRequestHMAC(validRequest)
-+      const validTime = performance.now() - validStart
-+      
-+      // Measure time for invalid signature
-+      const invalidStart = performance.now()
-+      const invalidRequest = new NextRequest('http://localhost/api/search?q=laptop', {
-+        headers: {
-+          'X-Signature': `sha256=${invalidSignature}`,
-+          'X-Timestamp': timestamp.toString(),
-+          'X-Partner-ID': TEST_PARTNER_ID
-+        }
-+      })
-+      await verifyRequestHMAC(invalidRequest)
-+      const invalidTime = performance.now() - invalidStart
-+      
-+      // Time difference should be minimal (within 50% of each other)
-+      const timeDifference = Math.abs(validTime - invalidTime)
-+      const averageTime = (validTime + invalidTime) / 2
-+      const percentageDifference = (timeDifference / averageTime) * 100
-+      
-+      expect(percentageDifference).toBeLessThan(50)
-+    })
-+  })
-+
-+  describe('Resource Exhaustion Protection', () => {
-+    it('handles multiple concurrent invalid requests efficiently', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      
-+      // Create 50 concurrent invalid requests
-+      const requests = Array.from({ length: 50 }, (_, i) => {
-+        return new NextRequest(`http://localhost/api/search?q=test${i}`, {
-+          headers: {
-+            'X-Signature': `sha256=${'invalid'.repeat(16)}`, // Invalid signature
-+            'X-Timestamp': timestamp.toString(),
-+            'X-Partner-ID': TEST_PARTNER_ID
-+          }
-+        })
-+      })
-+      
-+      const startTime = performance.now()
-+      const results = await Promise.all(requests.map(verifyRequestHMAC))
-+      const endTime = performance.now()
-+      
-+      // All should fail
-+      expect(results.every(result => result === null)).toBe(true)
-+      
-+      // Should complete within reasonable time (less than 1 second for 50 requests)
-+      expect(endTime - startTime).toBeLessThan(1000)
-+    })
-+  })
-+})
-diff --git a/src/__tests__/security/hmac.test.ts b/src/__tests__/security/hmac.test.ts
-new file mode 100644
-index 0000000..0f01208
---- /dev/null
-+++ b/src/__tests__/security/hmac.test.ts
-@@ -0,0 +1,346 @@
-+// src/__tests__/security/hmac.test.ts
-+// Comprehensive test suite for HMAC authentication helper
-+
-+import {
-+  generateHMACSignature,
-+  verifyHMACSignature,
-+  extractHMACFromRequest,
-+  verifyRequestHMAC,
-+  createHMACHeaders,
-+  isAuthenticationEnabled,
-+  isHMACEnabled,
-+  shouldEnforceAuthentication,
-+  clearReplayCache
-+} from '@/lib/security/hmac'
-+import { NextRequest } from 'next/server'
-+
-+// Test configuration
-+const TEST_SECRET = 'test-secret-minimum-32-characters-long'
-+const TEST_PARTNER_ID = 'test-partner'
-+
-+// Setup test environment
-+beforeAll(() => {
-+  process.env.PARTNER_SECRET_TEST_PARTNER = TEST_SECRET
-+  process.env.PARTNER_SECRET_DEFAULT = TEST_SECRET
-+  process.env.HMAC_TIMESTAMP_WINDOW = '300'
-+  process.env.ENABLE_SEARCH_AUTH = 'true'
-+  process.env.ENABLE_HMAC_AUTH = 'true'
-+})
-+
-+afterAll(() => {
-+  delete process.env.PARTNER_SECRET_TEST_PARTNER
-+  delete process.env.PARTNER_SECRET_DEFAULT
-+  delete process.env.HMAC_TIMESTAMP_WINDOW
-+  delete process.env.ENABLE_SEARCH_AUTH
-+  delete process.env.ENABLE_HMAC_AUTH
-+})
-+
-+// Clear replay cache before each test to ensure clean state
-+beforeEach(() => {
-+  clearReplayCache()
-+})
-+
-+describe('HMAC Helper Functions', () => {
-+  describe('generateHMACSignature', () => {
-+    it('generates correct signature for GET request', () => {
-+      const signature = generateHMACSignature(
-+        'GET',
-+        '/api/search',
-+        1705123456,
-+        '',
-+        TEST_SECRET
-+      )
-+      expect(signature).toMatch(/^[a-f0-9]{64}$/)
-+      expect(signature.length).toBe(64)
-+    })
-+    
-+    it('generates correct signature for POST request with body', () => {
-+      const body = JSON.stringify({ query: 'laptop' })
-+      const signature = generateHMACSignature(
-+        'POST',
-+        '/api/search',
-+        1705123456,
-+        body,
-+        TEST_SECRET
-+      )
-+      expect(signature).toMatch(/^[a-f0-9]{64}$/)
-+    })
-+    
-+    it('generates different signatures for different methods', () => {
-+      const getSignature = generateHMACSignature('GET', '/api/search', 1705123456, '', TEST_SECRET)
-+      const postSignature = generateHMACSignature('POST', '/api/search', 1705123456, '', TEST_SECRET)
-+      expect(getSignature).not.toBe(postSignature)
-+    })
-+    
-+    it('generates different signatures for different paths', () => {
-+      const searchSignature = generateHMACSignature('GET', '/api/search', 1705123456, '', TEST_SECRET)
-+      const suggestionsSignature = generateHMACSignature('GET', '/api/search/suggestions', 1705123456, '', TEST_SECRET)
-+      expect(searchSignature).not.toBe(suggestionsSignature)
-+    })
-+    
-+    it('generates different signatures for different timestamps', () => {
-+      const signature1 = generateHMACSignature('GET', '/api/search', 1705123456, '', TEST_SECRET)
-+      const signature2 = generateHMACSignature('GET', '/api/search', 1705123457, '', TEST_SECRET)
-+      expect(signature1).not.toBe(signature2)
-+    })
-+
-+    it('generates different signatures for different bodies', () => {
-+      const signature1 = generateHMACSignature('POST', '/api/search', 1705123456, '', TEST_SECRET)
-+      const signature2 = generateHMACSignature('POST', '/api/search', 1705123456, '{"query":"test"}', TEST_SECRET)
-+      expect(signature1).not.toBe(signature2)
-+    })
-+  })
-+  
-+  describe('verifyHMACSignature', () => {
-+    it('verifies valid signature', () => {
-+      const signature = generateHMACSignature('GET', '/api/search', 1705123456, '', TEST_SECRET)
-+      const isValid = verifyHMACSignature(signature, 'GET', '/api/search', 1705123456, '', TEST_SECRET)
-+      expect(isValid).toBe(true)
-+    })
-+    
-+    it('rejects invalid signature', () => {
-+      const isValid = verifyHMACSignature('invalid-signature', 'GET', '/api/search', 1705123456, '', TEST_SECRET)
-+      expect(isValid).toBe(false)
-+    })
-+    
-+    it('rejects signature with wrong secret', () => {
-+      const signature = generateHMACSignature('GET', '/api/search', 1705123456, '', 'secret1')
-+      const isValid = verifyHMACSignature(signature, 'GET', '/api/search', 1705123456, '', 'secret2')
-+      expect(isValid).toBe(false)
-+    })
-+
-+    it('rejects signature with wrong method', () => {
-+      const signature = generateHMACSignature('GET', '/api/search', 1705123456, '', TEST_SECRET)
-+      const isValid = verifyHMACSignature(signature, 'POST', '/api/search', 1705123456, '', TEST_SECRET)
-+      expect(isValid).toBe(false)
-+    })
-+
-+    it('rejects signature with wrong path', () => {
-+      const signature = generateHMACSignature('GET', '/api/search', 1705123456, '', TEST_SECRET)
-+      const isValid = verifyHMACSignature(signature, 'GET', '/api/different', 1705123456, '', TEST_SECRET)
-+      expect(isValid).toBe(false)
-+    })
-+  })
-+  
-+  describe('extractHMACFromRequest', () => {
-+    it('extracts valid HMAC data from request', () => {
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': 'sha256=abc123',
-+          'x-timestamp': '1705123456',
-+          'x-partner-id': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const hmacData = extractHMACFromRequest(request)
-+      expect(hmacData).toEqual({
-+        signature: 'abc123',
-+        timestamp: 1705123456,
-+        partnerId: TEST_PARTNER_ID
-+      })
-+    })
-+    
-+    it('returns null for missing headers', () => {
-+      const request = new NextRequest('http://localhost/api/search')
-+      const hmacData = extractHMACFromRequest(request)
-+      expect(hmacData).toBeNull()
-+    })
-+    
-+    it('handles sha256= prefix in signature', () => {
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': 'sha256=abc123',
-+          'x-timestamp': '1705123456',
-+          'x-partner-id': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const hmacData = extractHMACFromRequest(request)
-+      expect(hmacData?.signature).toBe('abc123')
-+    })
-+
-+    it('handles signature without sha256= prefix', () => {
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': 'abc123',
-+          'x-timestamp': '1705123456',
-+          'x-partner-id': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const hmacData = extractHMACFromRequest(request)
-+      expect(hmacData?.signature).toBe('abc123')
-+    })
-+
-+    it('returns null for invalid timestamp', () => {
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': 'abc123',
-+          'x-timestamp': 'invalid',
-+          'x-partner-id': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const hmacData = extractHMACFromRequest(request)
-+      expect(hmacData).toBeNull()
-+    })
-+  })
-+  
-+  describe('verifyRequestHMAC', () => {
-+    it('verifies valid HMAC request', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
-+      
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': `sha256=${signature}`,
-+          'x-timestamp': timestamp.toString(),
-+          'x-partner-id': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const payload = await verifyRequestHMAC(request)
-+      expect(payload).toEqual({
-+        partnerId: TEST_PARTNER_ID,
-+        timestamp,
-+        method: 'GET',
-+        path: '/api/search',
-+        isValid: true
-+      })
-+    })
-+    
-+    it('rejects expired timestamp', async () => {
-+      const expiredTimestamp = Math.floor(Date.now() / 1000) - 400 // 400 seconds ago
-+      const signature = generateHMACSignature('GET', '/api/search', expiredTimestamp, '', TEST_SECRET)
-+      
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': `sha256=${signature}`,
-+          'x-timestamp': expiredTimestamp.toString(),
-+          'x-partner-id': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const payload = await verifyRequestHMAC(request)
-+      expect(payload).toBeNull()
-+    })
-+    
-+    it('rejects unknown partner', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', 'unknown-secret')
-+      
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': `sha256=${signature}`,
-+          'x-timestamp': timestamp.toString(),
-+          'x-partner-id': 'unknown-partner'
-+        }
-+      })
-+      
-+      const payload = await verifyRequestHMAC(request)
-+      expect(payload).toBeNull()
-+    })
-+
-+    it('rejects future timestamp', async () => {
-+      const futureTimestamp = Math.floor(Date.now() / 1000) + 400 // 400 seconds in future
-+      const signature = generateHMACSignature('GET', '/api/search', futureTimestamp, '', TEST_SECRET)
-+      
-+      const request = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': `sha256=${signature}`,
-+          'x-timestamp': futureTimestamp.toString(),
-+          'x-partner-id': TEST_PARTNER_ID
-+        }
-+      })
-+      
-+      const payload = await verifyRequestHMAC(request)
-+      expect(payload).toBeNull()
-+    })
-+
-+    it('detects replay attacks', async () => {
-+      const timestamp = Math.floor(Date.now() / 1000)
-+      const signature = generateHMACSignature('GET', '/api/search', timestamp, '', TEST_SECRET)
-+
-+      const request1 = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': `sha256=${signature}`,
-+          'x-timestamp': timestamp.toString(),
-+          'x-partner-id': TEST_PARTNER_ID
-+        }
-+      })
-+
-+      const request2 = new NextRequest('http://localhost/api/search', {
-+        headers: {
-+          'x-signature': `sha256=${signature}`,
-+          'x-timestamp': timestamp.toString(),
-+          'x-partner-id': TEST_PARTNER_ID
-+        }
-+      })
-+
-+      // First request should succeed
-+      const payload1 = await verifyRequestHMAC(request1)
-+      expect(payload1).not.toBeNull()
-+
-+      // Second identical request should be rejected as replay
-+      const payload2 = await verifyRequestHMAC(request2)
-+      expect(payload2).toBeNull()
-+    })
-+  })
-+  
-+  describe('createHMACHeaders', () => {
-+    it('creates valid HMAC headers', () => {
-+      const headers = createHMACHeaders('GET', '/api/search', TEST_PARTNER_ID, '')
-+      
-+      expect(headers).toHaveProperty('X-Signature')
-+      expect(headers).toHaveProperty('X-Timestamp')
-+      expect(headers).toHaveProperty('X-Partner-ID')
-+      expect(headers['X-Partner-ID']).toBe(TEST_PARTNER_ID)
-+      expect(headers['X-Signature']).toMatch(/^sha256=[a-f0-9]{64}$/)
-+      expect(headers['X-Timestamp']).toMatch(/^\d+$/)
-+    })
-+
-+    it('throws error for unknown partner', () => {
-+      // Temporarily remove default secret to test error case
-+      const originalDefault = process.env.PARTNER_SECRET_DEFAULT
-+      delete process.env.PARTNER_SECRET_DEFAULT
-+
-+      expect(() => {
-+        createHMACHeaders('GET', '/api/search', 'unknown-partner', '')
-+      }).toThrow('No secret available for partner: unknown-partner')
-+
-+      // Restore default secret
-+      if (originalDefault) {
-+        process.env.PARTNER_SECRET_DEFAULT = originalDefault
-+      }
-+    })
-+  })
-+
-+  describe('Feature Flags', () => {
-+    it('isAuthenticationEnabled returns correct value', () => {
-+      expect(isAuthenticationEnabled()).toBe(true)
-+      
-+      process.env.ENABLE_SEARCH_AUTH = 'false'
-+      expect(isAuthenticationEnabled()).toBe(false)
-+      
-+      process.env.ENABLE_SEARCH_AUTH = 'true'
-+    })
-+
-+    it('isHMACEnabled returns correct value', () => {
-+      expect(isHMACEnabled()).toBe(true)
-+      
-+      process.env.ENABLE_HMAC_AUTH = 'false'
-+      expect(isHMACEnabled()).toBe(false)
-+      
-+      process.env.ENABLE_HMAC_AUTH = 'true'
-+    })
-+
-+    it('shouldEnforceAuthentication respects feature flag', () => {
-+      expect(shouldEnforceAuthentication('/api/search')).toBe(true)
-+      
-+      process.env.ENABLE_SEARCH_AUTH = 'false'
-+      expect(shouldEnforceAuthentication('/api/search')).toBe(false)
-+      
-+      process.env.ENABLE_SEARCH_AUTH = 'true'
-+    })
-+  })
-+})
-diff --git a/src/app/api/search/more/route.ts b/src/app/api/search/more/route.ts
-index f8bcd08..ed51729 100644
---- a/src/app/api/search/more/route.ts
-+++ b/src/app/api/search/more/route.ts
-@@ -3,13 +3,15 @@
- // This file defines the API endpoint for fetching additional pages of search results
- // for the "Load More" functionality on the search page.
- 
--import { NextResponse } from 'next/server';
-+import { NextRequest, NextResponse } from 'next/server';
- import { searchProducts } from '@/lib/data/products';
- import { TransformedProduct } from '@/lib/data/types';
- import { logger } from '@/lib/utils/logger';
- import { withTimeout, TIMEOUT_CONFIG } from '@/lib/timeoutConfig';
- import { getCacheMetrics } from '@/lib/cache/searchCache';
- import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';
-+import { applyRateLimit, rateLimits } from '@/lib/rateLimiter';
-+import { authenticateSearchRequest, createSearchUnauthorizedResponse } from '@/lib/security/auth-middleware';
- 
- // Default number of items per page
- const DEFAULT_PAGE_SIZE = 20;
-@@ -19,10 +21,30 @@ const DEFAULT_PAGE_SIZE = 20;
-  * This endpoint is designed to be called from the client-side to dynamically
-  * load more products without a full page refresh.
-  */
--export async function GET(request: Request) {
-+export async function GET(request: NextRequest) {
-   const requestStartTime = Date.now();
-   const requestId = Math.random().toString(36).substring(2, 10);
--  
-+
-+  // Apply rate limiting first
-+  const rateLimitResponse = applyRateLimit(request, rateLimits.search)
-+  if (rateLimitResponse) {
-+    return rateLimitResponse
-+  }
-+
-+  // Apply authentication
-+  const authResult = await authenticateSearchRequest(request)
-+  if (!authResult.success) {
-+    console.warn(`Search More API unauthorized access attempt from ${request.headers.get('x-forwarded-for') || 'unknown'}`)
-+    return createSearchUnauthorizedResponse(authResult.traceId)
-+  }
-+
-+  // Log successful authentication
-+  console.log(`Search More API access: ${authResult.method} authentication successful`, {
-+    endpoint: '/api/search/more',
-+    method: 'GET',
-+    traceId: authResult.traceId
-+  })
-+
-   try {
-     // Extract search parameters from the request URL
-     const { searchParams, pathname } = new URL(request.url);
-diff --git a/src/app/api/search/route.ts b/src/app/api/search/route.ts
-index d9ee03d..b331b2f 100644
---- a/src/app/api/search/route.ts
-+++ b/src/app/api/search/route.ts
-@@ -19,7 +19,8 @@ import type { SearchFilters, ApiResponse } from '@/lib/data/types'
- import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'
- import { validatePaginationParams, validateSearchQuery, validateFilterParams } from '@/lib/utils'
- import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'
--import { searchApiSchema, validateInput, createValidationErrorResponse } from '@/lib/validation/schemas';
-+import { searchApiSchema, validateInput, createValidationErrorResponse } from '@/lib/validation/schemas'
-+import { authenticateSearchRequest, createSearchUnauthorizedResponse } from '@/lib/security/auth-middleware'
- 
- /**
-  * Legacy response format for backward compatibility
-@@ -103,12 +104,26 @@ function isDebugEnabled(): boolean {
-  * - limit: Items per page (default: 20, max: 50)
-  */
- export async function GET(request: NextRequest): Promise<NextResponse<LegacySearchResponse>> {
--  // Apply rate limiting
-+  // Apply rate limiting first
-   const rateLimitResponse = applyRateLimit(request, rateLimits.search)
-   if (rateLimitResponse) {
-     return rateLimitResponse as NextResponse<LegacySearchResponse>
-   }
- 
-+  // Apply authentication
-+  const authResult = await authenticateSearchRequest(request)
-+  if (!authResult.success) {
-+    console.warn(`Search API unauthorized access attempt from ${request.headers.get('x-forwarded-for') || 'unknown'}`)
-+    return createSearchUnauthorizedResponse(authResult.traceId) as NextResponse<LegacySearchResponse>
-+  }
-+
-+  // Log successful authentication
-+  console.log(`Search API access: ${authResult.method} authentication successful`, {
-+    endpoint: '/api/search',
-+    method: 'GET',
-+    traceId: authResult.traceId
-+  })
-+
-   const debugData: DebugData = {
-     timing: {
-       start: Date.now(),
-diff --git a/src/app/api/search/suggestions/route.ts b/src/app/api/search/suggestions/route.ts
-index 3517f66..4390eeb 100644
---- a/src/app/api/search/suggestions/route.ts
-+++ b/src/app/api/search/suggestions/route.ts
-@@ -19,7 +19,8 @@ import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'
- import { applyRateLimit, rateLimits } from '@/lib/rateLimiter'
- import { validateSearchQuery } from '@/lib/utils'
- import type { SupabaseClient } from '@supabase/supabase-js'
--import { searchSuggestionsSchema, validateInput, createValidationErrorResponse } from '@/lib/validation/schemas';
-+import { searchSuggestionsSchema, validateInput, createValidationErrorResponse } from '@/lib/validation/schemas'
-+import { authenticateSearchRequest, createSearchUnauthorizedResponse } from '@/lib/security/auth-middleware'
- 
- /**
-  * Enhanced response format with routing data
-@@ -99,12 +100,26 @@ async function getEnhancedSearchSuggestions(supabase: SupabaseClient, query: str
- export async function GET(request: NextRequest): Promise<NextResponse<SearchSuggestionsResponse>> {
-   const startTime = Date.now()
- 
--  // Apply rate limiting
-+  // Apply rate limiting first
-   const rateLimitResponse = applyRateLimit(request, rateLimits.search)
-   if (rateLimitResponse) {
-     return rateLimitResponse as NextResponse<SearchSuggestionsResponse>
-   }
- 
-+  // Apply authentication
-+  const authResult = await authenticateSearchRequest(request)
-+  if (!authResult.success) {
-+    console.warn(`Search Suggestions API unauthorized access attempt from ${request.headers.get('x-forwarded-for') || 'unknown'}`)
-+    return createSearchUnauthorizedResponse(authResult.traceId) as NextResponse<SearchSuggestionsResponse>
-+  }
-+
-+  // Log successful authentication
-+  console.log(`Search Suggestions API access: ${authResult.method} authentication successful`, {
-+    endpoint: '/api/search/suggestions',
-+    method: 'GET',
-+    traceId: authResult.traceId
-+  })
-+
-   try {
-     // Parse and validate query parameters
-     const { searchParams } = new URL(request.url)
-diff --git a/src/lib/security/auth-middleware.ts b/src/lib/security/auth-middleware.ts
-new file mode 100644
-index 0000000..365abdb
---- /dev/null
-+++ b/src/lib/security/auth-middleware.ts
-@@ -0,0 +1,213 @@
-+// src/lib/security/auth-middleware.ts
-+// Dual authentication middleware for search endpoints (JWT + HMAC)
-+
-+import { NextRequest, NextResponse } from 'next/server'
-+import { verifyRequestJWT, type JWTPayload } from './jwt'
-+import { 
-+  verifyRequestHMAC, 
-+  shouldEnforceAuthentication, 
-+  isHMACEnabled,
-+  generateTraceId,
-+  logSecurityEvent,
-+  type HMACPayload 
-+} from './hmac'
-+
-+// Authentication result interface
-+export interface AuthResult {
-+  success: boolean
-+  method: 'JWT' | 'HMAC' | null
-+  payload: JWTPayload | HMACPayload | null
-+  error?: string
-+  traceId: string
-+}
-+
-+// Get client IP from request
-+function getClientIP(request: NextRequest): string {
-+  const forwarded = request.headers.get('x-forwarded-for')
-+  const realIP = request.headers.get('x-real-ip')
-+  const cfIP = request.headers.get('cf-connecting-ip')
-+  
-+  return cfIP || realIP || forwarded?.split(',')[0].trim() || 'unknown'
-+}
-+
-+// Dual authentication check for search endpoints
-+export async function authenticateSearchRequest(request: NextRequest): Promise<AuthResult> {
-+  const traceId = generateTraceId(request.method)
-+  const endpoint = new URL(request.url).pathname
-+  const ip = getClientIP(request)
-+  
-+  // Check feature flags first
-+  if (!shouldEnforceAuthentication(endpoint)) {
-+    return { 
-+      success: true, 
-+      method: null, 
-+      payload: null, 
-+      traceId 
-+    }
-+  }
-+  
-+  // Try JWT first (browser users with CAPTCHA)
-+  try {
-+    const jwtPayload = await verifyRequestJWT(request)
-+    if (jwtPayload) {
-+      logSecurityEvent({
-+        type: 'JWT_AUTH_SUCCESS',
-+        endpoint,
-+        method: request.method,
-+        ip,
-+        timestamp: new Date().toISOString(),
-+        traceId
-+      })
-+      
-+      return {
-+        success: true,
-+        method: 'JWT',
-+        payload: jwtPayload,
-+        traceId
-+      }
-+    }
-+  } catch (error) {
-+    console.warn('JWT verification error:', error)
-+  }
-+  
-+  // Try HMAC second (API partners) - only if enabled
-+  if (isHMACEnabled()) {
-+    try {
-+      const hmacPayload = await verifyRequestHMAC(request)
-+      if (hmacPayload) {
-+        logSecurityEvent({
-+          type: 'HMAC_AUTH_SUCCESS',
-+          endpoint,
-+          method: request.method,
-+          partnerId: hmacPayload.partnerId,
-+          ip,
-+          timestamp: new Date().toISOString(),
-+          traceId
-+        })
-+        
-+        return {
-+          success: true,
-+          method: 'HMAC',
-+          payload: hmacPayload,
-+          traceId
-+        }
-+      }
-+    } catch (error) {
-+      console.warn('HMAC verification error:', error)
-+    }
-+  }
-+  
-+  // Log authentication failure
-+  logSecurityEvent({
-+    type: 'HMAC_AUTH_FAILURE',
-+    endpoint,
-+    method: request.method,
-+    ip,
-+    timestamp: new Date().toISOString(),
-+    traceId,
-+    error: 'MISSING_AUTH',
-+    errorMessage: 'No valid authentication found'
-+  })
-+  
-+  return {
-+    success: false,
-+    method: null,
-+    payload: null,
-+    error: 'No valid authentication found',
-+    traceId
-+  }
-+}
-+
-+// Create consistent unauthorized response for search endpoints
-+export function createSearchUnauthorizedResponse(traceId: string): NextResponse {
-+  return NextResponse.json(
-+    {
-+      error: 'Unauthorized',
-+      message: 'Valid authentication required. Use JWT (browser) or HMAC (API) authentication.',
-+      code: 'AUTH_REQUIRED',
-+      traceId,
-+      supportedMethods: ['JWT', 'HMAC'],
-+      documentation: {
-+        jwt: 'For browser users: Complete CAPTCHA to receive JWT token',
-+        hmac: 'For API partners: Use HMAC-SHA256 signature authentication',
-+        guide: 'See partner integration guide for HMAC implementation examples'
-+      }
-+    },
-+    {
-+      status: 401,
-+      headers: {
-+        'Content-Type': 'application/json',
-+        'X-Trace-ID': traceId,
-+        'WWW-Authenticate': 'Bearer realm="Search API", HMAC realm="Partner API"'
-+      }
-+    }
-+  )
-+}
-+
-+// Middleware wrapper for search endpoints
-+export async function withSearchAuthentication(
-+  request: NextRequest,
-+  handler: (request: NextRequest, authResult: AuthResult) => Promise<NextResponse>
-+): Promise<NextResponse> {
-+  // Apply authentication
-+  const authResult = await authenticateSearchRequest(request)
-+  
-+  if (!authResult.success) {
-+    console.warn(`Search API unauthorized access attempt from ${getClientIP(request)}`)
-+    return createSearchUnauthorizedResponse(authResult.traceId)
-+  }
-+  
-+  // Log successful authentication
-+  console.log(`Search API access: ${authResult.method} authentication successful`, {
-+    endpoint: new URL(request.url).pathname,
-+    method: request.method,
-+    traceId: authResult.traceId,
-+    partnerId: authResult.method === 'HMAC' ? (authResult.payload as HMACPayload)?.partnerId : undefined
-+  })
-+  
-+  // Call the actual handler with authentication result
-+  return handler(request, authResult)
-+}
-+
-+// Helper to check if request has authentication headers
-+export function hasAuthenticationHeaders(request: NextRequest): boolean {
-+  // Check for JWT in Authorization header or cookie
-+  const authHeader = request.headers.get('authorization')
-+  const authCookie = request.cookies.get('auth-token')
-+
-+  if (authHeader?.startsWith('Bearer ') || authCookie) {
-+    return true
-+  }
-+
-+  // Check for HMAC headers using constants
-+  const signature = request.headers.get('x-signature')
-+  const timestamp = request.headers.get('x-timestamp')
-+  const partnerId = request.headers.get('x-partner-id')
-+
-+  return !!(signature && timestamp && partnerId)
-+}
-+
-+// Helper to determine authentication method from request
-+export function getAuthenticationMethod(request: NextRequest): 'JWT' | 'HMAC' | 'NONE' {
-+  // Check for JWT first
-+  const authHeader = request.headers.get('authorization')
-+  const authCookie = request.cookies.get('auth-token')
-+  
-+  if (authHeader?.startsWith('Bearer ') || authCookie) {
-+    return 'JWT'
-+  }
-+  
-+  // Check for HMAC headers
-+  const signature = request.headers.get('x-signature')
-+  const timestamp = request.headers.get('x-timestamp')
-+  const partnerId = request.headers.get('x-partner-id')
-+  
-+  if (signature && timestamp && partnerId) {
-+    return 'HMAC'
-+  }
-+  
-+  return 'NONE'
-+}
-+
-+// Export types for use in other modules
-+export type { JWTPayload, HMACPayload }
-diff --git a/src/lib/security/hmac.ts b/src/lib/security/hmac.ts
-new file mode 100644
-index 0000000..ee31816
---- /dev/null
-+++ b/src/lib/security/hmac.ts
-@@ -0,0 +1,548 @@
-+// src/lib/security/hmac.ts
-+// HMAC authentication helper with replay protection and partner management
-+
-+import crypto from 'crypto'
-+import { NextRequest, NextResponse } from 'next/server'
-+
-+// Configuration constants
-+const HMAC_ALGORITHM = 'sha256'
-+const DEFAULT_TIMESTAMP_WINDOW = 300 // 5 minutes
-+const MIN_SECRET_LENGTH = 32
-+
-+// Header constants to avoid typos
-+export const HMAC_HEADERS = {
-+  SIGNATURE: 'x-signature',
-+  TIMESTAMP: 'x-timestamp',
-+  PARTNER_ID: 'x-partner-id',
-+  NONCE: 'x-nonce',
-+  VERSION: 'x-version'
-+} as const
-+
-+// Supported API version
-+const SUPPORTED_API_VERSION = '1.0'
-+
-+// TypeScript interfaces
-+export interface HMACData {
-+  signature: string
-+  timestamp: number
-+  partnerId: string
-+  nonce?: string
-+  version?: string
-+}
-+
-+export interface HMACPayload {
-+  partnerId: string
-+  timestamp: number
-+  method: string
-+  path: string
-+  isValid: boolean
-+  nonce?: string
-+}
-+
-+export interface HMACConfig {
-+  algorithm: 'sha256'
-+  timestampWindow: number
-+  encoding: 'hex'
-+  requireNonce: boolean
-+  enableReplayProtection: boolean
-+}
-+
-+export type HMACError = 
-+  | 'MISSING_SIGNATURE'
-+  | 'MISSING_TIMESTAMP' 
-+  | 'MISSING_PARTNER_ID'
-+  | 'INVALID_SIGNATURE'
-+  | 'EXPIRED_TIMESTAMP'
-+  | 'UNKNOWN_PARTNER'
-+  | 'INVALID_FORMAT'
-+  | 'REPLAY_DETECTED'
-+  | 'BODY_HASH_MISMATCH'
-+
-+export interface HMACValidationResult {
-+  isValid: boolean
-+  error?: HMACError
-+  partnerId?: string
-+  timestamp?: number
-+}
-+
-+// In-memory replay protection (MVP implementation)
-+// Note: For production scale, migrate to Redis
-+const recentRequests = new Map<string, number>()
-+
-+// Export for testing purposes
-+export function clearReplayCache(): void {
-+  recentRequests.clear()
-+}
-+
-+// Boot-time secret validation
-+export function validateHMACConfiguration(): void {
-+  const errors: string[] = []
-+
-+  // Check if any partner secrets are configured
-+  const partnerSecrets = Object.keys(process.env)
-+    .filter(key => key.startsWith('PARTNER_SECRET_'))
-+    .map(key => ({ key, value: process.env[key] }))
-+
-+  if (partnerSecrets.length === 0) {
-+    errors.push('No PARTNER_SECRET_* environment variables found. At least PARTNER_SECRET_DEFAULT is required.')
-+  }
-+
-+  // Validate each secret length
-+  partnerSecrets.forEach(({ key, value }) => {
-+    if (!value) {
-+      errors.push(`${key} is defined but empty`)
-+    } else if (value.length < MIN_SECRET_LENGTH) {
-+      errors.push(`${key} is too short (${value.length} chars, minimum ${MIN_SECRET_LENGTH})`)
-+    }
-+  })
-+
-+  // Validate timestamp window
-+  const timestampWindow = process.env.HMAC_TIMESTAMP_WINDOW
-+  if (timestampWindow && (isNaN(parseInt(timestampWindow)) || parseInt(timestampWindow) <= 0)) {
-+    errors.push(`HMAC_TIMESTAMP_WINDOW must be a positive number, got: ${timestampWindow}`)
-+  }
-+
-+  if (errors.length > 0) {
-+    console.error('HMAC Configuration Validation Failed:')
-+    errors.forEach(error => console.error(`  - ${error}`))
-+    throw new Error(`HMAC configuration validation failed: ${errors.length} error(s) found`)
-+  }
-+
-+  console.log(`HMAC configuration validated: ${partnerSecrets.length} partner secret(s) configured`)
-+}
-+
-+// Call validation at module load (fail fast)
-+if (process.env.NODE_ENV !== 'test') {
-+  try {
-+    validateHMACConfiguration()
-+  } catch (error) {
-+    console.error('HMAC validation failed at startup:', error)
-+    // In production, we want to fail fast
-+    if (process.env.NODE_ENV === 'production') {
-+      process.exit(1)
-+    }
-+  }
-+}
-+
-+// Feature flag helpers
-+export function isAuthenticationEnabled(): boolean {
-+  return process.env.ENABLE_SEARCH_AUTH === 'true'
-+}
-+
-+export function isHMACEnabled(): boolean {
-+  return process.env.ENABLE_HMAC_AUTH === 'true'
-+}
-+
-+export function shouldEnforceAuthentication(endpoint: string): boolean {
-+  if (!isAuthenticationEnabled()) {
-+    console.log(`Authentication disabled via feature flag for ${endpoint}`)
-+    return false
-+  }
-+  return true
-+}
-+
-+// Core HMAC functions
-+export function generateHMACSignature(
-+  method: string,
-+  path: string,
-+  timestamp: number,
-+  body: string = '',
-+  secret: string
-+): string {
-+  // Create SHA256 hash of request body
-+  const bodyHash = crypto.createHash('sha256').update(body).digest('hex')
-+  
-+  // Create message string for signing
-+  const message = `${method}\n${path}\n${timestamp}\n${bodyHash}`
-+  
-+  // Generate HMAC signature
-+  return crypto
-+    .createHmac(HMAC_ALGORITHM, secret)
-+    .update(message)
-+    .digest('hex')
-+}
-+
-+export function verifyHMACSignature(
-+  signature: string,
-+  method: string,
-+  path: string,
-+  timestamp: number,
-+  body: string = '',
-+  secret: string
-+): boolean {
-+  try {
-+    const expectedSignature = generateHMACSignature(method, path, timestamp, body, secret)
-+
-+    // Ensure both signatures are the same length before comparison
-+    if (signature.length !== expectedSignature.length) {
-+      return false
-+    }
-+
-+    // Use constant-time comparison to prevent timing attacks
-+    return crypto.timingSafeEqual(
-+      Buffer.from(signature, 'hex'),
-+      Buffer.from(expectedSignature, 'hex')
-+    )
-+  } catch (error) {
-+    console.warn('HMAC signature verification error:', error)
-+    return false
-+  }
-+}
-+
-+// Request processing functions
-+export function extractHMACFromRequest(request: NextRequest): HMACData | null {
-+  const signature = request.headers.get(HMAC_HEADERS.SIGNATURE)
-+  const timestamp = request.headers.get(HMAC_HEADERS.TIMESTAMP)
-+  const partnerId = request.headers.get(HMAC_HEADERS.PARTNER_ID)
-+
-+  if (!signature || !timestamp || !partnerId) {
-+    return null
-+  }
-+
-+  // Remove 'sha256=' prefix if present
-+  const cleanSignature = signature.replace(/^sha256=/, '')
-+
-+  // Validate timestamp format
-+  const timestampNum = parseInt(timestamp, 10)
-+  if (isNaN(timestampNum)) {
-+    return null
-+  }
-+
-+  return {
-+    signature: cleanSignature,
-+    timestamp: timestampNum,
-+    partnerId,
-+    nonce: request.headers.get(HMAC_HEADERS.NONCE) || undefined,
-+    version: request.headers.get(HMAC_HEADERS.VERSION) || undefined
-+  }
-+}
-+
-+// Partner secret management
-+function getPartnerSecret(partnerId: string): string | null {
-+  // Try partner-specific secret first
-+  const secretKey = `PARTNER_SECRET_${partnerId.toUpperCase().replace(/[^A-Z0-9]/g, '_')}`
-+  let secret = process.env[secretKey]
-+  
-+  // Fall back to default secret if partner-specific not found
-+  if (!secret) {
-+    secret = process.env.PARTNER_SECRET_DEFAULT
-+  }
-+  
-+  if (!secret) {
-+    console.error(`No secret found for partner: ${partnerId}`)
-+    return null
-+  }
-+  
-+  if (secret.length < MIN_SECRET_LENGTH) {
-+    console.error(`Partner secret too short for: ${partnerId} (minimum ${MIN_SECRET_LENGTH} characters)`)
-+    return null
-+  }
-+  
-+  return secret
-+}
-+
-+// Get request body for signature verification
-+async function getRequestBody(request: NextRequest): Promise<string> {
-+  try {
-+    if (request.method === 'GET' || request.method === 'HEAD') {
-+      return ''
-+    }
-+    
-+    // Clone request to avoid consuming the body
-+    const clonedRequest = request.clone()
-+    const contentType = clonedRequest.headers.get('content-type') || ''
-+    
-+    if (contentType.includes('application/json')) {
-+      const body = await clonedRequest.json()
-+      return JSON.stringify(body)
-+    }
-+    
-+    if (contentType.includes('application/x-www-form-urlencoded')) {
-+      const formData = await clonedRequest.formData()
-+      return new URLSearchParams(formData as any).toString()
-+    }
-+    
-+    // For other content types, get raw text
-+    return await clonedRequest.text()
-+  } catch (error) {
-+    console.warn('Failed to parse request body for HMAC verification:', error)
-+    return ''
-+  }
-+}
-+
-+// Replay protection
-+function isReplayRequest(signature: string, timestamp: number): boolean {
-+  const key = `${signature}-${timestamp}`
-+  const now = Math.floor(Date.now() / 1000)
-+  
-+  // Clean old entries (older than timestamp window)
-+  const timestampWindow = parseInt(process.env.HMAC_TIMESTAMP_WINDOW || DEFAULT_TIMESTAMP_WINDOW.toString(), 10)
-+  for (const [existingKey, existingTime] of recentRequests.entries()) {
-+    if (now - existingTime > timestampWindow) {
-+      recentRequests.delete(existingKey)
-+    }
-+  }
-+  
-+  // Check if this request was seen before
-+  if (recentRequests.has(key)) {
-+    return true // Replay detected
-+  }
-+  
-+  // Store this request
-+  recentRequests.set(key, timestamp)
-+  return false
-+}
-+
-+// Generate trace ID for request correlation
-+export function generateTraceId(method?: string): string {
-+  const timestamp = Date.now()
-+  const random = Math.random().toString(36).substring(2, 9)
-+  const methodPrefix = method ? `${method.toLowerCase()}-` : ''
-+  const shortHash = timestamp.toString(36).substring(-4) // Last 4 chars of base36 timestamp
-+  return `hmac-${methodPrefix}${shortHash}-${random}`
-+}
-+
-+// Enhanced request verification with detailed error tracking
-+export async function verifyRequestHMAC(request: NextRequest): Promise<HMACPayload | null> {
-+  const hmacData = extractHMACFromRequest(request)
-+  if (!hmacData) {
-+    return null
-+  }
-+
-+  // Validate API version if provided
-+  if (hmacData.version && hmacData.version !== SUPPORTED_API_VERSION) {
-+    console.warn(`Unsupported API version: ${hmacData.version}, supported: ${SUPPORTED_API_VERSION}`)
-+    return null
-+  }
-+
-+  // Validate timestamp window
-+  const now = Math.floor(Date.now() / 1000)
-+  const timestampWindow = parseInt(process.env.HMAC_TIMESTAMP_WINDOW || DEFAULT_TIMESTAMP_WINDOW.toString(), 10)
-+
-+  if (Math.abs(now - hmacData.timestamp) > timestampWindow) {
-+    console.warn(`HMAC timestamp expired: ${hmacData.timestamp}, now: ${now}, window: ${timestampWindow}s, skew: ${now - hmacData.timestamp}s`)
-+    return null
-+  }
-+
-+  // Check for replay attacks
-+  if (isReplayRequest(hmacData.signature, hmacData.timestamp)) {
-+    console.warn(`Replay attack detected for partner: ${hmacData.partnerId}`)
-+    return null
-+  }
-+
-+  // Get partner secret
-+  const partnerSecret = getPartnerSecret(hmacData.partnerId)
-+  if (!partnerSecret) {
-+    console.warn(`Unknown partner ID: ${hmacData.partnerId}`)
-+    return null
-+  }
-+
-+  // Get request body for signature verification
-+  const body = await getRequestBody(request)
-+  const { pathname } = new URL(request.url)
-+
-+  // Verify signature
-+  const isValid = verifyHMACSignature(
-+    hmacData.signature,
-+    request.method,
-+    pathname,
-+    hmacData.timestamp,
-+    body,
-+    partnerSecret
-+  )
-+
-+  if (!isValid) {
-+    console.warn(`HMAC signature verification failed for partner: ${hmacData.partnerId}`)
-+    return null
-+  }
-+
-+  return {
-+    partnerId: hmacData.partnerId,
-+    timestamp: hmacData.timestamp,
-+    method: request.method,
-+    path: pathname,
-+    isValid: true,
-+    nonce: hmacData.nonce
-+  }
-+}
-+
-+// Enhanced verification with specific error codes
-+export async function verifyRequestHMACWithError(request: NextRequest): Promise<{ success: boolean; error?: HMACError; payload?: HMACPayload }> {
-+  const hmacData = extractHMACFromRequest(request)
-+  if (!hmacData) {
-+    return { success: false, error: 'MISSING_SIGNATURE' }
-+  }
-+
-+  // Validate API version if provided
-+  if (hmacData.version && hmacData.version !== SUPPORTED_API_VERSION) {
-+    return { success: false, error: 'INVALID_FORMAT' }
-+  }
-+
-+  // Validate timestamp window
-+  const now = Math.floor(Date.now() / 1000)
-+  const timestampWindow = parseInt(process.env.HMAC_TIMESTAMP_WINDOW || DEFAULT_TIMESTAMP_WINDOW.toString(), 10)
-+
-+  if (Math.abs(now - hmacData.timestamp) > timestampWindow) {
-+    return { success: false, error: 'EXPIRED_TIMESTAMP' }
-+  }
-+
-+  // Check for replay attacks
-+  if (isReplayRequest(hmacData.signature, hmacData.timestamp)) {
-+    return { success: false, error: 'REPLAY_DETECTED' }
-+  }
-+
-+  // Get partner secret
-+  const partnerSecret = getPartnerSecret(hmacData.partnerId)
-+  if (!partnerSecret) {
-+    return { success: false, error: 'UNKNOWN_PARTNER' }
-+  }
-+
-+  // Get request body for signature verification
-+  const body = await getRequestBody(request)
-+  const { pathname } = new URL(request.url)
-+
-+  // Verify signature
-+  const isValid = verifyHMACSignature(
-+    hmacData.signature,
-+    request.method,
-+    pathname,
-+    hmacData.timestamp,
-+    body,
-+    partnerSecret
-+  )
-+
-+  if (!isValid) {
-+    return { success: false, error: 'INVALID_SIGNATURE' }
-+  }
-+
-+  const payload: HMACPayload = {
-+    partnerId: hmacData.partnerId,
-+    timestamp: hmacData.timestamp,
-+    method: request.method,
-+    path: pathname,
-+    isValid: true,
-+    nonce: hmacData.nonce
-+  }
-+
-+  return { success: true, payload }
-+}
-+
-+// Create HMAC headers for outgoing requests
-+export function createHMACHeaders(
-+  method: string,
-+  path: string,
-+  partnerId: string,
-+  body: string = '',
-+  secret?: string,
-+  options?: { includeVersion?: boolean; nonce?: string }
-+): Record<string, string> {
-+  const timestamp = Math.floor(Date.now() / 1000)
-+
-+  // Use provided secret or get from environment
-+  let partnerSecret = secret
-+  if (!partnerSecret) {
-+    partnerSecret = getPartnerSecret(partnerId)
-+    if (!partnerSecret) {
-+      throw new Error(`No secret available for partner: ${partnerId}`)
-+    }
-+  }
-+
-+  const signature = generateHMACSignature(method, path, timestamp, body, partnerSecret)
-+
-+  const headers: Record<string, string> = {
-+    'X-Signature': `sha256=${signature}`,
-+    'X-Timestamp': timestamp.toString(),
-+    'X-Partner-ID': partnerId,
-+    'Content-Type': 'application/json'
-+  }
-+
-+  // Add optional headers
-+  if (options?.includeVersion) {
-+    headers['X-Version'] = SUPPORTED_API_VERSION
-+  }
-+
-+  if (options?.nonce) {
-+    headers['X-Nonce'] = options.nonce
-+  }
-+
-+  return headers
-+}
-+
-+// Error response creation with clock skew detection
-+export function createHMACErrorResponse(error: HMACError, traceId: string): NextResponse {
-+  const errorMap = {
-+    MISSING_SIGNATURE: { status: 401, message: "Missing X-Signature header" },
-+    MISSING_TIMESTAMP: { status: 401, message: "Missing X-Timestamp header" },
-+    MISSING_PARTNER_ID: { status: 401, message: "Missing X-Partner-ID header" },
-+    INVALID_SIGNATURE: { status: 401, message: "Invalid HMAC signature" },
-+    EXPIRED_TIMESTAMP: { status: 401, message: "Request timestamp expired" },
-+    UNKNOWN_PARTNER: { status: 401, message: "Unknown partner ID" },
-+    INVALID_FORMAT: { status: 400, message: "Invalid request format" },
-+    REPLAY_DETECTED: { status: 401, message: "Duplicate request detected" },
-+    BODY_HASH_MISMATCH: { status: 401, message: "Request body hash mismatch" }
-+  }
-+
-+  const { status, message } = errorMap[error]
-+  const serverTime = new Date().toISOString()
-+
-+  return NextResponse.json(
-+    {
-+      error: status === 401 ? 'Unauthorized' : 'Bad Request',
-+      message,
-+      code: error,
-+      traceId,
-+      serverTime, // Help partners detect clock skew
-+      supportedMethods: ['JWT', 'HMAC'],
-+      documentation: 'https://docs.cashback-deals.com/api/authentication'
-+    },
-+    {
-+      status,
-+      headers: {
-+        'Content-Type': 'application/json',
-+        'X-Trace-ID': traceId,
-+        'Date': new Date().toUTCString(), // Standard HTTP Date header for clock sync
-+        'WWW-Authenticate': 'Bearer realm="Search API", HMAC realm="Partner API"'
-+      }
-+    }
-+  )
-+}
-+
-+// Security event logging
-+interface SecurityEvent {
-+  type: 'HMAC_AUTH_SUCCESS' | 'HMAC_AUTH_FAILURE' | 'JWT_AUTH_SUCCESS' | 'JWT_AUTH_FAILURE'
-+  endpoint: string
-+  method: string
-+  partnerId?: string
-+  ip: string
-+  timestamp: string
-+  traceId: string
-+  error?: HMACError
-+  errorMessage?: string
-+}
-+
-+export function logSecurityEvent(event: SecurityEvent): void {
-+  const logEntry = {
-+    level: event.type.includes('FAILURE') ? 'WARN' : 'INFO',
-+    message: `Security Event: ${event.type}`,
-+    traceId: event.traceId,
-+    endpoint: event.endpoint,
-+    method: event.method,
-+    partnerId: event.partnerId,
-+    ip: event.ip,
-+    timestamp: event.timestamp,
-+    error: event.error,
-+    errorMessage: event.errorMessage
-+  }
-+
-+  // Log to CloudWatch via console (structured logging)
-+  console.log(JSON.stringify(logEntry))
-+}
-+
-+// Get client IP from request
-+function getClientIP(request: NextRequest): string {
-+  const forwarded = request.headers.get('x-forwarded-for')
-+  const realIP = request.headers.get('x-real-ip')
-+  const cfIP = request.headers.get('cf-connecting-ip')
-+
-+  return cfIP || realIP || forwarded?.split(',')[0].trim() || 'unknown'
-+}
-diff --git a/tests/e2e/auth-compatibility.spec.ts b/tests/e2e/auth-compatibility.spec.ts
-new file mode 100644
-index 0000000..e5fd95d
---- /dev/null
-+++ b/tests/e2e/auth-compatibility.spec.ts
-@@ -0,0 +1,312 @@
-+// tests/e2e/auth-compatibility.spec.ts
-+// End-to-end tests for JWT + HMAC compatibility using Playwright
-+
-+import { test, expect } from '@playwright/test'
-+import crypto from 'crypto'
-+
-+// Test configuration
-+const BASE_URL = process.env.BASE_URL || 'http://localhost:3000'
-+const PARTNER_ID = 'test-partner'
-+const PARTNER_SECRET = 'test-secret-minimum-32-characters-long'
-+
-+// HMAC helper functions
-+function generateHMACSignature(
-+  method: string,
-+  path: string,
-+  timestamp: number,
-+  body: string = '',
-+  secret: string = PARTNER_SECRET
-+): string {
-+  const bodyHash = crypto.createHash('sha256').update(body).digest('hex')
-+  const message = `${method}\n${path}\n${timestamp}\n${bodyHash}`
-+  return crypto.createHmac('sha256', secret).update(message).digest('hex')
-+}
-+
-+function createHMACHeaders(
-+  method: string,
-+  path: string,
-+  body: string = ''
-+): Record<string, string> {
-+  const timestamp = Math.floor(Date.now() / 1000)
-+  const signature = generateHMACSignature(method, path, timestamp, body)
-+  
-+  return {
-+    'X-Signature': `sha256=${signature}`,
-+    'X-Timestamp': timestamp.toString(),
-+    'X-Partner-ID': PARTNER_ID,
-+    'Content-Type': 'application/json'
-+  }
-+}
-+
-+test.describe('JWT + HMAC Compatibility Tests', () => {
-+  test.describe('Contact Form JWT Authentication (PR 1 Functionality)', () => {
-+    test('should still protect contact form with JWT', async ({ page, request }) => {
-+      // Test that contact form still requires JWT authentication
-+      const contactData = {
-+        name: 'Test User',
-+        email: '<EMAIL>',
-+        message: 'Test message for compatibility check'
-+      }
-+      
-+      // Try to submit without JWT (should fail)
-+      const response = await request.post(`${BASE_URL}/api/contact`, {
-+        headers: { 'Content-Type': 'application/json' },
-+        data: JSON.stringify(contactData)
-+      })
-+      
-+      expect(response.status()).toBe(401)
-+    })
-+
-+    test('should allow contact form submission with valid JWT', async ({ page }) => {
-+      // Navigate to contact page
-+      await page.goto(`${BASE_URL}/contact`)
-+      
-+      // Fill out the form
-+      await page.fill('[name="name"]', 'Test User')
-+      await page.fill('[name="email"]', '<EMAIL>')
-+      await page.fill('[name="message"]', 'Test message for compatibility')
-+      
-+      // Complete CAPTCHA (this would need to be mocked in test environment)
-+      // For now, we'll just check that the form exists and is functional
-+      expect(await page.locator('form').count()).toBeGreaterThan(0)
-+      expect(await page.locator('[name="name"]').inputValue()).toBe('Test User')
-+    })
-+  })
-+
-+  test.describe('Search API Dual Authentication', () => {
-+    test('should accept HMAC authentication for search', async ({ request }) => {
-+      const path = '/api/search'
-+      const headers = createHMACHeaders('GET', path)
-+      
-+      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
-+      
-+      expect(response.status()).toBe(200)
-+      
-+      const body = await response.json()
-+      expect(body).toHaveProperty('products')
-+    })
-+
-+    test('should handle mixed authentication scenarios', async ({ request }) => {
-+      // Test 1: Valid HMAC only
-+      const hmacHeaders = createHMACHeaders('GET', '/api/search')
-+      const hmacResponse = await request.get(`${BASE_URL}/api/search?q=laptop`, {
-+        headers: hmacHeaders
-+      })
-+      expect(hmacResponse.status()).toBe(200)
-+      
-+      // Test 2: Invalid JWT with valid HMAC (should fall back to HMAC)
-+      const mixedHeaders = {
-+        ...hmacHeaders,
-+        'Authorization': 'Bearer invalid-jwt-token'
-+      }
-+      const mixedResponse = await request.get(`${BASE_URL}/api/search?q=laptop`, {
-+        headers: mixedHeaders
-+      })
-+      expect(mixedResponse.status()).toBe(200)
-+      
-+      // Test 3: No authentication (should fail)
-+      const noAuthResponse = await request.get(`${BASE_URL}/api/search?q=laptop`)
-+      expect(noAuthResponse.status()).toBe(401)
-+    })
-+  })
-+
-+  test.describe('Authentication Method Detection', () => {
-+    test('should identify HMAC authentication in logs', async ({ request }) => {
-+      const path = '/api/search'
-+      const headers = createHMACHeaders('GET', path)
-+      
-+      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
-+      
-+      expect(response.status()).toBe(200)
-+      
-+      // Check that trace ID indicates HMAC authentication
-+      const body = await response.json()
-+      if (body.traceId) {
-+        expect(body.traceId).toMatch(/^hmac-/)
-+      }
-+    })
-+
-+    test('should handle malformed authentication gracefully', async ({ request }) => {
-+      // Test various malformed authentication scenarios
-+      const testCases = [
-+        {
-+          name: 'Malformed Authorization header',
-+          headers: { 'Authorization': 'Malformed header format' }
-+        },
-+        {
-+          name: 'Empty Authorization header',
-+          headers: { 'Authorization': '' }
-+        },
-+        {
-+          name: 'Incomplete HMAC headers',
-+          headers: {
-+            'X-Signature': 'sha256=incomplete',
-+            'X-Timestamp': Math.floor(Date.now() / 1000).toString()
-+            // Missing X-Partner-ID
-+          }
-+        },
-+        {
-+          name: 'Invalid timestamp format',
-+          headers: {
-+            'X-Signature': 'sha256=test',
-+            'X-Timestamp': 'not-a-number',
-+            'X-Partner-ID': PARTNER_ID
-+          }
-+        }
-+      ]
-+      
-+      for (const testCase of testCases) {
-+        const response = await request.get(`${BASE_URL}/api/search?q=laptop`, {
-+          headers: testCase.headers
-+        })
-+        
-+        expect(response.status()).toBe(401)
-+        
-+        const body = await response.json()
-+        expect(body.error).toBe('Unauthorized')
-+      }
-+    })
-+  })
-+
-+  test.describe('Backward Compatibility Validation', () => {
-+    test('should maintain existing API response formats', async ({ request }) => {
-+      const path = '/api/search'
-+      const headers = createHMACHeaders('GET', path)
-+      
-+      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
-+      
-+      expect(response.status()).toBe(200)
-+      
-+      const body = await response.json()
-+      
-+      // Verify the response format hasn't changed
-+      expect(body).toHaveProperty('products')
-+      expect(body).toHaveProperty('pagination')
-+      expect(body).toHaveProperty('filters')
-+      expect(body).toHaveProperty('total')
-+      expect(body).toHaveProperty('query')
-+      
-+      // Verify products structure
-+      if (body.products.length > 0) {
-+        const product = body.products[0]
-+        expect(product).toHaveProperty('id')
-+        expect(product).toHaveProperty('title')
-+        expect(product).toHaveProperty('price')
-+        expect(product).toHaveProperty('cashback')
-+      }
-+    })
-+
-+    test('should maintain search suggestions format', async ({ request }) => {
-+      const path = '/api/search/suggestions'
-+      const headers = createHMACHeaders('GET', path)
-+      
-+      const response = await request.get(`${BASE_URL}${path}?q=lap`, { headers })
-+      
-+      expect(response.status()).toBe(200)
-+      
-+      const body = await response.json()
-+      expect(body).toHaveProperty('suggestions')
-+      expect(Array.isArray(body.suggestions)).toBe(true)
-+      
-+      if (body.suggestions.length > 0) {
-+        const suggestion = body.suggestions[0]
-+        expect(suggestion).toHaveProperty('text')
-+        expect(suggestion).toHaveProperty('category')
-+      }
-+    })
-+  })
-+
-+  test.describe('Performance Impact Assessment', () => {
-+    test('should not significantly impact response times', async ({ request }) => {
-+      const path = '/api/search'
-+      
-+      // Test multiple requests to get average response time
-+      const requestCount = 5
-+      const responseTimes: number[] = []
-+      
-+      for (let i = 0; i < requestCount; i++) {
-+        const headers = createHMACHeaders('GET', `${path}?q=test${i}`)
-+        
-+        const startTime = Date.now()
-+        const response = await request.get(`${BASE_URL}${path}?q=test${i}`, { headers })
-+        const endTime = Date.now()
-+        
-+        expect(response.status()).toBe(200)
-+        responseTimes.push(endTime - startTime)
-+      }
-+      
-+      const averageResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length
-+      
-+      // Authentication overhead should be minimal
-+      expect(averageResponseTime).toBeLessThan(5000) // 5 seconds max
-+      
-+      console.log(`Average response time with HMAC auth: ${averageResponseTime}ms`)
-+    })
-+
-+    test('should handle concurrent requests efficiently', async ({ request }) => {
-+      const path = '/api/search'
-+      
-+      // Create 10 concurrent requests
-+      const concurrentRequests = Array.from({ length: 10 }, (_, i) => {
-+        const headers = createHMACHeaders('GET', `${path}?q=concurrent${i}`)
-+        return request.get(`${BASE_URL}${path}?q=concurrent${i}`, { headers })
-+      })
-+      
-+      const startTime = Date.now()
-+      const responses = await Promise.all(concurrentRequests)
-+      const endTime = Date.now()
-+      
-+      // All requests should succeed
-+      responses.forEach(response => {
-+        expect(response.status()).toBe(200)
-+      })
-+      
-+      const totalTime = endTime - startTime
-+      console.log(`10 concurrent requests completed in: ${totalTime}ms`)
-+      
-+      // Should complete within reasonable time
-+      expect(totalTime).toBeLessThan(10000) // 10 seconds max for 10 concurrent requests
-+    })
-+  })
-+
-+  test.describe('Error Consistency', () => {
-+    test('should provide consistent error formats across endpoints', async ({ request }) => {
-+      const endpoints = [
-+        '/api/search',
-+        '/api/search/suggestions',
-+        '/api/search/more'
-+      ]
-+      
-+      for (const endpoint of endpoints) {
-+        const response = await request.get(`${BASE_URL}${endpoint}?q=test`)
-+        
-+        expect(response.status()).toBe(401)
-+        
-+        const body = await response.json()
-+        expect(body).toHaveProperty('error')
-+        expect(body).toHaveProperty('message')
-+        expect(body).toHaveProperty('code')
-+        expect(body).toHaveProperty('traceId')
-+        expect(body).toHaveProperty('supportedMethods')
-+        
-+        expect(body.supportedMethods).toContain('JWT')
-+        expect(body.supportedMethods).toContain('HMAC')
-+      }
-+    })
-+  })
-+
-+  test.describe('Feature Flag Compatibility', () => {
-+    test('should work with authentication enabled', async ({ request }) => {
-+      // This test validates that the feature flags are working correctly
-+      // In a real test environment, you might toggle these flags
-+      
-+      const path = '/api/search'
-+      const headers = createHMACHeaders('GET', path)
-+      
-+      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
-+      
-+      // Should succeed when authentication is enabled and HMAC is provided
-+      expect(response.status()).toBe(200)
-+    })
-+  })
-+})
-diff --git a/tests/e2e/auth-hmac.spec.ts b/tests/e2e/auth-hmac.spec.ts
-new file mode 100644
-index 0000000..1cbb1d3
---- /dev/null
-+++ b/tests/e2e/auth-hmac.spec.ts
-@@ -0,0 +1,316 @@
-+// tests/e2e/auth-hmac.spec.ts
-+// End-to-end tests for HMAC authentication using Playwright
-+
-+import { test, expect } from '@playwright/test'
-+import crypto from 'crypto'
-+
-+// Test configuration
-+const BASE_URL = process.env.BASE_URL || 'http://localhost:3000'
-+const PARTNER_ID = 'test-partner'
-+const PARTNER_SECRET = 'test-secret-minimum-32-characters-long'
-+
-+// HMAC helper functions for testing
-+function generateHMACSignature(
-+  method: string,
-+  path: string,
-+  timestamp: number,
-+  body: string = '',
-+  secret: string = PARTNER_SECRET
-+): string {
-+  const bodyHash = crypto.createHash('sha256').update(body).digest('hex')
-+  const message = `${method}\n${path}\n${timestamp}\n${bodyHash}`
-+  return crypto.createHmac('sha256', secret).update(message).digest('hex')
-+}
-+
-+function createHMACHeaders(
-+  method: string,
-+  path: string,
-+  body: string = '',
-+  options?: { 
-+    secret?: string
-+    partnerId?: string
-+    includeVersion?: boolean
-+    customTimestamp?: number
-+  }
-+): Record<string, string> {
-+  const timestamp = options?.customTimestamp || Math.floor(Date.now() / 1000)
-+  const secret = options?.secret || PARTNER_SECRET
-+  const partnerId = options?.partnerId || PARTNER_ID
-+  
-+  const signature = generateHMACSignature(method, path, timestamp, body, secret)
-+  
-+  const headers: Record<string, string> = {
-+    'X-Signature': `sha256=${signature}`,
-+    'X-Timestamp': timestamp.toString(),
-+    'X-Partner-ID': partnerId,
-+    'Content-Type': 'application/json'
-+  }
-+  
-+  if (options?.includeVersion) {
-+    headers['X-Version'] = '1.0'
-+  }
-+  
-+  return headers
-+}
-+
-+test.describe('HMAC Authentication E2E Tests', () => {
-+  test.beforeEach(async ({ page }) => {
-+    // Set up any necessary test environment
-+    await page.goto(BASE_URL)
-+  })
-+
-+  test.describe('Search API Authentication', () => {
-+    test('should reject unauthenticated requests', async ({ request }) => {
-+      const response = await request.get(`${BASE_URL}/api/search?q=laptop`)
-+      
-+      expect(response.status()).toBe(401)
-+      
-+      const body = await response.json()
-+      expect(body.error).toBe('Unauthorized')
-+      expect(body.code).toBe('AUTH_REQUIRED')
-+      expect(body.supportedMethods).toContain('HMAC')
-+      expect(body.traceId).toMatch(/^hmac-/)
-+    })
-+
-+    test('should accept valid HMAC authentication', async ({ request }) => {
-+      const path = '/api/search'
-+      const headers = createHMACHeaders('GET', path, '', { includeVersion: true })
-+      
-+      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
-+      
-+      expect(response.status()).toBe(200)
-+      
-+      const body = await response.json()
-+      expect(body).toHaveProperty('products')
-+      expect(Array.isArray(body.products)).toBe(true)
-+    })
-+
-+    test('should reject invalid HMAC signature', async ({ request }) => {
-+      const path = '/api/search'
-+      const headers = createHMACHeaders('GET', path, '', { secret: 'wrong-secret' })
-+      
-+      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
-+      
-+      expect(response.status()).toBe(401)
-+      
-+      const body = await response.json()
-+      expect(body.code).toBe('AUTH_REQUIRED')
-+    })
-+
-+    test('should reject expired timestamp', async ({ request }) => {
-+      const path = '/api/search'
-+      const expiredTimestamp = Math.floor(Date.now() / 1000) - 400 // 400 seconds ago
-+      const headers = createHMACHeaders('GET', path, '', { customTimestamp: expiredTimestamp })
-+      
-+      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
-+      
-+      expect(response.status()).toBe(401)
-+    })
-+
-+    test('should reject unknown partner ID', async ({ request }) => {
-+      const path = '/api/search'
-+      const headers = createHMACHeaders('GET', path, '', { partnerId: 'unknown-partner' })
-+      
-+      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
-+      
-+      expect(response.status()).toBe(401)
-+    })
-+  })
-+
-+  test.describe('Search Suggestions API', () => {
-+    test('should authenticate search suggestions endpoint', async ({ request }) => {
-+      const path = '/api/search/suggestions'
-+      const headers = createHMACHeaders('GET', path)
-+      
-+      const response = await request.get(`${BASE_URL}${path}?q=lap`, { headers })
-+      
-+      expect(response.status()).toBe(200)
-+      
-+      const body = await response.json()
-+      expect(body).toHaveProperty('suggestions')
-+      expect(Array.isArray(body.suggestions)).toBe(true)
-+    })
-+
-+    test('should reject unauthenticated suggestions requests', async ({ request }) => {
-+      const response = await request.get(`${BASE_URL}/api/search/suggestions?q=lap`)
-+      
-+      expect(response.status()).toBe(401)
-+    })
-+  })
-+
-+  test.describe('Search More API', () => {
-+    test('should authenticate search more endpoint', async ({ request }) => {
-+      const path = '/api/search/more'
-+      const headers = createHMACHeaders('GET', path)
-+      
-+      const response = await request.get(`${BASE_URL}${path}?q=laptop&page=2`, { headers })
-+      
-+      expect(response.status()).toBe(200)
-+      
-+      const body = await response.json()
-+      expect(body).toHaveProperty('products')
-+    })
-+
-+    test('should reject unauthenticated more requests', async ({ request }) => {
-+      const response = await request.get(`${BASE_URL}/api/search/more?q=laptop&page=2`)
-+      
-+      expect(response.status()).toBe(401)
-+    })
-+  })
-+
-+  test.describe('POST Requests with Body', () => {
-+    test('should handle POST requests with JSON body', async ({ request }) => {
-+      const path = '/api/search'
-+      const body = JSON.stringify({
-+        query: 'laptop',
-+        filters: {
-+          brand: 'samsung',
-+          priceRange: { min: 500, max: 1500 }
-+        }
-+      })
-+      
-+      const headers = createHMACHeaders('POST', path, body, { includeVersion: true })
-+      
-+      const response = await request.post(`${BASE_URL}${path}`, {
-+        headers,
-+        data: body
-+      })
-+      
-+      expect(response.status()).toBe(200)
-+      
-+      const responseBody = await response.json()
-+      expect(responseBody).toHaveProperty('products')
-+    })
-+
-+    test('should reject POST with body hash mismatch', async ({ request }) => {
-+      const path = '/api/search'
-+      const actualBody = JSON.stringify({ query: 'laptop' })
-+      const signatureBody = JSON.stringify({ query: 'different' }) // Different body for signature
-+      
-+      const headers = createHMACHeaders('POST', path, signatureBody)
-+      
-+      const response = await request.post(`${BASE_URL}${path}`, {
-+        headers,
-+        data: actualBody
-+      })
-+      
-+      expect(response.status()).toBe(401)
-+    })
-+  })
-+
-+  test.describe('Error Response Format', () => {
-+    test('should return structured error responses', async ({ request }) => {
-+      const response = await request.get(`${BASE_URL}/api/search?q=laptop`)
-+      
-+      expect(response.status()).toBe(401)
-+      
-+      const body = await response.json()
-+      expect(body).toHaveProperty('error')
-+      expect(body).toHaveProperty('message')
-+      expect(body).toHaveProperty('code')
-+      expect(body).toHaveProperty('traceId')
-+      expect(body).toHaveProperty('serverTime')
-+      expect(body).toHaveProperty('supportedMethods')
-+      expect(body).toHaveProperty('documentation')
-+      
-+      // Check Date header for clock skew detection
-+      const dateHeader = response.headers()['date']
-+      expect(dateHeader).toBeDefined()
-+    })
-+
-+    test('should include trace ID in responses', async ({ request }) => {
-+      const response = await request.get(`${BASE_URL}/api/search?q=laptop`)
-+      
-+      const body = await response.json()
-+      expect(body.traceId).toMatch(/^hmac-get-[a-z0-9]+-[a-z0-9]+$/)
-+      
-+      // Check X-Trace-ID header
-+      const traceHeader = response.headers()['x-trace-id']
-+      expect(traceHeader).toBe(body.traceId)
-+    })
-+  })
-+
-+  test.describe('Rate Limiting Integration', () => {
-+    test('should apply rate limiting after authentication', async ({ request }) => {
-+      const path = '/api/search'
-+      const headers = createHMACHeaders('GET', path)
-+      
-+      // Make multiple requests quickly
-+      const requests = Array.from({ length: 5 }, () =>
-+        request.get(`${BASE_URL}${path}?q=test${Math.random()}`, { headers })
-+      )
-+      
-+      const responses = await Promise.all(requests)
-+      
-+      // All should either succeed (200) or be rate limited (429)
-+      // None should be authentication failures (401)
-+      responses.forEach(response => {
-+        expect([200, 429]).toContain(response.status())
-+      })
-+    })
-+  })
-+
-+  test.describe('API Version Support', () => {
-+    test('should accept supported API version', async ({ request }) => {
-+      const path = '/api/search'
-+      const headers = createHMACHeaders('GET', path, '', { includeVersion: true })
-+      
-+      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
-+      
-+      expect(response.status()).toBe(200)
-+    })
-+
-+    test('should work without version header', async ({ request }) => {
-+      const path = '/api/search'
-+      const headers = createHMACHeaders('GET', path) // No version header
-+      
-+      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
-+      
-+      expect(response.status()).toBe(200)
-+    })
-+  })
-+
-+  test.describe('Concurrent Requests', () => {
-+    test('should handle concurrent authenticated requests', async ({ request }) => {
-+      const path = '/api/search'
-+      
-+      // Create 10 concurrent requests with different signatures
-+      const requests = Array.from({ length: 10 }, (_, i) => {
-+        const headers = createHMACHeaders('GET', `${path}?q=test${i}`)
-+        return request.get(`${BASE_URL}${path}?q=test${i}`, { headers })
-+      })
-+      
-+      const responses = await Promise.all(requests)
-+      
-+      // All should succeed
-+      responses.forEach(response => {
-+        expect(response.status()).toBe(200)
-+      })
-+    })
-+  })
-+
-+  test.describe('Feature Flag Behavior', () => {
-+    test('should respect authentication when enabled', async ({ request }) => {
-+      // This test assumes authentication is enabled in the test environment
-+      const response = await request.get(`${BASE_URL}/api/search?q=laptop`)
-+      
-+      expect(response.status()).toBe(401)
-+    })
-+  })
-+
-+  test.describe('Performance Validation', () => {
-+    test('should respond within acceptable time limits', async ({ request }) => {
-+      const path = '/api/search'
-+      const headers = createHMACHeaders('GET', path)
-+      
-+      const startTime = Date.now()
-+      const response = await request.get(`${BASE_URL}${path}?q=laptop`, { headers })
-+      const endTime = Date.now()
-+      
-+      expect(response.status()).toBe(200)
-+      
-+      const responseTime = endTime - startTime
-+      expect(responseTime).toBeLessThan(5000) // Should respond within 5 seconds
-+    })
-+  })
-+})
diff --git a/docs/ENVIRONMENT_VARIABLES.md b/docs/ENVIRONMENT_VARIABLES.md
index 4401dcd..01b8520 100644
--- a/docs/ENVIRONMENT_VARIABLES.md
+++ b/docs/ENVIRONMENT_VARIABLES.md
@@ -29,6 +29,35 @@ This document describes how to set up and manage environment variables for the C
 | NODE_ENV                   | Node environment (development, production)    | development                       |
 | NEXT_PUBLIC_TURNSTILE_SITE_KEY | Cloudflare Turnstile site key                  | 1x00000000000000000000AA (test key) |
 
+## PR3: Sentry & IP Allowlist Configuration
+
+### Sentry Environment Variables
+
+| Variable Name                | Description                                  | Example Value                          |
+|-----------------------------|----------------------------------------------|--------------------------------------|
+| SENTRY_DSN                  | Sentry Data Source Name for error tracking   | https://<EMAIL>/project-id      |
+| ENABLE_SENTRY               | Enable Sentry in production (true/false)     | true                                  |
+| ENABLE_SENTRY_LOCAL         | Enable Sentry in development (true/false)    | false                                 |
+| SENTRY_TRACES_SAMPLE_RATE   | Sentry trace sampling rate (0.0-1.0)         | 0.01 (1% in production)               |
+| SENTRY_ENVIRONMENT          | Sentry environment name                       | production                            |
+
+### IP Allowlist Environment Variables
+
+| Variable Name                | Description                                  | Example Value                          |
+|-----------------------------|----------------------------------------------|--------------------------------------|
+| ENABLE_IP_ALLOWLIST         | Enable IP allowlist protection (true/false)  | false (default: disabled)             |
+| IP_ALLOWLIST_CIDRS          | Comma-separated list of allowed CIDR ranges  | 10.0.0.0/8,**********/12,127.0.0.1/32 |
+| IP_ALLOWLIST_LOG_VIOLATIONS | Log blocked IP attempts (true/false)         | true                                  |
+| IP_ALLOWLIST_BLOCK_BY_DEFAULT | Block IPs not in allowlist (true/false)    | true                                  |
+| IP_ALLOWLIST_INCLUDE_DEBUG  | Include debug info in error responses        | false                                 |
+
+### Security Notes
+
+- **Sentry DSN**: Keep this secret in production. Use environment-specific DSNs.
+- **IP Allowlist**: Start with `ENABLE_IP_ALLOWLIST=false` for initial deployment.
+- **CIDR Ranges**: Include office/VPN networks to prevent self-lockout.
+- **AWS Amplify**: Sensitive variables are server-only unless prefixed with `AMPLIFY_`.
+
 ## Usage
 
 1. Copy `.env.example` to `.env.local` for local development and fill in the appropriate values.
diff --git a/sentry.edge.config.ts b/sentry.edge.config.ts
index e156677..45f6668 100644
--- a/sentry.edge.config.ts
+++ b/sentry.edge.config.ts
@@ -1,16 +1,46 @@
 // This file configures the initialization of Sentry for edge features (middleware, edge routes, and so on).
 // The config you add here will be used whenever one of the edge features is loaded.
-// Note that this config is unrelated to the Vercel Edge Runtime and is also required when running locally.
+// Note that this config is unrelated to the AWS Amplify Edge Runtime and is also required when running locally.
 // https://docs.sentry.io/platforms/javascript/guides/nextjs/
 
 import * as Sentry from "@sentry/nextjs";
 
-Sentry.init({
-  dsn: "https://<EMAIL>/4509639010680912",
+// Environment-based Sentry configuration with feature flags
+const ENABLE_SENTRY = process.env.ENABLE_SENTRY === 'true';
+const ENABLE_SENTRY_LOCAL = process.env.ENABLE_SENTRY_LOCAL === 'true';
+const SENTRY_DSN = process.env.SENTRY_DSN;
+const SENTRY_TRACES_SAMPLE_RATE = parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE || '0.01');
+const SENTRY_ENVIRONMENT = process.env.SENTRY_ENVIRONMENT || process.env.NODE_ENV || 'development';
 
-  // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.
-  tracesSampleRate: 1,
+// Only initialize Sentry if enabled via environment variables
+const shouldInitializeSentry = () => {
+  // In production, require ENABLE_SENTRY=true
+  if (process.env.NODE_ENV === 'production') {
+    return ENABLE_SENTRY && SENTRY_DSN;
+  }
 
-  // Setting this option to true will print useful information to the console while you're setting up Sentry.
-  debug: false,
-});
+  // In development, require explicit ENABLE_SENTRY_LOCAL=true
+  if (process.env.NODE_ENV === 'development') {
+    return ENABLE_SENTRY_LOCAL && SENTRY_DSN;
+  }
+
+  // For other environments (test, staging), follow ENABLE_SENTRY
+  return ENABLE_SENTRY && SENTRY_DSN;
+};
+
+if (shouldInitializeSentry()) {
+  console.log(`[Sentry] Initializing edge-side Sentry for ${SENTRY_ENVIRONMENT} environment`);
+
+  Sentry.init({
+    dsn: SENTRY_DSN,
+    environment: SENTRY_ENVIRONMENT,
+
+    // Production: 1% sampling, Development: 100% sampling for debugging
+    tracesSampleRate: SENTRY_TRACES_SAMPLE_RATE,
+
+    // Enable debug in development only
+    debug: process.env.NODE_ENV === 'development',
+  });
+} else {
+  console.log('[Sentry] Edge-side Sentry disabled via environment configuration');
+}
diff --git a/sentry.server.config.ts b/sentry.server.config.ts
index 8499c3e..4cade83 100644
--- a/sentry.server.config.ts
+++ b/sentry.server.config.ts
@@ -4,12 +4,54 @@
 
 import * as Sentry from "@sentry/nextjs";
 
-Sentry.init({
-  dsn: "https://<EMAIL>/4509639010680912",
+// Environment-based Sentry configuration with feature flags
+const ENABLE_SENTRY = process.env.ENABLE_SENTRY === 'true';
+const ENABLE_SENTRY_LOCAL = process.env.ENABLE_SENTRY_LOCAL === 'true';
+const SENTRY_DSN = process.env.SENTRY_DSN;
+const SENTRY_TRACES_SAMPLE_RATE = parseFloat(process.env.SENTRY_TRACES_SAMPLE_RATE || '0.01');
+const SENTRY_ENVIRONMENT = process.env.SENTRY_ENVIRONMENT || process.env.NODE_ENV || 'development';
 
-  // Define how likely traces are sampled. Adjust this value in production, or use tracesSampler for greater control.
-  tracesSampleRate: 1,
+// Only initialize Sentry if enabled via environment variables
+const shouldInitializeSentry = () => {
+  // In production, require ENABLE_SENTRY=true
+  if (process.env.NODE_ENV === 'production') {
+    return ENABLE_SENTRY && SENTRY_DSN;
+  }
 
-  // Setting this option to true will print useful information to the console while you're setting up Sentry.
-  debug: false,
-});
+  // In development, require explicit ENABLE_SENTRY_LOCAL=true
+  if (process.env.NODE_ENV === 'development') {
+    return ENABLE_SENTRY_LOCAL && SENTRY_DSN;
+  }
+
+  // For other environments (test, staging), follow ENABLE_SENTRY
+  return ENABLE_SENTRY && SENTRY_DSN;
+};
+
+if (shouldInitializeSentry()) {
+  console.log(`[Sentry] Initializing server-side Sentry for ${SENTRY_ENVIRONMENT} environment`);
+
+  Sentry.init({
+    dsn: SENTRY_DSN,
+    environment: SENTRY_ENVIRONMENT,
+
+    // Production: 1% sampling, Development: 100% sampling for debugging
+    tracesSampleRate: SENTRY_TRACES_SAMPLE_RATE,
+
+    // Enable debug in development only
+    debug: process.env.NODE_ENV === 'development',
+
+    // Additional production optimizations
+    beforeSend(event) {
+      // Filter out noisy errors in production
+      if (process.env.NODE_ENV === 'production') {
+        // Skip common client-side errors that aren't actionable
+        if (event.exception?.values?.[0]?.type === 'ChunkLoadError') {
+          return null;
+        }
+      }
+      return event;
+    },
+  });
+} else {
+  console.log('[Sentry] Server-side Sentry disabled via environment configuration');
+}
diff --git a/src/app/api/sentry-example-api/route.ts b/src/app/api/sentry-example-api/route.ts
index a830cfa..7144edd 100644
--- a/src/app/api/sentry-example-api/route.ts
+++ b/src/app/api/sentry-example-api/route.ts
@@ -1,14 +1,9 @@
 import { NextResponse } from "next/server";
 
 export const dynamic = "force-dynamic";
-class SentryExampleAPIError extends Error {
-  constructor(message: string | undefined) {
-    super(message);
-    this.name = "SentryExampleAPIError";
-  }
-}
-// A faulty API route to test Sentry's error monitoring
+
+// Sentry debug endpoint removed for production security
+// This endpoint was used for development testing only
 export function GET() {
-  throw new SentryExampleAPIError("This error is raised on the backend called by the example page.");
-  return NextResponse.json({ data: "Testing Sentry Error..." });
+  return NextResponse.json({ error: "Not Found" }, { status: 404 });
 }
diff --git a/src/app/sentry-example-page/page.tsx b/src/app/sentry-example-page/page.tsx
index a436fc4..36c2d9a 100644
--- a/src/app/sentry-example-page/page.tsx
+++ b/src/app/sentry-example-page/page.tsx
@@ -1,209 +1,12 @@
 "use client";
 
 import Head from "next/head";
-import * as Sentry from "@sentry/nextjs";
-import { useState, useEffect } from "react";
-
-class SentryExampleFrontendError extends Error {
-  constructor(message: string | undefined) {
-    super(message);
-    this.name = "SentryExampleFrontendError";
-  }
-}
+import { notFound } from "next/navigation";
 
+// Sentry debug page removed for production security
+// This page was used for development testing only
 export default function Page() {
-  const [hasSentError, setHasSentError] = useState(false);
-  const [isConnected, setIsConnected] = useState(true);
-  
-  useEffect(() => {
-    async function checkConnectivity() {
-      const result = await Sentry.diagnoseSdkConnectivity();
-      setIsConnected(result !== 'sentry-unreachable');
-    }
-    checkConnectivity();
-  }, []);
-
-  return (
-    <div>
-      <Head>
-        <title>sentry-example-page</title>
-        <meta name="description" content="Test Sentry for your Next.js app!" />
-      </Head>
-
-      <main>
-        <div className="flex-spacer" />
-        <svg height="40" width="40" fill="none" xmlns="http://www.w3.org/2000/svg">
-          <path d="M21.85 2.995a3.698 3.698 0 0 1 1.353 1.354l16.303 28.278a3.703 3.703 0 0 1-1.354 5.053 3.694 3.694 0 0 1-1.848.496h-3.828a31.149 31.149 0 0 0 0-3.09h3.815a.61.61 0 0 0 .537-.917L20.523 5.893a.61.61 0 0 0-1.057 0l-3.739 6.494a28.948 28.948 0 0 1 9.63 10.453 28.988 28.988 0 0 1 3.499 13.78v1.542h-9.852v-1.544a19.106 19.106 0 0 0-2.182-8.85 19.08 19.08 0 0 0-6.032-6.829l-1.85 3.208a15.377 15.377 0 0 1 6.382 12.484v1.542H3.696A3.694 3.694 0 0 1 0 34.473c0-.648.17-1.286.494-1.849l2.33-4.074a8.562 8.562 0 0 1 2.689 1.536L3.158 34.17a.611.611 0 0 0 .538.917h8.448a12.481 12.481 0 0 0-6.037-9.09l-1.344-.772 4.908-8.545 1.344.77a22.16 22.16 0 0 1 7.705 7.444 22.193 22.193 0 0 1 3.316 10.193h3.699a25.892 25.892 0 0 0-3.811-12.033 25.856 25.856 0 0 0-9.046-8.796l-1.344-.772 5.269-9.136a3.698 3.698 0 0 1 3.2-1.849c.648 0 1.285.17 1.847.495Z" fill="currentcolor"/>
-        </svg>
-        <h1>
-          sentry-example-page
-        </h1>
-
-        <p className="description">
-          Click the button below, and view the sample error on the Sentry <a target="_blank" href="https://sanjmirch.sentry.io/issues/?project=4509639010680912">Issues Page</a>.
-          For more details about setting up Sentry, <a target="_blank"
-           href="https://docs.sentry.io/platforms/javascript/guides/nextjs/">read our docs</a>.
-        </p>
-
-        <button
-          type="button"
-          onClick={async () => {
-            await Sentry.startSpan({
-              name: 'Example Frontend/Backend Span',
-              op: 'test'
-            }, async () => {
-              const res = await fetch("/api/sentry-example-api");
-              if (!res.ok) {
-                setHasSentError(true);
-              }
-            });
-            throw new SentryExampleFrontendError("This error is raised on the frontend of the example page.");
-          }}
-          disabled={!isConnected}
-        >
-          <span>
-            Throw Sample Error
-          </span>
-        </button>
-
-        {hasSentError ? (
-          <p className="success">
-            Error sent to Sentry.
-          </p>
-        ) : !isConnected ? (
-          <div className="connectivity-error">
-            <p>It looks like network requests to Sentry are being blocked, which will prevent errors from being captured. Try disabling your ad-blocker to complete the test.</p>
-          </div>
-        ) : (
-          <div className="success_placeholder" />
-        )}
-
-        <div className="flex-spacer" />
-      
-      </main>
-
-      <style>{`
-        main {
-          display: flex;
-          min-height: 100vh;
-          flex-direction: column;
-          justify-content: center;
-          align-items: center;
-          gap: 16px;
-          padding: 16px;
-          font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", sans-serif;
-        }
-
-        h1 {
-          padding: 0px 4px;
-          border-radius: 4px;
-          background-color: rgba(24, 20, 35, 0.03);
-          font-family: monospace;
-          font-size: 20px;
-          line-height: 1.2;
-        }
-
-        p {
-          margin: 0;
-          font-size: 20px;
-        }
-
-        a {
-          color: #6341F0;
-          text-decoration: underline;
-          cursor: pointer;
-
-          @media (prefers-color-scheme: dark) {
-            color: #B3A1FF;
-          }
-        }
-
-        button {
-          border-radius: 8px;
-          color: white;
-          cursor: pointer;
-          background-color: #553DB8;
-          border: none;
-          padding: 0;
-          margin-top: 4px;
-
-          & > span {
-            display: inline-block;
-            padding: 12px 16px;
-            border-radius: inherit;
-            font-size: 20px;
-            font-weight: bold;
-            line-height: 1;
-            background-color: #7553FF;
-            border: 1px solid #553DB8;
-            transform: translateY(-4px);
-          }
-
-          &:hover > span {
-            transform: translateY(-8px);
-          }
-
-          &:active > span {
-            transform: translateY(0);
-          }
-
-          &:disabled {
-	            cursor: not-allowed;
-	            opacity: 0.6;
-	
-	            & > span {
-	              transform: translateY(0);
-	              border: none
-	            }
-	          }
-        }
-
-        .description {
-          text-align: center;
-          color: #6E6C75;
-          max-width: 500px;
-          line-height: 1.5;
-          font-size: 20px;
-
-          @media (prefers-color-scheme: dark) {
-            color: #A49FB5;
-          }
-        }
-
-        .flex-spacer {
-          flex: 1;
-        }
-
-        .success {
-          padding: 12px 16px;
-          border-radius: 8px;
-          font-size: 20px;
-          line-height: 1;
-          background-color: #00F261;
-          border: 1px solid #00BF4D;
-          color: #181423;
-        }
-
-        .success_placeholder {
-          height: 46px;
-        }
+  // Return 404 for production security
+  notFound();
 
-        .connectivity-error {
-          padding: 12px 16px;
-          background-color: #E50045;
-          border-radius: 8px;
-          width: 500px;
-          color: #FFFFFF;
-          border: 1px solid #A80033;
-          text-align: center;
-          margin: 0;
-        }
-        
-        .connectivity-error a {
-          color: #FFFFFF;
-          text-decoration: underline;
-        }
-      `}</style>
-    </div>
-  );
 }
diff --git a/src/instrumentation.ts b/src/instrumentation.ts
index 8aff09f..5a8c876 100644
--- a/src/instrumentation.ts
+++ b/src/instrumentation.ts
@@ -1,13 +1,44 @@
 import * as Sentry from '@sentry/nextjs';
 
-export async function register() {
-  if (process.env.NEXT_RUNTIME === 'nodejs') {
-    await import('../sentry.server.config');
+// Environment-based Sentry initialization
+const ENABLE_SENTRY = process.env.ENABLE_SENTRY === 'true';
+const ENABLE_SENTRY_LOCAL = process.env.ENABLE_SENTRY_LOCAL === 'true';
+
+// Determine if Sentry should be loaded
+const shouldLoadSentry = () => {
+  // In production, require ENABLE_SENTRY=true
+  if (process.env.NODE_ENV === 'production') {
+    return ENABLE_SENTRY;
+  }
+
+  // In development, require explicit ENABLE_SENTRY_LOCAL=true
+  if (process.env.NODE_ENV === 'development') {
+    return ENABLE_SENTRY_LOCAL;
   }
 
-  if (process.env.NEXT_RUNTIME === 'edge') {
-    await import('../sentry.edge.config');
+  // For other environments (test, staging), follow ENABLE_SENTRY
+  return ENABLE_SENTRY;
+};
+
+export async function register() {
+  // Only load Sentry configuration if enabled
+  if (shouldLoadSentry()) {
+    if (process.env.NEXT_RUNTIME === 'nodejs') {
+      await import('../sentry.server.config');
+    }
+
+    if (process.env.NEXT_RUNTIME === 'edge') {
+      await import('../sentry.edge.config');
+    }
+  } else {
+    console.log('[Sentry] Instrumentation disabled via environment configuration');
   }
 }
 
-export const onRequestError = Sentry.captureRequestError;
+// Only export Sentry error handler if Sentry is enabled
+export const onRequestError = shouldLoadSentry()
+  ? Sentry.captureRequestError
+  : (error: unknown, _request: Request) => {
+      // Fallback error logging when Sentry is disabled
+      console.error('[Error] Request error (Sentry disabled):', error);
+    };
