<!--
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: ARCHIVED from docs/UPDATES/AUTH-SPRINT/PR3/ to docs/archive/historical/auth_sprint_implementations/PR3/
📁 ORIGINAL LOCATION: /docs/UPDATES/AUTH-SPRINT/PR3/chat-gpt-prompt-pr3.md  
📁 NEW LOCATION: /docs/archive/historical/auth_sprint_implementations/PR3/chat-gpt-prompt-pr3.md
🎯 REASON: Historical PR3 ChatGPT prompt template for Sentry cleanup and IP allowlist implementation
📝 STATUS: Content preserved unchanged, archived as AI prompting reference
👥 REVIEW REQUIRED: Security team can reference for AI-assisted Sentry optimization and IP filtering implementation
🏷️ CATEGORY: Archive - Historical (PR3 AI Prompts)
📅 PURPOSE: Historical record of ChatGPT prompting strategy for Phase 3 Sentry cleanup and IP allowlist tasks
-->

@CLAUDE.md  @chat-gpt-prompt.md

We are continuing the Auth-Sprint.  
PR-1 (JWT) and PR-2 (HMAC + search routes) are merged and documented in  
  • /docs/UPDATES/AUTH-SPRINT/PR1/*  
  • /docs/UPDATES/AUTH-SPRINT/PR2/*

### 🔐 PR 3 scope — “Sentry clean-up / IP allow-list”
1. **Remove the public `/api/debug/sentry-test` route** from the production build *or* guard it behind a strict CIDR allow-list (`10.0.0.0/8`, `**********/12`, `***********/16` by default).  
2. Add an **IP allow-list middleware** that can be re-used by future internal tools; feature-flag it with `ENABLE_IP_ALLOWLIST`.  
3. **Update Sentry initialisation** so that:
   * `dsn` and `tracesSampleRate` come only from `process.env` in **prod**.  
   * Sentry remains enabled in staging but is fully disabled for local dev unless `ENABLE_SENTRY_LOCAL=true`.  

### Deliverables
Create, **but do *not* commit code changes** – we only want documentation & plans:

| File | Purpose |
|------|---------|
| `/docs/UPDATES/AUTH-SPRINT/PR3/executive-summary.md` | Non-technical summary, risk/benefit, CTO go/no-go checklist |
| `/docs/UPDATES/AUTH-SPRINT/PR3/implementation-plan.md` | 2-phase plan (<4 h): cleanup → middleware; include success criteria |
| `/docs/UPDATES/AUTH-SPRINT/PR3/technical-specifications.md` | Interfaces for `src/lib/security/ip-allowlist.ts`, env vars, feature flags |
| `/docs/UPDATES/AUTH-SPRINT/PR3/testing-strategy.md` | Jest + Playwright cases: • 200 from allowed IP • 403 from blocked • feature-flag off |
| `/docs/UPDATES/AUTH-SPRINT/PR3/deployment-guide.md` | Rollout steps, rollback (`ENABLE_IP_ALLOWLIST=false`), smoke-test curl examples |

Follow the same conventions used in PR-1 and PR-2:

* **No application code changes** – this is a planning artefact only.  
* Put every new doc under `/docs/UPDATES/AUTH-SPRINT/PR3/`.  
* Re-use our task-list comments (`Add Tasks / Update Task List`) so the Git-bot can mark progress.  
* Keep estimates tight (≤ 4 engineering hours total).  
* Assume Redis replay-cache isn’t available yet.

Return a short summary when you’re done similar to the “📋 PR 2 Implementation Plan – Complete Package” note.

