# 🏗️ Phase 2: Technical Architecture Diagrams & Implementation Guide

**Project:** Cashback Deals v2 - User Features Architecture  
**Version:** 1.0  
**Date:** January 14, 2025  
**Target Audience:** Engineering Team, DevOps, Solution Architects

---

## 📋 Table of Contents

1. [System Architecture Overview](#system-architecture-overview)
2. [Authentication Flow Diagrams](#authentication-flow-diagrams)
3. [Database Architecture](#database-architecture)
4. [API Architecture](#api-architecture)
5. [Frontend Component Architecture](#frontend-component-architecture)
6. [Security Architecture](#security-architecture)
7. [Data Flow Diagrams](#data-flow-diagrams)
8. [Deployment Architecture](#deployment-architecture)

---

## 🏛️ System Architecture Overview

### Current MVP Architecture
```
                    ┌─────────────────────────────────────────┐
                    │              Frontend               │
                    │         (Next.js 15.3.5)            │
                    │                                     │
                    │  ┌─────────────┐  ┌─────────────┐   │
                    │  │   Public    │  │   Contact   │   │
                    │  │   Catalog   │  │    Form     │   │
                    │  │             │  │             │   │
                    │  └─────────────┘  └─────────────┘   │
                    └─────────────────┬───────────────────┘
                                      │
                    ┌─────────────────┴───────────────────┐
                    │              Backend                │
                    │          (API Routes)               │
                    │                                     │
                    │  ┌─────────────┐  ┌─────────────┐   │
                    │  │   Public    │  │   Contact   │   │
                    │  │    API      │  │     API     │   │
                    │  │             │  │  (JWT+HMAC) │   │
                    │  └─────────────┘  └─────────────┘   │
                    └─────────────────┬───────────────────┘
                                      │
                    ┌─────────────────┴───────────────────┐
                    │             Database                │
                    │           (Supabase)                │
                    │                                     │
                    │  ┌─────────────────────────────────┐ │
                    │  │       Public Data RLS           │ │
                    │  │  ✅ products, brands, etc.     │ │
                    │  └─────────────────────────────────┘ │
                    └─────────────────────────────────────┘
```

### Target Phase 2 Architecture
```
                    ┌─────────────────────────────────────────┐
                    │              Frontend               │
                    │         (Next.js 15.3.5)            │
                    │                                     │
                    │  ┌─────────┐ ┌─────────┐ ┌─────────┐ │
                    │  │ Public  │ │  User   │ │ User    │ │
                    │  │ Catalog │ │  Auth   │ │Dashboard│ │
                    │  │         │ │         │ │         │ │
                    │  └─────────┘ └─────────┘ └─────────┘ │
                    │                                     │
                    │  ┌─────────┐ ┌─────────┐ ┌─────────┐ │
                    │  │ Favorites│ │ Claims  │ │Profile  │ │
                    │  │         │ │ System  │ │ Mgmt    │ │
                    │  └─────────┘ └─────────┘ └─────────┘ │
                    └─────────────────┬───────────────────┘
                                      │
                    ┌─────────────────┴───────────────────┐
                    │              Backend                │
                    │          (API Routes)               │
                    │                                     │
                    │  ┌─────────┐ ┌─────────┐ ┌─────────┐ │
                    │  │ Public  │ │  Auth   │ │  User   │ │
                    │  │   API   │ │   API   │ │   API   │ │
                    │  │         │ │(JWT+HMAC│ │ (Auth)  │ │
                    │  └─────────┘ └─────────┘ └─────────┘ │
                    │                                     │
                    │  ┌─────────┐ ┌─────────┐ ┌─────────┐ │
                    │  │Favorites│ │ Claims  │ │Analytics│ │
                    │  │   API   │ │   API   │ │   API   │ │
                    │  │ (Auth)  │ │ (Auth)  │ │ (Auth)  │ │
                    │  └─────────┘ └─────────┘ └─────────┘ │
                    └─────────────────┬───────────────────┘
                                      │
                    ┌─────────────────┴───────────────────┐
                    │          Authentication             │
                    │         System Layer                │
                    │                                     │
                    │  ┌─────────────────────────────────┐ │
                    │  │     JWT + HMAC System           │ │
                    │  │  ✅ Already Implemented         │ │
                    │  └─────────────────────────────────┘ │
                    │                                     │
                    │  ┌─────────────────────────────────┐ │
                    │  │     Supabase Auth Integration   │ │
                    │  │  🔄 Needs Implementation        │ │
                    │  └─────────────────────────────────┘ │
                    └─────────────────┬───────────────────┘
                                      │
                    ┌─────────────────┴───────────────────┐
                    │             Database                │
                    │           (Supabase)                │
                    │                                     │
                    │  ┌─────────────┐ ┌─────────────────┐ │
                    │  │ Public RLS  │ │   User RLS      │ │
                    │  │✅ Active    │ │✅ Ready         │ │
                    │  └─────────────┘ └─────────────────┘ │
                    │                                     │
                    │  ┌─────────────────────────────────┐ │
                    │  │        User Data Tables         │ │
                    │  │ ✅ profiles, favorites, claims  │ │
                    │  └─────────────────────────────────┘ │
                    └─────────────────────────────────────┘
```

---

## 🔐 Authentication Flow Diagrams

### User Registration Flow
```
    User                Frontend            Backend             Supabase          Email Service
     │                     │                  │                   │                   │
     │ 1. Fill registration│                  │                   │                   │
     │    form             │                  │                   │                   │
     ├────────────────────▶│                  │                   │                   │
     │                     │                  │                   │                   │
     │                     │ 2. Validate +    │                   │                   │
     │                     │    CAPTCHA       │                   │                   │
     │                     ├─────────────────▶│                   │                   │
     │                     │                  │                   │                   │
     │                     │                  │ 3. Create user    │                   │
     │                     │                  ├──────────────────▶│                   │
     │                     │                  │                   │                   │
     │                     │                  │ 4. User created + │                   │
     │                     │                  │    verification   │                   │
     │                     │                  │◀──────────────────┤                   │
     │                     │                  │                   │                   │
     │                     │                  │ 5. Send verification email            │
     │                     │                  ├──────────────────────────────────────▶│
     │                     │                  │                   │                   │
     │                     │ 6. Success +     │                   │                   │
     │                     │    instructions  │                   │                   │
     │                     │◀─────────────────┤                   │                   │
     │                     │                  │                   │                   │
     │ 7. Check email      │                  │                   │                   │
     │    notification     │                  │                   │                   │
     │◀────────────────────┤                  │                   │                   │
     │                     │                  │                   │                   │
     │ 8. Click email link │                  │                   │                   │
     ├────────────────────────────────────────┼──────────────────▶│                   │
     │                     │                  │                   │                   │
     │                     │                  │                   │ 9. Email verified │
     │◀────────────────────────────────────────┼───────────────────┤                   │
     │                     │                  │                   │                   │
```

### User Login Flow
```
    User                Frontend            Backend             Supabase            Session Store
     │                     │                  │                   │                     │
     │ 1. Enter credentials│                  │                   │                     │
     ├────────────────────▶│                  │                   │                     │
     │                     │                  │                   │                     │
     │                     │ 2. Validate form│                   │                     │
     │                     │    + HMAC sign   │                   │                     │
     │                     ├─────────────────▶│                   │                     │
     │                     │                  │                   │                     │
     │                     │                  │ 3. Verify with    │                     │
     │                     │                  │    Supabase Auth  │                     │
     │                     │                  ├──────────────────▶│                     │
     │                     │                  │                   │                     │
     │                     │                  │ 4. Auth success + │                     │
     │                     │                  │    user data      │                     │
     │                     │                  │◀──────────────────┤                     │
     │                     │                  │                   │                     │
     │                     │                  │ 5. Generate JWT + │                     │
     │                     │                  │    store session  │                     │
     │                     │                  ├─────────────────────────────────────────▶│
     │                     │                  │                   │                     │
     │                     │ 6. Set HttpOnly  │                   │                     │
     │                     │    cookie + data │                   │                     │
     │                     │◀─────────────────┤                   │                     │
     │                     │                  │                   │                     │
     │ 7. Redirect to      │                  │                   │                     │
     │    dashboard        │                  │                   │                     │
     │◀────────────────────┤                  │                   │                     │
     │                     │                  │                   │                     │
```

### JWT Token Validation Flow (Per Request)
```
    Frontend            Middleware           JWT Service         Session Store       Supabase
       │                   │                    │                   │                │
       │ 1. API request    │                    │                   │                │
       │   with cookies    │                    │                   │                │
       ├──────────────────▶│                    │                   │                │
       │                   │                    │                   │                │
       │                   │ 2. Extract JWT     │                   │                │
       │                   │    from cookie     │                   │                │
       │                   ├───────────────────▶│                   │                │
       │                   │                    │                   │                │
       │                   │                    │ 3. Validate JWT + │                │
       │                   │                    │    check session  │                │
       │                   │                    ├──────────────────▶│                │
       │                   │                    │                   │                │
       │                   │                    │ 4. Session valid + │                │
       │                   │                    │    user context   │                │
       │                   │                    │◀──────────────────┤                │
       │                   │                    │                   │                │
       │                   │ 5. Set auth context│                   │                │
       │                   │    in Supabase     │                   │                │
       │                   ├────────────────────────────────────────────────────────▶│
       │                   │                    │                   │                │
       │                   │ 6. Continue to     │                   │                │
       │                   │    protected route │                   │                │
       │                   │                    │                   │                │
```

---

## 🗄️ Database Architecture

### Current Database Schema (Public Data)
```
┌─────────────────────────────────────────────────────────────────┐
│                        Public Data                             │
│                    (RLS: Public Read)                          │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌───────────┐    ┌───────────┐    ┌─────────────┐             │
│  │ products  │    │  brands   │    │ categories  │             │
│  ├───────────┤    ├───────────┤    ├─────────────┤             │
│  │ id        │◀──┐│ id        │    │ id          │             │
│  │ name      │   ││ name      │   ┌┤ name        │             │
│  │ brand_id  ├───┘│ slug      │   ││ parent_id   │             │
│  │ category  ├────┼─────────────────┘│ slug        │             │
│  │ ...       │    │ ...       │    │ ...         │             │
│  └───────────┘    └───────────┘    └─────────────┘             │
│                                                                 │
│  ┌─────────────┐  ┌─────────────────┐  ┌───────────────────┐   │
│  │ promotions  │  │ product_retailer │  │ retailers         │   │
│  ├─────────────┤  │ _offers         │  ├───────────────────┤   │
│  │ id          │  ├─────────────────┤  │ id                │   │
│  │ title       │  │ product_id      ├─▶│ name              │   │
│  │ brand_id    ├─▶│ retailer_id     ├─▶│ website_url       │   │
│  │ ...         │  │ price           │  │ ...               │   │
│  └─────────────┘  └─────────────────┘  └───────────────────┘   │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### Phase 2 Database Schema (With User Data)
```
┌─────────────────────────────────────────────────────────────────┐
│                        Public Data                             │
│                    (RLS: Public Read)                          │
│                     ✅ Working                                 │
└─────────────────────────────────────────────────────────────────┘
                                │
                                │ Linked via foreign keys
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        User Data                               │
│               (RLS: User-specific Access)                      │
│                     🔄 Ready for Activation                    │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐                                           │
│  │ user_profiles   │                                           │
│  ├─────────────────┤                                           │
│  │ id (auth.uid()) │◀─┐                                        │
│  │ email           │  │                                        │
│  │ full_name       │  │                                        │
│  │ avatar_url      │  │                                        │
│  │ email_verified  │  │                                        │
│  │ created_at      │  │                                        │
│  └─────────────────┘  │                                        │
│                       │                                        │
│  ┌─────────────────┐  │  ┌─────────────────┐                  │
│  │ user_favorites  │  │  │ user_preferences│                  │
│  ├─────────────────┤  │  ├─────────────────┤                  │
│  │ id              │  │  │ id              │                  │
│  │ user_id         ├──┘  │ user_id         ├──┐               │
│  │ product_id      ├──┐  │ email_notifs    │  │               │
│  │ created_at      │  │  │ push_notifs     │  │               │
│  └─────────────────┘  │  │ fav_categories  │  │               │
│                       │  │ ...             │  │               │
│  ┌─────────────────┐  │  └─────────────────┘  │               │
│  │ user_claims     │  │                       │               │
│  ├─────────────────┤  │  ┌─────────────────┐  │               │
│  │ id              │  │  │ user_sessions   │  │               │
│  │ user_id         ├──┘  ├─────────────────┤  │               │
│  │ product_id      ├─────┤ user_id         ├──┘               │
│  │ promotion_id    │     │ session_token   │                  │
│  │ claim_amount    │     │ expires_at      │                  │
│  │ status          │     │ ip_address      │                  │
│  │ receipt_url     │     │ user_agent      │                  │
│  │ ...             │     │ ...             │                  │
│  └─────────────────┘     └─────────────────┘                  │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### RLS Policy Architecture
```
┌─────────────────────────────────────────────────────────────────┐
│                     Row Level Security                         │
│                    Policy Architecture                         │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│ PUBLIC DATA POLICIES (✅ Active)                               │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ CREATE POLICY "public_read_products" ON products            │ │
│ │ FOR SELECT TO public                                        │ │
│ │ USING (status = 'active');                                  │ │
│ │                                                             │ │
│ │ CREATE POLICY "public_read_brands" ON brands                │ │
│ │ FOR SELECT TO public;                                       │ │
│ │                                                             │ │
│ │ CREATE POLICY "public_read_promotions" ON promotions        │ │
│ │ FOR SELECT TO public                                        │ │
│ │ USING (status = 'active');                                  │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ USER DATA POLICIES (🔄 Ready for Activation)                  │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ CREATE POLICY "users_manage_profile" ON user_profiles       │ │
│ │ FOR ALL TO authenticated                                    │ │
│ │ USING (auth.uid() = id);                                    │ │
│ │                                                             │ │
│ │ CREATE POLICY "users_manage_favorites" ON user_favorites    │ │
│ │ FOR ALL TO authenticated                                    │ │
│ │ USING (auth.uid() = user_id);                               │ │
│ │                                                             │ │
│ │ CREATE POLICY "users_manage_claims" ON user_claims          │ │
│ │ FOR ALL TO authenticated                                    │ │
│ │ USING (auth.uid() = user_id);                               │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
│ ADMIN POLICIES (✅ Ready)                                      │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │ CREATE POLICY "admin_full_access" ON user_claims            │ │
│ │ FOR ALL TO authenticated                                    │ │
│ │ USING (                                                     │ │
│ │   auth.jwt() ->> 'role' = 'admin' OR                       │ │
│ │   auth.jwt() ->> 'role' = 'sys_architect'                  │ │
│ │ );                                                          │ │
│ └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

---

## 🔗 API Architecture

### Current API Structure (MVP)
```
/api/
├── search/
│   └── route.ts (✅ Public access with validation)
├── contact/
│   └── route.ts (✅ JWT + HMAC authentication)
└── health/
    └── route.ts (✅ Public health check)
```

### Phase 2 API Structure
```
/api/
├── auth/                    (🔄 New - Authentication)
│   ├── register/
│   │   └── route.ts         (POST: User registration)
│   ├── login/
│   │   └── route.ts         (POST: User login)
│   ├── logout/
│   │   └── route.ts         (POST: User logout)
│   ├── refresh/
│   │   └── route.ts         (POST: Token refresh)
│   ├── forgot-password/
│   │   └── route.ts         (POST: Password reset request)
│   ├── reset-password/
│   │   └── route.ts         (POST: Password reset)
│   └── verify-email/
│       └── route.ts         (GET: Email verification)
│
├── user/                    (🔄 New - User Management)
│   ├── profile/
│   │   └── route.ts         (GET/PUT: User profile)
│   ├── preferences/
│   │   └── route.ts         (GET/PUT: User preferences)
│   └── account/
│       └── route.ts         (DELETE: Account deletion)
│
├── favorites/               (🔄 New - Favorites System)
│   ├── route.ts             (GET/POST: List/Add favorites)
│   └── [productId]/
│       └── route.ts         (DELETE: Remove favorite)
│
├── claims/                  (🔄 New - Claims System)
│   ├── route.ts             (GET/POST: List/Submit claims)
│   ├── [claimId]/
│   │   └── route.ts         (GET/PUT/DELETE: Claim management)
│   └── [claimId]/status/
│       └── route.ts         (GET: Claim status)
│
├── search/                  (✅ Enhanced with user context)
│   └── route.ts             (Public + personalization for users)
├── contact/                 (✅ Existing)
│   └── route.ts             (Public with authentication)
└── health/                  (✅ Existing)
    └── route.ts             (Public health check)
```

### API Authentication Middleware
```typescript
// API Authentication Flow
┌─────────────────────────────────────────────────────────────┐
│                   Request Flow                              │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ 1. Request arrives → Middleware extracts JWT               │
│    ┌─────────────┐    ┌─────────────────┐                 │
│    │   Request   │───▶│  Extract JWT    │                 │
│    │             │    │  from Cookie    │                 │
│    └─────────────┘    └─────────────────┘                 │
│                                │                           │
│ 2. Validate JWT and HMAC       ▼                           │
│    ┌─────────────────┐    ┌─────────────────┐             │
│    │  Validate JWT   │    │  Validate HMAC  │             │
│    │  Signature +    │    │  Request        │             │
│    │  Expiry         │    │  Signature      │             │
│    └─────────────────┘    └─────────────────┘             │
│                                │                           │
│ 3. Set Supabase context        ▼                           │
│    ┌─────────────────────────────────────────┐             │
│    │  Set auth.uid() in Supabase context    │             │
│    │  → Activates RLS policies               │             │
│    └─────────────────────────────────────────┘             │
│                                │                           │
│ 4. Continue to route handler   ▼                           │
│    ┌─────────────────────────────────────────┐             │
│    │  Protected Route Handler                │             │
│    │  → Can access user data via RLS        │             │
│    └─────────────────────────────────────────┘             │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### API Response Standards
```typescript
// Success Response Format
{
  "success": true,
  "data": {
    // Response data
  },
  "pagination": {      // If applicable
    "page": 1,
    "pageSize": 20,
    "total": 100,
    "hasNext": true
  },
  "message": "Operation successful"
}

// Error Response Format
{
  "success": false,
  "error": "Error type",
  "message": "Human-readable error message",
  "details": {         // If applicable
    "fieldErrors": {
      "email": ["Invalid email format"]
    },
    "code": "VALIDATION_ERROR"
  }
}
```

---

## 🎨 Frontend Component Architecture

### Component Hierarchy
```
App (layout.tsx)
├── Header
│   ├── Navigation (Public)
│   ├── UserNavigation (Authenticated) 🔄 New
│   └── AuthButtons (Login/Register) 🔄 New
│
├── Pages
│   ├── Public Pages (✅ Existing)
│   │   ├── Home
│   │   ├── Products
│   │   ├── Brands
│   │   └── Contact
│   │
│   └── Authenticated Pages (🔄 New)
│       ├── Login/Register
│       ├── Dashboard
│       ├── Profile
│       ├── Favorites
│       └── Claims
│
├── Components
│   ├── UI Components (✅ Existing - shadcn/ui)
│   │   ├── Button, Input, Card, etc.
│   │   └── Form components
│   │
│   ├── Enhanced Components (🔄 Modified)
│   │   ├── ProductCard + Favorite button
│   │   ├── ProductDetails + Claim button
│   │   └── SearchResults + Personalization
│   │
│   └── New Components (🔄 New)
│       ├── Authentication
│       │   ├── LoginForm
│       │   ├── RegisterForm
│       │   └── PasswordReset
│       │
│       ├── User Dashboard
│       │   ├── DashboardOverview
│       │   ├── UserProfile
│       │   ├── UserFavorites
│       │   └── UserClaims
│       │
│       └── Interactive Features
│           ├── FavoriteButton
│           ├── ClaimForm
│           └── UserAnalytics
│
└── Providers (🔄 Enhanced)
    ├── AuthProvider 🔄 New
    ├── UserProvider 🔄 New
    ├── ThemeProvider ✅ Existing
    └── QueryProvider ✅ Existing
```

### State Management Architecture
```
┌─────────────────────────────────────────────────────────────┐
│                   State Management                         │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ AUTHENTICATION STATE                                        │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ AuthProvider                                            │ │
│ │ ├── user: User | null                                   │ │
│ │ ├── isLoading: boolean                                  │ │
│ │ ├── isAuthenticated: boolean                            │ │
│ │ ├── login(email, password)                              │ │
│ │ ├── register(userData)                                  │ │
│ │ ├── logout()                                            │ │
│ │ └── refreshAuth()                                       │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ USER DATA STATE                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ UserProvider                                            │ │
│ │ ├── profile: UserProfile | null                        │ │
│ │ ├── preferences: UserPreferences | null                │ │
│ │ ├── updateProfile(data)                                 │ │
│ │ └── updatePreferences(data)                             │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ FAVORITES STATE                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ useFavorites Hook                                       │ │
│ │ ├── favorites: string[]                                 │ │
│ │ ├── isLoading: boolean                                  │ │
│ │ ├── toggleFavorite(productId)                           │ │
│ │ ├── isFavorite(productId)                               │ │
│ │ └── refreshFavorites()                                  │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ CLAIMS STATE                                                │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ useClaims Hook                                          │ │
│ │ ├── claims: UserClaim[]                                 │ │
│ │ ├── isLoading: boolean                                  │ │
│ │ ├── submitClaim(claimData)                              │ │
│ │ ├── updateClaim(id, data)                               │ │
│ │ └── refreshClaims()                                     │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### React Query Integration
```typescript
// API Query Keys Structure
export const queryKeys = {
  // User-related queries
  user: {
    profile: ['user', 'profile'] as const,
    preferences: ['user', 'preferences'] as const,
    favorites: ['user', 'favorites'] as const,
    claims: ['user', 'claims'] as const,
  },
  
  // Enhanced public queries (with user context)
  products: {
    all: ['products'] as const,
    list: (filters: ProductFilters) => ['products', 'list', filters] as const,
    detail: (id: string) => ['products', 'detail', id] as const,
    personalized: (userId: string) => ['products', 'personalized', userId] as const,
  },
  
  // New user-specific queries
  favorites: {
    all: ['favorites'] as const,
    list: () => ['favorites', 'list'] as const,
  },
  
  claims: {
    all: ['claims'] as const,
    list: (filters?: ClaimFilters) => ['claims', 'list', filters] as const,
    detail: (id: string) => ['claims', 'detail', id] as const,
  }
};

// Query Invalidation Strategy
const invalidateUserQueries = () => {
  queryClient.invalidateQueries({ queryKey: queryKeys.user });
  queryClient.invalidateQueries({ queryKey: queryKeys.favorites });
  queryClient.invalidateQueries({ queryKey: queryKeys.claims });
};
```

---

## 🛡️ Security Architecture

### Defense in Depth Strategy
```
┌─────────────────────────────────────────────────────────────┐
│                   Security Layers                          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ LAYER 1: Network & Infrastructure                          │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ✅ Cloudflare Proxy + DDoS Protection                  │ │
│ │ ✅ AWS Amplify Security                                 │ │
│ │ ✅ HTTPS/TLS Encryption                                 │ │
│ │ ✅ Security Headers (CSP, HSTS, etc.)                  │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ LAYER 2: Application Security                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ✅ Input Validation (Zod schemas)                       │ │
│ │ ✅ XSS Prevention (React + DOMPurify)                   │ │
│ │ ✅ CORS Policy Enforcement                              │ │
│ │ ✅ Rate Limiting                                        │ │
│ │ 🔄 CAPTCHA Verification (Turnstile)                    │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ LAYER 3: Authentication & Authorization                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 🔄 JWT Authentication (5-min expiry)                   │ │
│ │ 🔄 HMAC Request Signing                                 │ │
│ │ 🔄 HttpOnly Cookie Storage                              │ │
│ │ 🔄 Session Management                                   │ │
│ │ 🔄 Multi-factor Authentication (future)                │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ LAYER 4: Data Access Control                               │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ ✅ Row Level Security (RLS) Policies                    │ │
│ │ ✅ Database User Isolation                              │ │
│ │ ✅ Principle of Least Privilege                         │ │
│ │ ✅ Audit Logging                                        │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ LAYER 5: Monitoring & Incident Response                    │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ 🔄 Security Event Logging                               │ │
│ │ 🔄 Anomaly Detection                                    │ │
│ │ 🔄 Real-time Alerting                                   │ │
│ │ 🔄 Incident Response Procedures                         │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ Legend: ✅ Implemented | 🔄 Phase 2 Implementation        │
└─────────────────────────────────────────────────────────────┘
```

### Authentication Security Implementation
```typescript
// JWT Security Configuration
const JWT_CONFIG = {
  algorithm: 'HS256',
  expiresIn: '5m',        // Short expiry for security
  issuer: 'cashback-deals',
  audience: 'authenticated-users',
  httpOnly: true,         // Prevent XSS access
  secure: true,           // HTTPS only
  sameSite: 'strict'      // CSRF protection
};

// HMAC Security Configuration
const HMAC_CONFIG = {
  algorithm: 'sha256',
  timestampTolerance: 300,  // 5 minutes
  replayProtection: true,
  secretRotation: true      // Future enhancement
};

// Session Security
const SESSION_CONFIG = {
  maxAge: 24 * 60 * 60 * 1000,  // 24 hours
  rolling: true,                 // Extend on activity
  maxConcurrent: 5,              // Limit concurrent sessions
  ipValidation: true,            // IP consistency check
  userAgentValidation: true      // User agent consistency
};
```

---

## 📊 Data Flow Diagrams

### User Registration Data Flow
```
Frontend              API Route           Supabase Auth        Database
   │                     │                      │                │
   │ 1. Submit form      │                      │                │
   ├────────────────────▶│                      │                │
   │                     │                      │                │
   │                     │ 2. Validate input   │                │
   │                     │    + CAPTCHA         │                │
   │                     │                      │                │
   │                     │ 3. Create user       │                │
   │                     ├─────────────────────▶│                │
   │                     │                      │                │
   │                     │ 4. User created      │                │
   │                     │◀─────────────────────┤                │
   │                     │                      │                │
   │                     │ 5. Create profile              │      │
   │                     ├────────────────────────────────────────▶
   │                     │                      │                │
   │                     │ 6. Profile created             │      │
   │                     │◀────────────────────────────────────────
   │                     │                      │                │
   │ 7. Success response │                      │                │
   │◀────────────────────┤                      │                │
   │                     │                      │                │
```

### Favorites Data Flow
```
User Action          Component           API Route           Database
     │                  │                   │                  │
     │ 1. Click heart   │                   │                  │
     ├─────────────────▶│                   │                  │
     │                  │                   │                  │
     │                  │ 2. Optimistic UI │                  │
     │                  │    update         │                  │
     │                  │                   │                  │
     │                  │ 3. API call       │                  │
     │                  ├──────────────────▶│                  │
     │                  │                   │                  │
     │                  │                   │ 4. Validate JWT  │
     │                  │                   │    + auth user   │
     │                  │                   │                  │
     │                  │                   │ 5. Toggle fav    │
     │                  │                   ├─────────────────▶│
     │                  │                   │                  │
     │                  │                   │ 6. Success       │
     │                  │                   │◀─────────────────┤
     │                  │                   │                  │
     │                  │ 7. Confirm update │                  │
     │                  │◀──────────────────┤                  │
     │                  │                   │                  │
     │ 8. Visual confirm │                  │                  │
     │◀─────────────────┤                   │                  │
     │                  │                   │                  │
```

### Claims Submission Data Flow
```
User                Form Component      API Route           Database           Email Service
 │                      │                  │                  │                    │
 │ 1. Fill claim form   │                  │                  │                    │
 ├─────────────────────▶│                  │                  │                    │
 │                      │                  │                  │                    │
 │                      │ 2. Upload receipt│                  │                    │
 │                      │    validation    │                  │                    │
 │                      │                  │                  │                    │
 │                      │ 3. Submit claim  │                  │                    │
 │                      ├─────────────────▶│                  │                    │
 │                      │                  │                  │                    │
 │                      │                  │ 4. Validate form │                    │
 │                      │                  │    + auth user   │                    │
 │                      │                  │                  │                    │
 │                      │                  │ 5. Save claim    │                    │
 │                      │                  ├─────────────────▶│                    │
 │                      │                  │                  │                    │
 │                      │                  │ 6. Claim saved   │                    │
 │                      │                  │◀─────────────────┤                    │
 │                      │                  │                  │                    │
 │                      │                  │ 7. Send notification                  │
 │                      │                  ├──────────────────────────────────────▶│
 │                      │                  │                  │                    │
 │                      │ 8. Success +     │                  │                    │
 │                      │    claim ID      │                  │                    │
 │                      │◀─────────────────┤                  │                    │
 │                      │                  │                  │                    │
 │ 9. Confirmation      │                  │                  │                    │
 │◀─────────────────────┤                  │                  │                    │
 │                      │                  │                  │                    │
```

---

## 🚀 Deployment Architecture

### Current Deployment (MVP)
```
┌─────────────────────────────────────────────────────────────┐
│                    Production                               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Cloudflare Proxy                                            │
│ ├── DDoS Protection                                         │
│ ├── SSL Termination                                         │
│ ├── Caching                                                 │
│ └── Bot Protection                                          │
│                     │                                       │
│                     ▼                                       │
│ AWS Amplify                                                 │
│ ├── Next.js Application                                     │
│ ├── Static Asset CDN                                        │
│ ├── Environment Variables                                   │
│ └── Build Pipeline                                          │
│                     │                                       │
│                     ▼                                       │
│ Supabase Cloud                                              │
│ ├── PostgreSQL Database                                     │
│ ├── Row Level Security                                      │
│ ├── Real-time APIs                                          │
│ └── Backups                                                 │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Phase 2 Deployment (Enhanced)
```
┌─────────────────────────────────────────────────────────────┐
│                    Production                               │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ Cloudflare Proxy                                            │
│ ├── DDoS Protection                                         │
│ ├── SSL Termination                                         │
│ ├── Caching (Enhanced)                                      │
│ ├── Bot Protection                                          │
│ └── Rate Limiting (Additional layer)                        │
│                     │                                       │
│                     ▼                                       │
│ AWS Amplify                                                 │
│ ├── Next.js Application (Enhanced)                          │
│ │   ├── Public Routes                                       │
│ │   ├── Protected Routes 🔄 New                            │
│ │   └── User Dashboard 🔄 New                              │
│ ├── Static Asset CDN                                        │
│ ├── Environment Variables (Enhanced)                        │
│ │   ├── Authentication Secrets                              │
│ │   └── Feature Flags                                       │
│ └── Build Pipeline (Enhanced)                               │
│     ├── Security Testing                                    │
│     └── User Feature Tests                                  │
│                     │                                       │
│                     ▼                                       │
│ Supabase Cloud (Enhanced)                                   │
│ ├── PostgreSQL Database                                     │
│ │   ├── Public Data (Active)                               │
│ │   └── User Data (Activated) 🔄 New                       │
│ ├── Supabase Auth (Activated) 🔄 New                       │
│ ├── Row Level Security (Enhanced)                           │
│ ├── Real-time APIs                                          │
│ └── Backups & Point-in-time Recovery                        │
│                                                             │
│ Additional Services 🔄 New                                  │
│ ├── Email Service (Notifications)                           │
│ ├── File Storage (Receipt uploads)                          │
│ └── Analytics & Monitoring                                  │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Environment Configuration
```bash
# Phase 2 Environment Variables

# Authentication System (Activate existing framework)
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
DISABLE_HMAC_VALIDATE=false

# User Features (New functionality)
ENABLE_USER_REGISTRATION=true
ENABLE_USER_FAVORITES=true
ENABLE_USER_CLAIMS=true
ENABLE_USER_ANALYTICS=true

# Security Configuration (Enhanced)
JWT_SECRET=${existing_jwt_secret}
HMAC_SECRET=${existing_hmac_secret}
SESSION_SECRET=${new_session_secret}

# Supabase Configuration (Enhanced)
NEXT_PUBLIC_SUPABASE_URL=${existing_url}
NEXT_PUBLIC_SUPABASE_ANON_KEY=${existing_key}
SUPABASE_SERVICE_ROLE_KEY=${existing_service_key}

# Email Service (New)
EMAIL_SERVICE_API_KEY=${email_service_key}
EMAIL_FROM_ADDRESS=<EMAIL>

# File Storage (New - for receipts)
AWS_S3_BUCKET_NAME=${receipt_bucket}
AWS_ACCESS_KEY_ID=${s3_access_key}
AWS_SECRET_ACCESS_KEY=${s3_secret_key}

# Analytics (Enhanced)
GOOGLE_ANALYTICS_ID=${enhanced_analytics_id}
USER_ANALYTICS_ENABLED=true
```

### Feature Flag Strategy
```typescript
// Feature flag implementation for safe rollout
export const FEATURE_FLAGS = {
  // Core authentication
  USER_AUTHENTICATION: process.env.ENABLE_USER_AUTHENTICATION === 'true',
  
  // User features (can be enabled independently)
  USER_REGISTRATION: process.env.ENABLE_USER_REGISTRATION === 'true',
  USER_FAVORITES: process.env.ENABLE_USER_FAVORITES === 'true',
  USER_CLAIMS: process.env.ENABLE_USER_CLAIMS === 'true',
  USER_DASHBOARD: process.env.ENABLE_USER_DASHBOARD === 'true',
  
  // Advanced features
  USER_ANALYTICS: process.env.ENABLE_USER_ANALYTICS === 'true',
  PERSONALIZATION: process.env.ENABLE_PERSONALIZATION === 'true',
  ADVANCED_SEARCH: process.env.ENABLE_ADVANCED_SEARCH === 'true',
  
  // Admin features
  ADMIN_PANEL: process.env.ENABLE_ADMIN_PANEL === 'true',
  CLAIM_PROCESSING: process.env.ENABLE_CLAIM_PROCESSING === 'true'
};

// Gradual rollout strategy
export const ROLLOUT_PERCENTAGE = {
  USER_FEATURES: parseInt(process.env.USER_FEATURES_ROLLOUT || '100'),
  ADVANCED_FEATURES: parseInt(process.env.ADVANCED_FEATURES_ROLLOUT || '0')
};
```

---

## 📈 Performance & Monitoring

### Performance Optimization Strategy
```
┌─────────────────────────────────────────────────────────────┐
│                Performance Monitoring                       │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│ FRONTEND PERFORMANCE                                        │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ • Component-level code splitting                        │ │
│ │ • Lazy loading for user features                        │ │
│ │ • React Query caching for user data                     │ │
│ │ • Optimistic UI updates                                 │ │
│ │ • Image optimization for user uploads                   │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ API PERFORMANCE                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ • Response caching for user data                        │ │
│ │ • Database query optimization                           │ │
│ │ • Pagination for large datasets                         │ │
│ │ • Request deduplication                                 │ │
│ │ • Background processing for claims                      │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
│ DATABASE PERFORMANCE                                        │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ • Proper indexing on user tables                        │ │
│ │ • Query optimization with EXPLAIN                       │ │
│ │ • Connection pooling                                    │ │
│ │ • Read replicas for analytics                           │ │
│ └─────────────────────────────────────────────────────────┘ │
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

### Monitoring Dashboard
```typescript
// Key Performance Indicators (KPIs)
const PERFORMANCE_METRICS = {
  // User Experience
  loginSuccessRate: { target: '>99%', alert: '<95%' },
  pageLoadTime: { target: '<2s', alert: '>3s' },
  apiResponseTime: { target: '<500ms', alert: '>1s' },
  
  // Business Metrics
  userRegistrationRate: { target: '>5%', alert: '<2%' },
  favoriteConversionRate: { target: '>15%', alert: '<10%' },
  claimSubmissionRate: { target: '>10%', alert: '<5%' },
  
  // Technical Metrics
  errorRate: { target: '<0.1%', alert: '>1%' },
  authenticationFailures: { target: '<1%', alert: '>5%' },
  databaseConnectionTime: { target: '<100ms', alert: '>500ms' }
};
```

---

**Document Version:** 1.0  
**Last Updated:** January 14, 2025  
**Review Cycle:** Every 2 weeks during implementation  
**Approval Required:** Engineering Lead, DevOps Lead, Product Manager