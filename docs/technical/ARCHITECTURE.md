# Architecture Documentation

*This file is auto-generated documentation for the Cashback Deals platform architecture. Last updated: 20th July 2025*

## Overview

The Cashback Deals platform is a modern e-commerce application built with Next.js 15 and React 19, featuring a hybrid rendering strategy, centralized data layer, and comprehensive security implementation.

## Technology Stack

### Core Framework
- **Next.js 15.3.5** - App Router with hybrid SSR/SSG (Security upgraded July 2025)
- **React 19.1.0** - Latest React with server components (Stable release upgrade July 2025)
- **TypeScript 5.8.3** - Full type safety across the application
- **Node.js 20.10.0** - Runtime environment (per amplify.yml)

### Database & Backend
- **Supabase 2.50.4** - PostgreSQL database with real-time features
- **Supabase Auth** - Authentication and user management
- **Row Level Security (RLS)** - Database-level security policies
- **Server-side data functions** - Centralized data layer with caching
- **Read-only Supabase client** - Security-focused database access

### Frontend & UI
- **Tailwind CSS 4.1.11** - Utility-first CSS framework (Major version upgrade)
- **shadcn/ui** - Reusable component library
- **Framer Motion 12.23.1** - Animation library
- **Lucide React 0.525.0** - Icon library

### Accessibility Compliance (v15.7.5)
- **WCAG AA Touch Targets** - All interactive elements meet 44x44px minimum requirement
- **Mobile SEO Enhancement** - Improved mobile experience with compliant touch targets
- **Button Component Standards** - Enhanced shadcn/ui button sizes for accessibility
- **Touch Target Utilities** - Custom CSS classes for consistent accessibility compliance
  - `.touch-target`: 44x44px minimum size
  - `.touch-target-square`: Exact 44x44px size
  - `.interactive-base`: Base accessibility-compliant interactive element

### State Management
- **React Query (TanStack Query) 5.82.0** - Server state management
- **URL State** - Client-side routing state via Next.js router
- **usePagination hooks** - Centralized URL parameter management
- **React Context** - Global app state (minimal usage)

### Security & Validation
- **Zod 4.0.0** - Schema validation and type safety
- **isomorphic-dompurify 2.26.0** - XSS protection
- **JWT Authentication** - Frontend authentication with HS256
- **HMAC Authentication** - Partner API authentication with replay protection
- **Rate Limiting** - API protection middleware with IP-based and auth-based modes
- **Cloudflare Turnstile** - Bot protection and CAPTCHA
- **Sentry 9.36.0** - Error tracking and performance monitoring
- **Row Level Security (RLS)** - Database-level security policies for data filtering
- **Centralized Domain Management** - Single source of truth for all domain references

## Rendering Strategy

### Hybrid Approach
The application uses a strategic mix of rendering patterns:

```mermaid
graph TB
    A[User Request] --> B{Route Type}
    B -->|Static Pages| C[SSG - Pre-generated]
    B -->|Dynamic Pages| D[SSR - Server Rendered]
    B -->|Interactive Components| E[Client Components]
    
    C --> F[CDN Cache]
    D --> G[Server Cache]
    E --> H[React Query Cache]
    
    F --> I[User]
    G --> I
    H --> I
```

### Rendering Patterns by Route Type

| Route Pattern | Rendering Strategy | Cache Strategy | Reason |
|---------------|-------------------|----------------|---------|
| `/` (Homepage) | SSG + ISR | 1 hour revalidate | Static content, good SEO |
| `/products` | SSR | Server-side cache | Dynamic filtering, pagination |
| `/products/[id]` | SSG + ISR | 30 min revalidate | Product detail pages, SEO critical |
| `/brands` | SSR | Server-side cache | Alphabetical navigation, filters |
| `/brands/[id]` | SSG + ISR | 1 hour revalidate | Brand detail pages |
| `/search` | SSR | Short cache | Dynamic search results |
| `/api/*` | Server Functions | Tiered caching | API endpoints |

## Data Flow Architecture

### High-Level Data Flow

```mermaid
sequenceDiagram
    participant U as User
    participant C as React Component
    participant H as usePagination Hook
    participant D as Data Layer
    participant RLS as Row Level Security
    participant S as Supabase
    participant Cache as Cache Layer
    
    U->>C: User interaction (page change)
    C->>H: Call goToPage(2)
    H->>H: Update URL params
    H->>D: Fetch data with new params
    D->>Cache: Check cache first
    Cache-->>D: Cache miss/hit
    D->>S: Query database (if cache miss)
    S->>RLS: Apply security policies
    RLS->>S: Filter data by status/permissions
    S-->>D: Return filtered data
    D->>Cache: Store in cache
    D-->>C: Return transformed data
    C->>U: Re-render with new data
```

### Detailed Component Data Flow

```typescript
// Example: Products page data flow
User clicks "Next Page" 
  → ProductsContent component
  → useProductsPagination hook
  → goToPage(2) function
  → URL updates to /products?page=2
  → Server component re-renders
  → getProducts(supabase, filters, page=2)
  → Check cache with key "products:page=2"
  → If cache miss: Query Supabase
  → Transform data to TransformedProduct[]
  → Return to component
  → Re-render with new data
```

## Key Architectural Patterns

### 1. Centralized Data Layer

The application uses a centralized data layer pattern located in `src/lib/data/`:

```typescript
// src/lib/data/index.ts - Single export point
export {
  getProducts,
  getProduct,
  getFeaturedProducts,
  // ... other data functions
} from './products'

// Usage in server components
import { getProducts } from '@/lib/data'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'

const supabase = createServerSupabaseReadOnlyClient()
const products = await getProducts(supabase, filters, page, limit)
```

**Benefits:**
- Consistent data fetching patterns
- Centralized caching strategy
- Type safety across the application
- Easy testing and mocking

### 2. URL State Management

The `usePagination` hook family serves as the single source of truth for URL state:

```typescript
// src/hooks/usePagination.ts
export function usePagination({
  defaultPage = 1,
  pageSize = 20,
  basePath = ''
}: UsePaginationOptions = {}): UsePaginationReturn {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // URL state management logic
  const goToPage = useCallback((page: number) => {
    const params = new URLSearchParams(searchParams?.toString() || '')
    // Clean URL logic - remove page=1 from URL
    if (page === defaultPage) {
      params.delete('page')
    } else {
      params.set('page', page.toString())
    }
    router.push(`${basePath}?${params.toString()}`, { scroll: false })
  }, [router, searchParams, defaultPage, basePath])
  
  return { currentPage, goToPage, updateFilters, clearFilters }
}
```

**Page-specific implementations:**
- `useProductsPagination()` - Products listing
- `useBrandsPagination()` - Brands listing  
- `useRetailersPagination()` - Retailers listing
- `usePromotionsPagination()` - Promotions listing

### 3. Server vs Client Components Strategy

**Server Components (default):**
- Page components (`page.tsx`)
- Layout components
- Data fetching components
- SEO components

**Client Components (`'use client'`):**
- Interactive components with state
- Components using hooks
- Form components
- Pagination components

```typescript
// Server Component - Products page
export default async function ProductsPage({
  searchParams
}: {
  searchParams: { [key: string]: string | string[] | undefined }
}) {
  const supabase = createServerSupabaseReadOnlyClient()
  const products = await getProducts(supabase, filters, page, limit)
  
  return (
    <div>
      <ProductsContent products={products} />
    </div>
  )
}

// Client Component - Interactive content
'use client'
export function ProductsContent({ products }: { products: TransformedProduct[] }) {
  const { currentPage, goToPage } = useProductsPagination()
  
  return (
    <div>
      <ProductGrid products={products} />
      <Pagination currentPage={currentPage} onPageChange={goToPage} />
    </div>
  )
}
```

### 4. Feature-Sliced Architecture

The application follows a feature-sliced architecture:

```
src/
├── app/                    # Next.js App Router
│   ├── (pages)/           # Route groups
│   ├── api/               # API routes
│   ├── sitemaps/          # Dynamic sitemap generation
│   └── globals.css        # Global styles
├── components/            # Reusable components
│   ├── ui/               # Base UI components (shadcn/ui)
│   ├── layout/           # Layout components (includes DeprecationBanner)
│   ├── pages/            # Page-specific components
│   └── search/           # Feature-specific components
├── config/                # Configuration management
│   ├── domains.ts        # Centralized domain management
│   └── sitemap.ts        # Sitemap configuration constants
├── lib/                   # Core utilities
│   ├── data/             # Data layer
│   ├── validation/       # Zod schemas
│   └── security/         # Security utilities
├── hooks/                # Custom React hooks
├── types/                # TypeScript definitions
└── utils/                # Helper functions
```

## Thread Safety & Concurrency

### Supabase Client Management

```typescript
// src/lib/supabase/server.ts
import { createServerClient } from '@supabase/ssr'

export function createServerSupabaseReadOnlyClient() {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      cookies: {
        get: () => undefined,
        set: () => {},
        remove: () => {},
      },
    }
  )
}
```

**Thread Safety Considerations:**
- Each request gets its own Supabase client instance
- Connection pooling handled by Supabase
- No shared state between requests
- Stateless server components

### Caching Strategy

```typescript
// src/lib/cache.ts
export const CACHE_DURATIONS = {
  SHORT: 300,    // 5 minutes - frequently changing data
  MEDIUM: 1800,  // 30 minutes - moderately stable data
  LONG: 3600,    // 1 hour - stable data
  EXTENDED: 86400 // 24 hours - very stable data
}

export function createCachedFunction<T>(
  fn: T,
  config: CacheConfig
): T {
  return unstable_cache(fn, [config.key], {
    revalidate: config.revalidate,
    tags: config.tags,
  }) as T
}
```

## Enhanced Search Architecture (v15.3.3)

### PostgreSQL Full-Text Search Performance Optimization

The search system has been enhanced with PostgreSQL native full-text search capabilities, delivering a **47.9ms UI performance improvement**:

#### Key Performance Enhancements:
- **SearchBar debounce**: Reduced from 300ms → 150ms for faster user response
- **SearchSuggestions debounce**: Optimized from 200ms → 150ms
- **Minimum query length**: Increased from 2 → 3 characters for better relevance
- **Average UI response time**: 152.1ms (down from ~200ms)

#### PostgreSQL Search Features:
- **Primary search engine**: `ts_vector` with `ts_rank` relevance scoring
- **Fallback mechanism**: Trigram similarity for typos and variations
- **Brand alias mapping**: Enhanced discoverability (e.g., "samsung" → "Samsung UK")
- **Smart join optimization**: Inner/left joins based on filter requirements

### Search Implementation Details

```typescript
// Enhanced search with PostgreSQL full-text search
const searchFilters: SearchFilters = {
  query: 'samsung phones',
  brand: 'samsung-uk',
  category: 'electronics',
  sortBy: 'relevance', // Uses ts_rank for text search
  minPrice: 100,
  maxPrice: 1000
}

// Brand alias mapping for better discoverability
const BRAND_ALIASES: Record<string, string[]> = {
  'samsung': ['samsung-uk'],
  'apple': ['apple', 'apple-uk'], 
  'sony': ['sony', 'sony-uk'],
  'lg': ['lg', 'lg-uk'],
}
```

### Search Performance Testing

The system includes comprehensive performance testing with real user simulation:

```bash
# Real UI performance testing
node ui-search-suggestions-test.js

# Test results example:
# ✅ EXCELLENT: Suggestions appear in 152.1ms after typing (feels instant)
# UI Overhead: ~144.1ms (vs ~8ms API-only)
# Success Rate: 100% (15/15 tests)
```

## Search + Pagination Flow Sequence

```mermaid
sequenceDiagram
    participant U as User
    participant SB as SearchBar
    participant SS as SearchSuggestions
    participant PH as usePagination
    participant API as /api/search
    participant DL as Data Layer (Enhanced)
    participant PG as PostgreSQL
    participant Cache as Cache Layer
    
    U->>SB: Type "samsung" (150ms debounce)
    SB->>SS: Trigger suggestions after 3 chars
    SS->>API: GET /api/search/suggestions?q=samsung
    API->>DL: getSearchSuggestions(supabase, query)
    DL->>PG: Brand alias lookup + full-text search
    PG-->>DL: ["Samsung UK", "Samsung Galaxy", ...]
    DL-->>API: Return suggestions with aliases
    API-->>SS: JSON response (avg 8ms)
    SS->>U: Display suggestions (total: 152ms)
    
    U->>SB: Submit search "samsung phones"
    SB->>PH: updateFilters({query: "samsung phones"})
    PH->>PH: Reset page to 1
    PH->>API: GET /api/search?q=samsung%20phones&page=1
    API->>API: Rate limit check
    API->>API: Validate input (Zod)
    API->>DL: searchProducts(supabase, filters, page)
    DL->>Cache: Check cache key "search:samsung%20phones:page=1"
    Cache-->>DL: Cache miss
    DL->>PG: ts_vector full-text search with ts_rank scoring
    Note over DL,PG: Primary: textSearch('search_vector', 'samsung phones')<br/>Fallback: ilike('%samsung phones%')
    PG-->>DL: Raw product data with relevance scores
    DL->>DL: Transform to TransformedProduct[] with brand aliases
    DL->>Cache: Store with 5min TTL
    DL-->>API: Return search results
    API-->>SB: JSON response
    SB->>U: Display results with relevance ranking
    
    U->>SB: Click "Next Page"
    SB->>PH: goToPage(2)
    PH->>API: GET /api/search?q=samsung%20phones&page=2
    API->>DL: searchProducts(supabase, filters, page=2)
    DL->>Cache: Check cache key "search:samsung%20phones:page=2"
    Cache-->>DL: Cache hit (fast response)
    DL-->>API: Return cached results
    API-->>SB: JSON response
    SB->>U: Display page 2 results
```

## Micro-Decisions & Trade-offs

### 1. URL State vs React State

**Decision:** Use URL state for pagination and filters
**Reasoning:** 
- SEO benefits (crawlable pages)
- Shareable URLs
- Browser back/forward support
- Bookmarkable states

### 2. Server vs Client Components

**Decision:** Server components by default, client components for interactivity
**Reasoning:**
- Better performance (less JavaScript)
- Improved SEO
- Reduced bundle size
- Better initial page load

### 3. React Query vs Direct Fetch

**Decision:** React Query for client-side data fetching, direct fetch for server components
**Reasoning:**
- React Query: Caching, error handling, loading states
- Direct fetch: Better SSR performance, no hydration issues

### 4. Supabase RLS vs Application-Level Security

**Decision:** Supabase RLS for data security, application-level for business logic
**Reasoning:**
- Database-level security cannot be bypassed
- Better performance with database-level filtering
- Centralized security policies

## Performance Considerations

### Bundle Optimization
- **Tree shaking**: Enabled for all packages
- **Code splitting**: Automatic route-based splitting
- **Dynamic imports**: For heavy components

### Database Optimization
- **Indexes**: Full-text search, foreign keys, composite indexes
- **Connection pooling**: Handled by Supabase
- **Query optimization**: Specific field selection, joins minimization

### Caching Strategy
- **4-tier cache system**: SHORT/MEDIUM/LONG/EXTENDED
- **Cache invalidation**: Tag-based invalidation
- **Stale-while-revalidate**: Background cache refresh

## Security Architecture

### Dual Authentication System

The application implements a sophisticated dual authentication architecture:

```mermaid
graph TB
    A[Client Request] --> B{Authentication Type}
    B -->|Frontend Users| C[JWT Authentication]
    B -->|Partner APIs| D[HMAC Authentication]
    
    C --> E[HS256 JWT Verification]
    E --> F[5-minute Token Expiry]
    F --> G[Cookie + Header Transport]
    
    D --> H[HMAC-SHA256 Signature]
    H --> I[Timestamp Window Check]
    I --> J[Replay Protection]
    J --> K[Partner Secret Validation]
    
    G --> L[Rate Limiting]
    K --> L
    L --> M[API Access Granted]
```

#### 1. JWT Authentication (Frontend)
- **Algorithm**: HS256 with 256-bit secret
- **Expiry**: 5 minutes (short-lived for security)
- **Transport**: Dual transport (HTTP-only cookies + Authorization header)
- **Payload**: Minimal payload (`sub: 'frontend'`)
- **Security**: Production secret validation, development fallback

#### 2. HMAC Authentication (Partner APIs)
- **Algorithm**: HMAC-SHA256
- **Components**: Method + Path + Timestamp + Body Hash
- **Timestamp Window**: 5 minutes (configurable)
- **Replay Protection**: In-memory cache with automatic cleanup
- **Partner Management**: Multi-partner support with individual secrets
- **Nonce Support**: Optional nonce for additional security

### Defense in Depth
1. **Input Validation** - Zod 4.0.0 schemas for all inputs
2. **Rate Limiting** - Dual-mode: IP-based (unauthenticated) + auth-based (authenticated)
3. **Authentication** - JWT + HMAC dual system
4. **Authorization** - Supabase Row Level Security (RLS)
5. **XSS Protection** - isomorphic-dompurify 2.26.0 sanitization
6. **CSRF Protection** - SameSite cookies + HMAC signatures
7. **HTTP Headers** - CSP, HSTS, X-Frame-Options, X-XSS-Protection
8. **Bot Protection** - Cloudflare Turnstile integration
9. **Error Tracking** - Sentry 9.36.0 with security event logging

## Infrastructure Architecture

### Deployment Infrastructure

```mermaid
graph TB
    A[Developer] --> B[GitHub Repository]
    B --> C[GitHub Actions CI/CD]
    C --> D[Quality Gates]
    D --> E[AWS Amplify]
    
    E --> F[Amplify Build]
    F --> G[Next.js SSG/SSR]
    G --> H[Amplify Hosting]
    
    H --> I[Cloudflare Proxy]
    I --> J[SSL Termination]
    I --> K[Security Headers]
    I --> L[CDN Caching]
    
    L --> M[End Users]
    
    subgraph "AWS Amplify"
        F
        G
        H
    end
    
    subgraph "Cloudflare Layer"
        I
        J
        K
        L
    end
```

### Infrastructure Components
- **Hosting**: AWS Amplify Console with automatic builds
- **CDN**: Cloudflare proxy for SSL termination and security headers
- **Node.js Version**: 20.10.0 (specified in amplify.yml)
- **Build Output**: Next.js standalone output for optimal performance
- **Caching**: Multi-layer caching (Amplify + Cloudflare + Next.js)
- **Database**: Supabase PostgreSQL with global distribution
- **Monitoring**: Sentry error tracking and performance monitoring

### Environment Management
- **Development**: localhost:3000 with .env.local
- **Staging**: staging.amplifyapp.com with Amplify environment variables
- **Production**: Custom domain via Cloudflare with production secrets
- **Testing**: Dedicated test environment with authentication bypasses

## Authentication Implementation Details

### Test Environment Support
The authentication system includes sophisticated test environment support:

```typescript
// Test mode bypasses for CI/CD
if (process.env.NODE_ENV === 'test' && process.env.TEST_MODE_BYPASS_AUTH === 'true') {
  // Mock JWT tokens
  if (token === 'valid_jwt_token') {
    return mockJWTPayload
  }
  
  // Mock HMAC signatures
  if (signature === 'valid_signature') {
    return mockHMACPayload
  }
}
```

### Rate Limiting Architecture

```mermaid
graph TB
    A[API Request] --> B{Authentication Present?}
    B -->|No| C[IP-based Rate Limiting]
    B -->|Yes| D[Auth-based Rate Limiting]
    
    C --> E[Strict Limits]
    D --> F[Generous Limits]
    
    E --> G{Limit Exceeded?}
    F --> G
    
    G -->|Yes| H[429 Rate Limited]
    G -->|No| I[Process Request]
```

### Security Event Logging
- **JWT Authentication Events**: Success/failure with trace IDs
- **HMAC Authentication Events**: Partner ID, timestamp, validation results
- **Rate Limiting Events**: IP addresses, request patterns
- **Security Violations**: XSS attempts, injection attempts
- **Performance Monitoring**: Request duration, error rates

## Domain Configuration & Infrastructure

### Centralized Domain Management (`src/config/domains.ts`)

The application implements a centralized domain configuration system that provides:

**Key Features:**
- **Environment-aware URL generation** - Automatic localhost detection in development
- **Production domain management** - Hardcoded stable domains for security policies
- **CORS origin configuration** - Centralized management of allowed origins
- **Canonical domain fallbacks** - Non-browser request handling

```typescript
// src/config/domains.ts
export const SITE_URL = getSiteUrl(); // Environment-aware base URL
export const PRODUCTION_DOMAINS = {
  AWS_AMPLIFY: {
    CURRENT: 'https://4-2.d3q274urye85k3.amplifyapp.com',
    MAIN: 'https://main.d3pcuskj59hcq9.amplifyapp.com',
    STAGING: 'https://staging.d3pcuskj59hcq9.amplifyapp.com',
  },
  CUSTOM: 'https://cashbackdeals.com'
};
```

**Benefits:**
- **Security**: Eliminates hardcoded domains in application code
- **Maintainability**: Single source of truth for all domain references
- **Deployment**: Environment-specific configuration without code changes
- **CORS**: Centralized management of allowed origins

### Enhanced Sitemap Architecture

#### Breadcrumb Navigation Pattern

**Dynamic Routing Strategy**

Breadcrumb navigation has been enhanced to use search-based routing for more flexible and SEO-friendly navigation:

```typescript
// Breadcrumb generation example
function generateBreadcrumbs(page: string, params: any) {
  switch(page) {
    case 'product':
      return [
        { label: 'Home', href: '/' },
        { 
          label: params.category, 
          href: `/search?category=${params.categorySlug}` 
        },
        { label: params.productName, href: null }
      ];
    case 'brand':
      return [
        { label: 'Home', href: '/' },
        { 
          label: 'Brands', 
          href: '/search?type=brand' 
        },
        { label: params.brandName, href: null }
      ];
  }
}
```

**Key Navigation Changes:**
- Replaced direct category page links (`/categories/${slug}`) with dynamic search-based routing
- Implemented flexible breadcrumb generation for products and brands
- Maintains SEO compatibility with structured data
- Supports dynamic filtering and navigation

**Benefits of Search-Based Routing:**
- More flexible navigation
- Easier maintenance of category/brand links
- Improved performance with search-optimized routing
- Better scalability for future content structures

#### Sitemap Performance Optimizations
- **Compression headers** - Automatic gzip/brotli compression for XML responses
- **Literal revalidate values** - Next.js static analysis compatibility
- **Configuration centralization** - `SITEMAP_PAGE_SIZE`, `CACHE_TTL_SITEMAP` constants
- **Enhanced caching strategy** - ISR with optimal cache-control headers

```typescript
// src/config/sitemap.ts
export const SITEMAP_HEADERS = {
  'Cache-Control': 'public, max-age=0, s-maxage=86400, stale-while-revalidate=3600',
  'Content-Type': 'application/xml',
  'Vary': 'Accept-Encoding',
};

// src/app/sitemaps/brands/[page]/route.ts
export const revalidate = 86400; // Literal value for Next.js static analysis
```

**Architecture Features:**
- **Paginated sitemaps** - 5000 URLs per sitemap file for optimal crawler performance
- **Dynamic generation** - Real-time data from Supabase with RLS filtering
- **Comprehensive testing** - Automated validation of sitemap structure and content
- **SEO optimization** - Proper lastmod dates using database timestamps

## User Communication System

### DeprecationBanner Component

**Features:**
- **Environment-controlled visibility** - Show/hide via environment variables
- **User dismissal persistence** - localStorage-based dismissal tracking
- **Accessibility compliance** - ARIA labels and semantic HTML
- **Responsive design** - Works across all device sizes

```typescript
// Environment configuration
NEXT_PUBLIC_SHOW_DEPRECATION_BANNER=true
NEXT_PUBLIC_DEPRECATION_SERVICE=CloudFront
NEXT_PUBLIC_DEPRECATION_DATE=2025-12-31
```

**Use Cases:**
- Service migration announcements (CloudFront → Cloudflare)
- API deprecation warnings
- Infrastructure change notifications
- Planned maintenance communications

## OpenGraph Metadata Limitations (v15.7.3)

### Technical Limitations and Engineering Trade-offs

**CRITICAL LIMITATION**: Next.js 15.3.5 App Router has a fundamental incompatibility with OpenGraph `product` type metadata, leading to the following architecture decisions:

#### OpenGraph Product Type Crisis Resolution (Aug 6, 2025)
```typescript
// FINAL IMPLEMENTATION - DO NOT MODIFY WITHOUT EXPLICIT CONFIRMATION
// Location: src/app/products/[id]/page.tsx - generateMetadata function

openGraph: {
  // NO type: 'product' - completely removed due to Next.js limitations
  title,
  description,
  url: `${process.env.NEXT_PUBLIC_SITE_URL}/products/${product.slug || product.id}`,
  siteName: 'RebateRay',
  locale: 'en_GB',
  images: primaryImage ? [{ url: primaryImage, alt: `${product.name} product image` }] : undefined,
},
// NOTE: Product-specific metadata removed entirely due to Next.js validation restrictions
// SEO is maintained through comprehensive JSON-LD structured data below
```

#### Engineering Trade-offs Made:
1. **Page Stability Priority**: Eliminated "Invalid OpenGraph type: product" validation errors that caused page failures
2. **Basic Social Sharing Only**: OpenGraph limited to title, description, image, and URL
3. **Advanced Product Metadata Sacrifice**: No product-specific OpenGraph tags (price, brand, availability)
4. **SEO Compensation**: Comprehensive JSON-LD structured data preserves full SEO capabilities
5. **Framework Limitation**: Next.js App Router rejects ALL attempts to use `og:type="product"` regardless of implementation

#### What This Means for Development:
- **DO NOT** attempt to re-add `og:type="product"` - will cause validation errors
- **DO NOT** use client-side Head components - doesn't work in App Router  
- **Social sharing will be basic** - this is an accepted trade-off for stability
- **SEO remains comprehensive** - JSON-LD structured data provides full product metadata
- **Page stability is guaranteed** - no console errors or validation warnings

#### Rollback Considerations:
- Reverting to advanced OpenGraph metadata will break page loading
- Alternative approaches (client-side injection, different metadata formats) have all been tested and failed
- The current implementation represents the only stable solution for Next.js 15.3.5+

## Next Steps / TODO

- [ ] Implement Redis for distributed caching and rate limiting
- [ ] Add real-time updates with Supabase subscriptions
- [x] ~~Implement advanced search with full-text search optimization~~ ✅ **Completed v15.3.3** - PostgreSQL full-text search with 47.9ms performance improvement
- [x] ~~Centralize domain configuration management~~ ✅ **Completed Aug 2025** - Domain configuration system with environment-aware URL generation
- [x] ~~Implement Row Level Security for data filtering~~ ✅ **Completed Aug 2025** - Database-level security policies for retailers and offers
- [x] ~~Optimize sitemap performance and architecture~~ ✅ **Completed Aug 2025** - Compression headers, centralized config, enhanced testing
- [x] ~~Resolve OpenGraph product type validation crisis~~ ✅ **Completed v15.7.3** - Framework limitations resolved through engineering trade-offs
- [ ] Add comprehensive API documentation with OpenAPI spec
- [ ] Implement chaos engineering tests for resilience
- [ ] Add comprehensive error boundary system
- [ ] Implement progressive web app features
- [ ] Add internationalization (i18n) support
- [ ] Implement blue-green deployment strategy
- [ ] Add automated security scanning in CI/CD
- [ ] **NEW**: Implement search analytics and query optimization based on usage patterns
- [ ] **NEW**: Add search result caching with smart invalidation for frequently searched terms
- [ ] **NEW**: Implement auto-complete with machine learning for better suggestions
- [ ] **NEW**: Document advanced breadcrumb navigation strategy with real-world implementation examples
- [ ] **FUTURE**: Monitor Next.js framework updates for potential OpenGraph product type support

### Revision History
- **[07 AUG 2025]**: Added Accessibility Compliance section documenting WCAG AA touch target implementation in v15.7.5.
- **[06 AUG 2025]**: Added OpenGraph Metadata Limitations section documenting v15.7.3 crisis resolution and technical trade-offs.

---

**Last Updated**: 6th August 2025  
**Version**: 2.2 - OpenGraph Crisis Resolution Documentation  
**Next Review**: November 2025