# Security Implementation & Guidelines

*This file is auto-generated documentation for security measures and implementation. Last updated: 20th July 2025*

## Security Overview

The Cashback Deals platform implements a comprehensive security-first approach with multiple layers of protection against common web vulnerabilities, including XSS, CSRF, injection attacks, and unauthorized access.

## Security Architecture

### Defense in Depth Strategy

```mermaid
graph TB
    User[User Request] --> CF[Cloudflare Protection]
    CF --> Guard[Runtime Security Guard-Rail]
    Guard --> CSP[Content Security Policy]
    CSP --> Headers[HTTP Security Headers]
    Headers --> Rate[Rate Limiting]
    Rate --> Validation[Input Validation]
    Validation --> Auth[Authentication]
    Auth --> RLS[Row Level Security]
    RLS --> Audit[Audit Logging]
    
    subgraph "Layer 0: Environment"
        Guard
        Supply[Supply Chain Protection]
        Secrets[Secrets Masking]
    end
    
    subgraph "Layer 1: Network"
        CF
    end
    
    subgraph "Layer 2: Application"
        CSP
        Headers
        Rate
    end
    
    subgraph "Layer 3: Input"
        Validation
    end
    
    subgraph "Layer 4: Access"
        Auth
        RLS
    end
    
    subgraph "Layer 5: Monitoring"
        Audit
    end
```

## 🔒 CRITICAL: Runtime Security Guard-Rails

### Production Security Protection System

The application implements **fail-fast security guard-rails** that prevent test-only bypass flags from being deployed to production environments. This system provides the first line of defense at the environment level.

#### Security Guard-Rail Architecture

```mermaid
sequenceDiagram
    participant App as Application Startup
    participant Guard as Security Guard-Rail
    participant Env as Environment Variables
    participant CI as CI/CD Pipeline
    participant Build as Build Artifacts
    
    Note over App,Build: Application Startup Protection
    App->>Guard: Import env-guard.ts
    Guard->>Env: Validate Environment Variables
    alt Unsafe flags detected in production
        Guard->>App: THROW SecurityError
        App->>App: Application refuses to start
    else Safe configuration
        Guard->>App: Allow startup to continue
    end
    
    Note over CI,Build: Build-Time Protection
    CI->>Build: Scan .next/ artifacts
    alt Test flags found in build
        CI->>CI: FAIL deployment
    else Clean build
        CI->>CI: Continue deployment
    end
```

#### Implementation: Runtime Guard-Rail

```typescript
// src/lib/env-guard.ts
const isTestEnv = process.env.NODE_ENV === 'test' || process.env.CI === 'true'

// Define unsafe flags that should only be enabled in test/CI environments
const UNSAFE_FLAGS = [
  'TEST_MODE_BYPASS_AUTH',
  'ENABLE_RATE_LIMITING', // If this flag exists and is 'false', it's unsafe
  'SKIP_ENV_VALIDATION',
  'DISABLE_HMAC_VALIDATE',
  'TEST_MODE_BYPASS_CORS',
  'BYPASS_IP_ALLOWLIST',
] as const

// Define flags that are unsafe when set to specific values
const UNSAFE_FLAG_VALUES: Record<string, string[]> = {
  'ENABLE_RATE_LIMITING': ['false'],
  'ENABLE_IP_ALLOWLIST': ['false'], // Only unsafe in production
  'ENABLE_SEARCH_AUTH': ['false'],
  'ENABLE_HMAC_AUTH': ['false'],
}

function validateEnvironmentSecurity(): void {
  if (isTestEnv) {
    // In test environments, all flags are allowed
    return
  }

  const violations: string[] = []

  // Check for boolean bypass flags that should never be 'true' in production
  UNSAFE_FLAGS.forEach((flag) => {
    if (process.env[flag] === 'true') {
      violations.push(
        `${flag}=true is not allowed in production (NODE_ENV=${process.env.NODE_ENV})`
      )
    }
  })

  // Check for flags with specific unsafe values
  Object.entries(UNSAFE_FLAG_VALUES).forEach(([flag, unsafeValues]) => {
    const currentValue = process.env[flag]
    if (currentValue && unsafeValues.includes(currentValue)) {
      // Special case: IP allowlist can be disabled in development
      if (flag === 'ENABLE_IP_ALLOWLIST' && process.env.NODE_ENV === 'development') {
        return
      }
      
      violations.push(
        `${flag}=${currentValue} is not allowed in production (NODE_ENV=${process.env.NODE_ENV})`
      )
    }
  })

  // If violations found, fail fast with clear error message
  if (violations.length > 0) {
    const errorMessage = [
      '🚨 SECURITY VIOLATION: Test-only bypass flags detected in production environment!',
      '',
      'The following unsafe environment variables are set:',
      ...violations.map(v => `  • ${v}`),
      '',
      'These flags are only allowed when NODE_ENV=test or CI=true.',
      'Please check your deployment configuration and environment variables.',
      '',
      'For security reasons, the application will not start with these settings.',
    ].join('\n')

    throw new Error(errorMessage)
  }
}

// Runtime validation check - this runs immediately when the module is imported
try {
  validateEnvironmentSecurity()
  
  // Log security status in development for debugging
  if (process.env.NODE_ENV === 'development') {
    console.log('🔒 Environment security guard-rail: PASSED')
  }
} catch (error) {
  // Log the security violation and re-throw to stop application startup
  console.error(error instanceof Error ? error.message : String(error))
  throw error
}
```

#### Integration: Application Startup

```typescript
// src/app/layout.tsx
// SECURITY: Import environment guard-rail first to validate security settings
import '@/lib/env-guard'

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Application continues only if security guard-rail passes
  return (
    <html lang="en">
      <body>
        {children}
      </body>
    </html>
  )
}
```

## 🔐 Supply Chain Security Protection

### Version Pinning Strategy

To prevent malicious dependency injections and ensure consistent builds, all CI/CD dependencies are pinned to specific versions:

#### Critical Dependencies
- **wait-on@8.0.3** - Server readiness checking in CI workflows
- **@lhci/cli@0.15.x** - Lighthouse CI auditing tool
- **jq@1.7.1** - JSON processing in performance monitoring

#### Security Benefits
- **Prevents Supply Chain Attacks**: Fixed versions eliminate risk of compromised updates
- **Ensures Consistent Builds**: Reproducible CI/CD pipeline behavior
- **Security Patch Management**: Controlled update process with security review

#### Implementation
```yaml
# All GitHub workflows use pinned versions:
- name: Install dependencies
  run: |
    npm install -g wait-on@8.0.3
    npm install -g @lhci/cli@0.15.x
    sudo apt-get install -y jq  # System package manager ensures consistent version
```

#### Version Update Process
1. **Security Advisory Monitoring**: Track CVEs for pinned dependencies
2. **Controlled Updates**: Manual version bumps with security review
3. **Testing Pipeline**: Full CI/CD validation before deployment
4. **Rollback Capability**: Previous versions maintained for emergency rollback

## 🛡️ Secrets Protection System

### GitHub Actions Secrets Masking

All workflows implement comprehensive secrets masking to prevent accidental credential exposure in CI logs:

```yaml
- name: Mask potentially sensitive environment values
  run: |
    echo "::add-mask::eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."  # Service role key
    echo "::add-mask::ci-jwt-secret-minimum-32-characters..."        # JWT secret
    echo "::add-mask::ci-test-default-secret-minimum-32..."          # Partner secret
    echo "::add-mask::1x0000000000000000000000000000000AA"           # Turnstile secret
    echo "🔒 Sensitive values masked from CI logs"
```

### Protected Values
- **Supabase Service Role Keys**: Even mock keys are masked to prevent pattern recognition
- **JWT Secrets**: Mock JWT secrets masked to prevent real key exposure patterns
- **Partner API Keys**: Test partner keys masked for consistency
- **Third-party Service Keys**: Cloudflare Turnstile and similar service keys

### Leak Detection
Additional scanning prevents real credential leakage:
```yaml
# Check for accidental real credential leakage
if grep -r "mock-project.supabase.co" .next/ 2>/dev/null | head -3; then
  echo "❌ SECURITY VIOLATION: Real Supabase project URL found in build artifacts"
  violations_found=true
fi
```

#### Build-Time Security Scanning

All GitHub workflows include comprehensive security scanning:

```yaml
# .github/workflows/*.yml
- name: Security check - Scan build artifacts for test flags
  run: |
    echo "🔍 Scanning build artifacts for accidentally included test flags..."
    
    # Define unsafe flags that should never appear in production builds
    UNSAFE_FLAGS=(
      "TEST_MODE_BYPASS_AUTH=true"
      "ENABLE_RATE_LIMITING=false"
      "SKIP_ENV_VALIDATION=true"
      "DISABLE_HMAC_VALIDATE=true"
      "TEST_MODE_BYPASS_CORS=true"
      "BYPASS_IP_ALLOWLIST=true"
    )
    
    violations_found=false
    
    # Check for unsafe flags in the build output
    for flag in "${UNSAFE_FLAGS[@]}"; do
      if grep -r --include="*.js" --include="*.html" --include="*.json" "$flag" .next/ 2>/dev/null | head -5; then
        echo "❌ SECURITY VIOLATION: Found '$flag' in build artifacts"
        violations_found=true
      fi
    done
    
    # Check for common test patterns
    if grep -r --include="*.js" "NODE_ENV.*test.*bypass\|TEST.*MODE.*true" .next/ 2>/dev/null | head -3; then
      echo "❌ SECURITY VIOLATION: Found test bypass patterns in build artifacts"
      violations_found=true
    fi
    
    if [ "$violations_found" = true ]; then
      echo ""
      echo "🚨 SECURITY CHECK FAILED!"
      echo "Test-only bypass flags found in production build artifacts."
      echo "This could result in disabled security in production."
      echo ""
      echo "Please check your environment variables and build process."
      exit 1
    else
      echo "✅ Security check passed - No unsafe flags found in build artifacts"
    fi
```

### Security Guard-Rail Features

#### ✅ Runtime Protection
- **Fail-Fast Design**: Application refuses to start with unsafe configurations
- **Environment Detection**: Smart detection of test/CI vs production environments
- **Comprehensive Coverage**: Protects against all known security bypass flags
- **Clear Error Messages**: Detailed violation reports with remediation guidance

#### ✅ Build-Time Protection
- **Artifact Scanning**: Automated detection of test flags in compiled code
- **Deployment Blocking**: Build fails if unsafe flags are found in production artifacts
- **Pattern Detection**: Advanced pattern matching for test bypass mechanisms
- **CI/CD Integration**: Seamless integration with GitHub Actions workflows

#### ✅ Multi-Layered Security
- **Runtime Validation**: Guards at application startup
- **Build Validation**: Guards at deployment time
- **Environment Isolation**: Clear separation between test and production configurations
- **Audit Trail**: Comprehensive logging of security violations

### Security Guard-Rail Configuration

#### Protected Environment Variables

| Variable | Production Safe | Test/CI Safe | Description |
|----------|----------------|---------------|-------------|
| `TEST_MODE_BYPASS_AUTH` | ❌ Never `true` | ✅ Allowed | Bypasses authentication for testing |
| `ENABLE_RATE_LIMITING` | ❌ Never `false` | ✅ Allowed | Controls API rate limiting |
| `DISABLE_HMAC_VALIDATE` | ❌ Never `true` | ✅ Allowed | Bypasses HMAC signature validation |
| `ENABLE_SEARCH_AUTH` | ❌ Never `false` | ✅ Allowed | Controls search endpoint authentication |
| `ENABLE_HMAC_AUTH` | ❌ Never `false` | ✅ Allowed | Controls HMAC authentication system |
| `ENABLE_IP_ALLOWLIST` | ⚠️ Can be `false` in dev | ✅ Allowed | Controls IP allowlist validation |
| `TEST_MODE_BYPASS_CORS` | ❌ Never `true` | ✅ Allowed | Bypasses CORS validation |
| `BYPASS_IP_ALLOWLIST` | ❌ Never `true` | ✅ Allowed | Bypasses IP allowlist checks |

#### Emergency Response

If security violations are detected:

1. **Immediate Response**: Application refuses to start with clear error message
2. **Error Details**: Specific flag violations listed with remediation guidance
3. **Rollback Capability**: Previous version can be restored within minutes
4. **Monitoring**: All security events are logged for audit and investigation

### Security Guard-Rail Testing

#### Validation Tests

```typescript
// Test that security guard-rail prevents unsafe configurations
describe('Security Guard-Rail', () => {
  beforeEach(() => {
    // Reset environment
    delete process.env.TEST_MODE_BYPASS_AUTH
    delete process.env.ENABLE_RATE_LIMITING
    process.env.NODE_ENV = 'production'
  })

  it('should prevent TEST_MODE_BYPASS_AUTH=true in production', () => {
    process.env.TEST_MODE_BYPASS_AUTH = 'true'
    
    expect(() => {
      require('@/lib/env-guard')
    }).toThrow('🚨 SECURITY VIOLATION: Test-only bypass flags detected in production environment!')
  })

  it('should prevent ENABLE_RATE_LIMITING=false in production', () => {
    process.env.ENABLE_RATE_LIMITING = 'false'
    
    expect(() => {
      require('@/lib/env-guard')
    }).toThrow('ENABLE_RATE_LIMITING=false is not allowed in production')
  })

  it('should allow all flags in test environment', () => {
    process.env.NODE_ENV = 'test'
    process.env.TEST_MODE_BYPASS_AUTH = 'true'
    process.env.ENABLE_RATE_LIMITING = 'false'
    
    expect(() => {
      require('@/lib/env-guard')
    }).not.toThrow()
  })
})
```

## Authentication & Authorization

### Dual Authentication Architecture

The application supports both JWT and HMAC authentication for API endpoints:

- **JWT Authentication**: Used for user-facing features (contact forms, user actions)
- **HMAC Authentication**: Used for partner API access with cryptographic signatures
- **Public Endpoints**: Search suggestions are public with IP-based rate limiting

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant P as Partner
    participant A as API Route
    participant S as Supabase Auth
    participant DB as Database
    
    Note over U,DB: JWT Authentication Flow
    U->>F: User Action
    F->>S: Get JWT Token
    S->>F: Return JWT
    F->>A: API Request + JWT Bearer Token
    A->>A: Validate JWT
    A->>DB: Query with User Context
    DB->>A: User-specific Results
    A->>F: Response Data
    
    Note over P,DB: HMAC Authentication Flow
    P->>P: Generate HMAC Signature
    P->>A: API Request + HMAC Headers
    A->>A: Verify HMAC Signature
    A->>A: Check Replay Protection
    A->>DB: Query with Partner Context
    DB->>A: Partner-specific Results
    A->>P: Response Data
    
    Note over U,A: Public Endpoint (Suggestions)
    U->>A: Search Suggestions Request
    A->>A: Apply IP Rate Limiting
    A->>DB: Query Public Data
    DB->>A: Public Results
    A->>U: Suggestions Response
```

### Authentication Decision Matrix

| Endpoint | Authentication | Rate Limiting | Notes |
|----------|---------------|---------------|-------|
| `/api/search` | JWT OR HMAC | Standard | Full search functionality |
| `/api/search/more` | JWT OR HMAC | Standard | Load more results |
| `/api/search/suggestions` | **Public** | IP-based (10 req/sec) | Better UX, non-sensitive data |
| `/api/contact` | JWT + Turnstile | Strict (5 req/10min) | Contact form protection |
| `/api/auth/*` | Public | Standard | Authentication endpoints |

### JWT Token Management

```typescript
// src/lib/security/jwt.ts
import { jwtVerify, SignJWT } from 'jose'
import { NextRequest } from 'next/server'

const secret = new TextEncoder().encode(process.env.JWT_SECRET!)

export async function verifyJWT(token: string) {
  try {
    const { payload } = await jwtVerify(token, secret)
    return payload
  } catch (error) {
    console.error('JWT verification failed:', error)
    return null
  }
}

export async function createJWT() {
  return await new SignJWT({ sub: 'frontend' })
    .setProtectedHeader({ alg: 'HS256', typ: 'JWT' })
    .setExpirationTime('5m') // 5 minutes for security
    .setIssuedAt()
    .sign(secret)
}

// Extract JWT from request headers
export function getJWTFromRequest(request: NextRequest): string | null {
  const authHeader = request.headers.get('Authorization')
  if (authHeader?.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }
  
  // Also check cookies for client-side requests
  const cookieToken = request.cookies.get('auth-token')
  return cookieToken?.value || null
}
```

### HMAC Authentication

HMAC (Hash-based Message Authentication Code) provides cryptographically secure API access for partners:

```typescript
// src/lib/security/hmac.ts
import crypto from 'crypto'

export function generateHMACSignature(
  method: string,
  path: string,
  timestamp: number,
  body: string = '',
  secret: string
): string {
  // Create SHA256 hash of request body
  const bodyHash = crypto.createHash('sha256').update(body).digest('hex')
  
  // Create message string for signing
  const message = `${method}\n${path}\n${timestamp}\n${bodyHash}`
  
  // Generate HMAC signature
  return crypto
    .createHmac('sha256', secret)
    .update(message)
    .digest('hex')
}

export function verifyHMACSignature(
  signature: string,
  method: string,
  path: string,
  timestamp: number,
  body: string = '',
  secret: string
): boolean {
  const expectedSignature = generateHMACSignature(method, path, timestamp, body, secret)
  
  // Use constant-time comparison to prevent timing attacks
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  )
}
```

#### HMAC Request Headers

Partners must include the following headers for HMAC authentication:

```typescript
const headers = {
  'X-Signature': `sha256=${signature}`,
  'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
  'X-Partner-ID': 'your-partner-id',
  'Content-Type': 'application/json'
}
```

#### HMAC Security Features

- **Timing-safe comparison**: Prevents timing attacks
- **Replay protection**: 5-minute timestamp window with duplicate request detection
- **Partner isolation**: Each partner has unique secrets via environment variables
- **Automatic cleanup**: Memory management for replay cache
- **Comprehensive logging**: Security events tracked for audit purposes

### ✅ **NEW: Enhanced Authentication Middleware (August 2025)**

#### Centralized Domain-Aware Authentication

The enhanced authentication middleware (`src/lib/security/auth-middleware.ts`) provides unified authentication handling with centralized domain management:

```typescript
// src/lib/security/auth-middleware.ts
import { CORS_ORIGINS, getCanonicalDomain } from '@/config/domains';
import { verifyRequestJWT, verifyRequestHMAC } from './jwt';

// Dual authentication check for search endpoints
export async function authenticateSearchRequest(request: NextRequest): Promise<AuthResult> {
  const traceId = generateTraceId(request.method)
  const endpoint = new URL(request.url).pathname
  
  // Try JWT first (browser users with CAPTCHA)
  try {
    const jwtPayload = await verifyRequestJWT(request)
    if (jwtPayload) {
      return { success: true, method: 'JWT', payload: jwtPayload, traceId }
    }
  } catch (error) {
    console.warn('JWT verification error:', error)
  }
  
  // Try HMAC second (API partners) - only if enabled
  if (isHMACEnabled()) {
    try {
      const hmacPayload = await verifyRequestHMAC(request)
      if (hmacPayload) {
        return { success: true, method: 'HMAC', payload: hmacPayload, traceId }
      }
    } catch (error) {
      console.warn('HMAC verification error:', error)
    }
  }
  
  return { success: false, method: null, payload: null, error: 'No valid authentication found', traceId }
}
```

#### Domain-Integrated CORS Management

The middleware integrates with the centralized domain configuration for CORS handling:

```typescript
// Centralized CORS origins from domain configuration
const ALLOWED_ORIGINS = [
  ...CORS_ORIGINS,  // From src/config/domains.ts
  ...getAllowedAmplifyDomains()
]

// Get allowed origin for CORS header
export function getAllowedOrigin(request: NextRequest): string {
  const origin = request.headers.get('origin')
  
  if (origin && isOriginAllowed(origin)) {
    return origin
  }
  
  // Default to canonical domain for non-browser requests
  return getCanonicalDomain()
}
```

#### Enhanced Security Logging

Comprehensive security event logging with trace IDs for audit compliance:

```typescript
// Security event logging interface
interface SecurityEvent {
  type: 'JWT_AUTH_SUCCESS' | 'HMAC_AUTH_SUCCESS' | 'HMAC_AUTH_FAILURE'
  endpoint: string
  method: string
  partnerId?: string
  ip: string
  timestamp: string
  traceId: string
  error?: string
  errorMessage?: string
}

// Log security events for audit
export function logSecurityEvent(event: SecurityEvent): void {
  console.log(`Security Event [${event.traceId}]:`, {
    type: event.type,
    endpoint: event.endpoint,
    method: event.method,
    partnerId: event.partnerId,
    ip: event.ip,
    timestamp: event.timestamp,
    error: event.error,
    errorMessage: event.errorMessage
  })
}
```

#### Middleware Wrapper Pattern

Unified middleware wrapper for consistent authentication handling:

```typescript
// Middleware wrapper for search endpoints
export async function withSearchAuthentication(
  request: NextRequest,
  handler: (request: NextRequest, authResult: AuthResult) => Promise<NextResponse>
): Promise<NextResponse> {
  // Apply authentication
  const authResult = await authenticateSearchRequest(request)
  
  if (!authResult.success) {
    console.warn(`Unauthorized access attempt from ${getClientIP(request)}`)
    return createSearchUnauthorizedResponse(authResult.traceId)
  }
  
  // Log successful authentication
  console.log(`API access: ${authResult.method} authentication successful`, {
    endpoint: new URL(request.url).pathname,
    traceId: authResult.traceId,
    partnerId: authResult.method === 'HMAC' ? (authResult.payload as HMACPayload)?.partnerId : undefined
  })
  
  // Call the actual handler with authentication result
  return handler(request, authResult)
}
```

#### Security Features

**Enhanced Security:**
- **Dual authentication support** - JWT and HMAC in a single request handler
- **Centralized domain management** - Single source of truth for CORS origins
- **Comprehensive audit logging** - Trace ID tracking for all security events
- **Client IP detection** - Multi-header IP extraction for accurate logging

**Operational Benefits:**
- **Simplified API development** - Single middleware wrapper for all endpoints
- **Consistent error responses** - Standardized unauthorized response handling
- **Feature flag support** - Environment-based authentication toggle
- **Development debugging** - Enhanced logging for troubleshooting

### Row Level Security (RLS) Policies

#### User Data Protection
```sql
-- Users can only access their own data
CREATE POLICY "Users can view own profile"
    ON public.users FOR SELECT
    TO authenticated
    USING (auth.uid() = id);

CREATE POLICY "Users can update own profile"
    ON public.users FOR UPDATE
    TO authenticated
    USING (auth.uid() = id);

-- User purchases are private
CREATE POLICY "Users can view own purchases"
    ON public.user_purchases FOR SELECT
    TO authenticated
    USING (user_id = auth.uid());

CREATE POLICY "Users can create purchase records"
    ON public.user_purchases FOR INSERT
    TO authenticated
    WITH CHECK (user_id = auth.uid());
```

#### Public Data Access
```sql
-- Products are viewable by everyone (only active ones)
CREATE POLICY "Products viewable by everyone"
    ON public.products FOR SELECT
    TO anon
    USING (status = 'active');

-- Brands are public
CREATE POLICY "Brands viewable by everyone"
    ON public.brands FOR SELECT
    TO anon
    USING (true);

-- Promotions are public but filtered
CREATE POLICY "Active promotions viewable by everyone"
    ON public.promotions FOR SELECT
    TO anon
    USING (status = 'active' AND purchase_end_date >= current_date);
```

#### Administrative Access
```sql
-- Staff can view audit logs
CREATE POLICY "Staff can view audit logs"
    ON public.audit_log FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM auth.users
            WHERE auth.uid() = id
            AND user_metadata->>'role' = 'staff'
        )
    );

-- Admin-only access to sensitive data
CREATE POLICY "Admin can manage all data"
    ON public.products FOR ALL
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM auth.users
            WHERE auth.uid() = id
            AND user_metadata->>'role' = 'admin'
        )
    );
```

#### ✅ **NEW: Enhanced Retailer Security Policies (August 2025)**

Database-level security filtering for retailer and offer visibility:

```sql
-- Enable RLS on retailers table
ALTER TABLE retailers ENABLE ROW LEVEL SECURITY;

-- Policy 1: Allow public read access to active retailers only
-- This is the main policy for public-facing features like sitemaps, product listings, etc.
CREATE POLICY "Public read access to active retailers" ON retailers
    FOR SELECT 
    USING (status = 'active');

-- Policy 2: Allow unrestricted read access for service role
-- This allows backend operations and admin functions to access all retailers regardless of status
CREATE POLICY "Service role unrestricted access" ON retailers
    FOR ALL 
    USING (auth.jwt() ->> 'role' = 'service_role');

-- Policy 3: Allow unrestricted read access for authenticated users with admin role
-- This allows admin users to manage retailers including inactive ones
CREATE POLICY "Admin unrestricted access" ON retailers
    FOR ALL 
    USING (
        auth.jwt() ->> 'role' = 'authenticated' 
        AND auth.jwt() ->> 'user_role' = 'admin'
    );

-- Enable RLS on product_retailer_offers table
ALTER TABLE product_retailer_offers ENABLE ROW LEVEL SECURITY;

-- Policy: Only show offers from active retailers
CREATE POLICY "Show offers from active retailers only" ON product_retailer_offers
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM retailers 
            WHERE retailers.id = product_retailer_offers.retailer_id 
            AND retailers.status = 'active'
        )
    );

-- Service role bypass for product_retailer_offers
CREATE POLICY "Service role unrestricted offers access" ON product_retailer_offers
    FOR ALL 
    USING (auth.jwt() ->> 'role' = 'service_role');

-- Admin unrestricted access for product_retailer_offers
CREATE POLICY "Admin unrestricted offers access" ON product_retailer_offers
    FOR ALL 
    USING (
        auth.jwt() ->> 'role' = 'authenticated' 
        AND auth.jwt() ->> 'user_role' = 'admin'
    );
```

**RLS Policy Benefits:**
- **Database-level enforcement** - Security policies applied at the database level, not application
- **Automatic filtering** - Inactive retailers automatically excluded from public queries
- **Service role bypass** - Backend operations can access all data when needed using service role
- **Admin access** - Admin users can manage all retailers regardless of status
- **Performance optimized** - RLS policies use indexes efficiently with minimal overhead (~5ms)
- **Cascade filtering** - Product offers automatically filtered by retailer status

## Input Validation & Sanitization

### Zod Schema Validation

```typescript
// src/lib/validation/schemas.ts
import { z } from 'zod'

// Security-focused validation patterns
const SUSPICIOUS_PATTERNS = [
  /<script/i,
  /javascript:/i,
  /vbscript:/i,
  /on\w+=/i,
  /<iframe/i,
  /eval\(/i,
  /alert\(/i,
  /document\./i,
  /window\./i,
  /select\s+.*\s+from/i,
  /union\s+select/i,
  /insert\s+into/i,
  /delete\s+from/i,
  /drop\s+table/i,
]

export const safeString = z.string().refine((val) => {
  return !SUSPICIOUS_PATTERNS.some(pattern => pattern.test(val))
}, 'Input contains potentially dangerous content')

export const searchQuery = z.string()
  .min(1, 'Search query is required')
  .max(200, 'Search query too long')
  .refine((val) => {
    return !SUSPICIOUS_PATTERNS.some(pattern => pattern.test(val))
  }, 'Search query contains suspicious content')

// Email validation with security checks
export const email = z.string()
  .email('Invalid email format')
  .max(255, 'Email too long')
  .refine((val) => {
    const suspiciousPatterns = [
      /javascript:/i,
      /vbscript:/i,
      /<.*>/,
      /script/i,
      /\.\./,
    ]
    return !suspiciousPatterns.some(pattern => pattern.test(val))
  }, 'Email contains suspicious content')
```

### API Input Validation

```typescript
// src/app/api/search/route.ts
import { validateInput, searchApiSchema } from '@/lib/validation/schemas'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const params = Object.fromEntries(searchParams)
    
    // Validate all input parameters
    const validation = validateInput(searchApiSchema, params)
    
    if (!validation.success) {
      return NextResponse.json(
        {
          error: 'Invalid input parameters',
          details: validation.details
        },
        { status: 400 }
      )
    }
    
    // Use validated data
    const { q, category, brand, sort, page, limit } = validation.data
    
    // Continue with validated inputs only
    const results = await searchProducts(supabase, {
      query: q,
      category,
      brand,
      sortBy: sort
    }, page, limit)
    
    return NextResponse.json(results)
  } catch (error) {
    console.error('Search API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
```

### XSS Prevention

```typescript
// src/lib/security/xss.ts
import DOMPurify from 'isomorphic-dompurify'

interface SanitizeOptions {
  allowedTags?: string[]
  allowedAttributes?: Record<string, string[]>
  stripIgnoreTag?: boolean
}

export function sanitizeHtml(
  dirty: string,
  options: SanitizeOptions = {}
): string {
  const config = {
    ALLOWED_TAGS: options.allowedTags || ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li'],
    ALLOWED_ATTR: options.allowedAttributes || {
      '*': ['class'],
      'a': ['href', 'title'],
      'img': ['src', 'alt', 'title'],
    },
    STRIP_IGNORE_TAG: options.stripIgnoreTag !== false,
    REMOVE_SCRIPT_TAG: true,
    REMOVE_COMMENTS: true,
  }
  
  return DOMPurify.sanitize(dirty, config)
}

// React component for safe HTML rendering
export function SafeHtml({ 
  html, 
  className = '',
  options = {} 
}: {
  html: string
  className?: string
  options?: SanitizeOptions
}) {
  const sanitizedHtml = sanitizeHtml(html, options)
  
  return (
    <div 
      className={className}
      dangerouslySetInnerHTML={{ __html: sanitizedHtml }}
    />
  )
}
```

## HTTP Security Headers

### Content Security Policy (CSP)

```typescript
// next.config.js - Security Headers Configuration
const getSecurityHeaders = () => {
  const cspDirectives = {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "https://challenges.cloudflare.com", // Turnstile
      ...(isDevelopment ? ["'unsafe-eval'", "'unsafe-inline'"] : [])
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'" // Required for Tailwind CSS
    ],
    'img-src': [
      "'self'",
      "data:",
      "https://*.supabase.co",
      "https://images.samsung.com",
      "https://placehold.co",
      "https://*.amazonaws.com",
      "https://*.cloudfront.net"
    ],
    'connect-src': [
      "'self'",
      "https://*.supabase.co",
      "wss://*.supabase.co"
    ],
    'font-src': ["'self'", "data:"],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
    'frame-src': ["https://challenges.cloudflare.com"],
    'upgrade-insecure-requests': []
  }
  
  const cspString = Object.entries(cspDirectives)
    .map(([directive, sources]) => {
      if (sources.length === 0) return directive
      return `${directive} ${sources.join(' ')}`
    })
    .join('; ')
  
  return [
    {
      key: 'Content-Security-Policy',
      value: cspString
    },
    {
      key: 'X-Content-Type-Options',
      value: 'nosniff'
    },
    {
      key: 'X-Frame-Options',
      value: 'DENY'
    },
    {
      key: 'X-XSS-Protection',
      value: '1; mode=block'
    },
    {
      key: 'Referrer-Policy',
      value: 'strict-origin-when-cross-origin'
    },
    {
      key: 'Permissions-Policy',
      value: 'camera=(), microphone=(), geolocation=(), payment=(), usb=(), browsing-topics=()'
    }
  ]
}
```

### HSTS and Security Headers

```typescript
// Production-only security headers
if (isProduction || isStaging) {
  baseHeaders.push({
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  })
  
  baseHeaders.push({
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  })
}

// API-specific headers
const apiHeaders = [
  {
    key: 'Cache-Control',
    value: 'public, s-maxage=1800, stale-while-revalidate=3600'
  },
  {
    key: 'Access-Control-Allow-Origin',
    value: process.env.NODE_ENV === 'production' 
      ? 'https://yourdomain.com' 
      : '*'
  },
  {
    key: 'Access-Control-Allow-Methods',
    value: 'GET, POST, PUT, DELETE, OPTIONS'
  },
  {
    key: 'Access-Control-Allow-Headers',
    value: 'Content-Type, Authorization'
  }
]
```

## Rate Limiting & DoS Protection

### Dual Rate Limiting Strategy

The application implements two types of rate limiting:

1. **Authentication-based rate limiting**: For protected endpoints
2. **IP-based rate limiting**: For public endpoints

### IP-based Rate Limiting (Public Endpoints)

```typescript
// src/lib/security/ip-rate-limiter.ts
interface IPRateLimitConfig {
  requestsPerSecond: number
  windowSeconds: number
}

export const SUGGESTIONS_RATE_LIMIT: IPRateLimitConfig = {
  requestsPerSecond: 10,  // 10 requests per second
  windowSeconds: 60       // 1-minute window = 600 total requests
}

export function applyIPRateLimit(
  request: NextRequest, 
  config: IPRateLimitConfig
): NextResponse | null {
  const ip = getClientIP(request)
  const now = Date.now()
  const windowMs = config.windowSeconds * 1000
  
  // Get or create rate limit entry for this IP
  let entry = ipRateLimits.get(ip)
  
  if (!entry || now - entry.lastReset > windowMs) {
    entry = { requests: 0, lastReset: now }
    ipRateLimits.set(ip, entry)
  }
  
  const maxRequests = config.requestsPerSecond * config.windowSeconds
  
  if (entry.requests >= maxRequests) {
    return NextResponse.json(
      {
        error: 'Rate limit exceeded',
        message: `Too many requests. Limit: ${config.requestsPerSecond} requests per second.`,
        retryAfter: Math.ceil((entry.lastReset + windowMs - now) / 1000)
      },
      { status: 429 }
    )
  }
  
  entry.requests++
  return null
}
```

### Authentication-based Rate Limiting

```typescript
// src/lib/rateLimiter.ts
interface RateLimitConfig {
  maxRequests: number
  windowSizeInSeconds: number
  identifier?: string
}

// Rate limit configurations by endpoint
export const rateLimits = {
  search: {
    maxRequests: 100,
    windowSizeInSeconds: 60,
    identifier: 'search'
  },
  contact: {
    maxRequests: 5,
    windowSizeInSeconds: 600, // 10 minutes
    identifier: 'contact'
  },
  products: {
    maxRequests: 30,
    windowSizeInSeconds: 60,
    identifier: 'products'
  }
}

export function applyRateLimit(
  request: NextRequest,
  config: RateLimitConfig
): NextResponse | null {
  const ip = getClientIP(request)
  const rateLimitKey = `${ip}:${config.identifier}`
  
  let requestData = ipRequestCounts.get(rateLimitKey)
  const now = Date.now()
  
  if (!requestData || now > requestData.resetTime) {
    requestData = {
      count: 0,
      resetTime: now + (config.windowSizeInSeconds * 1000)
    }
  }
  
  requestData.count++
  ipRequestCounts.set(rateLimitKey, requestData)
  
  if (requestData.count > config.maxRequests) {
    const timeUntilReset = Math.ceil((requestData.resetTime - now) / 1000)
    
    // Log rate limit exceeded
    console.warn(`Rate limit exceeded for ${ip} on ${config.identifier}`)
    
    return new NextResponse(
      JSON.stringify({
        error: 'Too many requests',
        message: `Rate limit exceeded. Try again in ${timeUntilReset} seconds.`
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'X-RateLimit-Limit': config.maxRequests.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': Math.ceil(requestData.resetTime / 1000).toString(),
          'Retry-After': timeUntilReset.toString()
        }
      }
    )
  }
  
  return null
}

function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const cfIP = request.headers.get('cf-connecting-ip')
  
  return cfIP || realIP || forwarded?.split(',')[0].trim() || 'unknown'
}
```

### Cloudflare Turnstile Integration

```typescript
// src/lib/security/turnstile.ts
export async function verifyTurnstileToken(token: string, ip: string): Promise<boolean> {
  try {
    const response = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        secret: process.env.TURNSTILE_SECRET_KEY,
        response: token,
        remoteip: ip,
      }),
    })
    
    const data = await response.json()
    
    if (data.success) {
      return true
    } else {
      console.warn('Turnstile verification failed:', data['error-codes'])
      return false
    }
  } catch (error) {
    console.error('Turnstile verification error:', error)
    return false
  }
}

// Usage in API routes
export async function POST(request: NextRequest) {
  const { turnstileToken, ...formData } = await request.json()
  const clientIP = getClientIP(request)
  
  // Verify Turnstile token
  const isValidToken = await verifyTurnstileToken(turnstileToken, clientIP)
  
  if (!isValidToken) {
    return NextResponse.json(
      { error: 'Invalid security token' },
      { status: 400 }
    )
  }
  
  // Process form submission
  // ...
}
```

## Database Security

### Connection Security

```typescript
// src/lib/supabase/server.ts
import { createServerClient } from '@supabase/ssr'

export function createServerSupabaseReadOnlyClient() {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      cookies: {
        // Disable cookie handling for server-side client
        get: () => undefined,
        set: () => {},
        remove: () => {},
      },
      auth: {
        persistSession: false,
        autoRefreshToken: false,
      },
    }
  )
}

// Separate client for authenticated operations
export function createServerSupabaseClient() {
  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      cookies: {
        get: (name) => cookies().get(name)?.value,
        set: (name, value, options) => cookies().set(name, value, options),
        remove: (name, options) => cookies().delete(name),
      },
    }
  )
}
```

### SQL Injection Prevention

```typescript
// src/lib/data/products.ts
// Always use parameterized queries
export async function getProductsByBrand(
  supabase: SupabaseClient,
  brandId: string,
  page: number = 1,
  limit: number = 20
): Promise<PaginatedResponse<TransformedProduct>> {
  const offset = (page - 1) * limit
  
  // Parameterized query - prevents SQL injection
  const { data, error, count } = await supabase
    .from('products')
    .select(`
      *,
      brand:brand_id (id, name, slug, logo_url),
      category:category_id (id, name, slug)
    `, { count: 'exact' })
    .eq('brand_id', brandId) // Parameterized
    .eq('status', 'active')
    .range(offset, offset + limit - 1)
    .order('created_at', { ascending: false })
  
  if (error) {
    console.error('Database error:', error)
    throw new Error('Failed to fetch products')
  }
  
  return {
    data: data.map(transformProduct),
    pagination: {
      page,
      pageSize: limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit),
      hasNext: page < Math.ceil((count || 0) / limit),
      hasPrev: page > 1
    }
  }
}
```

### Database Audit Logging

```sql
-- Audit trigger function
CREATE OR REPLACE FUNCTION audit_changes()
RETURNS trigger AS $$
BEGIN
    INSERT INTO audit_log (
        table_name,
        record_id,
        operation,
        old_data,
        new_data,
        changed_by
    )
    VALUES (
        TG_TABLE_NAME,
        COALESCE(NEW.id, OLD.id),
        TG_OP,
        CASE WHEN TG_OP = 'DELETE' THEN row_to_json(OLD) ELSE NULL END,
        CASE WHEN TG_OP IN ('INSERT', 'UPDATE') THEN row_to_json(NEW) ELSE NULL END,
        auth.uid()
    );
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY INVOKER;

-- Apply audit triggers to sensitive tables
CREATE TRIGGER audit_products
    AFTER INSERT OR UPDATE OR DELETE ON products
    FOR EACH ROW EXECUTE FUNCTION audit_changes();

CREATE TRIGGER audit_users
    AFTER INSERT OR UPDATE OR DELETE ON users
    FOR EACH ROW EXECUTE FUNCTION audit_changes();
```

## Secrets Management

### Environment Variables

```bash
# Production secrets (never commit these)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
JWT_SECRET=your-jwt-secret-key-minimum-32-characters
TURNSTILE_SECRET_KEY=your-turnstile-secret

# Authentication feature flags
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true

# HMAC configuration
HMAC_TIMESTAMP_WINDOW=300
PARTNER_SECRET_DEFAULT=your-default-partner-secret-minimum-32-chars
PARTNER_SECRET_PARTNER1=partner1-specific-secret-minimum-32-chars
PARTNER_SECRET_PARTNER2=partner2-specific-secret-minimum-32-chars

# Email configuration
EMAIL_SERVER=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-app-password

# API keys
STRIPE_SECRET_KEY=sk_live_...
WEBHOOK_SECRET=whsec_...
```

#### Environment Variable Security Requirements

- **JWT_SECRET**: Minimum 32 characters, cryptographically random
- **PARTNER_SECRET_***: Minimum 32 characters, unique per partner
- **HMAC_TIMESTAMP_WINDOW**: Seconds (default: 300 = 5 minutes)
- **Feature flags**: Use 'true'/'false' strings for authentication control

### Key Rotation Procedures

```typescript
// src/lib/security/keyRotation.ts
interface KeyRotationConfig {
  keyId: string
  rotationIntervalDays: number
  notificationEmail: string
}

export async function rotateJWTSecret(oldSecret: string, newSecret: string) {
  // 1. Update environment variable
  process.env.JWT_SECRET = newSecret
  
  // 2. Invalidate all existing tokens
  await invalidateAllTokens()
  
  // 3. Log rotation event
  await logSecurityEvent('JWT_SECRET_ROTATED', {
    timestamp: new Date().toISOString(),
    rotatedBy: 'system'
  })
  
  // 4. Send notification
  await sendSecurityNotification(
    'JWT secret has been rotated',
    'All users will need to re-authenticate'
  )
}

export async function checkKeyRotationSchedule() {
  const lastRotation = await getLastKeyRotation('JWT_SECRET')
  const daysSinceRotation = getDaysSince(lastRotation)
  
  if (daysSinceRotation >= 90) { // Rotate every 90 days
    await scheduleKeyRotation('JWT_SECRET')
  }
}
```

### CI/CD Secrets Management

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to Vercel
      uses: amondnet/vercel-action@v25
      with:
        vercel-token: ${{ secrets.VERCEL_TOKEN }}
        vercel-org-id: ${{ secrets.ORG_ID }}
        vercel-project-id: ${{ secrets.PROJECT_ID }}
        vercel-args: '--prod'
        
    - name: Run security checks
      env:
        SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY }}
        TURNSTILE_SECRET_KEY: ${{ secrets.TURNSTILE_SECRET_KEY }}
      run: |
        npm run test:security
        npm run audit:security
```

## Security Monitoring & Incident Response

### Security Event Logging

```typescript
// src/lib/security/hmac.ts
interface SecurityEvent {
  type: 'HMAC_AUTH_SUCCESS' | 'HMAC_AUTH_FAILURE' | 'JWT_AUTH_SUCCESS' | 'JWT_AUTH_FAILURE'
  endpoint: string
  method: string
  partnerId?: string
  ip: string
  timestamp: string
  traceId: string
  error?: HMACError
  errorMessage?: string
}

export function logSecurityEvent(event: SecurityEvent): void {
  // Downgrade log level for suggestions endpoint to DEBUG to reduce log noise
  let level = event.type.includes('FAILURE') ? 'WARN' : 'INFO'
  if (event.endpoint === '/api/search/suggestions' && event.type.includes('FAILURE')) {
    level = 'DEBUG'
  }
  
  const logEntry = {
    level,
    message: `Security Event: ${event.type}`,
    traceId: event.traceId,
    endpoint: event.endpoint,
    method: event.method,
    partnerId: event.partnerId,
    ip: event.ip,
    timestamp: event.timestamp,
    error: event.error,
    errorMessage: event.errorMessage
  }

  // Only log DEBUG level in development
  if (level === 'DEBUG' && process.env.NODE_ENV === 'production') {
    return
  }

  // Log to CloudWatch via console (structured logging)
  console.log(JSON.stringify(logEntry))
}

// Generate trace ID for request correlation
export function generateTraceId(method?: string): string {
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2, 9)
  const methodPrefix = method ? `${method.toLowerCase()}-` : ''
  const shortHash = timestamp.toString(36).substring(-4)
  return `hmac-${methodPrefix}${shortHash}-${random}`
}
```

#### Security Logging Features

- **Structured logging**: JSON format for easy parsing by log aggregators
- **Trace IDs**: Correlation across multiple requests and services
- **Smart log levels**: DEBUG level for suggestions endpoint to reduce noise
- **Comprehensive context**: IP, endpoint, method, partner ID, error details
- **Production optimization**: DEBUG logs filtered out in production

### Automated Security Scanning

```typescript
// src/lib/security/scanning.ts
export async function runSecurityScan() {
  const results = {
    vulnerabilities: [],
    recommendations: [],
    score: 0
  }
  
  // Check for common vulnerabilities
  results.vulnerabilities.push(...await scanForXSS())
  results.vulnerabilities.push(...await scanForSQLInjection())
  results.vulnerabilities.push(...await scanForCSRF())
  
  // Check security headers
  results.vulnerabilities.push(...await scanSecurityHeaders())
  
  // Check dependencies
  results.vulnerabilities.push(...await scanDependencies())
  
  // Calculate security score
  results.score = calculateSecurityScore(results.vulnerabilities)
  
  return results
}

export async function scanForXSS(): Promise<SecurityVulnerability[]> {
  const vulnerabilities: SecurityVulnerability[] = []
  
  // Check for dangerouslySetInnerHTML usage
  const dangerousUsage = await findDangerouslySetInnerHTML()
  if (dangerousUsage.length > 0) {
    vulnerabilities.push({
      type: 'XSS_RISK',
      severity: 'HIGH',
      description: 'Unsafe HTML rendering detected',
      files: dangerousUsage
    })
  }
  
  return vulnerabilities
}
```

## Incident Response Procedures

### Security Incident Classification

| Severity | Examples | Response Time | Actions |
|----------|----------|---------------|---------|
| **Critical** | Data breach, SQL injection, XSS exploitation | 15 minutes | Immediate containment, disable affected systems |
| **High** | Authentication bypass, privilege escalation | 1 hour | Investigate, patch, monitor |
| **Medium** | Rate limiting bypass, suspicious activity | 4 hours | Review logs, implement fixes |
| **Low** | Configuration issues, minor vulnerabilities | 24 hours | Schedule fix, update documentation |

### Incident Response Checklist

#### Immediate Response (0-15 minutes)
- [ ] **Identify** the security incident
- [ ] **Classify** severity level
- [ ] **Contain** the threat (disable systems if needed)
- [ ] **Notify** security team
- [ ] **Document** initial findings

#### Short-term Response (15 minutes - 1 hour)
- [ ] **Investigate** the extent of the breach
- [ ] **Preserve** evidence and logs
- [ ] **Implement** temporary fixes
- [ ] **Notify** stakeholders
- [ ] **Monitor** for continued threats

#### Medium-term Response (1-24 hours)
- [ ] **Develop** permanent fixes
- [ ] **Test** security patches
- [ ] **Deploy** fixes to production
- [ ] **Update** security policies
- [ ] **Conduct** post-incident review

#### Long-term Response (24+ hours)
- [ ] **Analyze** root cause
- [ ] **Implement** preventive measures
- [ ] **Update** security training
- [ ] **Review** security architecture
- [ ] **Document** lessons learned

### Communication Templates

```typescript
// src/lib/security/templates.ts
export const SECURITY_INCIDENT_TEMPLATE = `
SECURITY INCIDENT ALERT

Severity: {{severity}}
Incident Type: {{type}}
Detected At: {{timestamp}}
Affected Systems: {{systems}}

Description:
{{description}}

Immediate Actions Taken:
{{actions}}

Current Status: {{status}}

Contact: <EMAIL>
Reference: {{incidentId}}
`

export const SECURITY_PATCH_TEMPLATE = `
SECURITY PATCH NOTIFICATION

Patch ID: {{patchId}}
Release Date: {{releaseDate}}
Severity: {{severity}}

Vulnerabilities Fixed:
{{vulnerabilities}}

Deployment Schedule:
- Staging: {{stagingDate}}
- Production: {{productionDate}}

Downtime Expected: {{downtime}}
`
```

## Security Testing & Validation

### Automated Security Tests

```typescript
// src/__tests__/security/comprehensive.test.ts
describe('Comprehensive Security Tests', () => {
  describe('Input Validation', () => {
    it('prevents XSS attacks', async () => {
      const maliciousInputs = [
        '<script>alert("xss")</script>',
        'javascript:alert("xss")',
        '<img src="x" onerror="alert(\'xss\')">',
        'vbscript:alert("xss")'
      ]
      
      for (const input of maliciousInputs) {
        const result = await validateInput(searchApiSchema, { q: input })
        expect(result.success).toBe(false)
      }
    })
    
    it('prevents SQL injection', async () => {
      const sqlInjectionInputs = [
        "'; DROP TABLE products; --",
        "' OR '1'='1",
        "' UNION SELECT * FROM users --"
      ]
      
      for (const input of sqlInjectionInputs) {
        const result = await validateInput(searchApiSchema, { q: input })
        expect(result.success).toBe(false)
      }
    })
  })
  
  describe('Authentication Security', () => {
    it('rejects invalid JWT tokens', async () => {
      const invalidTokens = [
        'invalid.token.here',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature',
        ''
      ]
      
      for (const token of invalidTokens) {
        const result = await verifyJWT(token)
        expect(result).toBeNull()
      }
    })
    
    it('enforces rate limiting', async () => {
      const request = new NextRequest('http://localhost/api/search', {
        headers: { 'x-forwarded-for': '***********' }
      })
      
      // Exceed rate limit
      for (let i = 0; i <= rateLimits.search.maxRequests; i++) {
        const result = applyRateLimit(request, rateLimits.search)
        
        if (i < rateLimits.search.maxRequests) {
          expect(result).toBeNull()
        } else {
          expect(result?.status).toBe(429)
        }
      }
    })
  })
})
```

## Best Practices Summary

### Development Security Guidelines

1. **Input Validation**: Always validate and sanitize user inputs
2. **Authentication**: Use strong authentication mechanisms
3. **Authorization**: Implement proper access controls
4. **Secrets Management**: Never commit secrets to version control
5. **HTTPS**: Always use HTTPS in production
6. **Security Headers**: Implement comprehensive security headers
7. **Rate Limiting**: Protect against abuse and DoS attacks
8. **Audit Logging**: Log all security-relevant events
9. **Regular Updates**: Keep dependencies updated
10. **Security Testing**: Implement comprehensive security testing

### Security Review Checklist

- [ ] **Input validation** implemented for all user inputs
- [ ] **Authentication** properly configured and tested
- [ ] **Authorization** controls in place and verified
- [ ] **Rate limiting** implemented on all API endpoints
- [ ] **Security headers** configured correctly
- [ ] **Secrets** properly managed and rotated
- [ ] **Audit logging** enabled for security events
- [ ] **Dependencies** scanned for vulnerabilities
- [ ] **Security tests** passing
- [ ] **Incident response** procedures documented

## Test Environment Security

### Authentication Bypasses for Testing

The authentication system includes secure bypasses for automated testing:

```typescript
// Test mode bypasses in src/lib/security/jwt.ts
if (process.env.NODE_ENV === 'test' && process.env.TEST_MODE_BYPASS_AUTH === 'true') {
  if (token === 'valid_jwt_token') {
    return {
      sub: 'frontend',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 300
    }
  }
}

// Test mode bypasses in src/lib/security/hmac.ts
if (process.env.NODE_ENV === 'test' && process.env.TEST_MODE_BYPASS_AUTH === 'true') {
  if (hmacData.signature === 'valid_signature') {
    return {
      partnerId: hmacData.partnerId,
      timestamp: hmacData.timestamp,
      method: request.method,
      path: new URL(request.url).pathname,
      isValid: true,
      nonce: hmacData.nonce
    }
  }
}
```

### Test Environment Configuration

```bash
# .env.test - Test environment variables
NODE_ENV=test
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
TEST_MODE_BYPASS_AUTH=true
JWT_SECRET=test-secret-min-32-chars-for-testing
PARTNER_SECRET_DEFAULT=test-partner-secret-min-32-chars-required
```

### Security Safeguards for Test Bypasses

1. **Environment Isolation**: Bypasses only work when `NODE_ENV=test`
2. **Explicit Flag**: Requires `TEST_MODE_BYPASS_AUTH=true` flag
3. **Mock Tokens**: Only specific mock tokens (`valid_jwt_token`, `valid_signature`) work
4. **Production Protection**: Bypasses are never active in production environments
5. **CI/CD Integration**: Test environment automatically configured in GitHub Actions

### Test Authentication Examples

```typescript
// JWT Authentication Test
const response = await fetch('/api/search', {
  headers: {
    'Authorization': 'Bearer valid_jwt_token',
    'Content-Type': 'application/json'
  }
})

// HMAC Authentication Test
const response = await fetch('/api/search', {
  headers: {
    'X-Signature': 'valid_signature',
    'X-Timestamp': Math.floor(Date.now() / 1000).toString(),
    'X-Partner-ID': 'test-partner',
    'Content-Type': 'application/json'
  }
})
```

### Test Environment Best Practices

- **Separate Test Database**: Use dedicated Supabase test project
- **Isolated Secrets**: Test secrets are different from production
- **Automated Cleanup**: Test data automatically cleaned between runs
- **Security Validation**: Test environment security is validated in CI/CD

## Next Steps / TODO

- [ ] Implement automated vulnerability scanning
- [ ] Add penetration testing procedures
- [ ] Implement security awareness training
- [ ] Add automated security patch management
- [ ] Implement WAF (Web Application Firewall)
- [ ] Add DDoS protection and monitoring
- [ ] Implement security metrics dashboard
- [ ] Add automated incident response workflows
- [ ] Implement security compliance reporting
- [ ] Add advanced threat detection and response

---

**Last Updated**: January 2025  
**Version**: 2.0  
**Next Review**: March 2025