#!/usr/bin/env node

/**
 * Package Tracker - Automatic Documentation Update Script
 * 
 * This script automatically updates LIBRARIES_AND_UTILITIES.md when package.json changes.
 * It compares current packages with documented versions and updates accordingly.
 */

const fs = require('fs');
const path = require('path');

class PackageTracker {
  constructor() {
    this.packageJsonPath = path.join(process.cwd(), 'package.json');
    this.docPath = path.join(process.cwd(), 'docs/reference/LIBRARIES_AND_UTILITIES.md');
    this.packageJson = JSON.parse(fs.readFileSync(this.packageJsonPath, 'utf8'));
  }

  /**
   * Extract current package versions from package.json
   */
  getCurrentPackages() {
    return {
      dependencies: this.packageJson.dependencies || {},
      devDependencies: this.packageJson.devDependencies || {}
    };
  }

  /**
   * Parse the documentation file to extract documented versions
   */
  getDocumentedVersions() {
    const docContent = fs.readFileSync(this.docPath, 'utf8');
    const versionRegex = /\| `([^`]+)` \| ([^|]+) \|/g;
    const documented = {};
    
    let match;
    while ((match = versionRegex.exec(docContent)) !== null) {
      const packageName = match[1];
      const versionInfo = match[2].trim();
      documented[packageName] = versionInfo;
    }
    
    return documented;
  }

  /**
   * Compare current packages with documented versions
   */
  findVersionDiscrepancies() {
    const current = this.getCurrentPackages();
    const documented = this.getDocumentedVersions();
    const discrepancies = [];

    // Check all current packages
    const allPackages = { ...current.dependencies, ...current.devDependencies };
    
    for (const [packageName, currentVersion] of Object.entries(allPackages)) {
      const cleanCurrentVersion = currentVersion.replace(/[\^~]/, '');
      const documentedVersion = documented[packageName];
      
      if (!documentedVersion) {
        discrepancies.push({
          package: packageName,
          type: 'missing',
          currentVersion: cleanCurrentVersion,
          documentedVersion: null
        });
      } else if (!documentedVersion.includes(cleanCurrentVersion)) {
        discrepancies.push({
          package: packageName,
          type: 'outdated',
          currentVersion: cleanCurrentVersion,
          documentedVersion: documentedVersion
        });
      }
    }

    return discrepancies;
  }

  /**
   * Update documentation with current package versions
   */
  updateDocumentation() {
    const discrepancies = this.findVersionDiscrepancies();
    
    if (discrepancies.length === 0) {
      console.log('✅ All package versions are up to date in documentation');
      return;
    }

    console.log(`📦 Found ${discrepancies.length} package version discrepancies:`);
    
    let docContent = fs.readFileSync(this.docPath, 'utf8');
    let updateCount = 0;

    discrepancies.forEach(({ package: packageName, type, currentVersion, documentedVersion }) => {
      console.log(`  - ${packageName}: ${type === 'missing' ? 'not documented' : `${documentedVersion} → ${currentVersion}`}`);
      
      if (type === 'outdated') {
        // Update existing version
        const oldPattern = new RegExp(`(\\| \`${packageName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\` \\| )([^|]+)( \\|)`, 'g');
        const newVersion = documentedVersion.replace(/[\d.]+[\w.-]*/, currentVersion);
        docContent = docContent.replace(oldPattern, `$1${newVersion}$3`);
        updateCount++;
      }
      // Note: 'missing' packages require manual addition to maintain proper categorization
    });

    // Update the "Last updated" timestamp
    const timestamp = new Date().toLocaleDateString('en-GB', { 
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }).replace(/,/g, '').replace(/ ([0-9]{4})/, ' $1');
    docContent = docContent.replace(
      /\*Last updated: [^*]+\*/,
      `*Last updated: ${timestamp}*`
    );

    // Write updated content
    fs.writeFileSync(this.docPath, docContent, 'utf8');
    
    console.log(`✅ Updated ${updateCount} package versions in documentation`);
    
    if (discrepancies.some(d => d.type === 'missing')) {
      console.log('⚠️  Some packages are missing from documentation and need manual addition:');
      discrepancies
        .filter(d => d.type === 'missing')
        .forEach(({ package: packageName, currentVersion }) => {
          console.log(`   - ${packageName}@${currentVersion}`);
        });
    }
  }

  /**
   * Generate summary report
   */
  generateReport() {
    const current = this.getCurrentPackages();
    const totalDeps = Object.keys(current.dependencies).length;
    const totalDevDeps = Object.keys(current.devDependencies).length;
    
    console.log('\n📊 Package Summary:');
    console.log(`   Production dependencies: ${totalDeps}`);
    console.log(`   Development dependencies: ${totalDevDeps}`);
    console.log(`   Total packages: ${totalDeps + totalDevDeps}`);
    
    // Check for major version updates
    const majorUpdates = this.findMajorUpdates();
    if (majorUpdates.length > 0) {
      console.log('\n🚨 Major version updates detected:');
      majorUpdates.forEach(update => {
        console.log(`   - ${update.package}: ${update.from} → ${update.to}`);
      });
    }
  }

  /**
   * Detect major version changes (breaking changes)
   */
  findMajorUpdates() {
    // This would require access to previous package.json state
    // For now, we'll return empty array but could be enhanced
    // with git history analysis
    return [];
  }
}

// Main execution
if (require.main === module) {
  try {
    console.log('🔍 Checking package versions...');
    const tracker = new PackageTracker();
    tracker.updateDocumentation();
    tracker.generateReport();
    console.log('✅ Package tracking completed successfully');
  } catch (error) {
    console.error('❌ Error updating package documentation:', error.message);
    process.exit(1);
  }
}

module.exports = PackageTracker;