#!/usr/bin/env node

/**
 * Component Analyzer - Automatic Component Dependency Matrix Update
 * 
 * This script analyzes the codebase for new components, pages, and dependencies
 * and automatically updates the COMPONENT_DEPENDENCY_MATRIX.md file.
 */

const fs = require('fs');
const path = require('path');
const glob = require('glob');

class ComponentAnalyzer {
  constructor() {
    this.srcPath = path.join(process.cwd(), 'src');
    this.docPath = path.join(process.cwd(), 'docs/reference/COMPONENT_DEPENDENCY_MATRIX.md');
    this.components = new Map();
    this.pages = new Map();
    this.dependencies = new Map();
  }

  /**
   * Analyze all React components in the codebase
   */
  analyzeComponents() {
    console.log('🔍 Analyzing React components...');
    
    // Find all component files
    const componentFiles = glob.sync(`${this.srcPath}/**/*.{tsx,jsx}`, {
      ignore: ['**/*.test.*', '**/*.spec.*', '**/node_modules/**']
    });

    componentFiles.forEach(filePath => {
      this.analyzeFile(filePath);
    });

    console.log(`📊 Found ${this.components.size} components and ${this.pages.size} pages`);
  }

  /**
   * Analyze a single file for components and dependencies
   */
  analyzeFile(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const relativePath = path.relative(this.srcPath, filePath);
    
    // Detect if it's a page component (in app directory)
    const isPage = relativePath.startsWith('app/') && (
      filePath.endsWith('/page.tsx') || 
      filePath.endsWith('/page.jsx')
    );

    // Extract component info
    const componentInfo = {
      path: relativePath,
      isPage,
      isClientComponent: content.includes("'use client'"),
      imports: this.extractImports(content),
      exports: this.extractExports(content),
      dependencies: this.extractDependencies(content),
      rendering: this.determineRenderingStrategy(content, filePath)
    };

    if (isPage) {
      const route = this.extractRoute(filePath);
      this.pages.set(route, componentInfo);
    } else {
      const componentName = path.basename(filePath, path.extname(filePath));
      this.components.set(componentName, componentInfo);
    }
  }

  /**
   * Extract import statements from file content
   */
  extractImports(content) {
    const importRegex = /import\s+(?:{[^}]+}|\*\s+as\s+\w+|\w+)?\s*(?:,\s*{[^}]+})?\s*from\s+['"]([^'"]+)['"]/g;
    const imports = [];
    let match;

    while ((match = importRegex.exec(content)) !== null) {
      const importPath = match[1];
      if (!importPath.startsWith('.')) {
        imports.push(importPath);
      }
    }

    return [...new Set(imports)]; // Remove duplicates
  }

  /**
   * Extract export statements from file content
   */
  extractExports(content) {
    const exportRegex = /export\s+(?:default\s+)?(?:function|const|class|interface|type)\s+(\w+)/g;
    const exports = [];
    let match;

    while ((match = exportRegex.exec(content)) !== null) {
      exports.push(match[1]);
    }

    return exports;
  }

  /**
   * Extract dependencies based on imports
   */
  extractDependencies(content) {
    const imports = this.extractImports(content);
    const categories = {
      ui: [],
      hooks: [],
      data: [],
      utils: [],
      external: []
    };

    imports.forEach(importPath => {
      if (importPath.startsWith('@radix-ui/') || importPath.includes('shadcn')) {
        categories.ui.push(importPath);
      } else if (importPath.includes('hook') || importPath.startsWith('use')) {
        categories.hooks.push(importPath);
      } else if (importPath.includes('supabase') || importPath.includes('data')) {
        categories.data.push(importPath);
      } else if (importPath.includes('lib/') || importPath.includes('utils')) {
        categories.utils.push(importPath);
      } else if (!importPath.startsWith('@/')) {
        categories.external.push(importPath);
      }
    });

    return categories;
  }

  /**
   * Determine rendering strategy based on file location and content
   */
  determineRenderingStrategy(content, filePath) {
    if (content.includes("'use client'")) {
      return 'Client Component';
    }
    
    if (filePath.includes('/app/') && filePath.endsWith('/page.tsx')) {
      if (content.includes('generateStaticParams')) {
        return 'SSG + ISR';
      } else if (content.includes('revalidate')) {
        return 'ISR';
      } else {
        return 'SSR';
      }
    }

    return 'Server Component';
  }

  /**
   * Extract route from file path
   */
  extractRoute(filePath) {
    const appIndex = filePath.indexOf('/app/');
    if (appIndex === -1) return '/';
    
    let route = filePath.substring(appIndex + 5); // Remove everything before '/app/'
    route = route.replace('/page.tsx', '').replace('/page.jsx', '');
    
    if (route === '') return '/';
    if (!route.startsWith('/')) route = '/' + route;
    
    return route;
  }

  /**
   * Update the component dependency matrix documentation
   */
  updateDocumentation() {
    console.log('📝 Updating component dependency matrix...');
    
    let docContent = fs.readFileSync(this.docPath, 'utf8');
    
    // Update the page-to-component mapping table
    const pageTableContent = this.generatePageTable();
    docContent = this.replaceSection(docContent, 
      '### Core User Pages',
      '### Static/Legal Pages',
      pageTableContent
    );

    // Update component architecture breakdown
    const componentBreakdown = this.generateComponentBreakdown();
    docContent = this.replaceSection(docContent,
      '## Component Architecture Breakdown',
      '## Data Layer Dependencies',
      componentBreakdown
    );

    // Update timestamp
    const timestamp = new Date().toLocaleDateString('en-GB', { 
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }).replace(/,/g, '').replace(/ ([0-9]{4})/, ' $1');
    docContent = docContent.replace(
      /\*Last updated: [^*]+\*/,
      `*Last updated: ${timestamp}*`
    );

    fs.writeFileSync(this.docPath, docContent, 'utf8');
    console.log('✅ Component dependency matrix updated successfully');
  }

  /**
   * Generate the page-to-component mapping table
   */
  generatePageTable() {
    let table = '\n| Page / Route | Main Components | Key Libraries | Primary Files | Rendering |\n';
    table += '|--------------|----------------|---------------|---------------|-----------|\\n';

    // Sort pages by route
    const sortedPages = Array.from(this.pages.entries()).sort(([a], [b]) => a.localeCompare(b));

    sortedPages.forEach(([route, info]) => {
      const mainComponents = this.getMainComponentsForPage(route);
      const keyLibraries = info.dependencies.external.slice(0, 3).join(', ');
      const primaryFile = `src/${info.path}`;
      
      table += `| **${this.getPageDisplayName(route)}** (\`${route}\`) | ${mainComponents} | ${keyLibraries} | ${primaryFile} | ${info.rendering} |\\n`;
    });

    return table;
  }

  /**
   * Get main components used by a specific page
   */
  getMainComponentsForPage(route) {
    // This would require more sophisticated analysis of component usage
    // For now, return a placeholder that could be enhanced
    const commonComponents = ['Header', 'Footer', 'SEO'];
    
    // Add route-specific components based on patterns
    if (route.includes('products')) {
      commonComponents.push('ProductCard', 'ProductGrid', 'Pagination');
    } else if (route.includes('search')) {
      commonComponents.push('SearchBar', 'SearchSuggestions', 'ProductGrid');
    } else if (route.includes('brands')) {
      commonComponents.push('BrandLogo', 'BrandCard', 'AlphabetNavigation');
    }

    return commonComponents.slice(0, 4).map(c => `\`${c}\``).join(', ');
  }

  /**
   * Get display name for a route
   */
  getPageDisplayName(route) {
    if (route === '/') return 'Homepage';
    
    const segments = route.split('/').filter(Boolean);
    const lastSegment = segments[segments.length - 1];
    
    if (lastSegment?.startsWith('[') && lastSegment.endsWith(']')) {
      const param = lastSegment.slice(1, -1);
      const parent = segments[segments.length - 2] || 'Item';
      return `${parent.charAt(0).toUpperCase() + parent.slice(1)} Detail`;
    }

    return segments.map(s => s.charAt(0).toUpperCase() + s.slice(1)).join(' ');
  }

  /**
   * Generate component architecture breakdown
   */
  generateComponentBreakdown() {
    let content = '\n### Server Components (Data Fetching)\n\n';
    content += '| Component | Purpose | Data Sources | Cache Strategy |\n';
    content += '|-----------|---------|--------------|----------------|\n';

    // Filter server components (pages)
    const serverComponents = Array.from(this.pages.entries())
      .filter(([, info]) => !info.isClientComponent);

    serverComponents.forEach(([route, info]) => {
      const purpose = `${this.getPageDisplayName(route)} server logic`;
      const dataSources = this.getDataSources(info);
      const cacheStrategy = this.getCacheStrategy(info);
      
      content += `| \`src/${info.path}\` | ${purpose} | ${dataSources} | ${cacheStrategy} |\n`;
    });

    content += '\n### Client Components (Interactivity)\n\n';
    content += '| Component | Purpose | State Management | Key Hooks |\n';
    content += '|-----------|---------|------------------|-----------|\\n';

    // Filter client components
    const clientComponents = Array.from(this.components.entries())
      .filter(([, info]) => info.isClientComponent);

    clientComponents.slice(0, 10).forEach(([name, info]) => {
      const purpose = `${name} interactivity`;
      const stateManagement = this.getStateManagement(info);
      const keyHooks = this.getKeyHooks(info);
      
      content += `| \`${name}\` | ${purpose} | ${stateManagement} | ${keyHooks} |\n`;
    });

    return content;
  }

  /**
   * Helper methods for data extraction
   */
  getDataSources(info) {
    const dataDeps = info.dependencies.data;
    if (dataDeps.length === 0) return 'Static data';
    return dataDeps.slice(0, 2).map(d => `\`${d}\``).join(', ');
  }

  getCacheStrategy(info) {
    if (info.rendering.includes('ISR')) return 'ISR caching';
    if (info.rendering.includes('SSG')) return 'Static generation';
    return 'Server-side cache';
  }

  getStateManagement(info) {
    if (info.imports.includes('useState')) return 'Local state';
    if (info.imports.includes('@tanstack/react-query')) return 'React Query';
    return 'Props only';
  }

  getKeyHooks(info) {
    const hooks = info.imports.filter(imp => 
      imp.includes('use') || imp.includes('hook')
    );
    return hooks.slice(0, 3).map(h => `\`${h}\``).join(', ') || 'None';
  }

  /**
   * Replace a section in the documentation
   */
  replaceSection(content, startMarker, endMarker, newContent) {
    const startIndex = content.indexOf(startMarker);
    const endIndex = content.indexOf(endMarker);
    
    if (startIndex === -1 || endIndex === -1) {
      console.warn(`⚠️  Could not find section markers: ${startMarker} -> ${endMarker}`);
      return content;
    }

    return content.substring(0, startIndex) + 
           startMarker + newContent + '\n\n' +
           content.substring(endIndex);
  }

  /**
   * Generate summary report
   */
  generateReport() {
    console.log('\n📊 Component Analysis Summary:');
    console.log(`   Pages: ${this.pages.size}`);
    console.log(`   Components: ${this.components.size}`);
    
    const serverComponents = Array.from(this.pages.values()).filter(info => !info.isClientComponent).length;
    const clientComponents = Array.from(this.components.values()).filter(info => info.isClientComponent).length;
    
    console.log(`   Server Components: ${serverComponents}`);
    console.log(`   Client Components: ${clientComponents}`);
    
    // Most used libraries
    const libraryUsage = new Map();
    [...this.pages.values(), ...this.components.values()].forEach(info => {
      info.dependencies.external.forEach(lib => {
        libraryUsage.set(lib, (libraryUsage.get(lib) || 0) + 1);
      });
    });

    const topLibraries = Array.from(libraryUsage.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 5);

    console.log('\n📚 Most Used Libraries:');
    topLibraries.forEach(([lib, count]) => {
      console.log(`   - ${lib}: ${count} components`);
    });
  }
}

// Main execution
if (require.main === module) {
  try {
    console.log('🔍 Analyzing component dependencies...');
    const analyzer = new ComponentAnalyzer();
    analyzer.analyzeComponents();
    analyzer.updateDocumentation();
    analyzer.generateReport();
    console.log('✅ Component analysis completed successfully');
  } catch (error) {
    console.error('❌ Error analyzing components:', error.message);
    process.exit(1);
  }
}

module.exports = ComponentAnalyzer;