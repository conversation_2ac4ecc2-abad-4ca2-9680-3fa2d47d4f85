# GitHub Actions Workflow for Automatic Documentation Updates
# Place this file in .github/workflows/update-docs.yml

name: 📚 Auto-Update Documentation

on:
  push:
    branches: [main, staging, develop]
    paths:
      - 'package.json'
      - 'package-lock.json'
      - 'src/components/**/*'
      - 'src/app/**/*'
      - 'src/lib/**/*'
      - 'next.config.js'
      - 'tailwind.config.ts'
      - 'amplify.yml'
  pull_request:
    branches: [main]
    types: [opened, synchronize]

jobs:
  update-documentation:
    runs-on: ubuntu-latest
    steps:
      - name: 🔍 Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 2 # Need previous commit for comparison

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.10.0'
          cache: 'npm'

      - name: 📚 Install dependencies
        run: npm ci

      - name: 🔍 Detect changes
        id: changes
        run: |
          echo "package_changed=$(git diff --name-only HEAD^ HEAD | grep -E '^package\.json$|^package-lock\.json$' || echo 'false')" >> $GITHUB_OUTPUT
          echo "components_changed=$(git diff --name-only HEAD^ HEAD | grep -E '^src/components/|^src/app/' || echo 'false')" >> $GITHUB_OUTPUT
          echo "config_changed=$(git diff --name-only HEAD^ HEAD | grep -E '^next\.config\.js$|^tailwind\.config\.ts$|^amplify\.yml$' || echo 'false')" >> $GITHUB_OUTPUT

      - name: 📦 Update Libraries Documentation
        if: steps.changes.outputs.package_changed != 'false'
        run: node docs/automation/package-tracker.js

      - name: 🧩 Update Component Matrix
        if: steps.changes.outputs.components_changed != 'false'
        run: node docs/automation/component-analyzer.js

      - name: ⚙️ Update Architecture Documentation
        if: steps.changes.outputs.config_changed != 'false'
        run: node docs/automation/config-tracker.js

      - name: 📝 Update Version References
        run: node docs/automation/version-updater.js

      - name: ✅ Validate Documentation
        run: |
          # Check for broken internal links
          node docs/automation/link-checker.js
          
          # Validate markdown syntax
          npx markdownlint docs/**/*.md --config docs/automation/.markdownlint.json

      - name: 📊 Generate Documentation Report
        run: |
          echo "## 📚 Documentation Update Report" > doc-report.md
          echo "**Triggered by**: ${{ github.event_name }}" >> doc-report.md
          echo "**Branch**: ${{ github.ref_name }}" >> doc-report.md
          echo "**Commit**: ${{ github.sha }}" >> doc-report.md
          echo "" >> doc-report.md
          echo "### 🔄 Changes Detected:" >> doc-report.md
          echo "- Package changes: ${{ steps.changes.outputs.package_changed != 'false' }}" >> doc-report.md
          echo "- Component changes: ${{ steps.changes.outputs.components_changed != 'false' }}" >> doc-report.md
          echo "- Config changes: ${{ steps.changes.outputs.config_changed != 'false' }}" >> doc-report.md

      - name: 💬 Comment on PR (if applicable)
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const report = fs.readFileSync('doc-report.md', 'utf8');
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: report
            });

      - name: 🚀 Commit documentation updates
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        run: |
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"
          
          if [[ `git status --porcelain` ]]; then
            git add docs/
            git commit -m "docs: auto-update documentation

            📚 Automated documentation updates triggered by:
            - Package changes: ${{ steps.changes.outputs.package_changed != 'false' }}
            - Component changes: ${{ steps.changes.outputs.components_changed != 'false' }}
            - Config changes: ${{ steps.changes.outputs.config_changed != 'false' }}
            
            🤖 Generated by GitHub Actions
            📝 Commit: ${{ github.sha }}"
            
            git push
          else
            echo "No documentation changes to commit"
          fi

  # Separate job for security scanning of documentation
  security-scan:
    runs-on: ubuntu-latest
    steps:
      - name: 🔍 Checkout repository
        uses: actions/checkout@v4

      - name: 🔒 Scan for secrets in documentation
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./docs/
          base: main
          head: HEAD

      - name: 🛡️ Check for sensitive information
        run: |
          # Check for common sensitive patterns in documentation
          grep -r -i "password\|secret\|key\|token" docs/ && exit 1 || echo "No sensitive information found"
          grep -r -E "[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}" docs/ && exit 1 || echo "No IP addresses found"
          grep -r -E "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}" docs/ && exit 1 || echo "No email addresses found"