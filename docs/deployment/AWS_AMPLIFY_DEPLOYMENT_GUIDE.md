# AWS Amplify Deployment Guide

**Complete step-by-step instructions for deploying Next.js 15.3.5 application to AWS Amplify**

*Created: July 28, 2025*  
*Based on: Latest AWS Amplify documentation and Next.js 15.x best practices*

## 📋 Prerequisites

Before starting the deployment process, ensure you have:

### Required Accounts and Access
- **AWS Account** with billing enabled
- **GitHub account** with repository access
- **AWS CLI** installed and configured (optional but recommended)
- **Node.js 18.x or higher** (our project uses Node 18.x/20.x/22.x)

### Local Development Setup
```bash
# Verify your local setup works
npm run clean && npm run dev
# Test production build locally
npm run build && npm start
```

## 🚀 Step 1: Prepare Your Repository

### 1.1 Verify Build Configuration
Our application is configured for Amplify deployment with a comprehensive, production-ready `amplify.yml` file that includes:

**✅ IMPLEMENTED: Enterprise-Grade amplify.yml Features:**
- **Security Scanning**: Build artifacts scanned for test flags and credentials
- **Environment Validation**: Critical environment variables validated before build
- **Dependency Management**: `npm ci --production=false --prefer-offline` for reliable builds
- **Build Optimization**: 8GB memory allocation, 25-minute timeout, comprehensive caching
- **Security Masking**: Sensitive environment variables masked in build logs
- **Error Handling**: Detailed logging and error detection throughout build process

**Key Configuration Highlights:**
```yaml
# Core dependency installation (fixes autoprefixer errors)
- npm ci --production=false --prefer-offline

# Security scanning (production builds only)
- |
  if grep -r --include="*.js" "TEST_MODE_BYPASS_AUTH.*true" .next/ 2>/dev/null; then
    echo "❌ SECURITY VIOLATION: Test bypass flags found"
    exit 1
  fi

# Performance optimization
- export NODE_OPTIONS="--max-old-space-size=8192"
- timeout 25m npm run build
```

**Why this configuration matters:**
- **Fixes Build Failures**: `npm ci --production=false` includes devDependencies like autoprefixer
- **Security First**: Prevents accidental deployment of test-only configurations
- **Performance Optimized**: 8GB memory, 25min timeout, comprehensive caching
- **Production Ready**: Validates environment variables and scans for security issues

### 1.2 Environment Variables Setup
Create a list of required environment variables (you'll need these in Step 3):

**Required Variables:**
```bash
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Security Keys
TURNSTILE_SECRET_KEY=your_turnstile_secret
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_site_key

# Application Configuration
NEXT_PUBLIC_APP_URL=https://your-amplify-domain.com
NODE_ENV=production
```

**Security Note:** Never commit these values to your repository. They will be added securely through the Amplify console.

## 🏗️ Step 2: Create Amplify Application

### 2.1 Access AWS Amplify Console
1. Log into the [AWS Management Console](https://console.aws.amazon.com/)
2. Search for "Amplify" in the services search bar
3. Click "AWS Amplify" to open the Amplify console
4. Click "Get started" under "Host your web app"

### 2.2 Connect Your Repository
1. **Choose your Git provider**: Select "GitHub"
2. **Authorize AWS Amplify**: Click "Connect branch"
   - This opens GitHub authorization
   - Grant AWS Amplify access to your repositories
   - You can limit access to specific repositories for security
3. **Select Repository**: Choose your `cashback-deals-v2` repository
4. **Select Branch**: Choose your main branch (usually `main` or `master`)

**Why GitHub integration matters:**
- Automatic deployments on code changes
- Build logs and deployment history
- Easy rollback to previous versions
- Branch-based deployments for testing

### 2.3 Configure Build Settings
1. **App name**: Enter a descriptive name (e.g., "Cashback Deals v2")
2. **Environment**: Select "Existing app" if you have multiple environments
3. **Build and test settings**: 
   - Amplify should auto-detect the `amplify.yml` file
   - If not detected, you can manually specify:
     ```yaml
     version: 1
     frontend:
       phases:
         preBuild:
           commands:
             - npm ci --production=false
         build:
           commands:
             - npm run build
       artifacts:
         baseDirectory: .next
         files:
           - '**/*'
       cache:
         paths:
           - node_modules/**/*
           - .next/cache/**/*
     ```

## ⚙️ Step 3: Configure Environment Variables

### 3.1 Access Environment Variables
1. After creating the app, go to "App settings" → "Environment variables"
2. Click "Manage variables"

### 3.2 Add Production Variables
Add each environment variable one by one:

**Example Configuration:**
```
Variable name: NEXT_PUBLIC_SUPABASE_URL
Value: https://your-project.supabase.co
```

**Critical Variables to Add:**
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`
- `TURNSTILE_SECRET_KEY`
- `NEXT_PUBLIC_TURNSTILE_SITE_KEY`
- `NEXT_PUBLIC_APP_URL` (will be your Amplify domain)
- `NODE_ENV=production`

**Security Best Practice:**
- Mark sensitive variables (like `SUPABASE_SERVICE_ROLE_KEY`) as secret
- Use different values for production vs. staging environments
- Regularly rotate API keys and secrets

### 3.3 Advanced Settings
1. **Node.js version**: Set to "18" (matches our development environment)
2. **Build timeout**: Increase to 30 minutes if needed for large builds
3. **Enable build notifications**: Optional, for deployment status updates

## 🔧 Step 4: Configure Custom Domain (Optional)

### 4.1 Add Custom Domain
1. Go to "Domain management" in the Amplify console
2. Click "Add domain"
3. Enter your domain name (e.g., `cashbackdeals.com`)
4. Amplify will provide DNS configuration instructions

### 4.2 SSL Certificate
- Amplify automatically provisions SSL certificates via AWS Certificate Manager
- This process takes 15-30 minutes
- HTTPS is enforced by default (good for SEO and security)

**Why custom domains matter:**
- Professional appearance for users
- Better SEO rankings
- Consistent branding
- SSL certificate included automatically

## 🚀 Step 5: Deploy Your Application

### 5.1 Trigger First Deployment
1. Click "Save and deploy" to start the first build
2. Monitor the build process in real-time
3. Build typically takes 5-15 minutes depending on dependencies

### 5.2 Build Process Monitoring
The deployment goes through these phases:
1. **Provision**: Setting up build environment
2. **Build**: Running your `npm run build` command
3. **Deploy**: Uploading files to Amplify hosting
4. **Verify**: Health checks and final verification

**Common Build Issues and Solutions:**
- **Memory errors**: Increase build timeout or optimize build process
- **Environment variable errors**: Double-check variable names and values
- **Node.js version errors**: Ensure Node version matches local development

### 5.3 Verify Deployment
1. **Check build logs**: Review for any warnings or errors
2. **Test the live site**: Click the Amplify-provided URL
3. **Verify functionality**: Test key features like search, navigation, data loading
4. **Check performance**: Use browser dev tools to verify load times

## 🔒 Step 6: Configure Security Headers

### 6.1 Custom Headers Setup (✅ IMPLEMENTED)
Our application uses the **2025 AWS best practice** approach with a comprehensive `customHttp.yml` file in the root directory:

**✅ IMPLEMENTED: Enterprise Security Headers Configuration:**
- **Comprehensive CSP**: Content Security Policy with specific allowlists for Supabase, Sentry, Cloudflare
- **Performance Optimization**: Static asset caching with immutable headers
- **Security Headers**: HSTS, X-Frame-Options, X-Content-Type-Options, and more
- **Cross-Origin Policies**: Enhanced security for cross-origin interactions
- **API Route Protection**: No-cache headers for API endpoints

**Key Security Features in customHttp.yml:**
```yaml
# Comprehensive Content Security Policy
Content-Security-Policy: >-
  default-src 'self';
  script-src 'self' 'unsafe-eval' 'unsafe-inline' 
    https://challenges.cloudflare.com 
    https://*.sentry.io;
  connect-src 'self' 
    https://*.supabase.co 
    https://*.sentry.io;

# Security headers
Strict-Transport-Security: 'max-age=31536000; includeSubDomains; preload'
X-Frame-Options: 'DENY'
X-Content-Type-Options: 'nosniff'
Referrer-Policy: 'strict-origin-when-cross-origin'
```

**Advanced Security Implementation:**
- **Permissions Policy**: Restricts access to browser APIs (camera, microphone, etc.)
- **Cross-Origin Headers**: Enhanced security for cross-origin interactions
- **Static Asset Optimization**: 1-year caching for immutable assets
- **API Security**: No-cache headers for sensitive API endpoints

**Why customHttp.yml is better than Amplify console configuration:**
- Version controlled with your code
- Automatically deployed with your application
- More comprehensive configuration options
- Follows 2025 AWS Amplify best practices

## 📊 Step 7: Set Up Monitoring and Alerts

### 7.1 CloudWatch Integration
1. Enable CloudWatch metrics in Amplify settings
2. Monitor key metrics:
   - Build success rate
   - Deployment frequency
   - Application performance
   - Error rates

### 7.2 Build Notifications
Set up notifications for:
- Successful deployments
- Failed builds
- Security alerts
- Performance degradation

## 🔄 Step 8: Continuous Deployment Setup

### 8.1 Branch-Based Deployments
Configure different environments:
- **Production**: `main` branch → Production domain
- **Staging**: `develop` branch → Staging subdomain
- **Feature branches**: Individual feature testing

### 8.2 Automatic Deployments
Every push to connected branches automatically triggers:
1. Build process using `amplify.yml`
2. Environment variable injection
3. Security header application
4. DNS updates (if using custom domain)

## 🧪 Step 9: Testing Your Deployment

### 9.1 Functional Testing
Test critical functionality:
```bash
# Run local tests before deployment
npm run test
npm run test:e2e

# Test production build locally
npm run build && npm start
```

### 9.2 Performance Testing
- Use Lighthouse CI for performance audits
- Monitor Core Web Vitals
- Test loading times from different geographic locations

### 9.3 Security Testing
- Verify HTTPS enforcement
- Test Content Security Policy
- Check for exposed sensitive information

## 🔧 Step 10: Troubleshooting Common Issues

### 10.1 Build Failures

**✅ SOLVED: "Cannot find module 'autoprefixer'" Error**
This critical AWS Amplify deployment error has been resolved in our updated amplify.yml configuration. The issue was caused by AWS Amplify using `npm install` (production-only) instead of `npm ci --production=false`.

**Other Common Build Issues:**
- **Memory errors**: Resolved with NODE_OPTIONS="--max-old-space-size=8192"
- **Timeout errors**: Resolved with 25-minute build timeout
- **Environment variable errors**: Resolved with comprehensive validation

**📋 Complete Troubleshooting Guide**: See [`docs/development/BUILD_TROUBLESHOOTING.md`](../development/BUILD_TROUBLESHOOTING.md) for comprehensive solutions to:
- AWS Amplify specific deployment errors
- Environment variable issues
- Memory and performance problems
- Security validation errors
- Step-by-step debugging procedures

### 10.2 Environment Variable Issues
**Issue**: Environment variables not working
**Solution**: 
- Verify variable names match exactly (case-sensitive)
- Check for trailing spaces in values
- Ensure NEXT_PUBLIC_ prefix for client-side variables
- Use the validation built into our amplify.yml configuration

### 10.2 Runtime Errors
**Issue**: Application loads but features don't work
**Solution**:
- Check browser console for JavaScript errors
- Verify API endpoints are accessible
- Test Supabase database connectivity

### 10.3 Performance Issues
**Issue**: Slow loading times
**Solution**:
- Enable caching in `amplify.yml`
- Optimize images and assets
- Review and optimize database queries

## 📚 Maintenance and Updates

### 11.1 Regular Updates
- Monitor AWS Amplify service updates
- Update Node.js version when supported
- Regular security audits and dependency updates
- Performance monitoring and optimization

### 11.2 Backup and Recovery
- Amplify maintains deployment history
- Database backups managed through Supabase
- Source code version control through GitHub

## 🔗 Additional Resources

### AWS Documentation
- [AWS Amplify Hosting Guide](https://docs.aws.amazon.com/amplify/latest/userguide/getting-started.html)
- [Next.js on Amplify](https://docs.aws.amazon.com/amplify/latest/userguide/server-side-rendering-amplify.html)
- [Environment Variables](https://docs.aws.amazon.com/amplify/latest/userguide/environment-variables.html)

### Internal Documentation
- [`docs/technical/ARCHITECTURE.md`](../technical/ARCHITECTURE.md) - System architecture
- [`docs/development/ENVIRONMENT_SETUP.md`](../development/ENVIRONMENT_SETUP.md) - Environment configuration
- [`docs/deployment/CI_CD.md`](./CI_CD.md) - CI/CD pipeline details

### Support and Troubleshooting
- AWS Support (if you have a support plan)
- [AWS Amplify Community](https://github.com/aws-amplify/amplify-hosting)
- Internal troubleshooting: [`docs/development/TROUBLESHOOTING.md`](../development/TROUBLESHOOTING.md)

---

**Deployment Checklist:**
- [ ] Repository connected to Amplify
- [ ] Environment variables configured
- [ ] Custom domain set up (optional)
- [ ] Security headers configured
- [ ] First deployment successful
- [ ] Functionality testing completed
- [ ] Performance testing completed
- [ ] Monitoring and alerts configured
- [ ] Documentation updated with live URLs

**Next Steps:**
After successful deployment, consider:
- Setting up staging environments
- Implementing feature flags
- Adding performance monitoring
- Planning disaster recovery procedures