# CI/CD Pipeline Documentation

*This file documents continuous integration and deployment processes. Last updated: 20th July 2025*

## Overview

The Cashback Deals platform uses GitHub Actions for CI/CD with automated testing, security scanning, and deployment to AWS Amplify. The pipeline ensures code quality, security, and performance before production deployment.

## Pipeline Architecture

```mermaid
graph TB
    DEV[Developer Push] --> GH[GitHub Repository]
    GH --> LINT[Lint & Type Check]
    LINT --> TEST[Unit & Integration Tests]
    TEST --> SEC[Security Scanning]
    SEC --> E2E[E2E Testing]
    E2E --> BUILD[Build Application]
    BUILD --> DEPLOY{Deploy Decision}
    
    DEPLOY -->|main branch| PROD[Production Deployment]
    DEPLOY -->|develop branch| STAGING[Staging Deployment]
    DEPLOY -->|feature branch| PREVIEW[Preview Deployment]
    
    PROD --> LIGHTHOUSE[Lighthouse CI]
    LIGHTHOUSE --> MONITOR[Performance Monitoring]
    MONITOR --> NOTIFY[Notifications]
    
    subgraph "Quality Gates"
        LINT
        TEST
        SEC
        MASK[Secrets Masking]
        VERSION[Version Pinning]
        E2E
    end
    
    subgraph "AWS Amplify Environments"
        PROD
        STAGING
        PREVIEW
    end
    
    subgraph "Cloudflare Layer"
        CDN[CDN & Security Headers]
        WAF[Web Application Firewall]
        SSL[SSL Termination]
    end
    
    PROD --> CDN
    STAGING --> CDN
    PREVIEW --> CDN
```

## GitHub Actions Workflows

### Main CI Pipeline

```yaml
# .github/workflows/ci.yml
name: Continuous Integration

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  # Quality Gates
  lint-and-type-check:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x, 22.x]
        
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run ESLint
        run: npm run lint
      
      - name: Run TypeScript check
        run: npx tsc --noEmit
      
      - name: Check code formatting
        run: npx prettier --check .

  unit-tests:
    runs-on: ubuntu-latest
    needs: lint-and-type-check
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:coverage
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        if: matrix.node-version == '20.x'
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  integration-tests:
    runs-on: ubuntu-latest
    needs: unit-tests
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run integration tests
        run: npm run test:api
        env:
          SUPABASE_TEST_URL: ${{ secrets.SUPABASE_TEST_URL }}
          SUPABASE_TEST_KEY: ${{ secrets.SUPABASE_TEST_KEY }}
          ENABLE_SEARCH_AUTH: true
          ENABLE_HMAC_AUTH: true
          TEST_MODE_BYPASS_AUTH: true

  security-scanning:
    runs-on: ubuntu-latest
    needs: integration-tests
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run security audit
        run: npm audit --audit-level=high
      
      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high

  build-test:
    runs-on: ubuntu-latest
    needs: security-scanning
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Setup Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build application
        run: npm run build
        env:
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-files
          path: .next/
          retention-days: 1

  lighthouse:
    runs-on: ubuntu-latest
    needs: build-test
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Build project
      run: npm run build
      
    - name: Run Lighthouse SEO audit
      run: npm run audit:seo
      
    - name: Run Lighthouse performance audit
      run: npm run audit:performance
```

### SEO and Performance Testing

```yaml
# .github/workflows/seo-testing.yml
name: SEO Testing and Monitoring

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run SEO monitoring daily at 6 AM UTC
    - cron: '0 6 * * *'

jobs:
  seo-testing:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build
      env:
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}

    - name: Start application
      run: |
        npm start &
        sleep 30
      env:
        NODE_ENV: production

    - name: Wait for application
      run: |
        timeout 60 bash -c 'until curl -f http://localhost:3000; do sleep 2; done'

    - name: Run SEO tests
      run: npm run seo:test
      env:
        BASE_URL: http://localhost:3000

    - name: Run Lighthouse SEO audit
      uses: treosh/lighthouse-ci-action@v10
      with:
        configPath: './lighthouse.config.js'
        uploadArtifacts: true
        temporaryPublicStorage: true

    - name: Validate sitemap
      run: |
        curl -f http://localhost:3000/sitemap.xml > sitemap.xml
        xmllint --noout sitemap.xml && echo "✅ Sitemap is valid XML"

    - name: Upload SEO test results
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: seo-test-results
        path: |
          seo-reports/
          lighthouse-results/
          sitemap.xml
```

## AWS Amplify Configuration

### Build Configuration (amplify.yml)

```yaml
version: 1
frontend:
  phases:
    preBuild:
      commands:
        - nvm install 20.10.0
        - nvm use 20.10.0
        - npm ci
        # Optional: Run tests during build
        - npm run test
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
      - .next/cache/**/*

# Performance optimizations
customHeaders:
  - pattern: '**/*'
    headers:
      - key: 'Strict-Transport-Security'
        value: 'max-age=31536000; includeSubDomains'
      - key: 'X-Content-Type-Options'
        value: 'nosniff'
      - key: 'X-Frame-Options'
        value: 'DENY'
      - key: 'X-XSS-Protection'
        value: '1; mode=block'
      - key: 'Referrer-Policy'
        value: 'strict-origin-when-cross-origin'
```

### Environment Management in Amplify

**Environment Variables Configuration:**

**Production:**
```
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://www.cashbackdeals.com
NEXT_PUBLIC_SUPABASE_URL=your-production-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-role-key
JWT_SECRET=your-production-jwt-secret-min-32-chars
PARTNER_SECRET_DEFAULT=your-production-partner-secret-min-32-chars
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your-production-turnstile-key
TURNSTILE_SECRET_KEY=your-production-turnstile-secret
```

**Staging:**
```
NODE_ENV=staging
NEXT_PUBLIC_SITE_URL=https://staging.d1a2b3c4e5f6g7h8i9.amplifyapp.com
NEXT_PUBLIC_SUPABASE_URL=your-staging-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-staging-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-staging-service-role-key
JWT_SECRET=your-staging-jwt-secret-min-32-chars
PARTNER_SECRET_DEFAULT=your-staging-partner-secret-min-32-chars
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your-staging-turnstile-key
TURNSTILE_SECRET_KEY=your-staging-turnstile-secret
```

## Environment Management

### Environment Hierarchy

```mermaid
graph TB
    DEV[Development<br/>localhost:3000] --> PREVIEW[Preview<br/>feature-branch.amplifyapp.com]
    PREVIEW --> STAGING[Staging<br/>staging.amplifyapp.com]
    STAGING --> PROD[Production<br/>cashbackdeals.com]
    
    subgraph "Configuration"
        DEV --> DEVENV[.env.local]
        PREVIEW --> PREVIEWENV[Amplify Preview Env]
        STAGING --> STAGINGENV[Amplify Staging Env]
        PROD --> PRODENV[Amplify Production Env]
    end
    
    subgraph "Cloudflare Proxy"
        PROD --> CF[Cloudflare CDN]
        STAGING --> CF
        CF --> SSL[SSL Termination]
        CF --> WAF[Security Headers]
        CF --> CACHE[Edge Caching]
    end
```

### Environment Variables by Environment

| Variable | Development | Preview | Staging | Production |
|----------|-------------|---------|---------|------------|
| `NEXT_PUBLIC_SUPABASE_URL` | Local/Dev project | Dev project | Staging project | Production project |
| `SUPABASE_SERVICE_ROLE_KEY` | Dev key | Dev key | Staging key | Production key |
| `NEXT_PUBLIC_TURNSTILE_SITE_KEY` | Test key | Test key | Staging key | Production key |
| `NODE_ENV` | development | production | staging | production |
| `AWS_AMPLIFY_ENV` | - | preview | staging | production |

## Branch Strategy & Deployment Flow

### Git Flow Implementation

```mermaid
gitGraph
    commit id: "Initial"
    branch develop
    checkout develop
    commit id: "Dev Setup"
    
    branch feature/auth-sprint
    checkout feature/auth-sprint
    commit id: "JWT Auth"
    commit id: "HMAC Auth"
    commit id: "Security Tests"
    
    checkout develop
    merge feature/auth-sprint
    commit id: "Merge Auth"
    
    branch release/v2.0
    checkout release/v2.0
    commit id: "Release Prep"
    commit id: "Bug Fixes"
    
    checkout main
    merge release/v2.0
    commit id: "v2.0 Release"
    
    checkout develop
    merge main
    commit id: "Sync Main"
```

### Deployment Triggers

| Branch | Trigger | Environment | URL |
|--------|---------|-------------|-----|
| `feature/*` | Push | Preview | `feature-name.d1a2b3c4e5.amplifyapp.com` |
| `develop` | Push | Staging | `staging.d1a2b3c4e5.amplifyapp.com` |
| `main` | Push | Production | `cashbackdeals.com` (via Cloudflare) |
| `main` | Tag | Production | `cashbackdeals.com` (via Cloudflare) |

## Quality Gates & Checks

### Automated Quality Checks

```yaml
# Quality gate configuration
quality_gates:
  lint:
    command: "npm run lint"
    required: true
    failure_action: "block"
  
  type_check:
    command: "npx tsc --noEmit"
    required: true
    failure_action: "block"
  
  unit_tests:
    command: "npm run test:coverage"
    required: true
    coverage_threshold: 80
    failure_action: "block"
  
  integration_tests:
    command: "npm run test:api"
    required: true
    failure_action: "block"
  
  security_scan:
    command: "npm audit --audit-level=high"
    required: true
    failure_action: "block"
  
  build_test:
    command: "npm run build"
    required: true
    failure_action: "block"
  
  lighthouse:
    performance_threshold: 90
    seo_threshold: 95
    accessibility_threshold: 90
    best_practices_threshold: 90
    failure_action: "warn"
```

### Manual Approval Gates

```yaml
# Manual approval configuration for Amplify
environments:
  production:
    protection_rules:
      - type: "required_reviewers"
        required_reviewers: 2
        dismiss_stale_reviews: true
      
      - type: "wait_timer"
        wait_timer: 5 # minutes
      
      - type: "environment_protection"
        allowed_users: ["admin", "lead-developer"]
        allowed_teams: ["engineering"]
```

## Build Optimization

### Build Configuration

```typescript
// next.config.js - CI/CD optimizations for Amplify
const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
})

module.exports = withBundleAnalyzer({
  // Build optimizations
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react', 'framer-motion'],
  },
  
  // Output configuration for Amplify
  output: 'standalone',
  
  // Webpack optimizations for CI
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // Optimize for faster builds in CI
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      }
    }
    return config
  },
  
  // Reduce build time
  swcMinify: true,
  compress: true,
  
  // Environment-specific optimizations
  ...(process.env.NODE_ENV === 'production' && {
    productionBrowserSourceMaps: false,
    poweredByHeader: false,
  }),
})
```

### Caching Strategy

```yaml
# GitHub Actions cache configuration
- name: Cache Node.js dependencies
  uses: actions/cache@v4
  with:
    path: ~/.npm
    key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
    restore-keys: |
      ${{ runner.os }}-node-

- name: Cache Next.js build
  uses: actions/cache@v4
  with:
    path: .next/cache
    key: ${{ runner.os }}-nextjs-${{ hashFiles('**/package-lock.json') }}
    restore-keys: |
      ${{ runner.os }}-nextjs-

- name: Cache Playwright browsers
  uses: actions/cache@v4
  with:
    path: ~/.cache/ms-playwright
    key: ${{ runner.os }}-playwright-${{ hashFiles('**/package-lock.json') }}
    restore-keys: |
      ${{ runner.os }}-playwright-
```

## Monitoring & Alerting

### Deployment Monitoring

```typescript
// src/lib/monitoring/deployment.ts
export interface DeploymentMetrics {
  buildTime: number
  deploymentTime: number
  testResults: {
    unit: { passed: number; failed: number }
    integration: { passed: number; failed: number }
    e2e: { passed: number; failed: number }
  }
  lighthouse: {
    performance: number
    seo: number
    accessibility: number
    bestPractices: number
  }
}

export async function trackDeployment(metrics: DeploymentMetrics) {
  // Send metrics to Sentry or monitoring service
  await fetch('https://monitoring.cashbackdeals.com/api/deployments', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      timestamp: new Date().toISOString(),
      environment: process.env.AWS_AMPLIFY_ENV || 'development',
      commitSha: process.env.AWS_COMMIT_ID,
      branch: process.env.AWS_BRANCH,
      metrics,
    }),
  })
}
```

### Alert Configuration

```yaml
# Alert configuration for Amplify deployments
alerts:
  build_failure:
    channels: ["slack", "email"]
    recipients: ["engineering-team"]
    template: |
      🚨 Amplify Build Failed
      Branch: {{ branch }}
      Commit: {{ commit }}
      App: {{ amplify_app_id }}
      Error: {{ error }}
      
  deployment_failure:
    channels: ["slack", "email", "pagerduty"]
    recipients: ["engineering-team", "on-call"]
    template: |
      🚨 Amplify Deployment Failed
      Environment: {{ environment }}
      Branch: {{ branch }}
      App: {{ amplify_app_id }}
      Error: {{ error }}
      
  performance_degradation:
    channels: ["slack"]
    recipients: ["engineering-team"]
    conditions:
      - lighthouse_performance < 85
      - build_time > 300000 # 5 minutes
    template: |
      ⚠️ Performance Alert
      Performance Score: {{ lighthouse_performance }}
      Build Time: {{ build_time }}ms
      Environment: {{ environment }}
```

## Rollback Procedures

### Amplify Rollback via CLI

```bash
# Install Amplify CLI
npm install -g @aws-amplify/cli

# Configure CLI
amplify configure

# List deployments
amplify status

# Rollback to previous deployment
amplify env checkout production
amplify publish --rollback

# Or rollback to specific commit
amplify publish --rollback --commit-sha abc123def456
```

### Manual Rollback Steps

1. **Identify the target version** to rollback to
2. **Check the deployment history** in Amplify Console
3. **Verify the target version** is stable and tested
4. **Execute the rollback** via Amplify Console or CLI
5. **Monitor the rollback** deployment
6. **Verify the application** is working correctly
7. **Update Cloudflare cache** if needed
8. **Notify stakeholders** of the rollback
9. **Investigate the root cause** of the issue

### Automated Rollback Workflow

```yaml
# .github/workflows/rollback.yml
name: Rollback Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to rollback'
        required: true
        type: choice
        options:
          - production
          - staging
      target_commit:
        description: 'Commit SHA to rollback to'
        required: true
        type: string

jobs:
  rollback:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout target commit
      uses: actions/checkout@v4
      with:
        ref: ${{ inputs.target_commit }}
    
    - name: Setup Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
      env:
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
    
    - name: Configure Amplify CLI
      run: |
        npm install -g @aws-amplify/cli
        amplify configure --amplify-app-id ${{ secrets.AMPLIFY_APP_ID }}
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        AWS_REGION: us-east-1
    
    - name: Deploy rollback
      run: |
        amplify env checkout ${{ inputs.environment }}
        amplify publish --yes
    
    - name: Verify rollback
      run: |
        sleep 60
        DEPLOYMENT_URL="${{ inputs.environment == 'production' && 'https://cashbackdeals.com' || 'https://staging.d1a2b3c4e5.amplifyapp.com' }}"
        curl -f "${DEPLOYMENT_URL}/api/health" || exit 1
    
    - name: Send rollback notification
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        text: |
          🔄 Amplify Rollback Completed
          Environment: ${{ inputs.environment }}
          Commit: ${{ inputs.target_commit }}
          URL: ${{ inputs.environment == 'production' && 'https://cashbackdeals.com' || 'https://staging.d1a2b3c4e5.amplifyapp.com' }}
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

## Security in CI/CD

### 🔒 Critical Security Enhancements

#### Supply Chain Protection
All CI dependencies are pinned to specific versions to prevent supply chain attacks:

```yaml
# Version-pinned dependencies in all workflows
- name: Install dependencies
  run: |
    npm install -g wait-on@8.0.3          # Server readiness checking
    npm install -g @lhci/cli@0.15.x       # Lighthouse CI auditing
    sudo apt-get install -y jq            # JSON processing
```

**Security Benefits:**
- Prevents malicious dependency updates
- Ensures reproducible builds
- Maintains security patch control

#### Secrets Masking Protection
All workflows implement comprehensive secrets masking to prevent credential exposure:

```yaml
- name: Mask potentially sensitive environment values
  run: |
    echo "::add-mask::eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."  # Service role key
    echo "::add-mask::ci-jwt-secret-minimum-32-characters..."        # JWT secret  
    echo "::add-mask::ci-test-default-secret-minimum-32..."          # Partner secret
    echo "::add-mask::1x0000000000000000000000000000000AA"           # Turnstile key
    echo "🔒 Sensitive values masked from CI logs"
```

**Protected Values:**
- Supabase service role keys (even mock values)
- JWT secrets and partner API keys
- Third-party service authentication tokens
- Real credential pattern detection

#### Credential Leak Detection
Additional scanning prevents accidental real credential inclusion:

```yaml
# Build artifact security scanning
- name: Security check - Scan build artifacts
  run: |
    # Check for accidental real credential leakage
    if grep -r "real-project-id.supabase.co" .next/ 2>/dev/null; then
      echo "❌ SECURITY VIOLATION: Real credentials found in build"
      exit 1
    fi
```

### Secret Management

```yaml
# Secrets configuration for Amplify
secrets:
  # AWS Amplify
  AWS_ACCESS_KEY_ID: "aws-access-key-id"
  AWS_SECRET_ACCESS_KEY: "aws-secret-access-key" 
  AMPLIFY_APP_ID: "amplify-app-id"
  
  # Database
  SUPABASE_SERVICE_ROLE_KEY: "service-role-key"
  SUPABASE_TEST_URL: "test-db-url"
  SUPABASE_TEST_KEY: "test-db-key"
  
  # Security scanning
  SNYK_TOKEN: "snyk-token-here"
  
  # Monitoring
  SLACK_WEBHOOK: "slack-webhook-url"
  LIGHTHOUSE_TOKEN: "lighthouse-token"
  
  # Code quality
  CODECOV_TOKEN: "codecov-token"
  
  # Authentication
  JWT_SECRET: "production-jwt-secret-min-32-chars"
  PARTNER_SECRET_DEFAULT: "production-partner-secret-min-32-chars"
```

## Performance Budgets

### Lighthouse Configuration

```javascript
// lighthouse.config.js
module.exports = {
  ci: {
    collect: {
      url: ['http://localhost:3000'],
      startServerCommand: 'npm start',
      startServerReadyPattern: 'ready on',
    },
    assert: {
      assertions: {
        'categories:performance': ['error', { minScore: 0.9 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['error', { minScore: 0.9 }],
        'categories:seo': ['error', { minScore: 0.95 }],
        'first-contentful-paint': ['error', { maxNumericValue: 2000 }],
        'largest-contentful-paint': ['error', { maxNumericValue: 4000 }],
        'cumulative-layout-shift': ['error', { maxNumericValue: 0.1 }],
      },
    },
    upload: {
      target: 'temporary-public-storage',
    },
  },
}
```

## Best Practices

### CI/CD Guidelines

1. **Fast Feedback**: Keep build times under 10 minutes
2. **Fail Fast**: Run quick tests first, expensive tests later
3. **Parallel Execution**: Run independent jobs in parallel
4. **Caching**: Cache dependencies and build artifacts
5. **Security**: Scan for vulnerabilities on every build
6. **Monitoring**: Track build metrics and deployment health
7. **Rollback**: Always have a rollback strategy
8. **Documentation**: Document all pipeline processes

### Troubleshooting Common Issues

#### Build Failures

```bash
# Check build logs in Amplify Console
# Or locally:
npm run build 2>&1 | tee build.log

# Memory issues
export NODE_OPTIONS="--max-old-space-size=4096"

# TypeScript errors
npx tsc --noEmit --skipLibCheck

# Missing dependencies
npm ci --prefer-offline
```

#### Deployment Issues

```bash
# Check Amplify logs in console
amplify console

# Environment variables
aws amplify list-apps
aws amplify get-app --app-id <app-id>

# Domain configuration
aws amplify list-domain-associations --app-id <app-id>
```

## Infrastructure Notes

- **Hosting**: AWS Amplify Console with automatic builds
- **CDN**: Cloudflare proxy for SSL termination and security headers
- **Node.js Version**: 20.10.0 (specified in amplify.yml)
- **Build Output**: Next.js standalone output for optimal performance
- **Caching**: Multi-layer caching (Amplify + Cloudflare + Next.js)

---

**Last Updated**: January 2025  
**Version**: 2.0  
**Next Review**: March 2025