<!--
📋 DOCUMENTATION AUDIT NOTICE - JULY 27, 2025
===========================================
🔄 ACTION: MOVED from docs/ root to docs/deployment/
📁 ORIGINAL LOCATION: /docs/ENVIRONMENT_VARIABLES.md  
📁 NEW LOCATION: /docs/deployment/ENVIRONMENT_VARIABLES.md
🎯 REASON: Environment configuration is critical for deployment procedures
📝 STATUS: Content preserved unchanged, location optimized for deployment team access
👥 REVIEW REQUIRED: DevOps and deployment team should verify all environment variables before production
🏷️ CATEGORY: Deployment - Environment Configuration
-->

# Environment Variable Configuration

This document describes how to set up and manage environment variables for the Cashback Deals project across different environments: local, staging, and production.

## Environment Variable Files

- `.env.local` - Local development environment variables (ignored by git)
- `.env.staging` - Staging environment variables
- `.env.production` - Production environment variables
- `.env.example` - Example environment variables file for reference

## Required Environment Variables

| Variable Name                | Description                                  | Example Value                          |
|-----------------------------|----------------------------------------------|--------------------------------------|
| NEXT_PUBLIC_SITE_URL         | Base URL of the site                          | https://www.example.com               |
| NEXT_PUBLIC_SUPABASE_URL     | Supabase project URL                          | https://xyzcompany.supabase.co       |
| NEXT_PUBLIC_SUPABASE_ANON_KEY| Supabase anonymous API key                    | public-anon-key                      |
| SUPABASE_SERVICE_ROLE_KEY    | Supabase service role key (server-side only) | service-role-key                     |
| EMAIL_SERVER                | SMTP server for sending emails                | smtp.gmail.com                       |
| EMAIL_PORT                  | SMTP server port                               | 587                                 |
| EMAIL_SECURE                | Use secure connection for SMTP (true/false)  | false                               |
| EMAIL_USER                  | SMTP username                                  | <EMAIL>                    |
| EMAIL_PASSWORD              | SMTP password                                  | password                           |
| EMAIL_FROM                  | Default "from" email address                   | "Cashback Deals" <<EMAIL>> |
| NEXT_PUBLIC_DEBUG_ENABLED   | Enable debug mode on frontend (true/false)    | true                               |
| NEXT_PUBLIC_DEBUG_LEVEL     | Debug level (e.g., error, warn, info, debug)  | error                              |
| DEBUG                      | Enable debug mode on backend (true/false)     | false                              |
| NODE_ENV                   | Node environment (development, production)    | development                       |
| NEXT_PUBLIC_TURNSTILE_SITE_KEY | Cloudflare Turnstile site key                  | 1x00000000000000000000AA (test key) |

## PR3: Sentry & IP Allowlist Configuration

### Sentry Environment Variables

| Variable Name                | Description                                  | Example Value                          |
|-----------------------------|----------------------------------------------|--------------------------------------|
| SENTRY_DSN                  | Sentry Data Source Name for error tracking   | https://<EMAIL>/project-id      |
| ENABLE_SENTRY               | Enable Sentry in production (true/false)     | true                                  |
| ENABLE_SENTRY_LOCAL         | Enable Sentry in development (true/false)    | false                                 |
| SENTRY_TRACES_SAMPLE_RATE   | Sentry trace sampling rate (0.0-1.0)         | 0.01 (1% in production)               |
| SENTRY_ENVIRONMENT          | Sentry environment name                       | production                            |

### IP Allowlist Environment Variables

| Variable Name                | Description                                  | Example Value                          |
|-----------------------------|----------------------------------------------|--------------------------------------|
| ENABLE_IP_ALLOWLIST         | Enable IP allowlist protection (true/false)  | false (default: disabled)             |
| IP_ALLOWLIST_CIDRS          | Comma-separated list of allowed CIDR ranges  | 10.0.0.0/8,**********/12,127.0.0.1/32 |
| IP_ALLOWLIST_LOG_VIOLATIONS | Log blocked IP attempts (true/false)         | true                                  |

## PR5: Authentication Security & Testing Variables

### JWT Authentication Variables

| Variable Name                | Description                                  | Example Value                          |
|-----------------------------|----------------------------------------------|--------------------------------------|
| JWT_SECRET_KEY              | Secret key for JWT token signing             | super-secret-jwt-key-min-32-chars     |
| JWT_EXPIRES_IN              | JWT token expiration time                    | 7d                                    |
| JWT_ISSUER                  | JWT token issuer                             | cashback-deals-api                    |
| JWT_AUDIENCE                | JWT token audience                           | cashback-deals-users                  |

### HMAC Authentication Variables

| Variable Name                | Description                                  | Example Value                          |
|-----------------------------|----------------------------------------------|--------------------------------------|
| HMAC_SECRET_KEY             | Secret key for HMAC signature generation     | super-secret-hmac-key-min-32-chars    |
| HMAC_ALGORITHM              | HMAC algorithm to use                        | sha256                                |
| HMAC_TIMESTAMP_TOLERANCE    | Maximum age of HMAC timestamp (seconds)      | 300                                   |

### Test Environment Variables

| Variable Name                | Description                                  | Example Value                          |
|-----------------------------|----------------------------------------------|--------------------------------------|
| NODE_ENV                    | Set to 'test' for test environment          | test                                  |
| BYPASS_AUTH_IN_TEST         | Bypass authentication checks in tests        | true                                  |
| BYPASS_RATE_LIMITING_IN_TEST| Bypass rate limiting in tests               | true                                  |
| BYPASS_IP_ALLOWLIST_IN_TEST | Bypass IP allowlist in tests                | true                                  |
| TEST_SUPABASE_URL           | Test Supabase URL (if different)            | https://test.supabase.co              |
| TEST_SUPABASE_ANON_KEY      | Test Supabase anonymous key                 | test-anon-key                         |

## PR5: Security & Turnstile Variables

### Cloudflare Turnstile Variables

| Variable Name                | Description                                  | Example Value                          |
|-----------------------------|----------------------------------------------|--------------------------------------|
| NEXT_PUBLIC_TURNSTILE_SITE_KEY | Cloudflare Turnstile site key (public)       | 1x00000000000000000000AA (test key)   |
| TURNSTILE_SECRET_KEY        | Cloudflare Turnstile secret key (server-side)| secret-turnstile-key                  |
| ENABLE_TURNSTILE            | Enable Turnstile protection (true/false)     | true                                  |
| TURNSTILE_BYPASS_IN_TEST    | Bypass Turnstile in test environment         | true                                  |

### Security Feature Flags

| Variable Name                | Description                                  | Example Value                          |
|-----------------------------|----------------------------------------------|--------------------------------------|
| ENABLE_CSRF_PROTECTION      | Enable CSRF protection (true/false)          | true                                  |
| ENABLE_XSS_PROTECTION       | Enable XSS protection headers (true/false)   | true                                  |
| ENABLE_CONTENT_SECURITY_POLICY | Enable CSP headers (true/false)           | true                                  |
| ENABLE_RATE_LIMITING        | Enable API rate limiting (true/false)        | true                                  |

## Environment-Specific Configurations

### Development Environment (.env.local)

```env
# Basic Development Configuration
NODE_ENV=development
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_DEBUG_ENABLED=true
NEXT_PUBLIC_DEBUG_LEVEL=debug
DEBUG=true

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Security (Development - Relaxed)
ENABLE_IP_ALLOWLIST=false
ENABLE_RATE_LIMITING=false
ENABLE_SENTRY_LOCAL=false
NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA

# Authentication (Development)
JWT_SECRET_KEY=development-jwt-secret-key-at-least-32-characters
HMAC_SECRET_KEY=development-hmac-secret-key-at-least-32-characters
```

### Staging Environment (.env.staging)

```env
# Staging Configuration
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://staging.cashbackdeals.com
NEXT_PUBLIC_DEBUG_ENABLED=false
NEXT_PUBLIC_DEBUG_LEVEL=error
DEBUG=false

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_staging_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_staging_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_staging_service_role_key

# Security (Staging - Production-like)
ENABLE_IP_ALLOWLIST=true
IP_ALLOWLIST_CIDRS=10.0.0.0/8,**********/12
ENABLE_RATE_LIMITING=true
ENABLE_SENTRY=true
SENTRY_DSN=your_staging_sentry_dsn
SENTRY_ENVIRONMENT=staging
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_staging_turnstile_site_key
TURNSTILE_SECRET_KEY=your_staging_turnstile_secret

# Authentication (Staging)
JWT_SECRET_KEY=staging-jwt-secret-key-minimum-32-characters-long
HMAC_SECRET_KEY=staging-hmac-secret-key-minimum-32-characters-long
```

### Production Environment (.env.production)

```env
# Production Configuration
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://www.cashbackdeals.com
NEXT_PUBLIC_DEBUG_ENABLED=false
NEXT_PUBLIC_DEBUG_LEVEL=error
DEBUG=false

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_production_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_service_role_key

# Email Configuration (Production)
EMAIL_SERVER=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_FROM="Cashback Deals" <<EMAIL>>

# Security (Production - Maximum Security)
ENABLE_IP_ALLOWLIST=true
IP_ALLOWLIST_CIDRS=production_cidrs_only
IP_ALLOWLIST_LOG_VIOLATIONS=true
ENABLE_RATE_LIMITING=true
ENABLE_CSRF_PROTECTION=true
ENABLE_XSS_PROTECTION=true
ENABLE_CONTENT_SECURITY_POLICY=true
ENABLE_SENTRY=true
SENTRY_DSN=your_production_sentry_dsn
SENTRY_ENVIRONMENT=production
SENTRY_TRACES_SAMPLE_RATE=0.01
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_production_turnstile_site_key
TURNSTILE_SECRET_KEY=your_production_turnstile_secret

# Authentication (Production)
JWT_SECRET_KEY=production-jwt-secret-minimum-32-chars-high-entropy
JWT_EXPIRES_IN=7d
JWT_ISSUER=cashback-deals-api
JWT_AUDIENCE=cashback-deals-users
HMAC_SECRET_KEY=production-hmac-secret-minimum-32-chars-high-entropy
HMAC_ALGORITHM=sha256
HMAC_TIMESTAMP_TOLERANCE=300
```

## Test Environment Configuration (.env.test)

```env
# Test Environment Configuration
NODE_ENV=test
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_DEBUG_ENABLED=true
NEXT_PUBLIC_DEBUG_LEVEL=debug

# Test Supabase (Mock)
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=test-anon-key
SUPABASE_SERVICE_ROLE_KEY=test-service-role-key

# Test Security (Bypassed)
BYPASS_AUTH_IN_TEST=true
BYPASS_RATE_LIMITING_IN_TEST=true
BYPASS_IP_ALLOWLIST_IN_TEST=true
TURNSTILE_BYPASS_IN_TEST=true
ENABLE_IP_ALLOWLIST=false
ENABLE_RATE_LIMITING=false
ENABLE_SENTRY=false

# Test Authentication
JWT_SECRET_KEY=test-jwt-secret-key-for-testing-only
HMAC_SECRET_KEY=test-hmac-secret-key-for-testing-only
NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA
```

## CI Environment Configuration (.env.ci)

```env
# CI Environment (GitHub Actions)
NODE_ENV=test
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_DEBUG_ENABLED=false
NEXT_PUBLIC_DEBUG_LEVEL=error

# Mock Supabase for CI
NEXT_PUBLIC_SUPABASE_URL=http://localhost:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=ci-test-anon-key
SUPABASE_SERVICE_ROLE_KEY=ci-test-service-role-key

# CI Security (All Bypassed)
BYPASS_AUTH_IN_TEST=true
BYPASS_RATE_LIMITING_IN_TEST=true
BYPASS_IP_ALLOWLIST_IN_TEST=true
TURNSTILE_BYPASS_IN_TEST=true
ENABLE_IP_ALLOWLIST=false
ENABLE_RATE_LIMITING=false
ENABLE_SENTRY=false

# CI Authentication (Safe Test Keys)
JWT_SECRET_KEY=ci-test-jwt-secret-key-safe-for-public-ci
HMAC_SECRET_KEY=ci-test-hmac-secret-key-safe-for-public-ci
NEXT_PUBLIC_TURNSTILE_SITE_KEY=1x00000000000000000000AA
TURNSTILE_SECRET_KEY=1x0000000000000000000000000000000AA
```

## Security Considerations

### Secret Management

1. **Never commit sensitive environment variables to version control**
2. **Use different secrets for each environment**
3. **Rotate secrets regularly**
4. **Use strong, randomly generated secrets for production**
5. **Store production secrets in secure secret management systems**

### Environment Variable Validation

The application validates environment variables on startup:

- Required variables are checked for presence
- Secret keys are validated for minimum length (32 characters)
- CIDR ranges are validated for correct format
- URLs are validated for correct format

### Default Values and Fallbacks

- Most security features are disabled by default for development
- Test environment bypasses all security checks
- Production environment requires all security features to be explicitly configured
- Missing optional variables use sensible defaults

## Troubleshooting

### Common Issues

1. **Missing required environment variables**: Check the console output on startup
2. **Invalid CIDR ranges**: Ensure IP allowlist CIDRs follow the format `x.x.x.x/y`
3. **JWT/HMAC secret too short**: Must be at least 32 characters
4. **Turnstile validation failing**: Check site key and secret key match your Cloudflare configuration
5. **Supabase connection issues**: Verify URL and API keys are correct for your environment

### Debugging Environment Variables

```bash
# Check which environment variables are loaded
npm run dev -- --debug-env

# Validate environment configuration
npm run validate-env

# Test environment-specific configuration
NODE_ENV=test npm run build
NODE_ENV=production npm run build
```

### Environment Variable Testing

```bash
# Test local development setup
cp .env.example .env.local
# Edit .env.local with your values
npm run dev

# Test CI configuration
cp .env.ci .env.test
npm run test:ci

# Test production build
NODE_ENV=production npm run build
```