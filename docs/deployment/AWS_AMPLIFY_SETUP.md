# AWS Amplify Hosting Setup Guide

*Complete guide for setting up AWS Amplify hosting for Cashback Deals v2. Last updated: July 21, 2025*

## 🎯 Overview

This guide covers the complete setup of AWS Amplify Console for hosting the Cashback Deals v2 application, including environment configuration, build settings, domain setup, and deployment automation.

## 📋 Prerequisites

Before starting, ensure you have:
- AWS Account with appropriate permissions
- GitHub repository with your code
- Domain name (optional, for custom domain setup)
- Cloudflare account (for CDN and security layer)
- Environment variables and secrets ready

## 🚀 Initial Amplify Setup

### **Step 1: Create New Amplify App**

1. **Navigate to AWS Amplify Console**
   - Go to [AWS Amplify Console](https://console.aws.amazon.com/amplify/home)
   - Click "New app" → "Host web app"

2. **Connect Repository**
   - Select "GitHub" as your repository provider
   - Authorize AWS Amplify to access your GitHub account
   - Select your repository: `cashback-deals-v2`
   - Choose the branch: `main` for production

3. **Configure App Settings**
   - **App name**: `cashback-deals-v2`
   - **Environment name**: `production`
   - **Enable full-stack deployments**: No (we're using hosting only)

### **Step 2: Build Settings Configuration**

Configure the build settings with the following `amplify.yml`:

```yaml
version: 1
frontend:
  phases:
    preBuild:
      commands:
        # Use Node.js 20.x LTS for security and performance
        - nvm install 20.10.0
        - nvm use 20.10.0
        
        # Install dependencies
        - npm ci
        
        # Optional: Run tests during build (uncomment if desired)
        # - npm run test:ci
        
        # Security: Mask sensitive environment values
        - echo "::add-mask::$SUPABASE_SERVICE_ROLE_KEY"
        - echo "::add-mask::$JWT_SECRET"
        - echo "::add-mask::$PARTNER_SECRET_DEFAULT"
        - echo "::add-mask::$TURNSTILE_SECRET_KEY"
        
    build:
      commands:
        # Build the application
        - npm run build
        
        # Security check - scan for accidentally included test flags
        - |
          echo "🔍 Running security scan on build artifacts..."
          if grep -r --include="*.js" --include="*.html" "TEST_MODE_BYPASS_AUTH=true" .next/ 2>/dev/null; then
            echo "❌ SECURITY VIOLATION: Test bypass flags found in build"
            exit 1
          fi
          echo "✅ Security scan passed"
          
    postBuild:
      commands:
        # Optional: Generate sitemap
        - echo "✅ Build completed successfully"
        
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
      
  cache:
    paths:
      - node_modules/**/*
      - .next/cache/**/*

# Custom HTTP headers for security and performance
customHeaders:
  - pattern: '**/*'
    headers:
      # Security Headers
      - key: 'Strict-Transport-Security'
        value: 'max-age=31536000; includeSubDomains; preload'
      - key: 'X-Content-Type-Options'
        value: 'nosniff'
      - key: 'X-Frame-Options'
        value: 'DENY'
      - key: 'X-XSS-Protection'
        value: '1; mode=block'
      - key: 'Referrer-Policy'
        value: 'strict-origin-when-cross-origin'
      - key: 'Permissions-Policy'
        value: 'camera=(), microphone=(), location=()'
        
  # Cache headers for static assets
  - pattern: '/static/**'
    headers:
      - key: 'Cache-Control'
        value: 'public, max-age=31536000, immutable'
        
  # API route headers
  - pattern: '/api/**'
    headers:
      - key: 'Cache-Control'
        value: 'no-cache, no-store, must-revalidate'
```

## ⚙️ Environment Variables Configuration

### **Production Environment Variables**

Set these environment variables in Amplify Console → App settings → Environment variables:

#### **Core Application Settings**
```bash
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://your-domain.com
AWS_REGION=us-east-1
```

#### **Supabase Configuration**
```bash
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-role-key
```

#### **Authentication & Security**
```bash
# Security Settings (REQUIRED - no test bypass flags)
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
ENABLE_CAPTCHA=true
ENABLE_IP_ALLOWLIST=true
ENABLE_SENTRY=true

# Secrets (32+ characters minimum)
JWT_SECRET=your-production-jwt-secret-minimum-32-characters
PARTNER_SECRET_DEFAULT=your-production-partner-secret-minimum-32-characters

# IP Allowlist Configuration
IP_ALLOWLIST_CIDRS=your-allowed-ip-ranges
IP_ALLOWLIST_LOG_VIOLATIONS=true
IP_ALLOWLIST_BLOCK_BY_DEFAULT=true
```

#### **Third-Party Services**
```bash
# Cloudflare Turnstile
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your-production-turnstile-site-key
TURNSTILE_SECRET_KEY=your-production-turnstile-secret-key

# Sentry (Error Monitoring)
SENTRY_DSN=your-production-sentry-dsn
SENTRY_ENVIRONMENT=production
SENTRY_TRACES_SAMPLE_RATE=0.1

# Email Configuration
EMAIL_SERVER=smtp.your-provider.com
EMAIL_PORT=587
EMAIL_SECURE=true
EMAIL_USER=your-email-username
EMAIL_PASSWORD=your-email-password
EMAIL_FROM=<EMAIL>
```

#### **Performance & Monitoring**
```bash
# Performance Settings
NEXT_PUBLIC_ANALYTICS_ENABLED=true
ENABLE_PERFORMANCE_MONITORING=true
ENABLE_WEB_VITALS_REPORTING=true

# Cache Configuration
CACHE_TTL_SHORT=300
CACHE_TTL_MEDIUM=1800
CACHE_TTL_EXTENDED=86400
```

### **Staging Environment Variables**

For staging environment, use similar variables but with staging-specific values:
```bash
NODE_ENV=staging
NEXT_PUBLIC_SITE_URL=https://staging.d1a2b3c4e5f6g7h8i9.amplifyapp.com
SENTRY_ENVIRONMENT=staging
# ... other staging-specific values
```

## 🌍 Domain Configuration

### **Step 1: Custom Domain Setup**

1. **Add Domain in Amplify Console**
   - Go to App settings → Domain management
   - Click "Add domain"
   - Enter your domain: `cashbackdeals.com`
   - Add subdomains: `www.cashbackdeals.com`

2. **DNS Configuration**
   - Amplify will provide CNAME records
   - Add these to your domain's DNS settings

### **Step 2: Cloudflare Integration**

Since you're using Cloudflare as a proxy:

1. **Cloudflare DNS Setup**
   ```
   Type: CNAME
   Name: @
   Content: your-amplify-domain.amplifyapp.com
   TTL: Auto
   Proxy status: Proxied (orange cloud)
   ```

2. **Cloudflare Page Rules**
   ```
   Rule: cashbackdeals.com/*
   Settings:
   - Security Level: Medium
   - Cache Level: Standard
   - Browser Cache TTL: 4 hours
   - Always Use HTTPS: On
   ```

3. **SSL/TLS Configuration**
   - SSL/TLS encryption mode: Full (strict)
   - Always Use HTTPS: On
   - Minimum TLS Version: 1.2

## 🔄 Branch-Based Deployments

### **Production Branch (main)**
- **URL**: `https://cashbackdeals.com`
- **Environment**: Production
- **Auto-deploy**: Enabled
- **Build**: Full security, all guards enabled

### **Staging Branch (develop)**
- **URL**: `https://staging.d1a2b3c4e5f6g7h8i9.amplifyapp.com`
- **Environment**: Staging
- **Auto-deploy**: Enabled
- **Build**: Staging configuration

### **Feature Branches**
- **URL**: `https://feature-name.d1a2b3c4e5f6g7h8i9.amplifyapp.com`
- **Environment**: Preview
- **Auto-deploy**: Enabled for pull requests
- **Build**: Test configuration

## 🛡️ Security Configuration

### **Build-time Security Checks**

Add these security scans to your build process:

```yaml
# In amplify.yml build phase
build:
  commands:
    - npm run build
    
    # Security scan for test flags
    - |
      echo "🔍 Scanning for security violations..."
      VIOLATIONS=0
      
      # Check for test bypass flags
      if grep -r --include="*.js" "TEST_MODE_BYPASS_AUTH=true" .next/; then
        echo "❌ Found test bypass flags in production build"
        VIOLATIONS=$((VIOLATIONS + 1))
      fi
      
      # Check for debug flags
      if grep -r --include="*.js" "DEBUG.*true" .next/; then
        echo "❌ Found debug flags in production build"
        VIOLATIONS=$((VIOLATIONS + 1))
      fi
      
      # Check for development URLs
      if grep -r --include="*.js" "localhost:3000" .next/; then
        echo "❌ Found localhost references in production build"
        VIOLATIONS=$((VIOLATIONS + 1))
      fi
      
      if [ $VIOLATIONS -gt 0 ]; then
        echo "🚨 Security violations found in build artifacts"
        exit 1
      fi
      
      echo "✅ Security scan passed"
```

### **Runtime Security Headers**

Configure these security headers in your custom headers:

```yaml
customHeaders:
  - pattern: '**/*'
    headers:
      # Content Security Policy
      - key: 'Content-Security-Policy'
        value: "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://challenges.cloudflare.com https://*.sentry.io; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; connect-src 'self' https://*.supabase.co https://*.sentry.io; frame-src 'self' https://challenges.cloudflare.com"
      
      # Security headers
      - key: 'X-Content-Type-Options'
        value: 'nosniff'
      - key: 'X-Frame-Options'
        value: 'DENY'
      - key: 'X-XSS-Protection'
        value: '1; mode=block'
      - key: 'Strict-Transport-Security'
        value: 'max-age=31536000; includeSubDomains; preload'
      - key: 'Referrer-Policy'
        value: 'strict-origin-when-cross-origin'
```

## 🚨 Monitoring & Alerts

### **Build Monitoring**

Set up these monitoring configurations:

1. **Build Failure Alerts**
   - SNS topic for build failures
   - Email notifications to engineering team
   - Slack integration for immediate alerts

2. **Deployment Monitoring**
   - CloudWatch alarms for deployment failures
   - Performance monitoring post-deployment
   - Rollback triggers for critical failures

### **Application Monitoring**

1. **Sentry Integration**
   ```bash
   # Environment variables for Sentry
   SENTRY_DSN=your-sentry-dsn
   SENTRY_ENVIRONMENT=production
   SENTRY_TRACES_SAMPLE_RATE=0.1
   SENTRY_PROFILES_SAMPLE_RATE=0.1
   ```

2. **CloudWatch Logs**
   - Application logs from Lambda functions
   - Build logs from Amplify
   - Access logs from CloudFront

## 🔧 Troubleshooting

### **Common Build Issues**

#### **Build Timeout**
```yaml
# Increase build timeout in amplify.yml
build:
  commands:
    - timeout 30m npm run build
```

#### **Memory Issues**
```yaml
# Add memory configuration
build:
  commands:
    - export NODE_OPTIONS="--max-old-space-size=4096"
    - npm run build
```

#### **Environment Variable Issues**
```bash
# Debug environment variables
preBuild:
  commands:
    - echo "NODE_ENV: $NODE_ENV"
    - echo "SUPABASE_URL: ${NEXT_PUBLIC_SUPABASE_URL:0:20}..."
    - echo "Environment variables loaded: $(env | wc -l) variables"
```

### **Deployment Issues**

#### **DNS Problems**
```bash
# Check DNS propagation
dig cashbackdeals.com
nslookup cashbackdeals.com

# Check Cloudflare status
curl -I https://cashbackdeals.com
```

#### **SSL Certificate Issues**
```bash
# Check certificate status
openssl s_client -connect cashbackdeals.com:443 -servername cashbackdeals.com

# Verify certificate chain
curl -vI https://cashbackdeals.com
```

### **Performance Issues**

#### **Slow Build Times**
```yaml
# Optimize build process
cache:
  paths:
    - node_modules/**/*
    - .next/cache/**/*
    - ~/.npm/**/*

preBuild:
  commands:
    - npm ci --prefer-offline
```

#### **Large Bundle Size**
```yaml
# Bundle analysis
build:
  commands:
    - ANALYZE=true npm run build
    - echo "Bundle analysis complete"
```

## 📊 Performance Optimization

### **Build Optimization**

1. **Caching Strategy**
   ```yaml
   cache:
     paths:
       - node_modules/**/*
       - .next/cache/**/*
       - ~/.npm/**/*
       - .next/static/**/*
   ```

2. **Parallel Builds**
   ```yaml
   build:
     commands:
       - npm run build --parallel
   ```

3. **Build Artifacts**
   ```yaml
   artifacts:
     baseDirectory: .next
     files:
       - '**/*'
     exclude:
       - node_modules/**/*
       - .next/cache/**/*
   ```

### **Runtime Optimization**

1. **CDN Configuration**
   ```yaml
   customHeaders:
     - pattern: '/static/**'
       headers:
         - key: 'Cache-Control'
           value: 'public, max-age=31536000, immutable'
     
     - pattern: '/_next/static/**'
       headers:
         - key: 'Cache-Control'
           value: 'public, max-age=31536000, immutable'
   ```

2. **Compression**
   ```yaml
   customHeaders:
     - pattern: '**/*.js'
       headers:
         - key: 'Content-Encoding'
           value: 'gzip'
     - pattern: '**/*.css'
       headers:
         - key: 'Content-Encoding'
           value: 'gzip'
   ```

## 🔄 CI/CD Integration

### **GitHub Actions Integration**

The application uses GitHub Actions for CI/CD. Ensure these secrets are set:

```bash
# In GitHub repository secrets
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AMPLIFY_APP_ID=your-amplify-app-id

# Supabase secrets
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url

# Other secrets
SENTRY_AUTH_TOKEN=your-sentry-token
CODECOV_TOKEN=your-codecov-token
```

### **Automated Deployment Workflow**

1. **Code Push** → GitHub repository
2. **GitHub Actions** → Run tests and security scans
3. **Amplify** → Trigger build and deployment
4. **Cloudflare** → Purge cache and update DNS
5. **Monitoring** → Verify deployment and alert on issues

## 📚 Additional Resources

### **AWS Amplify Documentation**
- [Amplify Console User Guide](https://docs.aws.amazon.com/amplify/latest/userguide/)
- [Build Settings Reference](https://docs.aws.amazon.com/amplify/latest/userguide/build-settings.html)
- [Custom Domain Setup](https://docs.aws.amazon.com/amplify/latest/userguide/custom-domains.html)

### **Next.js on Amplify**
- [Next.js Deployment Guide](https://nextjs.org/docs/deployment)
- [Next.js Environment Variables](https://nextjs.org/docs/basic-features/environment-variables)
- [Next.js Performance](https://nextjs.org/docs/advanced-features/measuring-performance)

### **Security Best Practices**
- [OWASP Security Headers](https://owasp.org/www-project-secure-headers/)
- [Content Security Policy](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP)
- [AWS Security Best Practices](https://aws.amazon.com/security/security-resources/)

---

## 📞 Quick Reference

**Essential Commands:**
- Deploy manually: Push to `main` branch
- Check build status: AWS Amplify Console
- View logs: Amplify Console → Build history
- Rollback: Amplify Console → Redeploy previous version

**Important URLs:**
- Production: `https://cashbackdeals.com`
- Staging: `https://staging.d1a2b3c4e5f6g7h8i9.amplifyapp.com`
- Amplify Console: `https://console.aws.amazon.com/amplify`

**Emergency Contacts:**
- Build failures: Check GitHub Actions and Amplify Console
- DNS issues: Check Cloudflare dashboard
- Performance issues: Check Sentry and CloudWatch