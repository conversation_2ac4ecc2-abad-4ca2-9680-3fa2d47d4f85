# Deployment Guide for Staging and Production

*This guide provides step-by-step deployment instructions for multiple platforms. Last updated: 20th July 2025*

This guide provides step-by-step instructions for deploying the Cashback Deals v2 application to staging and production environments across different hosting platforms.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Environment Setup](#environment-setup)
3. [Platform-Specific Deployment](#platform-specific-deployment)
   - [Amazon Amplify (Recommended)](#amazon-amplify-recommended)
   - [Vercel](#vercel)
   - [Netlify](#netlify)
4. [Environment Variables Configuration](#environment-variables-configuration)
5. [Testing Your Deployment](#testing-your-deployment)
6. [Common Issues and Troubleshooting](#common-issues-and-troubleshooting)
7. [Security Checklist](#security-checklist)

## Prerequisites

Before deploying, ensure you have:

- ✅ GitHub repository with latest code
- ✅ Supabase project set up for staging/production
- ✅ Domain name configured (optional but recommended)
- ✅ SSL certificates (handled automatically by hosting platforms)
- ✅ Email service credentials (Gmail/SendGrid)
- ✅ Cloudflare Turnstile keys for bot protection

## Environment Setup

### 1. Prepare Your Code

```bash
# Ensure you're on the main branch
git checkout main
git pull origin main

# Run tests to ensure everything works
npm run test
npm run lint
npm run build
```

### 2. Environment Files Structure

Your project should have these environment files:
```
├── .env.local          # Development (gitignored)
├── .env.test           # Testing (gitignored)
├── .env.staging        # Staging template (committed)
└── .env.production     # Production template (committed)
```

## Platform-Specific Deployment

### Amazon Amplify (Recommended)

**Why Amplify?** Best Next.js support, automatic SSL, global CDN, and easy environment management.

#### Step 1: Connect Repository
1. Go to [AWS Amplify Console](https://console.aws.amazon.com/amplify/)
2. Click "New app" → "Host web app"
3. Choose "GitHub" and authorize AWS access
4. Select your repository and branch (`main` for production, `develop` for staging)

#### Step 2: Configure Build Settings
Amplify should auto-detect your `amplify.yml`. If not, use this configuration:

```yaml
version: 1
frontend:
  phases:
    preBuild:
      commands:
        - nvm install 20.10.0
        - nvm use 20.10.0
        - npm ci
        - npm run test  # Optional: run tests during build
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
      - .next/cache/**/*
```

#### Step 3: Environment Variables
In Amplify Console → App Settings → Environment variables, add:

**For Staging:**
```
NODE_ENV=staging
NEXT_PUBLIC_SITE_URL=https://staging-your-app.amplifyapp.com
NEXT_PUBLIC_SUPABASE_URL=your-staging-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-staging-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-staging-service-role-key
JWT_SECRET=your-staging-jwt-secret-min-32-chars
PARTNER_SECRET_DEFAULT=your-staging-partner-secret-min-32-chars
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your-staging-turnstile-key
TURNSTILE_SECRET_KEY=your-staging-turnstile-secret
EMAIL_SERVER=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-staging-email-password
EMAIL_FROM="Cashback Deals Staging" <<EMAIL>>
```

**For Production:**
```
NODE_ENV=production
NEXT_PUBLIC_SITE_URL=https://www.cashbackdeals.com
NEXT_PUBLIC_SUPABASE_URL=your-production-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-production-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-production-service-role-key
JWT_SECRET=your-production-jwt-secret-min-32-chars
PARTNER_SECRET_DEFAULT=your-production-partner-secret-min-32-chars
ENABLE_SEARCH_AUTH=true
ENABLE_HMAC_AUTH=true
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your-production-turnstile-key
TURNSTILE_SECRET_KEY=your-production-turnstile-secret
EMAIL_SERVER=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your-production-email-password
EMAIL_FROM="Cashback Deals" <<EMAIL>>
```

#### Step 4: Deploy
1. Click "Save and deploy"
2. Monitor the build logs
3. Once complete, test your application

### Vercel

#### Step 1: Install Vercel CLI
```bash
npm install -g vercel
```

#### Step 2: Login and Setup
```bash
vercel login
vercel --cwd /path/to/your/project
```

#### Step 3: Configure vercel.json
Create `vercel.json` in your project root:

```json
{
  "framework": "nextjs",
  "buildCommand": "npm run test && npm run build",
  "installCommand": "npm ci",
  "env": {
    "NODE_ENV": "production"
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

#### Step 4: Set Environment Variables
```bash
# For staging
vercel env add NEXT_PUBLIC_SITE_URL preview
vercel env add SUPABASE_SERVICE_ROLE_KEY preview
# ... add all other variables

# For production
vercel env add NEXT_PUBLIC_SITE_URL production
vercel env add SUPABASE_SERVICE_ROLE_KEY production
# ... add all other variables
```

#### Step 5: Deploy
```bash
# Deploy to preview (staging)
vercel

# Deploy to production
vercel --prod
```

### Netlify

#### Step 1: Connect Repository
1. Go to [Netlify](https://app.netlify.com/)
2. Click "New site from Git"
3. Choose GitHub and select your repository

#### Step 2: Configure netlify.toml
Create `netlify.toml` in your project root:

```toml
[build]
  command = "npm run test && npm run build"
  publish = ".next"

[build.environment]
  NODE_ENV = "production"
  NEXT_PRIVATE_TARGET = "server"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

#### Step 3: Environment Variables
In Netlify Dashboard → Site Settings → Environment Variables, add all required variables (same as Amplify configuration above).

## Environment Variables Configuration

### Required Variables for All Environments

| Variable | Description | Example |
|----------|-------------|---------|
| `NODE_ENV` | Environment type | `staging` or `production` |
| `NEXT_PUBLIC_SITE_URL` | Your site URL | `https://www.example.com` |
| `NEXT_PUBLIC_SUPABASE_URL` | Supabase project URL | `https://xxx.supabase.co` |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Supabase anonymous key | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| `SUPABASE_SERVICE_ROLE_KEY` | Supabase service role key | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| `JWT_SECRET` | JWT signing secret (≥32 chars) | `your-super-secure-jwt-secret-min-32-characters` |
| `PARTNER_SECRET_DEFAULT` | HMAC partner secret (≥32 chars) | `your-super-secure-partner-secret-min-32-characters` |
| `ENABLE_SEARCH_AUTH` | Enable search authentication | `true` |
| `ENABLE_HMAC_AUTH` | Enable HMAC authentication | `true` |
| `NEXT_PUBLIC_TURNSTILE_SITE_KEY` | Cloudflare Turnstile site key | `0x4AAAAAAABkU1wLHgqHqS` |
| `TURNSTILE_SECRET_KEY` | Cloudflare Turnstile secret | `0x4AAAAAAABkU1wLHgqHqS...` |

### Email Configuration (Optional)

| Variable | Description | Example |
|----------|-------------|---------|
| `EMAIL_SERVER` | SMTP server | `smtp.gmail.com` |
| `EMAIL_PORT` | SMTP port | `587` |
| `EMAIL_SECURE` | Use TLS | `false` |
| `EMAIL_USER` | SMTP username | `<EMAIL>` |
| `EMAIL_PASSWORD` | SMTP password | `your-app-password` |
| `EMAIL_FROM` | From address | `"Your App" <<EMAIL>>` |

### Security Variables (Never Set These in Production)

| Variable | Description | Production Value |
|----------|-------------|------------------|
| `TEST_MODE_BYPASS_AUTH` | Test authentication bypass | ❌ **NEVER SET** |
| `DISABLE_HMAC_VALIDATE` | Disable HMAC validation | ❌ **NEVER SET** |
| `NEXT_PUBLIC_DEBUG_ENABLED` | Enable debug mode | ❌ **NEVER SET** |

## Testing Your Deployment

### 1. Smoke Tests
After deployment, test these critical paths:

```bash
# Test homepage
curl -I https://your-domain.com

# Test API endpoints
curl -X GET https://your-domain.com/api/products

# Test authentication (should return 401)
curl -X GET https://your-domain.com/api/search
```

### 2. Functional Tests
1. **Homepage**: Loads correctly with featured products
2. **Search**: Search functionality works
3. **Product Pages**: Individual product pages load
4. **Brand Pages**: Brand listing and detail pages work
5. **Contact Form**: Contact form submits successfully
6. **API Authentication**: Protected endpoints require authentication

### 3. Performance Tests
```bash
# Run Lighthouse audit
npm run audit:performance
npm run audit:seo
```

### 4. Security Tests
```bash
# Check security headers
curl -I https://your-domain.com | grep -E "(X-Frame-Options|X-XSS-Protection|Content-Security-Policy)"

# Test rate limiting
for i in {1..10}; do curl -X GET https://your-domain.com/api/search; done
```

## Common Issues and Troubleshooting

### Build Failures

**Issue**: `npm run build` fails
```bash
# Solutions:
1. Clear cache: npm run clean:build
2. Check Node.js version: node --version (should be 20.x)
3. Reinstall dependencies: rm -rf node_modules package-lock.json && npm install
```

**Issue**: TypeScript errors
```bash
# Solutions:
1. Run type check: npm run typecheck
2. Fix errors in TypeScript files
3. Ensure all dependencies are installed
```

### Environment Variable Issues

**Issue**: Supabase connection fails
```bash
# Check:
1. NEXT_PUBLIC_SUPABASE_URL is correct
2. NEXT_PUBLIC_SUPABASE_ANON_KEY is valid
3. SUPABASE_SERVICE_ROLE_KEY has proper permissions
```

**Issue**: Authentication not working
```bash
# Check:
1. JWT_SECRET is ≥32 characters
2. ENABLE_SEARCH_AUTH=true
3. ENABLE_HMAC_AUTH=true
4. No TEST_MODE_BYPASS_AUTH in production
```

### Performance Issues

**Issue**: Slow page loads
```bash
# Solutions:
1. Enable caching in hosting platform
2. Optimize images: check src/components/ui/OptimizedImage.tsx
3. Review Web Vitals: npm run performance:check
```

### SSL/Domain Issues

**Issue**: SSL certificate errors
```bash
# Solutions:
1. Wait for certificate provisioning (can take 24-48 hours)
2. Verify domain DNS settings
3. Check platform-specific SSL documentation
```

## Security Checklist

Before going live, verify:

- [ ] **Environment Variables**
  - [ ] All secrets are unique per environment
  - [ ] No test bypasses enabled
  - [ ] JWT_SECRET is ≥32 characters
  - [ ] PARTNER_SECRET_DEFAULT is ≥32 characters

- [ ] **HTTP Security Headers**
  - [ ] X-Frame-Options: DENY
  - [ ] X-XSS-Protection: 1; mode=block
  - [ ] X-Content-Type-Options: nosniff
  - [ ] Content-Security-Policy configured

- [ ] **Authentication**
  - [ ] HMAC authentication enabled
  - [ ] JWT authentication enabled
  - [ ] Rate limiting functional

- [ ] **Database Security**
  - [ ] Row Level Security (RLS) enabled in Supabase
  - [ ] Service role key restricted
  - [ ] Anonymous key has minimal permissions

- [ ] **Monitoring**
  - [ ] Error tracking configured
  - [ ] Performance monitoring active
  - [ ] Security event logging enabled

## Post-Deployment Steps

1. **Monitor Application**
   - Check error logs for any issues
   - Monitor performance metrics
   - Review security events

2. **Update DNS** (if using custom domain)
   - Point your domain to the hosting platform
   - Configure CDN if needed

3. **Set Up Monitoring**
   - Configure uptime monitoring
   - Set up alerting for critical errors
   - Monitor Web Vitals

4. **Backup Strategy**
   - Ensure database backups are configured
   - Document rollback procedures
   - Test disaster recovery plan

## Support and Troubleshooting

If you encounter issues:

1. Check the [Troubleshooting Guide](./TROUBLESHOOTING.md)
2. Review platform-specific documentation
3. Check GitHub Actions for CI/CD issues
4. Monitor application logs for errors

For platform-specific help:
- **Amplify**: [AWS Amplify Documentation](https://docs.amplify.aws/)
- **Vercel**: [Vercel Documentation](https://vercel.com/docs)
- **Netlify**: [Netlify Documentation](https://docs.netlify.com/)

---

**Last Updated**: 20th July 2025  
**Version**: 2.0  
**Next Review**: October 2025