# Search Load More Implementation Reference

**Last Updated:** August 3, 2025  
**Version:** v15.7.0  
**Status:** Production Ready - **HOTFIX APPLIED** ✅

> **🔧 HOTFIX v15.7.0 (Aug 3, 2025):** Fixed critical Load More parameter synchronization issues affecting brand, price, sort, and subcategory searches. All search filter types now maintain context through pagination.

## Overview

This document provides a comprehensive reference for the "Load More" functionality in the search results system of Cashback Deals v2. The implementation supports infinite scroll-style pagination for all search query types including text search, category filters, brand filters, and combined queries.

## Architecture Overview

The Load More functionality is built on a three-tier architecture:

1. **Server-Side Initial Load** (`src/app/search/page.tsx`) - SSR for SEO and deep-linking support
2. **Client-Side Load More** (`src/components/pages/SearchPageClient.tsx`) - Interactive pagination
3. **API Endpoint** (`src/app/api/search/more/route.ts`) - Authenticated data fetching

## Core Components

### 1. Server-Side Search Page (`src/app/search/page.tsx`)

**Purpose:** Initial page load with SEO optimization and deep-linking support

**Key Features:**
- **Deep-linking support:** Loads all products up to the current page for shareable URLs
- **Dynamic metadata generation** for SEO optimization  
- **Server-side search** with `searchProducts()` function
- **Structured data** injection for search engines
- **Error boundary** with fallback UI

**URL Parameters Supported:**
- `q` - Search query string
- `page` - Current page number
- `category` - Category filter
- `subcategory` - Subcategory filter ✅ **FIXED v15.7.0**
- `brand` - Brand filter ✅ **FIXED v15.7.0**
- `minPrice` - Minimum price filter ✅ **ADDED v15.7.0**
- `maxPrice` - Maximum price filter ✅ **ADDED v15.7.0**
- `sortBy` - Sort order preference ✅ **ADDED v15.7.0**

**Reference Links:**
- [ARCHITECTURE.md - Enhanced Search Architecture](../technical/ARCHITECTURE.md#3-enhanced-search-architecture-v1533)
- [Data Layer Types](../technical/DATA_MODEL.md)

### 2. Client-Side Search Component (`src/components/pages/SearchPageClient.tsx`)

**Purpose:** Interactive Load More functionality with authentication and state management

**Key Features:**

#### Load More Button Implementation
- **Button positioning:** `loadMoreButtonRef` for scroll position management
- **Loading states:** Spinner animation with disabled button state
- **Authentication integration:** JWT-based request authentication via `useJWTAuth`
- **Error handling:** CAPTCHA prompt for authentication failures
- **Smart scrolling:** Maintains user context after loading new content

#### State Management
```typescript
const [products, setProducts] = useState(initialProducts);
const [currentPage, setCurrentPage] = useState(initialPage);
const [hasMore, setHasMore] = useState(initialProducts.length < totalCount);
const [isLoading, setIsLoading] = useState(false);
```

#### Load More Logic Flow:
1. **Pre-checks:** Verify not loading and has more products
2. **Authentication:** Clear previous errors and authenticate request
3. **API Call:** Make authenticated request to `/api/search/more`
4. **State Update:** Append new products and update pagination state
5. **URL Update:** Update browser URL without page reload (`scroll: false`)
6. **Smart Scroll:** Scroll to show new content while maintaining context

#### Scroll Position Management
```typescript
// Before loading
const buttonPosition = loadMoreButton.getBoundingClientRect().top + window.scrollY;

// After loading
const headerHeight = 80;
const scrollToPosition = buttonPosition - headerHeight - 16;
window.scrollTo({ top: Math.max(0, scrollToPosition), behavior: 'smooth' });
```

**Reference Links:**
- [usePagination Hook Documentation](../reference/COMPONENT_DEPENDENCY_MATRIX.md)
- [Authentication Guide](../technical/SECURITY.md)

### 3. Load More API Endpoint (`src/app/api/search/more/route.ts`)

**Purpose:** Secure API endpoint for fetching additional search results

**Key Features:**

#### Security Implementation
- **CORS enforcement:** `enforceCorsPolicy()` with origin validation
- **Rate limiting:** `applyRateLimit()` with search-specific limits
- **JWT Authentication:** `authenticateSearchRequest()` with comprehensive logging
- **User classification:** Analytics for user type detection (real users vs bots)

#### Analytics & Monitoring
```typescript
// Load More attempt tracking
console.log(JSON.stringify({
  event: 'LOAD_MORE_ATTEMPT',
  timestamp: new Date().toISOString(),
  userType: userType,
  searchParams: { hasQuery, page, category }
}));
```

#### Response Structure
```typescript
{
  products: TransformedProduct[],
  currentPage: number,
  pageSize: number,
  totalCount: number,
  totalPages: number,
  hasMore: boolean
}
```

**Reference Links:**
- [Rate Limiting Configuration](../technical/SECURITY.md#rate-limiting)
- [CORS Security Policy](../technical/SECURITY.md#cors-policy)

## Search Data Layer (`src/lib/data/search.ts`)

**Purpose:** PostgreSQL-powered search with caching and performance optimization

### Enhanced Search Features (v15.3.3)

#### Full-Text Search Implementation
```typescript
// Primary: PostgreSQL full-text search for relevance
query = query.textSearch('search_vector', searchTerm, { 
  type: 'websearch',
  config: 'english' 
});

// Fallback: Fuzzy search for typos and variations
query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
```

#### Brand Alias Mapping
```typescript
const BRAND_ALIASES: Record<string, string[]> = {
  'samsung': ['samsung-uk'],
  'apple': ['apple', 'apple-uk'], 
  'sony': ['sony', 'sony-uk'],
  // Enhanced discoverability for common search terms
};
```

#### Smart Join Optimization
- **Inner joins** when filters require specific relationships (brand, category)
- **Left joins** for optional data (promotions, retailer offers)
- **Performance consideration:** Reduces query complexity based on active filters

**Reference Links:**
- [Database Schema](../technical/DATA_MODEL.md)
- [Caching Strategy](../technical/ARCHITECTURE.md#performance-optimization)

## URL State Management

### Pagination Hook (`src/hooks/usePagination.ts`)

**Purpose:** Centralized URL state management for all pagination scenarios

**Key Features:**
- **Clean URLs:** Page 1 doesn't appear in URL (`/search?q=phones` not `/search?q=phones&page=1`)
- **Filter integration:** Automatic page reset when filters change
- **Browser navigation:** Full back/forward button support
- **Type safety:** TypeScript interfaces for all parameters

**Usage Pattern:**
```typescript
// Basic pagination
const { currentPage, goToPage, updateFilters } = usePagination();

// Page-specific implementation
export function useSearchPagination() {
  return usePagination({
    defaultPage: 1,
    pageSize: 20,
    basePath: '/search'
  });
}
```

**Reference Links:**
- [URL Management Pattern](../development/WORKFLOWS.md#url-patterns)

## Search Query Support Matrix

| Query Type | Example | Load More Support | URL Pattern | Status |
|------------|---------|-------------------|-------------|---------|
| **Text Search** | `q=samsung phones` | ✅ Full | `/search?q=samsung+phones&page=2` | Working |
| **Category Filter** | `category=electronics` | ✅ Full | `/search?category=electronics&page=2` | Working |
| **Brand Filter** | `brand=samsung-uk` | ✅ Full | `/search?brand=samsung-uk&page=2` | ✅ **FIXED v15.7.0** |
| **Subcategory Filter** | `subcategory=phones` | ✅ Full | `/search?subcategory=phones&page=2` | ✅ **ADDED v15.7.0** |
| **Price Range Filter** | `minPrice=100&maxPrice=500` | ✅ Full | `/search?minPrice=100&maxPrice=500&page=2` | ✅ **ADDED v15.7.0** |
| **Sort Order** | `sortBy=price_asc` | ✅ Full | `/search?sortBy=price_asc&page=2` | ✅ **ADDED v15.7.0** |
| **Combined Query** | `q=phones&brand=apple&minPrice=200` | ✅ Full | `/search?q=phones&brand=apple&minPrice=200&page=2` | ✅ **FIXED v15.7.0** |
| **Complex Filters** | All parameters combined | ✅ Full | `/search?q=phone&brand=samsung&minPrice=100&maxPrice=500&sortBy=price_desc&page=3` | ✅ **FIXED v15.7.0** |
| **Empty Search** | No parameters | ⚠️ Limited | `/search` (shows all products) | Working |

## Performance Characteristics

### Debounce Optimization (v15.3.3)
- **SearchBar:** 150ms debounce for real-time search
- **SearchSuggestions:** 150ms debounce for autocomplete
- **Minimum query length:** 3 characters for full-text search relevance

### Caching Strategy
```typescript
export const searchProducts = createCachedFunction(_searchProducts, {
  key: 'searchProducts',
  revalidate: CACHE_DURATIONS.SHORT, // 5 minutes
  tags: [CACHE_TAGS.SEARCH, CACHE_TAGS.PRODUCTS],
});
```

### Response Time Targets
- **Initial page load:** < 2.5s (including SSR)
- **Load More API:** < 500ms average
- **UI response:** < 152ms (47.9ms improvement in v15.3.3)

**Reference Links:**
- [Performance Optimization Guide](../performance/PERFORMANCE_SEO.md)

## Authentication & Security

### JWT Authentication Flow
1. **Initial load:** No authentication required (public content)
2. **Load More trigger:** Check `useJWTAuth` authentication state
3. **API request:** Include JWT token via `makeAuthenticatedRequest()`
4. **Failure handling:** Show CAPTCHA verification prompt
5. **Success:** Continue with normal load more flow

### User Type Classification
```typescript
function classifyUserType(userAgent: string, referer: string): 
  'real_user' | 'seo_bot' | 'llm_bot' | 'unknown_bot' | 'development'
```

### Rate Limiting
- **Search endpoint:** 100 requests per 15 minutes per IP
- **Load More endpoint:** 50 requests per 10 minutes per IP
- **Authentication bypass:** Authenticated users get higher limits

**Reference Links:**
- [Security Implementation](../technical/SECURITY.md)
- [Rate Limiting Configuration](../development/SECURITY_GUARD_RAILS.md)

## Error Handling

### Client-Side Error Scenarios
1. **Network failures:** Show retry button with exponential backoff
2. **Authentication required:** Display CAPTCHA verification prompt
3. **Rate limiting:** Show "too many requests" message with countdown
4. **Server errors:** Fallback to contact support message

### Server-Side Error Handling
```typescript
// Comprehensive error logging with context
logger.error('Error loading more products', errorForLog, {
  requestId,
  currentPage,
  productsCount: products.length,
  loadDuration: Date.now() - loadStartTime
});
```

## Debug & Monitoring

### Performance Monitoring
```typescript
// Request tracing
const requestId = Math.random().toString(36).substring(2, 10);

// Performance headers
'X-Request-ID': requestId,
'X-Response-Time': `${Date.now() - requestStartTime}ms`,
'X-Search-Duration': `${searchDuration}ms`,
'X-Cache-Hit-Rate': `${cacheMetrics.stats.hitRate}%`
```

### Analytics Events
- `LOAD_MORE_ATTEMPT` - Every load more button click
- `LOAD_MORE_AUTH_SUCCESS` - Successful authentication
- `LOAD_MORE_AUTH_FAILURE` - Authentication failures with user classification

**Reference Links:**
- [Troubleshooting Guide](../development/TROUBLESHOOTING.md)
- [Build Troubleshooting](../development/BUILD_TROUBLESHOOTING.md)

## Testing Strategy

### Test Coverage Areas
1. **Unit Tests:** Data transformation and utility functions
2. **Integration Tests:** API endpoint functionality
3. **End-to-End Tests:** Complete load more user flows
4. **Performance Tests:** Load time and response time validation

### Key Test Scenarios
- Load more with different query types
- Authentication failure handling
- Network interruption recovery
- Deep-linking to specific pages
- Scroll position maintenance

**Reference Links:**
- [Testing Architecture](../development/TESTING.md)
- [Performance Testing](../performance/PERFORMANCE_SEO.md)

## Deployment Considerations

### Environment Variables
```bash
# Required for search functionality
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Required for authentication
NEXT_PUBLIC_TURNSTILE_SITE_KEY=your_turnstile_key
TURNSTILE_SECRET_KEY=your_turnstile_secret
```

### Build Requirements
- **Node.js:** 18.x+ (Node 22.x migration planned for Sep 2025)
- **Next.js:** 15.3.5+ (security-hardened version)
- **PostgreSQL:** Full-text search extensions enabled

**Reference Links:**
- [Environment Setup](../development/ENVIRONMENT_SETUP.md)
- [AWS Amplify Deployment](../deployment/AWS_AMPLIFY_DEPLOYMENT_GUIDE.md)

## Browser Support

### JavaScript Requirements
- **Modern browsers:** Full functionality with Load More button
- **No JavaScript:** Fallback pagination links in `<noscript>` tags
- **Progressive enhancement:** Core search works without JavaScript

### Accessibility
- **ARIA labels:** Load More button properly labeled
- **Keyboard navigation:** Full keyboard support
- **Screen readers:** Status announcements for loading states

---

## 🔧 HOTFIX v15.7.0 - Load More Parameter Synchronization

### Critical Issues Resolved (August 3, 2025)

#### 1. **Brand Search Pagination Failure**
**Problem:** Brand searches (e.g., Samsung, Apple) showed "Load More" button but clicking resulted in 404 errors.

**Root Cause:** Brand parameter was extracted from URL but not included in SearchFilters object passed to search function.

**Fix Applied:**
```typescript
// BEFORE (Broken)
const filters: SearchFilters = {
  query,
  category: category || undefined,
  // brand parameter missing!
};

// AFTER (Fixed)
const filters: SearchFilters = {
  query,
  category: category || undefined,
  brand: brand || undefined, // ✅ ADDED
};
```

#### 2. **Missing Search Parameters**
**Problem:** Price filters (`minPrice`/`maxPrice`), sort order (`sortBy`), and subcategory filters were completely ignored in Load More requests.

**Root Cause:** Parameters not extracted from URL searchParams and not included in SearchFilters interface.

**Fix Applied:**
- Added parameter extraction: `minPrice`, `maxPrice`, `sortBy`
- Enhanced SearchFilters interface with `subcategory` support
- Updated data layer to handle subcategory filtering

#### 3. **SearchFilters Interface Limitation**
**Problem:** Subcategory parameter had deprecation comment and was not supported in SearchFilters interface.

**Fix Applied:**
```typescript
// BEFORE (Incomplete Interface)
export interface SearchFilters {
  query?: string
  category?: string
  brand?: string
  minPrice?: number
  maxPrice?: number
  sortBy?: 'relevance' | 'price_asc' | 'price_desc' | 'newest' | 'featured'
}

// AFTER (Complete Interface)
export interface SearchFilters {
  query?: string
  category?: string
  subcategory?: string  // ✅ ADDED
  brand?: string
  minPrice?: number
  maxPrice?: number
  sortBy?: 'relevance' | 'price_asc' | 'price_desc' | 'newest' | 'featured'
}
```

### Files Modified in Hotfix

1. **`/src/app/api/search/more/route.ts`**
   - Lines 175-177: Added `minPrice`, `maxPrice`, `sortBy` parameter extraction
   - Lines 212-219: Complete SearchFilters object population
   - Lines 190-198: Enhanced logging for debugging
   - Line 203: Updated validation logic

2. **`/src/lib/data/types.ts`**
   - Line 318: Added `subcategory` to SearchFilters interface

3. **`/src/lib/data/search.ts`**
   - Lines 177-182: Added subcategory filtering support

### Testing Verification

#### Manual Testing Scenarios
- ✅ Brand search: `/search?brand=samsung-uk` → Load More works
- ✅ Price range: `/search?minPrice=100&maxPrice=500` → Filters maintained
- ✅ Sort order: `/search?sortBy=price_asc` → Sort preserved
- ✅ Combined filters: Complex multi-parameter searches work end-to-end

#### Build Verification
- ✅ TypeScript compilation successful
- ✅ All interface changes compatible
- ✅ API endpoints accept all parameter types

### Impact Assessment

**Before Hotfix:**
- 5 major search filter types broken in pagination
- User confusion from inconsistent search results
- Potential user abandonment due to broken functionality

**After Hotfix:**
- 100% search filter parity between initial load and Load More
- Consistent user experience across all search scenarios
- Complete parameter coverage for all implemented search features

---

## Quick Reference Links

### Technical Documentation
- [System Architecture](../technical/ARCHITECTURE.md)
- [Database Model](../technical/DATA_MODEL.md)
- [Security Implementation](../technical/SECURITY.md)

### Development Guides
- [Local Development](../development/LOCAL_DEVELOPMENT_GUIDE.md)
- [Testing Strategy](../development/TESTING.md)
- [Troubleshooting](../development/TROUBLESHOOTING.md)

### Deployment & Performance
- [AWS Amplify Setup](../deployment/AWS_AMPLIFY_SETUP.md)
- [Performance Optimization](../performance/PERFORMANCE_SEO.md)
- [Component Dependencies](../reference/COMPONENT_DEPENDENCY_MATRIX.md)

---

**Document Maintainer:** Technical Architecture Team  
**Review Schedule:** Quarterly  
**Next Review:** November 2025