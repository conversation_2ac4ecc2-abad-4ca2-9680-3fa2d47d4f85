// Sitemap Configuration Constants
export const SITEMAP_PAGE_SIZE = 5000;
export const CACHE_TTL_SITEMAP = 86400; // 24 hours

// Cache control header for optimal search engine crawler efficiency  
export const SITEMAP_CACHE_HEADERS = {
  'Cache-Control': 'public, max-age=0, s-maxage=86400, stale-while-revalidate=3600',
  'Content-Type': 'application/xml',
};

// Compression headers for sitemap optimization
export const SITEMAP_COMPRESSION_HEADERS = {
  'Vary': 'Accept-Encoding',
};

// Combined headers for sitemap routes (cache + compression)
export const SITEMAP_HEADERS = {
  ...SITEMAP_CACHE_HEADERS,
  ...SITEMAP_COMPRESSION_HEADERS,
};