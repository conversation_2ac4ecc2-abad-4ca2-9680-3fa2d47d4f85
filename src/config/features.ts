export const featureFlags = {
	search: {
		categoryFilter: true,
		subCategoryFilter: false,
		recommendedFilter: false,
		brandsSearch: true, // Controls visibility of the search box on brands page
	},
	navigation: {
		alphabetNav: {
			brands: true, // Controls visibility on brands page
		}
	}
} as const;

export type FeatureFlags = typeof featureFlags;

export const FEATURES = {
	ENABLE_RETAILER_FILTER: false, // Toggle retailer filtering in the FilterMenu
	FORCE_DEBUG_MODE: false, // Override environment-based debug visibility
	SHOW_EMBEDDED_SEARCH_BAR: false, // Controls visibility of the search bar on the search page
	SHOW_FILTER_CONTROLS: false, // Controls visibility of the filter controls on the search page
	SHOW_SORT_CONTROLS: true, // Controls visibility of the sort controls on the search page
	SHOW_BRANDS_SEARCH: false, // Controls visibility of the search box on brands page
	SHOW_FEATURED_RETAILERS: false, // Controls visibility of the featured retailers on the homepage
} as const;

export type Feature = keyof typeof FEATURES;

// Helper function to check if a feature is enabled
export function isFeatureEnabled(feature: Feature): boolean {
	return FEATURES[feature];
}
