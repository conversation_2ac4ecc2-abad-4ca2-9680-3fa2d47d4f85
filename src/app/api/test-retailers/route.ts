import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';
import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const supabase = createServerSupabaseReadOnlyClient();
    
    // Test database connection and get retailers count
    const { count, error } = await supabase
      .from('retailers')
      .select('*', { count: 'exact', head: true });
    
    if (error) {
      return NextResponse.json({ 
        error: 'Database error', 
        details: error.message 
      }, { status: 500 });
    }
    
    // Get first few retailers with data
    const { data: retailers, error: dataError } = await supabase
      .from('retailers')
      .select('id, name, slug, created_at')
      .limit(5);
    
    if (dataError) {
      return NextResponse.json({ 
        error: 'Data fetch error', 
        details: dataError.message 
      }, { status: 500 });
    }
    
    return NextResponse.json({
      success: true,
      count,
      retailers: retailers || []
    });
    
  } catch (error) {
    return NextResponse.json({ 
      error: 'Server error', 
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}