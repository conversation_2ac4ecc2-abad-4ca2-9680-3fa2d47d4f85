// src/app/robots.ts
// This file generates the robots.txt file for the website

import { MetadataRoute } from 'next';
import { env } from '@/env.mjs';

export default function robots(): MetadataRoute.Robots {
  return {
    rules: {
      userAgent: '*',
      allow: '/',
      // Disallow admin routes or any private sections
      disallow: ['/api/', '/admin/'],
    },
    sitemap: `${env.NEXT_PUBLIC_SITE_URL}/sitemap.xml`,
  };
}