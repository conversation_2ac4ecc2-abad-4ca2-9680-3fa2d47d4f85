'use client'

import React, { useState, useEffect, useMemo, useRef } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { ProductGrid } from '@/components/ProductGrid'
import { ProductCard } from '@/components/ProductCard'
import { Pagination, PaginationInfo } from '@/components/ui/pagination'
import { SortControls } from '@/components/search/SortControls'
import { TransformedProduct } from '@/lib/data/types'

type SortOption = 'recommended' | 'price_asc' | 'price_desc' | 'cashback_desc' | 'cashback_asc' | 'newest';

// Types
interface ApiResponse {
    data: TransformedProduct[] | null;
    error: string | null;
    pagination?: {
        page: number;
        pageSize: number;
        total: number;
        totalPages: number;
        hasNext: boolean;
        hasPrev: boolean;
    };
}

interface ProductsContentProps {
    initialData?: ApiResponse;
    filterOptions: {
        brands: Array<{ id: string; name: string }>;
        promotions: Array<{
            id: string;
            title: string;
            brand_id: string | null;
            brand_name: string;
        }>;
    };
    initialPage?: number;
    initialFilters?: {
        promotionId?: string;
        brandId?: string;
        categoryId?: string;
        search?: string;
    };
}

export default function ProductsContent({
    initialData,
    filterOptions,
    initialPage = 1,
    initialFilters = {}
}: ProductsContentProps) {
    const router = useRouter();
    const searchParams = useSearchParams();
    const productGridRef = useRef<HTMLDivElement>(null); // Added ref
    const [selectedSort, setSelectedSort] = useState<SortOption>('recommended');

    const handleSortChange = (value: SortOption) => {
        setSelectedSort(value);
    }

    const sortedProducts = useMemo(() => {
        return [...(initialData?.data || [])].sort((a, b) => {
            switch (selectedSort) {
                case 'price_asc':
                    return (a.minPrice || 0) - (b.minPrice || 0);
                case 'price_desc':
                    return (b.minPrice || 0) - (a.minPrice || 0);
                case 'cashback_desc':
                    return (b.cashbackAmount || 0) - (a.cashbackAmount || 0);
                case 'cashback_asc':
                    return (a.cashbackAmount || 0) - (b.cashbackAmount || 0);
                case 'newest':
                    return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
                default:
                    return 0;
            }
        });
    }, [initialData?.data, selectedSort]);

    // Create a stable, primitive key from the searchParams object.
    // This string is guaranteed to be different if any parameter changes.
    const paramsKey = searchParams.toString();

    //const currentPage = parseInt(searchParams?.get('page') || 'initialPage.toString()', 10);
    const currentPage = parseInt(searchParams?.get('page') || initialPage.toString(), 10);

    useEffect(() => {
        const scrollParam = searchParams.get('scroll');

        if (scrollParam === 'false') {
            const storedScrollY = sessionStorage.getItem('productsListScrollPosition');
            if (storedScrollY) {
                const scrollValue = parseInt(storedScrollY, 10);
                requestAnimationFrame(() => {
                    window.scrollTo(0, scrollValue);
                });
                sessionStorage.removeItem('productsListScrollPosition'); // Clear after use
            }
        } else {
            if (productGridRef.current) {
                requestAnimationFrame(() => {
                    productGridRef.current?.scrollIntoView({ behavior: 'smooth', block: 'start' });
                });
            } else {
                requestAnimationFrame(() => {
                    window.scrollTo(0, 0);
                });
            }
        }
    }, [paramsKey, searchParams, initialData]);

    useEffect(() => {
        return () => {
        };
    }, []); // Empty dependency array for mount/unmount

    const goToPage = (page: number) => {
        const params = new URLSearchParams(searchParams?.toString() || '');
        if (page === 1) {
            params.delete('page');
        } else {
            params.set('page', page.toString());
        }
        // Added 2025-06-29 16:30:00 - Rationale: Ensure 'scroll' parameter is removed for pagination links.
        params.delete('scroll');
        const newUrl = `/products${params.toString() ? `?${params.toString()}` : ''}`;
        router.push(newUrl);
    };

    const products = initialData?.data || [];
    const pagination = initialData?.pagination;
    const hasError = initialData?.error;

    return (
        <div className="min-h-screen">
            <div className="container px-4 py-6 md:py-12">
                <div className="flex flex-col md:flex-row gap-6">
                    <div className="flex-1">
                        {hasError ? (
                            <div className="text-center py-8 text-red-500" data-testid="pagination-error">
                                Error loading products: {hasError}
                            </div>
                        ) : products.length > 0 ? (
                            <>
                                {pagination && (
                                    <div className="mb-6 flex justify-between items-center">
                                        <PaginationInfo
                                            currentPage={pagination.page}
                                            totalPages={pagination.totalPages}
                                            totalItems={pagination.total}
                                            itemsPerPage={pagination.pageSize}
                                        />
                                        <SortControls selectedSort={selectedSort} handleSortChange={handleSortChange} />
                                    </div>
                                )}

                                <ProductGrid products={sortedProducts} currentPage={currentPage} />

                                {pagination && pagination.totalPages > 1 && (
                                    <div className="mt-8 flex justify-center">
                                        <Pagination
                                            currentPage={currentPage}
                                            totalPages={pagination.totalPages}
                                            onPageChange={goToPage}
                                        />
                                    </div>
                                )}
                            </>
                        ) : (
                            <div className="text-center py-8">
                                <h3 className="text-lg font-semibold mb-2">No products found</h3>
                                <p className="text-sm text-foreground/70">
                                    Try adjusting your filters or check back later
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
