

import { MetadataRoute } from 'next';
import { SITE_URL } from '@/config/domains';
import { SITEMAP_HEADERS } from '@/config/sitemap';

// The revalidate config must be a literal value for Next.js's static analysis.
// Do not replace with an imported variable.
export const revalidate = 86400; // 24 hours

export async function GET() {
    const staticRoutes = [
        { url: `${SITE_URL}`, lastModified: new Date(), changeFrequency: 'daily', priority: 1.0 },
        { url: `${SITE_URL}/products`, lastModified: new Date(), changeFrequency: 'daily', priority: 0.9 },
        { url: `${SITE_URL}/brands`, lastModified: new Date(), changeFrequency: 'weekly', priority: 0.9 },
        { url: `${SITE_URL}/retailers`, lastModified: new Date(), changeFrequency: 'weekly', priority: 0.9 },
        { url: `${SITE_URL}/search`, lastModified: new Date(), changeFrequency: 'daily', priority: 0.8 },
        { url: `${SITE_URL}/contact`, lastModified: new Date(), changeFrequency: 'monthly', priority: 0.5 },
    ] as MetadataRoute.Sitemap;

    const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    ${staticRoutes.map(route => `<url><loc>${route.url}</loc><lastmod>${route.lastModified instanceof Date ? route.lastModified.toISOString() : new Date().toISOString()}</lastmod><changefreq>${route.changeFrequency}</changefreq><priority>${route.priority}</priority></url>`).join('\n')}
</urlset>`;

    return new Response(sitemap, {
        headers: SITEMAP_HEADERS,
    });
}

