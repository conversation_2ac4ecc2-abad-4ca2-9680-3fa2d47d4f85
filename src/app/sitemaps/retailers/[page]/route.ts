import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';
import { SITEMAP_PAGE_SIZE, SITEMAP_HEADERS } from '@/config/sitemap';
import { SITE_URL } from '@/config/domains';

// The revalidate config must be a literal value for Next.js's static analysis.
// Do not replace with an imported variable.
export const revalidate = 86400; // 24 hours

export async function GET(
  _req: Request,
  context: { params: Promise<{ page: string }> }
) {
  const params = await context.params;
  const pageNum = Number(params.page) || 1;
  const start   = (pageNum - 1) * SITEMAP_PAGE_SIZE;
  const end     = start + SITEMAP_PAGE_SIZE - 1;

  const supabase = createServerSupabaseReadOnlyClient();
  
  // Query retailers - now benefits from RLS filtering (only active retailers)
  const { data: retailers = [] } = await supabase
    .from('retailers')
    .select('slug, updated_at, created_at')
    .range(start, end);

  const body = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${(retailers || [])
  .filter(r => r.slug) // Only include retailers with slugs
  .map(r => {
    return `<url><loc>${SITE_URL}/retailers/${r.slug}</loc><lastmod>${new Date(r.updated_at || r.created_at).toISOString()}</lastmod></url>`;
  }).join('\n')}
</urlset>`;

  return new Response(body, { headers: SITEMAP_HEADERS });
}