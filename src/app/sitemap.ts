import { MetadataRoute } from 'next';
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server';
import { SITEMAP_PAGE_SIZE } from '@/config/sitemap';
import { SITE_URL } from '@/config/domains';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const supabase = createServerSupabaseReadOnlyClient();

  // Get counts efficiently using direct queries
  const { count: productsCount } = await supabase
    .from('products')
    .select('*', { count: 'exact', head: true });

  const { count: brandsCount } = await supabase
    .from('brands')
    .select('*', { count: 'exact', head: true });

  const { count: retailersCount } = await supabase
    .from('retailers')
    .select('*', { count: 'exact', head: true });

  // Build sitemap INDEX URLs (not individual page URLs)
  const sitemaps: MetadataRoute.Sitemap = [
    {
      url: `${SITE_URL}/sitemaps/static`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1
    }
  ];

  // Add paginated sitemap URLs
  for (let i = 0; i < Math.ceil((productsCount || 0) / SITEMAP_PAGE_SIZE); i++) {
    sitemaps.push({
      url: `${SITE_URL}/sitemaps/products/${i + 1}`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.8
    });
  }

  for (let i = 0; i < Math.ceil((brandsCount || 0) / SITEMAP_PAGE_SIZE); i++) {
    sitemaps.push({
      url: `${SITE_URL}/sitemaps/brands/${i + 1}`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.7
    });
  }

  for (let i = 0; i < Math.ceil((retailersCount || 0) / SITEMAP_PAGE_SIZE); i++) {
    sitemaps.push({
      url: `${SITE_URL}/sitemaps/retailers/${i + 1}`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.6
    });
  }

  return sitemaps;
}