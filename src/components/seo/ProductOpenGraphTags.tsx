'use client';

import React from 'react';
import Head from 'next/head';
import { TransformedProduct, TransformedRetailerOffer } from '@/lib/data/types';
import { SITE_URL } from '@/config/domains';

interface ProductOpenGraphTagsProps {
  product: TransformedProduct;
  retailerOffers?: TransformedRetailerOffer[];
}

export const ProductOpenGraphTags: React.FC<ProductOpenGraphTagsProps> = ({
  product,
  retailerOffers
}) => {
  // Use retailerOffers prop or fallback to product.retailerOffers
  const offers = retailerOffers || product.retailerOffers || [];
  const validOffers = offers.filter(o => typeof o.price === 'number' && o.price > 0);

  // Calculate primary price (lowest valid offer)
  const primaryPrice = validOffers.length > 0 
    ? Math.min(...validOffers.map(offer => offer.price as number))
    : null;

  // Get primary image
  const primaryImage = product.images && product.images.length > 0
    ? (product.images[0].startsWith('http') 
        ? product.images[0] 
        : `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${product.images[0]}`)
    : product.brand?.logoUrl || null;

  // Build product URL
  const productUrl = `${SITE_URL}/products/${product.slug || product.id}`;

  // Build plural_title (product variations/similar names)
  const buildPluralTitle = () => {
    const titles = [product.name];
    
    // Add brand + product name variation
    if (product.brand?.name) {
      titles.push(`${product.brand.name} ${product.name}`);
    }
    
    // Add model number if available
    if (product.specifications?.model_number || product.specifications?.sku) {
      const modelNumber = product.specifications?.model_number || product.specifications?.sku;
      titles.push(modelNumber);
    }
    
    // Add category-specific variation
    if (product.category?.name) {
      titles.push(`${product.name} - ${product.category.name}`);
    }
    
    // Remove duplicates and join
    return [...new Set(titles)].join(', ');
  };

  const pluralTitle = buildPluralTitle();

  // Create description with fallback
  const description = product.description 
    ? (product.description.length > 155 
        ? `${product.description.substring(0, 155)}...` 
        : product.description)
    : `Get cashback on ${product.name} from ${product.brand?.name || 'top retailers'}. Compare prices and save money with our exclusive offers.`;

  const metaTags = [
    // Core OpenGraph tags
    { property: "og:type", content: "product" },
    { property: "og:title", content: `${product.name} - Best Cashback Deals` },
    { property: "og:url", content: productUrl },
    { property: "og:description", content: description },
    
    // Image with fallback
    ...(primaryImage ? [{ property: "og:image", content: primaryImage }] : []),
    { property: "og:image:alt", content: `${product.name} product image` },
    
    // Site information
    { property: "og:site_name", content: "RebateRay" },
    { property: "og:locale", content: "en_GB" },
    
    // Product-specific OpenGraph tags
    { property: "product:plural_title", content: pluralTitle },
    
    // Price information (only if we have valid offers)
    ...(primaryPrice ? [
      { property: "product:price.amount", content: primaryPrice.toString() },
      { property: "product:price.currency", content: "GBP" }
    ] : []),
    
    // Brand information
    ...(product.brand?.name ? [
      { property: "product:brand", content: product.brand.name }
    ] : []),
    
    // Category information  
    ...(product.category?.name ? [
      { property: "product:category", content: product.category.name }
    ] : []),
    
    // Availability status
    { property: "product:availability", content: validOffers.length > 0 ? "in stock" : "out of stock" },
    
    // Condition (assuming new products)
    { property: "product:condition", content: "new" },
    
    // Product ID/SKU
    { property: "product:product_link", content: productUrl },
    ...(product.specifications?.sku ? [
      { property: "product:sku", content: product.specifications.sku }
    ] : []),
    ...(product.specifications?.model_number ? [
      { property: "product:mpn", content: product.specifications.model_number }
    ] : []),
    
    // Retailer information (primary offer)
    ...(validOffers.length > 0 && validOffers[0].retailer ? [
      { property: "product:retailer", content: validOffers[0].retailer.name },
      ...(validOffers[0].retailer.websiteUrl ? [
        { property: "product:retailer_item_id", content: validOffers[0].retailer.websiteUrl }
      ] : [])
    ] : []),
    
    // Twitter Card fallbacks for better social sharing
    { name: "twitter:card", content: "summary_large_image" },
    { name: "twitter:title", content: `${product.name} - Best Cashback Deals` },
    { name: "twitter:description", content: description },
    ...(primaryImage ? [{ name: "twitter:image", content: primaryImage }] : [])
  ];

  return (
    <Head>
      {metaTags.map((tag, index) => {
        if (tag.property) {
          return <meta key={index} property={tag.property} content={tag.content} />;
        } else if (tag.name) {
          return <meta key={index} name={tag.name} content={tag.content} />;
        }
        return null;
      })}
    </Head>
  );
};