'use client'

import { useState, useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation';
import { Search as SearchIcon } from 'lucide-react'
import { useDebounce } from '../../hooks/useDebounce'
import { SearchSuggestions } from './SearchSuggestions'
import { validateSearchQuery, sanitizeString } from '@/lib/security/utils'

interface Suggestion {
  id: string
  name: string
}

interface SearchBarProps {
  onSearch?: (query: string) => void
  onSearchSubmit?: () => void
  suggestions?: Suggestion[]
  isLoading?: boolean
  error?: string | null
  isOpen?: boolean
  onOpenChange?: (isOpen: boolean) => void
  inHeader?: boolean
  initialValue?: string
}

export function SearchBar({
  onSearch,
  onSearchSubmit,
  suggestions,
  isLoading = false,
  error = null,
  isOpen,
  onOpenChange,
  initialValue = ''
}: SearchBarProps) {
  const [query, setQuery] = useState(initialValue)
  const [internalIsOpen, setIsOpen] = useState(false)
  const [validationError, setValidationError] = useState<string | null>(null)
  const isOpenControlled = isOpen !== undefined ? isOpen : internalIsOpen

  // Update query when initialValue changes (e.g., when navigating to search with brand/category)
  useEffect(() => {
    setQuery(initialValue)
  }, [initialValue])

  const handleOpenChange = useCallback((open: boolean) => {
    setIsOpen(open)
    onOpenChange?.(open)
  }, [onOpenChange])
  const searchRef = useRef<HTMLDivElement>(null)

  const debouncedQuery = useDebounce(query, 150)

  useEffect(() => {
    onSearch?.(debouncedQuery)
  }, [debouncedQuery, onSearch])

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const router = useRouter();
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Enhanced input change handler with validation and sanitization
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;

    // Prevent DoS attacks with length limit
    if (rawValue.length > 200) {
      setValidationError('Search query too long (max 200 characters)');
      return;
    }

    // Validate and sanitize the input
    const validation = validateSearchQuery(rawValue);

    if (!validation.isValid && rawValue.length > 0) {
      setValidationError('Search query contains invalid characters');
      return;
    }

    // Clear any previous validation errors
    setValidationError(null);

    // Use sanitized value
    setQuery(validation.sanitized);
    handleOpenChange(true);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const searchTerm = query.trim();

    if (!searchTerm) {
      setValidationError('Please enter a search term');
      return;
    }

    // Final validation before navigation
    const validation = validateSearchQuery(searchTerm);
    if (!validation.isValid) {
      setValidationError('Invalid search query');
      return;
    }

    // Navigate to search page with the validated query
    router.push(`/search?q=${encodeURIComponent(validation.sanitized)}`);
    // Close suggestions and blur the input
    handleOpenChange(false);
    searchInputRef.current?.blur();
    setValidationError(null);

    // Notify parent on search submit
    if (typeof onSearchSubmit === 'function') {
      onSearchSubmit();
    }
  };

  const handleSuggestionSelect = (suggestion: string) => {
    const searchTerm = suggestion.trim();

    if (!searchTerm) return;

    // Validate the suggestion before using it
    const validation = validateSearchQuery(searchTerm);
    if (!validation.isValid) {
      setValidationError('Invalid suggestion selected');
      return;
    }

    setQuery(validation.sanitized);
    setValidationError(null);

    // Give UI a moment to update before navigation
    setTimeout(() => {
      router.push(`/search?q=${encodeURIComponent(validation.sanitized)}`);
      // Notify parent on search submit
      if (typeof onSearchSubmit === 'function') {
        onSearchSubmit();
      }
    }, 100);
    // Close suggestions
    handleOpenChange(false);
  };

  // New: Handle closing suggestions
  const handleCloseSuggestions = () => {
    handleOpenChange(false);
  };

  // New: Handle direct form submission from suggestions
  const handleSubmitSearch = (query: string) => {
    const searchTerm = query.trim();
    
    if (!searchTerm) {
      setValidationError('Please enter a search term');
      return;
    }

    // Final validation before navigation
    const validation = validateSearchQuery(searchTerm);
    if (!validation.isValid) {
      setValidationError('Invalid search query');
      return;
    }

    // Navigate to search page with the validated query
    router.push(`/search?q=${encodeURIComponent(validation.sanitized)}`);
    // Close suggestions and blur the input
    handleOpenChange(false);
    searchInputRef.current?.blur();
    setValidationError(null);

    // Notify parent on search submit
    if (typeof onSearchSubmit === 'function') {
      onSearchSubmit();
    }
  };

  // Enhanced click-outside handling
  useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(e.target as Node)) {
        // Check if click is on form submit button - don't close if so
        const isSubmitButton = (e.target as Element)?.closest('button[type="submit"]');
        if (!isSubmitButton) {
          handleOpenChange(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [handleOpenChange]);

  return (
    <div className="relative w-full max-w-md" ref={searchRef}>
      <form 
        onSubmit={handleSubmit} 
        className="w-full"
        role="search"
        aria-label="Search products"
      >
        <div className="relative flex items-center">
          <input
            ref={searchInputRef}
            type="search"
            placeholder="Search products, brands, or categories..."
            className="w-full pl-4 pr-10 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary"
            value={query}
            onChange={handleInputChange}
            onFocus={() => {
              if (query.length > 0) {
                handleOpenChange(true);
              }
            }}
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                handleOpenChange(false);
                searchInputRef.current?.blur();
              }
            }}
            aria-label="Search products"
            aria-controls="search-suggestions"
            aria-autocomplete="list"
            autoComplete="off"
            autoCorrect="off"
            autoCapitalize="off"
            spellCheck="false"
          />
          <button
            type="submit"
            className="absolute right-0 top-0 h-full px-3 min-w-11 flex items-center justify-center text-primary hover:text-primary/80 transition-colors"
            aria-label="Submit search"
          >
            <SearchIcon className="h-5 w-5" />
          </button>
        </div>
      </form>

      {/* Validation error display */}
      {validationError && (
        <div className="absolute top-full left-0 right-0 mt-1 p-2 bg-red-50 border border-red-200 rounded-md text-red-600 text-sm z-50">
          {validationError}
        </div>
      )}

      {isOpenControlled && query.length > 0 && !validationError && (
        <div 
          id="search-suggestions" 
          role="listbox" 
          aria-label="Search suggestions"
          className="z-50"
        >
          <SearchSuggestions 
            query={query} 
            onSelect={handleSuggestionSelect}
            onClose={handleCloseSuggestions}
            onSubmitSearch={handleSubmitSearch}
            onSearchSubmit={onSearchSubmit}
          />
        </div>
      )}
    </div>
  )
}
