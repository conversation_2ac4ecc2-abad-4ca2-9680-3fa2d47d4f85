'use client';

import { SlidersHorizontal } from 'lucide-react';
import { motion } from 'framer-motion';

interface FilterControlsProps {
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
}

export function FilterControls({ showFilters, setShowFilters }: FilterControlsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
    >
      <button
        onClick={() => setShowFilters(!showFilters)}
        className="flex items-center gap-2 px-4 py-3 min-h-11 text-sm font-medium text-foreground/70 bg-secondary/10 rounded-lg hover:bg-secondary/20 transition-colors"
      >
        <SlidersHorizontal className="h-4 w-4" />
        Filters
      </button>
    </motion.div>
  );
}
