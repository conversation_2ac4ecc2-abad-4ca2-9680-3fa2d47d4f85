'use client';

import { motion } from 'framer-motion';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

type SortOption = 'recommended' | 'price_asc' | 'price_desc' | 'cashback_desc' | 'cashback_asc' | 'newest';

const sortOptions: Record<SortOption, string> = {
    recommended: 'Recommended',
    price_asc: 'Price: Low to High',
    price_desc: 'Price: High to Low',
    cashback_desc: 'Cashback: High to Low',
    cashback_asc: 'Cashback: Low to High',
    newest: 'Newest First',
};

interface SortControlsProps {
  selectedSort: SortOption;
  handleSortChange: (value: SortOption) => void;
}

export function SortControls({ selectedSort, handleSortChange }: SortControlsProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.2 }}
      className="flex items-center gap-2"
    >
      <Select onValueChange={handleSortChange} defaultValue={selectedSort}>
        <SelectTrigger className="w-full text-sm font-medium text-foreground/70 bg-background border-2 border-border rounded-lg hover:bg-secondary/20 transition-colors focus:ring-2 focus:ring-primary/20 sm:w-[240px]">
          <span className="text-sm text-foreground/70 mr-2">Sort by:</span>
          <SelectValue placeholder="Sort by" />
        </SelectTrigger>
        <SelectContent position="popper" className="w-[var(--radix-select-trigger-width)]">
          {Object.entries(sortOptions).map(([value, label]) => (
            <SelectItem key={value} value={value}>
              {label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </motion.div>
  );
}
