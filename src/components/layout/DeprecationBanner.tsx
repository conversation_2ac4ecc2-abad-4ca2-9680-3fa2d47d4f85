'use client';

import React, { useState, useEffect } from 'react';
import { X, AlertTriangle, ExternalLink } from 'lucide-react';

interface DeprecationBannerProps {
  /**
   * The service being deprecated (e.g., 'CloudFront', 'Cloudflare')
   */
  service: string;
  /**
   * Deprecation date in ISO format (e.g., '2025-12-31')
   */
  deprecationDate: string;
  /**
   * URL for more information about the deprecation
   */
  infoUrl?: string;
  /**
   * Whether to show the banner (can be controlled by environment variables)
   */
  show?: boolean;
  /**
   * Custom message override
   */
  customMessage?: string;
}

const DeprecationBanner: React.FC<DeprecationBannerProps> = ({
  service,
  deprecationDate,
  infoUrl,
  show = true,
  customMessage
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
    
    // Check if user has already dismissed this banner
    const dismissedKey = `deprecation-banner-${service.toLowerCase()}-dismissed`;
    const isDismissed = localStorage.getItem(dismissedKey) === 'true';
    
    // Only show if not dismissed and show prop is true
    if (!isDismissed && show) {
      setIsVisible(true);
    }
  }, [service, show]);

  const handleDismiss = () => {
    const dismissedKey = `deprecation-banner-${service.toLowerCase()}-dismissed`;
    localStorage.setItem(dismissedKey, 'true');
    setIsVisible(false);
  };

  // Don't render on server or if not visible
  if (!isClient || !isVisible) {
    return null;
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const defaultMessage = `${service} services will be deprecated on ${formatDate(deprecationDate)}. Please prepare for the transition to ensure uninterrupted service.`;
  const message = customMessage || defaultMessage;

  return (
    <div className="bg-amber-50 border-l-4 border-amber-400 p-4 shadow-sm" role="alert">
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" aria-hidden="true" />
          <div className="flex-1">
            <h3 className="text-sm font-medium text-amber-800">
              Service Deprecation Notice
            </h3>
            <p className="mt-1 text-sm text-amber-700">
              {message}
            </p>
            {infoUrl && (
              <div className="mt-2">
                <a
                  href={infoUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-sm text-amber-700 hover:text-amber-900 underline focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2"
                >
                  Learn more about the migration
                  <ExternalLink className="ml-1 h-3 w-3" aria-hidden="true" />
                </a>
              </div>
            )}
          </div>
        </div>
        <button
          type="button"
          onClick={handleDismiss}
          className="ml-4 flex-shrink-0 rounded-md bg-amber-50 p-1.5 text-amber-500 hover:bg-amber-100 hover:text-amber-600 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 focus:ring-offset-amber-50"
          aria-label="Dismiss deprecation notice"
        >
          <X className="h-4 w-4" aria-hidden="true" />
        </button>
      </div>
    </div>
  );
};

export default DeprecationBanner;