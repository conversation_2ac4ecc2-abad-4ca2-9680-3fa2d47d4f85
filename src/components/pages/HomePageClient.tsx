'use client';

import Link from 'next/link'
import { motion } from 'framer-motion'
import { FeaturedProductCard } from '../FeaturedProductCard'
// Update this import
import { FeaturedPromotionCard } from '../products/featured-promotion-card'
import { ArrowRight, Search, CreditCard, FileCheck, Wallet } from 'lucide-react'
import { BrandLogo } from '@/components/ui/OptimizedImage'
import { TransformedProduct, TransformedBrand, TransformedRetailer, TransformedPromotion, Brand, Category } from '@/lib/data/types'
import { isFeatureEnabled } from '@/config/features';

interface HomePageClientProps {
    featuredProducts: TransformedProduct[]
    featuredPromotions: TransformedPromotion[]
    featuredBrands: TransformedBrand[]
    featuredRetailers: TransformedRetailer[]
    fallbackPurchaseEndDate: string
}

type FeaturedPromotion = Pick<TransformedPromotion, 'id' | 'title' | 'description' | 'purchaseEndDate'> & {
    brand: Pick<Brand, 'id' | 'name'> | null;
    category: Pick<Category, 'name'> | null;
}

export function HomePageClient({
    featuredProducts,
    featuredPromotions,
    featuredBrands,
    featuredRetailers,
    fallbackPurchaseEndDate
}: HomePageClientProps) {
    console.log('Featured Products:', featuredProducts);
    console.log('Featured Promotions:', featuredPromotions);
    // Convert products to promotion format for compatibility with existing FeaturedProductCard
    const promotionsFromProducts: FeaturedPromotion[] = featuredProducts.map(product => ({
        id: product.id,
        title: product.name,
        description: product.promotion?.description || product.description || `${product.name} with cashback offer`,
        purchaseEndDate: product.promotion?.purchaseEndDate || fallbackPurchaseEndDate,
        brand: product.brand ? {
            id: product.brand.id,
            name: product.brand.name
        } : null,
        category: product.category ? {
            name: product.category.name
        } : null
    }));

    return (
        <div className="flex flex-col gap-12 pb-20">
            {/* Hero Section */}
            <motion.section
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gradient-to-r from-primary/10 via-secondary/10 to-background py-20"
            >
                <div className="container">
                    <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.2 }}
                        className="max-w-2xl"
                    >
                        <h1 className="text-4xl font-bold tracking-tight text-primary sm:text-6xl">
                            Get cashback and reward rebates from your favorite brands
                        </h1>
                        <p className="mt-6 text-lg text-foreground/70">
                            Discover exclusive Rebate offers and save on your purchases with RebateRay
                        </p>
                        <motion.div
                            whileHover={{ scale: 1.05 }}
                            className="mt-8"
                        >
                            <Link
                                href="/products"
                                className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors"
                            >
                                Browse Deals
                                <ArrowRight className="h-4 w-4" />
                            </Link>
                        </motion.div>
                    </motion.div>
                </div>
            </motion.section>

            {/* Featured Products */}
            <section className="container py-12">
                <h2 className="text-2xl font-bold text-primary mb-8 text-center">
                    Featured Products
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {featuredProducts.length > 0 ? (
                        featuredProducts.slice(0, 8).map((product) => {
                            // Format product data to match the new FeaturedProductCard interface
                            // Convert images to string[] format expected by TransformedProduct
                            const images = Array.isArray(product.images) 
                                ? product.images.map(img => 
                                    typeof img === 'string' ? img : (img as any).url || ''
                                  ).filter(Boolean)
                                : [];
                                
                            const formattedProduct: TransformedProduct = {
                                ...product,
                                images,
                                // Ensure all required fields have proper defaults
                                description: product.description || '',
                                specifications: product.specifications || null,
                                status: product.status || 'active',
                                isFeatured: product.isFeatured || false,
                                isSponsored: product.isSponsored || false,
                                modelNumber: (product as any).modelNumber || '',
                                createdAt: product.createdAt || new Date().toISOString(),
                                updatedAt: product.updatedAt || new Date().toISOString(),
                                // Ensure retailerOffers is an array
                                retailerOffers: (product.retailerOffers || []).map((offer: any) => ({
                                    id: offer.id || '',
                                    retailer: {
                                        id: offer.retailer?.id || '',
                                        name: offer.retailer?.name || 'Unknown Retailer',
                                        logoUrl: offer.retailer?.logoUrl || null,
                                        websiteUrl: offer.retailer?.websiteUrl || null,
                                        status: offer.retailer?.status || null,
                                        featured: offer.retailer?.featured || false,
                                        sponsored: offer.retailer?.sponsored || false,
                                        claimPeriod: offer.retailer?.claimPeriod || null,
                                        createdAt: offer.retailer?.createdAt || new Date().toISOString(),
                                        updatedAt: offer.retailer?.updatedAt || new Date().toISOString()
                                    },
                                    price: offer.price ?? null,
                                    stockStatus: offer.stockStatus || null,
                                    url: offer.url || null,
                                    createdAt: offer.createdAt || new Date().toISOString()
                                }))
                            };
                            
                            return (
                                <FeaturedProductCard
                                    key={product.id}
                                    product={formattedProduct}
                                />
                            );
                        })
                    ) : (
                        <div className="col-span-full text-center py-12">
                            <p className="text-foreground/70">No featured products available</p>
                        </div>
                    )}
                </div>
            </section>

            {/* Featured Promotions Section */}
            <section className="container py-12">
                <h2 className="text-2xl font-bold text-primary mb-8 text-center">
                    Featured Promotions
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {featuredPromotions.length > 0 ? (
                        featuredPromotions.slice(0, 4).map((promotion, i) => (
                            <FeaturedPromotionCard
                                key={promotion.id}
                                brand={promotion.brand || { id: 'unknown', name: 'Unknown Brand' }}
                                category={promotion.category || { name: 'General' }}
                                promotion={{
                                    id: promotion.id,
                                    description: promotion.description || 'Special promotion',
                                    purchase_end_date: promotion.purchaseEndDate
                                }}
                            />
                        ))
                    ) : (
                        <div className="col-span-3 text-center py-12">
                            <p className="text-foreground/70">No featured promotions available</p>
                        </div>
                    )}
                </div>
            </section>

            {/* Featured Brands Section */}
            {featuredBrands.length > 0 && (
                <section className="container py-12">
                    <h2 className="text-2xl font-bold text-primary mb-8 text-center">
                        Featured Brands
                    </h2>
                    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6">
                        {featuredBrands.map((brand) => (
                            <Link
                                key={brand.id}
                                href={`/brands/${brand.slug || brand.id}`}
                                className="group"
                            >
                                <div className="card p-4 text-center hover:shadow-lg transition-shadow">
                                    {brand.logoUrl && (
                                        <BrandLogo
                                            src={brand.logoUrl}
                                            alt={`${brand.name} brand logo`}
                                            width={48}
                                            height={48}
                                            className="h-12 w-12 mx-auto mb-2 object-contain"
                                            brandName={brand.name}
                                            loading="lazy"
                                        />
                                    )}
                                    <h3 className="font-medium text-sm group-hover:text-primary transition-colors">
                                        {brand.name}
                                    </h3>
                                </div>
                            </Link>
                        ))}
                    </div>
                </section>
            )}

            {/* Featured Retailers Section */}
            {isFeatureEnabled('SHOW_FEATURED_RETAILERS') && featuredRetailers.length > 0 && (
                <section className="container py-12">
                    <h2 className="text-2xl font-bold text-primary mb-8 text-center">
                        Featured Retailers
                    </h2>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                        {featuredRetailers.map((retailer) => (
                            <Link
                                key={retailer.id}
                                href={`/retailers/${retailer.slug || retailer.id}`}
                                className="group"
                            >
                                <div className="card p-4 text-center hover:shadow-lg transition-shadow">
                                    {retailer.logoUrl && (
                                        <BrandLogo
                                            src={retailer.logoUrl}
                                            alt={`${retailer.name} retailer logo`}
                                            width={48}
                                            height={48}
                                            className="h-12 w-12 mx-auto mb-2 object-contain"
                                            brandName={retailer.name}
                                            loading="lazy"
                                        />
                                    )}
                                    <h3 className="font-medium text-sm group-hover:text-primary transition-colors">
                                        {retailer.name}
                                    </h3>
                                    <p className="text-xs text-foreground/60 mt-1">
                                        {retailer.claimPeriod || 'Cashback available'}
                                    </p>
                                </div>
                            </Link>
                        ))}
                    </div>
                </section>
            )}

            {/* How It Works Section */}
            <section className="container py-12">
                <h2 className="text-2xl font-bold text-primary mb-12 text-center">How It Works</h2>
                <div className="relative">
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-8 md:gap-16">
                        {[
                            {
                                title: "Find Deal",
                                description: "Discover offers and Compare prices after cashback",
                                icon: Search
                            },
                            {
                                title: "Make Purchase",
                                description: "Buy from your favorite participating retailer",
                                icon: CreditCard
                            },
                            {
                                title: "Submit Claim",
                                description: "Upload your proof of purchase",
                                icon: FileCheck
                            },
                            {
                                title: "Receive Cashback",
                                description: "Get your money back quickly",
                                icon: Wallet
                            }
                        ].map((step, index) => {
                            const Icon = step.icon;
                            return (
                                <motion.div
                                    key={index}
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ delay: index * 0.1 }}
                                    className="text-center relative"
                                >
                                    <div className="bg-primary/10 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                                        <Icon className="h-8 w-8 text-primary" />
                                    </div>
                                    <h3 className="font-semibold text-lg mb-2">{step.title}</h3>
                                    <p className="text-foreground/70 text-sm">{step.description}</p>

                                    {/* Connection line (hidden on mobile) */}
                                    {index < 3 && (
                                        <div className="hidden md:block absolute top-8 left-full w-16 h-0.5 bg-primary/20 -translate-y-0.5" />
                                    )}
                                </motion.div>
                            );
                        })}
                    </div>
                </div>
            </section>
        </div>
    );
}
