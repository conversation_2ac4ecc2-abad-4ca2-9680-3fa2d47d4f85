'use client';

import { motion } from 'framer-motion';
import { ArrowLeft, ArrowRight, ExternalLink, Tag, Clock, Store, Globe } from 'lucide-react';
import Link from 'next/link';
import { BrandLogo, ProductImage } from '@/components/ui/OptimizedImage';
import { TransformedRetailer, TransformedProduct } from '@/lib/data/types';

interface RetailerPageClientProps {
  retailer: TransformedRetailer;
  products: TransformedProduct[];
}

export function RetailerPageClient({ retailer, products }: RetailerPageClientProps) {
  return (
    <div className="flex flex-col min-h-screen">
      <Link
        href="/retailers"
        className="container py-4 inline-flex items-center gap-2 text-sm font-medium text-primary hover:text-primary/90"
      >
        <ArrowLeft className="h-4 w-4" /> Back to Retailers
      </Link>

      {/* Hero Section */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-gradient-to-r from-primary/10 via-secondary/10 to-background py-20"
        role="banner"
        aria-label="Retailer Information"
      >
        <div className="container">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="grid md:grid-cols-2 gap-8 items-center"
          >
            <div className="h-64 bg-secondary/10 rounded-lg flex items-center justify-center overflow-hidden p-8">
              {retailer.logoUrl ? (
                <div className="relative w-full h-full max-w-[200px] mx-auto flex items-center justify-center">
                  <BrandLogo
                    src={retailer.logoUrl}
                    alt={`${retailer.name} retailer logo`}
                    width={200}
                    height={200}
                    className="object-contain max-w-full max-h-full w-auto h-auto"
                    loading="eager"
                    priority={true}
                    brandName={retailer.name}
                    fallbackSrc={`https://placehold.co/600x600/f1f5f9/64748b.png?text=${encodeURIComponent(retailer.name)}`}
                  />
                </div>
              ) : (
                <div className="text-2xl font-bold text-secondary/40">{retailer.name}</div>
              )}
            </div>
            <div>
              <h1 className="text-4xl font-bold text-primary mb-6">{retailer.name}</h1>
              <p className="text-lg text-foreground/70 mb-6">
                {retailer.name || `Shop at ${retailer.name} and earn cashback on your purchases. Discover exclusive deals and offers with guaranteed cashback rewards.`}
              </p>
              <div className="flex flex-wrap gap-4 mb-6">
                <div className="flex items-center gap-2 text-sm text-foreground/70">
                  <Store className="h-4 w-4 text-primary" />
                  <span>{products.length} Product(s) Available</span>
                </div>
                {retailer.websiteUrl && (
                  <div className="flex items-center gap-2 text-sm text-foreground/70">
                    <Globe className="h-4 w-4 text-primary" />
                    <a
                      href={retailer.websiteUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="hover:text-primary transition-colors"
                    >
                      Visit Website
                    </a>
                  </div>
                )}
                {retailer.claimPeriod && (
                  <div className="flex items-center gap-2 text-sm text-foreground/70">
                    <Clock className="h-4 w-4 text-primary" />
                    <span>{retailer.claimPeriod}</span>
                  </div>
                )}
              </div>
              {retailer.websiteUrl && (
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  className="mt-8"
                >
                  <a
                    href={retailer.websiteUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center gap-2 bg-primary text-primary-foreground px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors"
                  >
                    Shop at {retailer.name}
                    <ExternalLink className="h-4 w-4" />
                  </a>
                </motion.div>
              )}
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Content */}
      <div className="container py-12">
        {/* Cashback Information */}
        <div className="bg-secondary/10 rounded-lg p-6 mb-12">
          <h2 className="text-2xl font-bold text-primary mb-4">Cashback Information</h2>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-2">How to Earn Cashback</h3>
              <ol className="list-decimal list-inside space-y-2 text-sm text-foreground/70">
                <li>Find your desired product from {retailer.name}</li>
                <li>Click through to {retailer.name} from our platform</li>
                <li>Complete your purchase as normal</li>
                <li>Submit your receipt for cashback verification</li>
                <li>Receive your cashback within the claim period</li>
              </ol>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Important Information</h3>
              <ul className="space-y-2 text-sm text-foreground/70">
                <li>• Cashback rates vary by product and promotion</li>
                <li>• Claims must be submitted within the specified period</li>
                <li>• Terms and conditions apply to all offers</li>
                <li>• Cashback is subject to verification</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Available Products */}
        {products.length > 0 && (
          <>
            <h3 className="text-2xl font-bold text-primary mb-8">
              Available Products from {retailer.name}
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
              {products.map((product) => (
                <Link
                  key={product.id}
                  href={`/products/${product.slug || product.id}`}
                  className="group"
                >
                  <div className="card p-4 hover:shadow-lg transition-shadow">
                    {product.images && product.images.length > 0 && (
                      <ProductImage
                        src={product.images[0]}
                        alt={`${product.name} available at ${retailer.name}`}
                        width={300}
                        height={200}
                        className="h-48 w-full object-cover rounded mb-3"
                        productName={product.name}
                        brandName={product.brand?.name}
                        loading="lazy"
                      />
                    )}
                    <h4 className="font-medium text-sm group-hover:text-primary transition-colors mb-2">
                      {product.name}
                    </h4>
                    {product.brand && (
                      <p className="text-xs text-foreground/60 mb-2">
                        by {product.brand.name}
                      </p>
                    )}
                    {product.cashbackAmount && product.cashbackAmount > 0 && (
                      <p className="text-sm text-green-600 font-medium">
                        £{product.cashbackAmount.toFixed(2)} Cashback Available
                      </p>
                    )}
                    {product.category && (
                      <div className="flex items-center gap-1 mt-2">
                        <Tag className="h-3 w-3 text-primary" />
                        <span className="text-xs text-foreground/60">
                          {product.category.name}
                        </span>
                      </div>
                    )}
                  </div>
                </Link>
              ))}
            </div>

            {products.length >= 12 && (
              <div className="text-center">
                <Link
                  href={`/products?retailer=${retailer.id}`}
                  className="inline-flex items-center gap-2 text-primary hover:text-primary/90 font-medium"
                >
                  View All Products from {retailer.name}
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </div>
            )}
          </>
        )}

        {/* No Products Available */}
        {products.length === 0 && (
          <div className="text-center py-12">
            <h3 className="text-xl font-semibold text-foreground/70 mb-4">
              No Products Currently Available
            </h3>
            <p className="text-foreground/60 mb-6">
              We&apos;re working to add more products from {retailer.name}. Check back soon for new cashback opportunities!
            </p>
            {retailer.websiteUrl && (
              <a
                href={retailer.websiteUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 text-primary hover:text-primary/90 font-medium"
              >
                Visit {retailer.name} Website
                <ExternalLink className="h-4 w-4" />
              </a>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
