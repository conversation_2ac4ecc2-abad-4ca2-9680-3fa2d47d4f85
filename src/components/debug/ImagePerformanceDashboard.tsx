/**
 * Image Performance Dashboard Component
 * Displays real-time image loading performance metrics and recommendations
 * Only visible in development mode
 */

'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  getImageStats, 
  getImageRecommendations,
  imagePerformanceMonitor 
} from '@/lib/monitoring/imagePerformance';
import { getCircuitBreakerStatus } from '@/lib/imageLoader';
import { AlertTriangle, CheckCircle, Clock, TrendingUp, RefreshCw } from 'lucide-react';

interface ImagePerformanceDashboardProps {
  isVisible?: boolean;
  onToggle?: () => void;
}

export function ImagePerformanceDashboard({ 
  isVisible = false, 
  onToggle 
}: ImagePerformanceDashboardProps) {
  const [stats, setStats] = useState(getImageStats());
  const [recommendations, setRecommendations] = useState(getImageRecommendations());
  const [circuitBreakerStatus, setCircuitBreakerStatus] = useState(getCircuitBreakerStatus());
  const [isExpanded, setIsExpanded] = useState(false);

  // Update stats every 5 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(getImageStats());
      setRecommendations(getImageRecommendations());
      setCircuitBreakerStatus(getCircuitBreakerStatus());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const refreshStats = () => {
    setStats(getImageStats());
    setRecommendations(getImageRecommendations());
    setCircuitBreakerStatus(getCircuitBreakerStatus());
  };

  const clearStats = () => {
    imagePerformanceMonitor.clearMetrics();
    refreshStats();
  };

  const exportStats = () => {
    const data = imagePerformanceMonitor.exportMetrics();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `image-performance-${new Date().toISOString()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 left-4 z-[9999]">
        <Button
          onClick={onToggle}
          variant="outline"
          size="sm"
          className="bg-white shadow-lg border-2 border-blue-500 hover:bg-blue-50"
        >
          <TrendingUp className="w-4 h-4 mr-2 text-blue-600" />
          <span className="text-blue-600 font-semibold">Performance</span>
        </Button>
      </div>
    );
  }

  const successRate = stats.totalLoads > 0 
    ? ((stats.successfulLoads / stats.totalLoads) * 100).toFixed(1)
    : '0';

  const samsungSuccessRate = stats.samsungImageStats.totalLoads > 0
    ? ((stats.samsungImageStats.successfulLoads / stats.samsungImageStats.totalLoads) * 100).toFixed(1)
    : '0';

  return (
    <div className="fixed bottom-4 left-4 z-[9999] w-96 max-h-[80vh] overflow-y-auto">
      <div className="bg-white shadow-xl border-2 border-blue-500 rounded-lg">
        <div className="p-4 pb-3 border-b">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold flex items-center">
              <TrendingUp className="w-5 h-5 mr-2" />
              Image Performance
            </h3>
            <div className="flex gap-2">
              <Button
                onClick={() => setIsExpanded(!isExpanded)}
                variant="ghost"
                size="sm"
              >
                {isExpanded ? 'Collapse' : 'Expand'}
              </Button>
              <Button
                onClick={onToggle}
                variant="ghost"
                size="sm"
              >
                ×
              </Button>
            </div>
          </div>
        </div>

        <div className="p-4 space-y-4">
          {/* Overall Stats */}
          <div className="grid grid-cols-2 gap-3">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.totalLoads}</div>
              <div className="text-sm text-gray-600">Total Loads</div>
            </div>
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{successRate}%</div>
              <div className="text-sm text-gray-600">Success Rate</div>
            </div>
          </div>

          {/* Samsung Stats */}
          {stats.samsungImageStats.totalLoads > 0 && (
            <div className="border rounded-lg p-3">
              <h4 className="font-semibold text-sm mb-2 flex items-center">
                <AlertTriangle className="w-4 h-4 mr-1 text-orange-500" />
                Samsung Images
              </h4>
              <div className="grid grid-cols-3 gap-2 text-xs">
                <div className="text-center">
                  <div className="font-bold">{stats.samsungImageStats.totalLoads}</div>
                  <div className="text-gray-600">Total</div>
                </div>
                <div className="text-center">
                  <div className="font-bold text-green-600">{samsungSuccessRate}%</div>
                  <div className="text-gray-600">Success</div>
                </div>
                <div className="text-center">
                  <div className="font-bold text-red-600">{stats.samsungImageStats.timeoutCount}</div>
                  <div className="text-gray-600">Timeouts</div>
                </div>
              </div>
              {stats.samsungImageStats.averageLoadTime > 0 && (
                <div className="mt-2 text-xs text-center">
                  Avg Load Time: {stats.samsungImageStats.averageLoadTime.toFixed(0)}ms
                </div>
              )}
            </div>
          )}

          {/* Circuit Breaker Status */}
          {Object.keys(circuitBreakerStatus).length > 0 && (
            <div className="border rounded-lg p-3">
              <h4 className="font-semibold text-sm mb-2">Circuit Breakers</h4>
              {Object.entries(circuitBreakerStatus).map(([domain, status]) => (
                <div key={domain} className="flex items-center justify-between text-xs mb-1">
                  <span className="truncate">{domain}</span>
                  <Badge 
                    variant={status.state === 'CLOSED' ? 'default' : 'destructive'}
                    className="text-xs"
                  >
                    {status.state}
                  </Badge>
                </div>
              ))}
            </div>
          )}

          {/* Recommendations */}
          {recommendations.length > 0 && (
            <div className="border rounded-lg p-3">
              <h4 className="font-semibold text-sm mb-2 flex items-center">
                <CheckCircle className="w-4 h-4 mr-1 text-blue-500" />
                Recommendations
              </h4>
              <div className="space-y-1">
                {recommendations.slice(0, isExpanded ? recommendations.length : 2).map((rec, index) => (
                  <div key={index} className="text-xs text-gray-700 bg-blue-50 p-2 rounded">
                    {rec}
                  </div>
                ))}
                {!isExpanded && recommendations.length > 2 && (
                  <div className="text-xs text-gray-500 text-center">
                    +{recommendations.length - 2} more recommendations
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Expanded Details */}
          {isExpanded && (
            <>
              {/* Performance Metrics */}
              <div className="border rounded-lg p-3">
                <h4 className="font-semibold text-sm mb-2">Performance Metrics</h4>
                <div className="space-y-2 text-xs">
                  <div className="flex justify-between">
                    <span>Average Load Time:</span>
                    <span>{stats.averageLoadTime.toFixed(0)}ms</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Failed Loads:</span>
                    <span className="text-red-600">{stats.failedLoads}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fallback Usage:</span>
                    <span className="text-orange-600">{stats.fallbackUsageCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Retries:</span>
                    <span>{stats.retryStats.totalRetries}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Success After Retry:</span>
                    <span className="text-green-600">{stats.retryStats.successAfterRetry}</span>
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button
              onClick={refreshStats}
              variant="outline"
              size="sm"
              className="flex-1"
            >
              <RefreshCw className="w-3 h-3 mr-1" />
              Refresh
            </Button>
            <Button
              onClick={clearStats}
              variant="outline"
              size="sm"
              className="flex-1"
            >
              Clear
            </Button>
            <Button
              onClick={exportStats}
              variant="outline"
              size="sm"
              className="flex-1"
            >
              Export
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Hook to manage dashboard visibility
 */
export function useImagePerformanceDashboard() {
  const [isVisible, setIsVisible] = useState(false);

  // Show dashboard with keyboard shortcut (Ctrl/Cmd + Shift + I)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'I') {
        event.preventDefault();
        setIsVisible(prev => !prev);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  return {
    isVisible,
    toggle: () => setIsVisible(prev => !prev),
    show: () => setIsVisible(true),
    hide: () => setIsVisible(false)
  };
}
