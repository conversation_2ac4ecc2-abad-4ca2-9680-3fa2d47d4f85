/**
 * Environment Security Guard-Rail
 * 
 * This module prevents test-only bypass flags from being enabled in non-test environments.
 * It provides fail-fast security protection against accidental deployment of insecure configurations.
 * 
 * CRITICAL: This file must be imported early in the application lifecycle to ensure
 * security checks run before any authentication or rate limiting logic.
 */

// Determine if we're in a legitimate test environment
const isTestEnv = process.env.NODE_ENV === 'test' || process.env.CI === 'true'

// Define unsafe flags that should only be enabled in test/CI environments
const UNSAFE_FLAGS = [
  'TEST_MODE_BYPASS_AUTH',
  'ENABLE_RATE_LIMITING', // If this flag exists and is 'false', it's unsafe
  'SKIP_ENV_VALIDATION',
  'DISABLE_HMAC_VALIDATE',
  'TEST_MODE_BYPASS_CORS',
  'BYPASS_IP_ALLOWLIST',
] as const

// Define flags that are unsafe when set to specific values
const UNSAFE_FLAG_VALUES: Record<string, string[]> = {
  'ENABLE_RATE_LIMITING': ['false'],
  'ENABLE_IP_ALLOWLIST': ['false'], // Only unsafe in production
  'ENABLE_SEARCH_AUTH': ['false'],
  'ENABLE_HMAC_AUTH': ['false'],
}

/**
 * Validates that test-only bypass flags are not enabled in production environments
 * @throws {Error} If unsafe flags are detected in non-test environments
 */
function validateEnvironmentSecurity(): void {
  if (isTestEnv) {
    // In test environments, all flags are allowed
    return
  }

  const violations: string[] = []

  // Check for boolean bypass flags that should never be 'true' in production
  UNSAFE_FLAGS.forEach((flag) => {
    if (process.env[flag] === 'true') {
      violations.push(
        `${flag}=true is not allowed in production (NODE_ENV=${process.env.NODE_ENV})`
      )
    }
  })

  // Check for flags with specific unsafe values
  Object.entries(UNSAFE_FLAG_VALUES).forEach(([flag, unsafeValues]) => {
    const currentValue = process.env[flag]
    if (currentValue && unsafeValues.includes(currentValue)) {
      // Special cases: These can be disabled in development for smooth development experience
      if (process.env.NODE_ENV === 'development' && (
        flag === 'ENABLE_IP_ALLOWLIST' ||
        flag === 'ENABLE_SEARCH_AUTH' ||
        flag === 'ENABLE_HMAC_AUTH'
      )) {
        return
      }
      
      violations.push(
        `${flag}=${currentValue} is not allowed in production (NODE_ENV=${process.env.NODE_ENV})`
      )
    }
  })

  // If violations found, fail fast with clear error message
  if (violations.length > 0) {
    const errorMessage = [
      '🚨 SECURITY VIOLATION: Test-only bypass flags detected in production environment!',
      '',
      'The following unsafe environment variables are set:',
      ...violations.map(v => `  • ${v}`),
      '',
      'These flags are only allowed when NODE_ENV=test or CI=true.',
      'Please check your deployment configuration and environment variables.',
      '',
      'For security reasons, the application will not start with these settings.',
    ].join('\n')

    throw new Error(errorMessage)
  }
}

/**
 * Gets the current environment security status for debugging
 * @returns Object with environment details and flag status
 */
export function getEnvironmentSecurityStatus() {
  return {
    isTestEnv,
    nodeEnv: process.env.NODE_ENV,
    ci: process.env.CI,
    flagStatus: Object.fromEntries(
      UNSAFE_FLAGS.map(flag => [flag, process.env[flag] || 'undefined'])
    ),
    flagValues: Object.fromEntries(
      Object.keys(UNSAFE_FLAG_VALUES).map(flag => [flag, process.env[flag] || 'undefined'])
    ),
  }
}

/**
 * Runtime validation check - this runs immediately when the module is imported
 * 
 * IMPORTANT: This validation runs at module import time to ensure security
 * checks happen as early as possible in the application lifecycle.
 */
try {
  validateEnvironmentSecurity()
  
  // Log security status in development for debugging
  if (process.env.NODE_ENV === 'development') {
    console.log('🔒 Environment security guard-rail: PASSED')
  }
} catch (error) {
  // Log the security violation and re-throw to stop application startup
  console.error(error instanceof Error ? error.message : String(error))
  throw error
}

// Export the validation function for manual checks if needed
export { validateEnvironmentSecurity }