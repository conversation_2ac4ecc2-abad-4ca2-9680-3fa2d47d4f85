import { Metadata } from 'next';
import { SITE_URL } from '@/config/domains';

// Base metadata object that will be used as default for all pages
export const siteConfig = {
  name: 'RebateRay',
  description: 'Discover and compare cashback deals and rebates from top brands in the UK.',
  url: SITE_URL,
};

// Helper function to ensure image URLs are absolute and accessible to external services
function getAbsoluteImageUrl(image: string): string {
  if (!image) return '';
  
  // If it's already a full URL, return it as is
  if (image.startsWith('http')) {
    return image;
  }
  
  // Construct the Supabase storage URL for relative paths
  return `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/${image}`;
}

// Helper function to construct metadata for any page
export function constructMetadata({
  title,
  description,
  image,
  noIndex = false,
  pathname,
  openGraph,
}: {
  title?: string;
  description?: string;
  image?: string;
  noIndex?: boolean;
  pathname?: string;
  openGraph?: Record<string, any>;
}): Metadata {
  const metaTitle = title 
    ? `${title} | ${siteConfig.name}` 
    : `${siteConfig.name} - Find the Best Rebates and Cashback Reward Deals`;
  
  const metaDescription = description || siteConfig.description;
  
  // Construct canonical URL
  const url = pathname 
    ? `${siteConfig.url}${pathname}` 
    : siteConfig.url;
  
  // Ensure image URL is absolute for social media crawlers
  const absoluteImageUrl = image ? getAbsoluteImageUrl(image) : undefined;

  return {
    title: metaTitle,
    description: metaDescription,
    metadataBase: new URL(siteConfig.url),
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      url,
      siteName: siteConfig.name,
      images: absoluteImageUrl ? [{ url: absoluteImageUrl }] : undefined,
      type: 'website',
      ...(openGraph || {}),
    } as any,
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: absoluteImageUrl ? [absoluteImageUrl] : undefined,
    },
    robots: {
      index: !noIndex,
      follow: !noIndex,
    },
    alternates: {
      canonical: url,
    },
  };
}
