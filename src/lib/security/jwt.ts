// src/lib/security/jwt.ts
// JWT authentication helper with dual transport support (cookies + headers)

import { SignJWT, jwtVerify, type KeyLike } from 'jose'
import { NextRequest, NextResponse } from 'next/server'

// JWT configuration  
function getHS256Key(): KeyLike {
  const raw = process.env.JWT_SECRET
  
  // Critical: Never allow fallback in production
  if (process.env.NODE_ENV === 'production' && !raw) {
    throw new Error('JWT_SECRET environment variable is required in production')
  }
  
  // Development fallback only
  const secretString = raw || 'dev-fallback-secret-min-32-chars-required'
  
  // Validate minimum key length for HS256 (256 bits = 32 bytes)
  if (secretString.length < 32) {
    throw new Error('JWT_SECRET must be ≥32 chars')
  }
  
  return require('crypto').createSecretKey(Buffer.from(secretString, 'utf8'))
}

const JWT_ALGORITHM = 'HS256'
const JWT_EXPIRY = '5m' // 5 minutes as specified

// JWT payload structure
export interface JWTPayload {
  sub: 'frontend'
  iat: number
  exp: number
}

// Creates signed JWT with 5-minute expiry
export async function createJWT(): Promise<string> {
  try {
    const secret = getHS256Key()
    const jwt = await new SignJWT({ sub: 'frontend' })
      .setProtectedHeader({ alg: JWT_ALGORITHM, typ: 'JWT' })
      .setIssuedAt()
      .setExpirationTime(JWT_EXPIRY)
      .sign(secret)

    return jwt
  } catch (error) {
    // Re-throw specific errors for production validation
    if (error instanceof Error && error.message.includes('JWT_SECRET environment variable is required in production')) {
      throw error
    }
    console.error('JWT creation failed:', error)
    throw new Error('Failed to create JWT token')
  }
}

// Verifies JWT and returns payload or null
export async function verifyJWT(token: string): Promise<JWTPayload | null> {
  try {
    const secret = getHS256Key()
    const { payload } = await jwtVerify(token, secret)
    
    // Validate payload structure
    if (payload.sub !== 'frontend') {
      console.warn('Invalid JWT subject:', payload.sub)
      return null
    }

    return payload as JWTPayload
  } catch (error) {
    // Don't log full error details for security (could expose token info)
    console.warn('JWT verification failed')
    return null
  }
}

// Extracts JWT from Authorization header or cookie
export function extractJWTFromRequest(request: NextRequest): string | null {
  // Check Authorization header first (for API clients)
  const authHeader = request.headers.get('Authorization')
  if (authHeader?.startsWith('Bearer ')) {
    return authHeader.substring(7)
  }

  // Fallback to cookie (for browser requests)
  const cookieToken = request.cookies.get('auth-token')
  return cookieToken?.value || null
}

// Sets JWT as secure HttpOnly cookie
export function setJWTCookie(response: NextResponse, token: string): void {
  const cookieOptions = [
    `auth-token=${token}`,
    'HttpOnly',
    'SameSite=Lax',
    'Path=/',
    'Max-Age=300', // 5 minutes (matches JWT expiry)
    ...(process.env.NODE_ENV === 'production' ? ['Secure'] : [])
  ].join('; ')

  response.headers.set('Set-Cookie', cookieOptions)
}

// Clears JWT cookie
export function clearJWTCookie(response: NextResponse): void {
  const cookieOptions = [
    'auth-token=',
    'HttpOnly',
    'SameSite=Lax',
    'Path=/',
    'Max-Age=0',
    ...(process.env.NODE_ENV === 'production' ? ['Secure'] : [])
  ].join('; ')

  response.headers.set('Set-Cookie', cookieOptions)
}

// Middleware helper: verify JWT from request
export async function verifyRequestJWT(request: NextRequest): Promise<JWTPayload | null> {
  const token = extractJWTFromRequest(request)
  if (!token) {
    return null
  }

  // Test mode bypass for mock tokens
  if (process.env.NODE_ENV === 'test' && process.env.TEST_MODE_BYPASS_AUTH === 'true') {
    if (token === 'valid_jwt_token') {
      return {
        sub: 'frontend',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 300
      }
    }
  }

  return await verifyJWT(token)
}

// Creates 401 response for authentication failures
export function createUnauthorizedResponse(message: string = 'Invalid or missing authentication token'): NextResponse {
  return NextResponse.json(
    { 
      error: 'Unauthorized',
      message,
      code: 'AUTH_TOKEN_REQUIRED'
    },
    { 
      status: 401,
      headers: {
        'Content-Type': 'application/json',
        'WWW-Authenticate': 'Bearer realm="API"'
      }
    }
  )
}