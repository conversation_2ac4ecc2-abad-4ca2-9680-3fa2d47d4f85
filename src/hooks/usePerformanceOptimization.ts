// src/hooks/usePerformanceOptimization.ts
// Performance optimization hooks for Core Web Vitals improvement

import React, { useEffect, useCallback, useRef, useState, useMemo } from 'react';
import { useRouter } from 'next/navigation';

interface PerformanceOptimizationOptions {
  enablePrefetch?: boolean;
  enablePreload?: boolean;
  enableIntersectionObserver?: boolean;
  enableLayoutShiftPrevention?: boolean;
}

/**
 * Hook for optimizing component performance and Core Web Vitals
 */
export function usePerformanceOptimization(options: PerformanceOptimizationOptions = {}) {
  const {
    enablePrefetch = true,
    enablePreload = true,
    enableIntersectionObserver = true,
    enableLayoutShiftPrevention = true,
  } = options;

  const router = useRouter();
  const observerRef = useRef<IntersectionObserver | null>(null);
  const [isVisible, setIsVisible] = useState(false);

  // Prefetch pages on hover for faster navigation
  const prefetchOnHover = useCallback((href: string) => {
    if (!enablePrefetch) return;

    return {
      onMouseEnter: () => {
        router.prefetch(href);
      },
    };
  }, [router, enablePrefetch]);

  // Preload critical resources
  const preloadResource = useCallback((href: string, as: string, type?: string) => {
    if (!enablePreload || typeof window === 'undefined') return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (type) link.type = type;
    link.onload = () => {
      console.log(`Preloaded resource: ${href}`);
    };
    link.onerror = () => {
      console.warn(`Failed to preload resource: ${href}`);
    };
    document.head.appendChild(link);
  }, [enablePreload]);

  // Intersection observer for lazy loading and performance tracking
  const observeElement = useCallback((element: HTMLElement | null, callback?: () => void) => {
    if (!enableIntersectionObserver || !element) return;

    if (observerRef.current) {
      observerRef.current.disconnect();
    }

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            callback?.();
            
            // Mark performance milestone
            if ('performance' in window) {
              performance.mark(`element-visible-${Date.now()}`);
            }
          }
        });
      },
      {
        rootMargin: '50px', // Start loading 50px before element is visible
        threshold: 0.1,
      }
    );

    observerRef.current.observe(element);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [enableIntersectionObserver]);

  // Prevent layout shift by reserving space
  const preventLayoutShift = useCallback((width?: number, height?: number) => {
    if (!enableLayoutShiftPrevention) return {};

    return {
      style: {
        aspectRatio: width && height ? `${width} / ${height}` : undefined,
        minHeight: height ? `${height}px` : undefined,
        minWidth: width ? `${width}px` : undefined,
      },
    };
  }, [enableLayoutShiftPrevention]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      observerRef.current?.disconnect();
    };
  }, []);

  return {
    prefetchOnHover,
    preloadResource,
    observeElement,
    preventLayoutShift,
    isVisible,
  };
}

/**
 * Hook for optimizing image loading and preventing layout shift
 */
export function useImageOptimization() {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [dimensions, setDimensions] = useState<{ width: number; height: number } | null>(null);

  const handleLoad = useCallback((event: React.SyntheticEvent<HTMLImageElement>) => {
    const img = event.currentTarget;
    setIsLoaded(true);
    setDimensions({ width: img.naturalWidth, height: img.naturalHeight });
    
    // Mark LCP candidate
    if ('performance' in window) {
      performance.mark('image-loaded');
    }
  }, []);

  const handleError = useCallback(() => {
    setHasError(true);
    console.warn('Image failed to load');
  }, []);

  const getImageProps = useCallback((priority = false) => ({
    onLoad: handleLoad,
    onError: handleError,
    loading: priority ? 'eager' as const : 'lazy' as const,
    style: {
      opacity: isLoaded ? 1 : 0,
      transition: 'opacity 0.3s ease-in-out',
    },
  }), [handleLoad, handleError, isLoaded]);

  return {
    isLoaded,
    hasError,
    dimensions,
    getImageProps,
  };
}

/**
 * Hook for debouncing expensive operations to improve FID
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Hook for throttling scroll events to improve performance
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastRun = useRef(Date.now());

  return useCallback(
    ((...args) => {
      if (Date.now() - lastRun.current >= delay) {
        callback(...args);
        lastRun.current = Date.now();
      }
    }) as T,
    [callback, delay]
  );
}

/**
 * Hook for measuring component render performance
 */
export function useRenderPerformance(componentName: string) {
  const renderStart = useRef<number>(0);
  const renderCount = useRef<number>(0);

  useEffect(() => {
    renderStart.current = performance.now();
    renderCount.current++;
  });

  useEffect(() => {
    const renderEnd = performance.now();
    const renderTime = renderEnd - renderStart.current;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`🎯 ${componentName} render #${renderCount.current}: ${renderTime.toFixed(2)}ms`);
      
      // Warn about slow renders
      if (renderTime > 16) { // 60fps = 16.67ms per frame
        console.warn(`⚠️ Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`);
      }
    }

    // Mark performance milestone
    if ('performance' in window) {
      performance.mark(`${componentName}-render-${renderCount.current}`);
    }
  });

  return renderCount.current;
}

/**
 * Hook for optimizing scroll performance
 */
export function useScrollOptimization() {
  const [scrollY, setScrollY] = useState(0);
  const [isScrolling, setIsScrolling] = useState(false);
  const scrollTimeout = useRef<NodeJS.Timeout | undefined>(undefined);

  const throttledScrollHandler = useThrottle(() => {
    setScrollY(window.scrollY);
    setIsScrolling(true);

    // Clear existing timeout
    if (scrollTimeout.current) {
      clearTimeout(scrollTimeout.current);
    }

    // Set scroll end detection
    scrollTimeout.current = setTimeout(() => {
      setIsScrolling(false);
    }, 150);
  }, 16); // ~60fps

  useEffect(() => {
    window.addEventListener('scroll', throttledScrollHandler, { passive: true });
    
    return () => {
      window.removeEventListener('scroll', throttledScrollHandler);
      if (scrollTimeout.current) {
        clearTimeout(scrollTimeout.current);
      }
    };
  }, [throttledScrollHandler]);

  return { scrollY, isScrolling };
}

/**
 * Hook for optimizing component updates with React.memo
 */
export function useOptimizedMemo<T>(
  factory: () => T,
  deps: React.DependencyList,
  componentName?: string
): T {
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const memoizedValue = useMemo(() => factory(), deps);
  
  if (process.env.NODE_ENV === 'development' && componentName) {
    console.log(`🔄 ${componentName} memo recalculated`);
  }
  
  return memoizedValue;
}
