import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  ...compat.extends("next/core-web-vitals"),
  {
    ignores: [
      "node_modules/**/*",
      ".next/**/*",
      "out/**/*",
      "build/**/*",
      "dist/**/*",
      "src/app/test-api-routes/**/*",
      "src/app/test-data-layer/**/*", 
      "src/app/test-featured-debug/**/*",
      "src/app/api/test-email/**/*",
      "*.d.ts",
      ".env*",
      "!.env.example",
      ".DS_Store",
      "*.log",
      "npm-debug.log*",
      "yarn-debug.log*",
      "yarn-error.log*"
    ]
  }
];

export default eslintConfig;