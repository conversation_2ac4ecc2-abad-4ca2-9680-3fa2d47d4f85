# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### **Ultra-Simple Local Development (99% of daily work)**
- `NODE_ENV=test npm run build && npm run start` - Start production-like development server (FASTEST - use this for all daily development)
- `npm run clean && NODE_ENV=test npm run build && npm run start` - When cache issues occur

### Build and Development
- `NODE_ENV=test npm run build && npm run start` - Standard development workflow
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run clean` - Remove .next directory
- `npm run clean:build` - Clean and build
- `npm run preview` - Build and start production server

### **Build Testing (when needed)**
- `NODE_ENV=test npm run build && npm run start` - Test production build without authentication barriers
- `npm run clean && NODE_ENV=test npm run build && npm run start` - Clean build testing

### Testing
**Unit & Integration Tests (Jest)**:
- `npm run test` - Run all Jest tests (unit, integration, security)
- `npm run test:watch` - Run tests in watch mode
- `npm run test:coverage` - Run tests with coverage report
- `npm run test:ci` - Run tests in CI mode with simplified config
- `npm run test:api` - Run API integration tests
- `npm run test:metadata` - Run metadata validation tests
- `npm run test:camelcase` - Run camelCase validation tests

**End-to-End Tests (Playwright)**:
- `npm run test:e2e` - Run Playwright E2E tests
- `npm run test:e2e:headed` - Run E2E tests with browser UI
- `npm run test:e2e:ui` - Run E2E tests with Playwright UI mode

### Linting and Quality
- `npm run lint` - Run ESLint
- `npx eslint --fix` - Auto-fix ESLint issues

### SEO and Performance
- `npm run seo:test` - Run SEO tests
- `npm run audit:seo` - Run Lighthouse SEO audit
- `npm run audit:performance` - Run Lighthouse performance audit
- `npm run performance:check` - Check Web Vitals

## Project Architecture

### Technology Stack
- **Framework**: Next.js 15.3.5 with App Router (Security upgraded July 2025)
- **Database**: Supabase (PostgreSQL)
- **UI Framework**: React 19.1.0 with TypeScript (Stable release upgrade July 2025)
- **Styling**: Tailwind CSS with shadcn/ui components
- **State Management**: React Query for server state
- **Authentication**: Supabase Auth
- **Testing**: Jest with React Testing Library
- **Deployment**: AWS Amplify with Cloudflare security

### Key Architectural Patterns

#### 1. Data Layer Architecture
The application uses a centralized data layer pattern located in `src/lib/data/`:
- **Server-side data functions**: All database operations use server-side functions with proper caching
- **Type safety**: Comprehensive TypeScript interfaces in `src/lib/data/types.ts`
- **Caching strategy**: Redis-like caching with `createCachedFunction` utility
- **Supabase client**: Server-side read-only client for security (`src/lib/supabase/server.ts`)

#### 2. URL State Management with usePagination
The `usePagination` hook (`src/hooks/usePagination.ts`) is the **single source of truth** for all paginated URLs:
- **Centralized parameter handling**: All URL parameters including transient ones like `scroll`
- **Page-specific hooks**: `useProductsPagination`, `useRetailersPagination`, `useBrandsPagination`
- **Clean URLs**: Page 1 doesn't show in URL, maintains SEO-friendly structure
- **Browser navigation**: Full support for back/forward navigation

#### 3. Enhanced Search Architecture (v15.3.3)
Comprehensive PostgreSQL-powered search system with real-time performance optimization:

**🚀 Performance Optimization (47.9ms improvement)**:
- **SearchBar debounce**: Reduced from 300ms → 150ms for faster response
- **SearchSuggestions debounce**: Optimized from 200ms → 150ms
- **Minimum query length**: Increased from 2 → 3 characters for relevance
- **UI response time**: Average 152.1ms (down from ~200ms)

**🔍 PostgreSQL Full-Text Search Integration**:
- **Primary search**: ts_vector with ts_rank relevance scoring
- **Fallback search**: Trigram similarity for typos and variations
- **Brand alias mapping**: Enhanced discoverability (e.g., "samsung" → "Samsung UK")
- **Smart join optimization**: Inner/left joins based on filter requirements

**📊 Search Components**:
- **Search API**: `/api/search/route.ts` with rate limiting and validation
- **Enhanced data layer**: `src/lib/data/search.ts` with PostgreSQL full-text search
- **UI components**: `SearchBar`, `SearchSuggestions` in `src/components/search/`
- **Intelligent suggestions**: Auto-complete with brand aliases and fuzzy matching
- **Performance testing**: Real user simulation with `ui-search-suggestions-test.js`

#### 4. Security Implementation
Multi-layered security approach with runtime protection:
- **🔒 CRITICAL: Runtime Security Guard-Rails**: Production environment protection in `src/lib/env-guard.ts`
- **HTTP Security Headers**: CSP, HSTS, XSS protection in `next.config.js`
- **Rate Limiting**: API route protection with `src/lib/rateLimiter.ts`
- **Input Validation**: Zod schemas in `src/lib/validation/schemas.ts`
- **DOMPurify**: XSS prevention with isomorphic-dompurify
- **Cloudflare Turnstile**: Bot protection and CAPTCHA

#### 5. Performance Optimization
- **Image optimization**: Next.js Image with multiple remote patterns
- **Caching layers**: Multiple cache durations (SHORT, MEDIUM, EXTENDED)
- **Web Vitals monitoring**: Real-time performance tracking
- **Code splitting**: Webpack optimization for vendor chunks
  - **SEO optimization**: Structured data, dynamic sitemaps, metadata utils

### Directory Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── api/               # API routes with rate limiting
│   ├── brands/            # Brand listing and detail pages
│   ├── products/          # Product listing and detail pages
│   ├── search/            # Search functionality
│   └── layout.tsx         # Root layout with providers
├── components/            # Reusable UI components
│   ├── layout/           # Header, footer, SEO components
│   ├── pages/            # Page-specific client components
│   ├── search/           # Search-related components
│   ├── ui/               # shadcn/ui components
│   └── debug/            # Development debugging tools
├── lib/                   # Core utilities and services
│   ├── data/             # Data layer (products, brands, search)
│   ├── supabase/         # Database clients
│   ├── validation/       # Zod schemas
│   └── security/         # Security utilities
├── hooks/                # Custom React hooks
├── types/                # TypeScript type definitions
└── utils/                # Utility functions

tests/                     # Enterprise Test Architecture (July 2025)
├── unit/                 # Unit tests (components, utilities, data layer)
├── integration/          # Integration tests (API routes, auth workflows)
├── e2e/                  # End-to-end tests with Playwright
├── security/             # Security tests (HMAC auth, XSS, rate limiting)
├── performance/          # Performance and load tests
├── __mocks__/            # Centralized mock implementations
├── fixtures/             # Shared test data and fixtures
├── setup/                # Test environment configuration
└── Test_Archives/        # Historical test consolidation records
```

### Database Schema
Core entities with full-text search capabilities:
- **Products**: With brand, category, promotion relationships
- **Brands**: With search vector and featured status
- **Retailers**: With claim periods and offer management
- **Promotions**: With time-based validation and cashback rules
- **Product_Retailer_Offers**: Price comparison and stock status

### Key Development Patterns

#### 1. Component Architecture
- **Client components**: Use 'use client' directive, located in `src/components/pages/`
- **Server components**: Default for page.tsx files, handle data fetching
- **UI components**: shadcn/ui based, in `src/components/ui/`
- **Layout components**: Header, footer, SEO in `src/components/layout/`
- **Search components**: Optimized for performance with configurable debouncing

#### 1.1. Search Component Performance Guidelines
- **Debounce timing**: Use 150ms for responsive UX (balance between speed and API efficiency)
- **Minimum query length**: 3 characters for full-text search relevance
- **Fallback patterns**: Always implement graceful degradation for search failures
- **Testing approach**: Use real user simulation for accurate performance metrics

#### 2. Data Fetching Pattern
```typescript
// Always use server-side data functions
import { getProducts } from '@/lib/data/products'
import { searchProducts, getSearchSuggestions } from '@/lib/data/search'
import { createServerSupabaseReadOnlyClient } from '@/lib/supabase/server'

// In server components
const supabase = createServerSupabaseReadOnlyClient()
const products = await getProducts(supabase, filters, page, limit)

// For search functionality
const searchResults = await searchProducts(supabase, searchFilters, page, limit)
const suggestions = await getSearchSuggestions(supabase, query, 5)
```

#### 2.1. Search Data Pattern
```typescript
// PostgreSQL full-text search with fallback
const searchFilters: SearchFilters = {
  query: 'samsung phones',
  brand: 'samsung-uk', // Brand slug for filtering
  category: 'electronics',
  sortBy: 'relevance', // Uses ts_rank for text search
  minPrice: 100,
  maxPrice: 1000
}

// Enhanced search with brand aliases
const results = await searchProducts(supabase, searchFilters, 1, 20)
// Returns products with relevance scoring and brand alias resolution
```

#### 3. URL Management Pattern
```typescript
// Always use pagination hooks for URL state
const { currentPage, goToPage, updateFilters } = useProductsPagination()

// For navigation
goToPage(2) // Handles URL construction and scroll behavior
updateFilters({ brand: 'samsung' }) // Resets to page 1

// For search pages with enhanced query handling
const { updateSearchFilters } = useSearchPagination()
updateSearchFilters({ query: 'samsung', brand: 'samsung-uk' })
```

#### 4. Error Handling
- **API routes**: Use try-catch with structured error responses
- **Components**: Error boundaries and fallback UI
- **Validation**: Zod schema validation with detailed error messages
- **Monitoring**: Debug logging in development mode

#### 5. 🚨 CRITICAL: Product OpenGraph Final Implementation
**NEVER MODIFY WITHOUT EXPLICIT CONFIRMATION** - The product page OpenGraph implementation in `src/app/products/[id]/page.tsx` represents the FINAL STABLE SOLUTION after extensive crisis resolution:

**Implementation Location**: `generateMetadata` function in `src/app/products/[id]/page.tsx`

**FINAL STABLE CODE SECTION (PROTECTED)**:
```typescript
// 🚨 CRITICAL: FINAL OPENGRAPH METADATA IMPLEMENTATION 
// DO NOT MODIFY WITHOUT EXPLICIT CONFIRMATION
// ALL ATTEMPTS TO USE og:type="product" FAILED NEXT.JS VALIDATION
// THIS IS THE STABLE SOLUTION THAT ELIMINATES VALIDATION ERRORS
openGraph: {
  // NO type: 'product' - completely removed due to Next.js limitations
  title,
  description,
  url: `${process.env.NEXT_PUBLIC_SITE_URL}/products/${product.slug || product.id}`,
  siteName: 'RebateRay',
  locale: 'en_GB',
  images: primaryImage ? [{ url: primaryImage, alt: `${product.name} product image` }] : undefined,
},
other: {
  // Product metadata WITHOUT problematic og:type
  'product:plural_title': pluralTitle,
  'product:price.amount': primaryPrice?.toString(),
  'product:brand': product.brand?.name,
  // ... comprehensive product metadata (NO og:type)
}
```

**Why This Final Implementation Is Critical**:
- **Crisis Resolution**: Completely eliminates "Invalid OpenGraph type: product" validation errors that caused page failures
- **Page Stability**: Ensures product pages load successfully without console errors
- **Framework Limitation**: Next.js App Router rejects ALL attempts to use og:type="product" regardless of implementation approach
- **SEO Compromise**: Trades advanced product metadata for guaranteed page stability
- **Structured Data Preserved**: Maintains comprehensive SEO through JSON-LD schema markup

**CRITICAL: What This Implementation Does NOT Include**:
- ❌ **NO og:type="product"** - removed entirely to prevent validation errors
- ❌ **NO product-specific OpenGraph tags** - sacrificed for page stability  
- ❌ **NO ProductOpenGraphTags component** - client-side approach abandoned
- ✅ **Basic OpenGraph only** - title, description, image, URL for social sharing
- ✅ **Full SEO via structured data** - comprehensive JSON-LD schema remains intact

**Modification Protocol**:
1. **EXPLICIT CONFIRMATION REQUIRED** before ANY changes to this implementation
2. **DO NOT attempt to re-add og:type="product"** - will cause validation errors
3. **DO NOT use client-side Head components** - doesn't work in App Router
4. **Test page loading after any changes** - verify no console errors
5. **Social sharing will be basic** - this is an accepted trade-off for stability

### Environment Variables
Key environment variables (see `docs/ENVIRONMENT_VARIABLES.md`):
- `NEXT_PUBLIC_SUPABASE_URL` - Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY` - Public Supabase key
- `SUPABASE_SERVICE_ROLE_KEY` - Server-side Supabase key
- `NEXT_PUBLIC_TURNSTILE_SITE_KEY` - Cloudflare Turnstile
- `TURNSTILE_SECRET_KEY` - Turnstile server validation

### Testing Strategy
**Enterprise-Grade Test Architecture (July 2025 Transformation)**:
- **Location**: All tests consolidated in `tests/` directory with logical categorization
- **Unit tests**: Jest with React Testing Library in `tests/unit/`
- **Integration tests**: API route testing with Supabase in `tests/integration/`
- **End-to-end tests**: Playwright for critical user flows in `tests/e2e/`
- **Security tests**: XSS, rate limiting, HMAC authentication in `tests/security/`
- **Performance tests**: Web Vitals and Lighthouse audits
- **Test infrastructure**: Centralized mocks in `tests/__mocks__/`, shared fixtures in `tests/fixtures/`
- **Archive system**: Test consolidation history maintained in `tests/Test_Archives/`

### Deployment and Infrastructure
- **Hosting**: Our production build is deployed via AWS Amplify Console
- **Database**: Supabase with migrations in `supabase/migrations/`
- **CDN**: Cloudflare for security headers and bot protection
- **Images**: Next.js Image optimization with remote patterns
- **Monitoring**: Web Vitals and error tracking
- # INFRASTRUCTURE:
Our production build is deployed via AWS Amplify Console. A Cloudflare proxy terminates TLS and routes traffic to the Amplify distribution. No Vercel services are used in this pipeline. The refereces to Vercel are incorrect and out of date. 

## 🚨 CRITICAL: Anti-Hallucination Verification Protocol

**MANDATORY: This section must be followed before EVER claiming completion of database, infrastructure, or system-level changes OR IMPLEMENATION OF SCOPE OF WORK.**

### Types of Hallucination Mistakes to Prevent

1. **File Creation vs. Implementation Conflation**
   - ❌ NEVER claim database changes are "implemented" just because migration files exist
   - ❌ NEVER report system changes as "active" without functional verification
   - ✅ ALWAYS distinguish between "migration file created" vs "migration applied to database"

2. **Assumption-Based Reporting**
   - ❌ NEVER assume migration status from code patterns or existing files
   - ❌ NEVER claim "verified" without actually verifying using available tools
   - ✅ ALWAYS use MCP tools (Supabase, etc.) to check actual system state

3. **Status Inflation**
   - ❌ NEVER use language like "implemented", "active", "working" without verification
   - ❌ NEVER mark tasks as "completed" when they are only "prepared"
   - ✅ ALWAYS use accurate language: "ready to implement", "migration file created", "prepared for application"

### Mandatory Pre-Completion Verification

Before claiming ANY database or infrastructure changes:

#### Database Changes
```bash
# REQUIRED: Verify actual database schema
- Use Supabase MCP tools to check table structure
- Run actual queries to verify RLS policies are active
- Test functionality with real database calls
- Confirm migrations are in applied state, not just file state
```

#### System Configuration  
```bash
# REQUIRED: Test actual functionality
- Verify environment variables are loaded correctly
- Test API endpoints actually work with new configuration
- Confirm security policies are enforced, not just defined
```

#### Infrastructure Changes
```bash
# REQUIRED: Verify deployment state
- Check that changes are active in target environment
- Test actual functionality, not just configuration files
- Confirm services are running with new settings
```

### Forbidden Language Patterns

❌ **NEVER USE** without verification:
- "implemented database-level security"
- "RLS policies are active" 
- "migration has been applied"
- "system is now configured to..."
- "database changes are live"

✅ **ACCURATE ALTERNATIVES**:
- "RLS migration file created, ready for application"
- "Configuration prepared, needs deployment"
- "Migration ready to apply to database"
- "Changes staged for implementation"

### Completion Statement Protocol

Before providing ANY summary of achievements:

1. **Review each claimed item individually**
2. **Verify actual implementation status using available tools**
3. **Distinguish between preparation work vs. actual deployment**
4. **Acknowledge what still needs to be done**

#### Example Correct Summary Format:
```
✅ Migration files created (NOT YET APPLIED to database):
   - retailers RLS policies (needs application via Supabase MCP)
   - updated_at column (needs verification if applied)

✅ Code changes implemented and active:
   - Domain centralization in application code
   - Deprecation banner component (ready to show when enabled)

❌ Still needed:
   - Apply RLS migration to actual database
   - Verify updated_at column exists in production
   - Enable deprecation banner via environment variables
```

### Emergency Self-Check Questions

Before claiming completion, ask:
1. "Did I actually run commands to verify this change is active?"
2. "Am I confusing file creation with system implementation?"
3. "Have I used available MCP tools to verify system state?"
4. "Would this claim mislead the user about what's actually working?"

**If any answer is uncertain, DO NOT claim completion.**

## Important Notes

### Security Requirements
- **🔒 CRITICAL: Runtime Security Protection**: The application uses `src/lib/env-guard.ts` to prevent test-only bypass flags in production
- **🔒 CRITICAL: Supply Chain Security**: All CI dependencies are version-pinned (wait-on@8.0.3, @lhci/cli@0.15.x) to prevent malicious updates
- **🔒 CRITICAL: Secrets Protection**: GitHub Actions workflows use ::add-mask:: to prevent accidental credential exposure in logs
- **🔒 Multi-layered Security Scanning**: Build artifacts are scanned for test flags and credential leakage before deployment
- Never expose the service role key in client-side code
- Always validate user inputs with Zod schemas
- Use DOMPurify for any dynamic HTML content
- Implement rate limiting on all API routes
- Follow CSP guidelines for script and style sources

### Performance Guidelines
- Use server-side data functions with proper caching
- Implement lazy loading for images and components
- Monitor Web Vitals and maintain good Core Web Vitals scores
- Use appropriate cache headers for API responses

### Development Workflow
1. Run `NODE_ENV=test npm run build && npm run start` for development server
2. Use `npm run lint` before committing
3. Run `npm run test` to ensure all tests pass
4. Use `npm run build` to verify production build
5. Check `npm run audit:seo` for SEO compliance

### Common Issues and Solutions
- **Security guard-rail failures**: Check environment variables for test-only bypass flags in production
- **CI dependency issues**: Verify pinned versions (wait-on@8.0.3, @lhci/cli@0.15.x) in workflow files
- **Secrets logging**: Ensure ::add-mask:: is applied before any environment variable usage in CI
- **Build security failures**: Check for test flags or real credentials accidentally included in .next/ artifacts
- **Build errors**: Run `npm run clean:build` to clear cache
- **Type errors**: Check `src/lib/data/types.ts` for interface definitions
- **Pagination issues**: Always use the `usePagination` hook family
- **Search performance issues**: 
  - Check debounce timing (should be 150ms)
  - Verify minimum query length (3 characters for full-text search)
  - Test PostgreSQL full-text search with `ui-search-suggestions-test.js`
  - Review brand alias mappings in `src/lib/data/search.ts`
- **Search not working**: Verify API route and data layer functions
- **Security headers**: Check `next.config.js` CSP configuration

### 📚 Enterprise Documentation System

The project now uses a comprehensive enterprise-grade documentation system organized by audience and purpose:

#### **📋 Technical Architecture** ([docs/technical/](docs/technical/))
- [`docs/technical/ARCHITECTURE.md`](docs/technical/ARCHITECTURE.md) - System architecture, technology stack, design patterns
- [`docs/technical/DATA_MODEL.md`](docs/technical/DATA_MODEL.md) - Database schema, relationships, caching strategies  
- [`docs/technical/SECURITY.md`](docs/technical/SECURITY.md) - Multi-layered security implementation and guidelines
- [`docs/technical/SECURITY_UPGRADE_CHANGELOG.md`](docs/technical/SECURITY_UPGRADE_CHANGELOG.md) - Framework security upgrades

#### **🛠️ Development Workflows** ([docs/development/](docs/development/))
- [`docs/development/LOCAL_DEVELOPMENT_GUIDE.md`](docs/development/LOCAL_DEVELOPMENT_GUIDE.md) - Daily development commands and ultra-simple workflow
- [`docs/development/ENVIRONMENT_SETUP.md`](docs/development/ENVIRONMENT_SETUP.md) - Environment variables for all deployment environments
- [`docs/development/SECURITY_GUARD_RAILS.md`](docs/development/SECURITY_GUARD_RAILS.md) - Security system with bypass methods for development
- [`docs/development/BUILD_TROUBLESHOOTING.md`](docs/development/BUILD_TROUBLESHOOTING.md) - Complete troubleshooting for build and deployment issues
- [`docs/development/WORKFLOWS.md`](docs/development/WORKFLOWS.md) - Git workflows, development setup, IDE configuration
- [`docs/development/TESTING.md`](docs/development/TESTING.md) - Testing strategies, environment setup, best practices
- [`docs/development/TROUBLESHOOTING.md`](docs/development/TROUBLESHOOTING.md) - Common issues, solutions, debugging procedures

#### **🚀 Deployment & Infrastructure** ([docs/deployment/](docs/deployment/))
- [`docs/deployment/AWS_AMPLIFY_DEPLOYMENT_GUIDE.md`](docs/deployment/AWS_AMPLIFY_DEPLOYMENT_GUIDE.md) - **NEW: Complete step-by-step AWS Amplify deployment tutorial**
- [`docs/deployment/AWS_AMPLIFY_SETUP.md`](docs/deployment/AWS_AMPLIFY_SETUP.md) - Complete AWS Amplify hosting setup with security headers
- [`docs/deployment/CI_CD.md`](docs/deployment/CI_CD.md) - Continuous integration and deployment pipelines
- [`docs/deployment/DEPLOYMENT_GUIDE.md`](docs/deployment/DEPLOYMENT_GUIDE.md) - Step-by-step deployment procedures
- [`docs/deployment/ENVIRONMENT_VARIABLES.md`](docs/deployment/ENVIRONMENT_VARIABLES.md) - Environment configuration for all environments
- [`docs/deployment/GITHUB_WORKFLOW_FIXES.md`](docs/deployment/GITHUB_WORKFLOW_FIXES.md) - CI/CD pipeline fixes and configuration

#### **📚 Reference Materials** ([docs/reference/](docs/reference/))
- [`docs/reference/COMPONENT_DEPENDENCY_MATRIX.md`](docs/reference/COMPONENT_DEPENDENCY_MATRIX.md) - Page-to-component mapping
- [`docs/reference/LIBRARIES_AND_UTILITIES.md`](docs/reference/LIBRARIES_AND_UTILITIES.md) - Package dependencies and utilities

#### **⚡ Performance & SEO** ([docs/performance/](docs/performance/))
- [`docs/performance/PERFORMANCE_SEO.md`](docs/performance/PERFORMANCE_SEO.md) - Core Web Vitals, SEO strategies, optimization

#### **🔒 Security Documentation** ([docs/security/](docs/security/))
- [`docs/security/SECURITY_POLICY.md`](docs/security/SECURITY_POLICY.md) - Vulnerability disclosure and security policies

#### **📦 Archive & Historical Content** ([docs/archive/](docs/archive/))
- `docs/archive/completed_features/` - Archived feature implementation documentation
- `docs/archive/development_notes/` - Historical development notes and analysis
- `docs/archive/historical/` - Original project documentation and historical records

#### **Quick Reference Commands**

**Daily Development Workflow:**
```bash
# Fresh start for development (most common)
npm run clean && NODE_ENV=test npm run build && npm run start

# Production testing with authentication disabled
NODE_ENV=test npm run build && npm run start

# Complete reset when issues persist
rm -rf .next node_modules && npm install && NODE_ENV=test npm run build && npm run start
```

**Emergency Troubleshooting:**
```bash
# Security guard-rail bypass
NODE_ENV=test npm run build

# Kill processes on port 3000  
lsof -ti:3000 | xargs kill -9

# Memory issues
NODE_OPTIONS="--max-old-space-size=4096" npm run build

# Nuclear reset
rm -rf .next node_modules package-lock.json && npm install
```

**See the individual documentation files for comprehensive step-by-step guidance on each topic.**

## Memory: Comprehensive Documentation Suite

I have created a complete documentation suite for the Cashback Deals v2 codebase to assist with onboarding and development. Each file provides detailed technical guidance for specific aspects of the system.

### Documentation Files Summary & Links

### 📚 **Comprehensive Documentation Suite**

All technical documentation is organized in the [`docs/`](docs/) directory with logical categorization:

#### **Technical Architecture** ([docs/technical/](docs/technical/))
- [**ARCHITECTURE.md**](docs/technical/ARCHITECTURE.md) - System architecture, technology stack, design patterns
- [**DATA_MODEL.md**](docs/technical/DATA_MODEL.md) - Database schema, relationships, caching strategies  
- [**SECURITY.md**](docs/technical/SECURITY.md) - Multi-layered security implementation and guidelines

#### **Development Workflows** ([docs/development/](docs/development/))
- [**WORKFLOWS.md**](docs/development/WORKFLOWS.md) - Developer onboarding, Git workflows, IDE setup
- [**TESTING.md**](docs/development/TESTING.md) - Testing strategies, environment setup, best practices
- [**TROUBLESHOOTING.md**](docs/development/TROUBLESHOOTING.md) - Common issues, solutions, debugging procedures

#### **Deployment & Infrastructure** ([docs/deployment/](docs/deployment/))
- [**CI_CD.md**](docs/deployment/CI_CD.md) - GitHub Actions workflows, AWS Amplify deployment
- [**DEPLOYMENT_GUIDE.md**](docs/deployment/DEPLOYMENT_GUIDE.md) - Multi-platform deployment procedures

#### **Reference Materials** ([docs/reference/](docs/reference/))
- [**COMPONENT_DEPENDENCY_MATRIX.md**](docs/reference/COMPONENT_DEPENDENCY_MATRIX.md) - Page-to-component mapping
- [**LIBRARIES_AND_UTILITIES.md**](docs/reference/LIBRARIES_AND_UTILITIES.md) - Package dependencies and utilities

#### **Performance & SEO** ([docs/performance/](docs/performance/))
- [**PERFORMANCE_SEO.md**](docs/performance/PERFORMANCE_SEO.md) - Core Web Vitals, SEO strategies, optimization

### 🎯 **Quick Navigation Guide**

**For Architecture Questions**: Use [`technical/ARCHITECTURE.md`](docs/technical/ARCHITECTURE.md) for system design and patterns
**For Database Work**: Use [`technical/DATA_MODEL.md`](docs/technical/DATA_MODEL.md) for schema and relationships
**For New Developer Setup**: Use [`development/WORKFLOWS.md`](docs/development/WORKFLOWS.md) for onboarding
**For Testing Issues**: Use [`development/TESTING.md`](docs/development/TESTING.md) for test setup and debugging
**For Security Concerns**: Use [`technical/SECURITY.md`](docs/technical/SECURITY.md) for authentication and security
**For Deployment Issues**: Use [`deployment/CI_CD.md`](docs/deployment/CI_CD.md) and [`deployment/DEPLOYMENT_GUIDE.md`](docs/deployment/DEPLOYMENT_GUIDE.md)
**For Performance Problems**: Use [`performance/PERFORMANCE_SEO.md`](docs/performance/PERFORMANCE_SEO.md) for optimization
**For Debugging**: Use [`development/TROUBLESHOOTING.md`](docs/development/TROUBLESHOOTING.md) for solutions
**For Dependencies**: Use [`reference/LIBRARIES_AND_UTILITIES.md`](docs/reference/LIBRARIES_AND_UTILITIES.md) for packages
**For Component Mapping**: Use [`reference/COMPONENT_DEPENDENCY_MATRIX.md`](docs/reference/COMPONENT_DEPENDENCY_MATRIX.md)

### 🚀 **Quick Reference for Common Tasks**

- **Adding new features**: [`technical/ARCHITECTURE.md`](docs/technical/ARCHITECTURE.md) → [`technical/DATA_MODEL.md`](docs/technical/DATA_MODEL.md) → [`development/TESTING.md`](docs/development/TESTING.md)
- **Fixing bugs**: [`development/TROUBLESHOOTING.md`](docs/development/TROUBLESHOOTING.md) first, then relevant technical docs
- **Performance issues**: [`performance/PERFORMANCE_SEO.md`](docs/performance/PERFORMANCE_SEO.md) and [`development/TROUBLESHOOTING.md`](docs/development/TROUBLESHOOTING.md)
- **Security concerns**: [`technical/SECURITY.md`](docs/technical/SECURITY.md) and [`development/TESTING.md`](docs/development/TESTING.md) security sections
- **Deployment problems**: [`deployment/DEPLOYMENT_GUIDE.md`](docs/deployment/DEPLOYMENT_GUIDE.md) → [`deployment/CI_CD.md`](docs/deployment/CI_CD.md) → [`development/TROUBLESHOOTING.md`](docs/development/TROUBLESHOOTING.md)
- **New developer onboarding**: [`development/WORKFLOWS.md`](docs/development/WORKFLOWS.md) → [`technical/ARCHITECTURE.md`](docs/technical/ARCHITECTURE.md) → [`docs/README.md`](docs/README.md)

## Memory: Security Upgrade - Next.js 15.3.5 + React 19.1.0

**Date:** July 9, 2025  
**Status:** ✅ Successfully Completed

### Major Framework Security Upgrade
Successfully completed comprehensive security upgrade from Next.js 15.1.4 → 15.3.5 and React 19.0.0 → 19.1.0 with all runtime issues resolved and UI functionality maintained.

**Key Changes:**
- **Security patches** applied via Next.js 15.3.5
- **React 19.1.0 stable release** with performance improvements
- **TailwindCSS 4.x compatibility** via @tailwindcss/postcss
- **Enhanced Content Security Policy** for Sentry integration
- **Fixed React 19.1.0 Image component warnings**

**Files Modified:**
- `package.json` - Framework version updates
- `postcss.config.mjs` - TailwindCSS 4.x PostCSS plugin fix
- `tailwind.config.ts` - Fixed darkMode TypeScript syntax
- `next.config.js` - Enhanced CSP for Sentry domains + worker-src
- `src/components/FeaturedProductCard.tsx` - Fixed Image positioning
- `jest.config.js` - Excluded Playwright tests from Jest

**New Infrastructure:**
- `.github/workflows/ci.yml` - CI pipeline with Node 18.x/20.x/22.x matrix
- `amplify.yml` - AWS Amplify deployment configuration

**Verification Results:**
- ✅ UI renders correctly with all sections displaying
- ✅ No CSP violations in console logs
- ✅ Data fetching works (Products, Promotions, Brands, Retailers)
- ✅ Server stable on Next.js 15.3.5 + React 19.1.0
- ✅ All tests pass and build succeeds

**📋 Complete Details:** See [SECURITY_UPGRADE_CHANGELOG.md](SECURITY_UPGRADE_CHANGELOG.md) for comprehensive documentation of all changes, rollback procedures, and security impact analysis.

**Next Actions:**
- Monitor production for any runtime issues
- Plan Node.js 22.x migration before Sep 15, 2025
- Set up automated security scanning for future vulnerabilities

## Task Master AI Instructions
**Import Task Master's development workflow commands and guidelines, treat as if import is in the main CLAUDE.md file.**
@./.taskmaster/CLAUDE.md
