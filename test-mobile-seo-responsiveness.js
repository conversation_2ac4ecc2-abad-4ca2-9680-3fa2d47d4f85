/**
 * Test Script 8: Mobile SEO & Responsiveness Test
 * Target: Samsung product page - Mobile optimization validation
 * Testing: Viewport, responsive design, touch targets, mobile images
 */

const { chromium } = require('playwright');

// Test configuration
const TARGET_URL = 'http://localhost:3001/products/samsung-series-5-nq5b5763dbk-compact-oven-with-microwave-combi-clean-black-nq5b5763dbku4';

// Mobile viewport configurations
const MOBILE_VIEWPORTS = [
  { 
    name: 'iPhone SE', 
    width: 375, 
    height: 667, 
    deviceScaleFactor: 2,
    isMobile: true,
    hasTouch: true
  },
  { 
    name: 'iPhone 12 Pro', 
    width: 390, 
    height: 844, 
    deviceScaleFactor: 3,
    isMobile: true,
    hasTouch: true
  },
  { 
    name: 'Samsung Galaxy S20', 
    width: 360, 
    height: 800, 
    deviceScaleFactor: 3,
    isMobile: true,
    hasTouch: true
  },
  { 
    name: 'iPad', 
    width: 768, 
    height: 1024, 
    deviceScaleFactor: 2,
    isMobile: true,
    hasTouch: true
  },
  { 
    name: 'iPad Pro', 
    width: 1024, 
    height: 1366, 
    deviceScaleFactor: 2,
    isMobile: true,
    hasTouch: true
  }
];

// Test results storage
let testResults = {
  viewport: { score: 0, details: [], passed: 0, total: 0 },
  responsiveDesign: { score: 0, details: [], passed: 0, total: 0 },
  touchTargets: { score: 0, details: [], passed: 0, total: 0 },
  responsiveImages: { score: 0, details: [], passed: 0, total: 0 },
  mobilePerformance: { score: 0, details: [], passed: 0, total: 0 },
  mobileUsability: { score: 0, details: [], passed: 0, total: 0 }
};

async function runMobileSEOTest() {
  console.log('🔍 Test Script 8: Mobile SEO & Responsiveness Test');
  console.log('=' .repeat(60));
  console.log(`Target URL: ${TARGET_URL}`);
  console.log(`Testing ${MOBILE_VIEWPORTS.length} mobile viewports\n`);

  const browser = await chromium.launch({ 
    headless: false,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    // Test 1: Viewport Meta Tag Validation
    await testViewportConfiguration(browser);
    
    // Test 2: Responsive Design Testing
    await testResponsiveDesign(browser);
    
    // Test 3: Touch Target Validation
    await testTouchTargets(browser);
    
    // Test 4: Responsive Images Testing
    await testResponsiveImages(browser);
    
    // Test 5: Mobile Performance Testing
    await testMobilePerformance(browser);
    
    // Test 6: Mobile Usability Testing
    await testMobileUsability(browser);
    
    // Generate final report
    generateFinalReport();
    
  } catch (error) {
    console.error('❌ Test execution failed:', error);
  } finally {
    await browser.close();
  }
}

async function testViewportConfiguration(browser) {
  console.log('📱 Test 1: Viewport Meta Tag Validation');
  console.log('-'.repeat(40));
  
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    await page.goto(TARGET_URL);
    
    // Check viewport meta tag
    const viewportMeta = await page.locator('meta[name="viewport"]').getAttribute('content');
    
    if (viewportMeta) {
      testResults.viewport.passed++;
      testResults.viewport.details.push('✅ Viewport meta tag found');
      
      // Validate viewport content
      const hasWidthDevice = viewportMeta.includes('width=device-width');
      const hasInitialScale = viewportMeta.includes('initial-scale=1');
      
      if (hasWidthDevice) {
        testResults.viewport.passed++;
        testResults.viewport.details.push('✅ width=device-width configured');
      } else {
        testResults.viewport.details.push('❌ Missing width=device-width');
      }
      
      if (hasInitialScale) {
        testResults.viewport.passed++;
        testResults.viewport.details.push('✅ initial-scale=1 configured');
      } else {
        testResults.viewport.details.push('❌ Missing initial-scale=1');
      }
      
      console.log(`   Viewport content: ${viewportMeta}`);
      
    } else {
      testResults.viewport.details.push('❌ Viewport meta tag missing');
    }
    
    testResults.viewport.total = 3;
    testResults.viewport.score = (testResults.viewport.passed / testResults.viewport.total) * 100;
    
    console.log(`   Score: ${testResults.viewport.score.toFixed(1)}/100\n`);
    
  } catch (error) {
    console.error('   Error testing viewport:', error);
  } finally {
    await context.close();
  }
}

async function testResponsiveDesign(browser) {
  console.log('📐 Test 2: Responsive Design Validation');
  console.log('-'.repeat(40));
  
  for (const viewport of MOBILE_VIEWPORTS) {
    console.log(`   Testing ${viewport.name} (${viewport.width}x${viewport.height})`);
    
    const context = await browser.newContext({
      viewport: { width: viewport.width, height: viewport.height },
      deviceScaleFactor: viewport.deviceScaleFactor,
      isMobile: viewport.isMobile,
      hasTouch: viewport.hasTouch
    });
    
    const page = await context.newPage();
    
    try {
      await page.goto(TARGET_URL);
      await page.waitForLoadState('networkidle');
      
      // Check for horizontal scrollbar
      const hasHorizontalScroll = await page.evaluate(() => {
        return document.documentElement.scrollWidth > window.innerWidth;
      });
      
      if (!hasHorizontalScroll) {
        testResults.responsiveDesign.passed++;
        testResults.responsiveDesign.details.push(`✅ ${viewport.name}: No horizontal scroll`);
      } else {
        testResults.responsiveDesign.details.push(`❌ ${viewport.name}: Horizontal scroll detected`);
      }
      
      // Check main content visibility
      const mainContent = await page.locator('main').isVisible();
      if (mainContent) {
        testResults.responsiveDesign.passed++;
        testResults.responsiveDesign.details.push(`✅ ${viewport.name}: Main content visible`);
      } else {
        testResults.responsiveDesign.details.push(`❌ ${viewport.name}: Main content not visible`);
      }
      
      // Check navigation accessibility
      const navVisible = await page.locator('nav, header').first().isVisible();
      if (navVisible) {
        testResults.responsiveDesign.passed++;
        testResults.responsiveDesign.details.push(`✅ ${viewport.name}: Navigation accessible`);
      } else {
        testResults.responsiveDesign.details.push(`❌ ${viewport.name}: Navigation not accessible`);
      }
      
    } catch (error) {
      console.error(`   Error testing ${viewport.name}:`, error);
    } finally {
      await context.close();
    }
  }
  
  testResults.responsiveDesign.total = MOBILE_VIEWPORTS.length * 3;
  testResults.responsiveDesign.score = (testResults.responsiveDesign.passed / testResults.responsiveDesign.total) * 100;
  
  console.log(`   Score: ${testResults.responsiveDesign.score.toFixed(1)}/100\n`);
}

async function testTouchTargets(browser) {
  console.log('👆 Test 3: Touch Target Validation');
  console.log('-'.repeat(40));
  
  // Test on iPhone 12 Pro as representative mobile device
  const viewport = MOBILE_VIEWPORTS[1]; // iPhone 12 Pro
  const context = await browser.newContext({
    viewport: { width: viewport.width, height: viewport.height },
    deviceScaleFactor: viewport.deviceScaleFactor,
    isMobile: viewport.isMobile,
    hasTouch: viewport.hasTouch
  });
  
  const page = await context.newPage();
  
  try {
    await page.goto(TARGET_URL);
    await page.waitForLoadState('networkidle');
    
    // Define touch target selectors
    const touchTargets = [
      { selector: 'button', name: 'Buttons' },
      { selector: 'a', name: 'Links' },
      { selector: '[role="button"]', name: 'Interactive elements' },
      { selector: 'input', name: 'Input fields' },
      { selector: '.clickable, [onClick]', name: 'Clickable elements' }
    ];
    
    for (const target of touchTargets) {
      const elements = await page.locator(target.selector).all();
      let validTargets = 0;
      let totalTargets = elements.length;
      
      for (const element of elements) {
        try {
          const box = await element.boundingBox();
          if (box) {
            // Check minimum touch target size (44px recommended)
            const minDimension = Math.min(box.width, box.height);
            if (minDimension >= 44) {
              validTargets++;
            }
          }
        } catch (e) {
          // Element might not be visible
        }
      }
      
      if (totalTargets > 0) {
        const percentage = (validTargets / totalTargets) * 100;
        testResults.touchTargets.passed += validTargets;
        testResults.touchTargets.total += totalTargets;
        
        if (percentage >= 80) {
          testResults.touchTargets.details.push(`✅ ${target.name}: ${validTargets}/${totalTargets} (${percentage.toFixed(1)}%) adequate size`);
        } else {
          testResults.touchTargets.details.push(`⚠️ ${target.name}: ${validTargets}/${totalTargets} (${percentage.toFixed(1)}%) adequate size`);
        }
        
        console.log(`   ${target.name}: ${validTargets}/${totalTargets} targets ≥44px`);
      }
    }
    
    testResults.touchTargets.score = testResults.touchTargets.total > 0 ? 
      (testResults.touchTargets.passed / testResults.touchTargets.total) * 100 : 100;
    
    console.log(`   Score: ${testResults.touchTargets.score.toFixed(1)}/100\n`);
    
  } catch (error) {
    console.error('   Error testing touch targets:', error);
  } finally {
    await context.close();
  }
}

async function testResponsiveImages(browser) {
  console.log('🖼️ Test 4: Responsive Images Testing');
  console.log('-'.repeat(40));
  
  const viewport = MOBILE_VIEWPORTS[1]; // iPhone 12 Pro
  const context = await browser.newContext({
    viewport: { width: viewport.width, height: viewport.height },
    deviceScaleFactor: viewport.deviceScaleFactor,
    isMobile: viewport.isMobile,
    hasTouch: viewport.hasTouch
  });
  
  const page = await context.newPage();
  
  try {
    await page.goto(TARGET_URL);
    await page.waitForLoadState('networkidle');
    
    // Test Next.js Image components
    const images = await page.locator('img').all();
    let responsiveImages = 0;
    let lazyImages = 0;
    let optimizedImages = 0;
    
    for (const img of images) {
      try {
        const src = await img.getAttribute('src');
        const sizes = await img.getAttribute('sizes');
        const srcset = await img.getAttribute('srcset');
        const loading = await img.getAttribute('loading');
        
        // Check for responsive features
        if (sizes || srcset) {
          responsiveImages++;
        }
        
        // Check for lazy loading
        if (loading === 'lazy') {
          lazyImages++;
        }
        
        // Check for Next.js optimization
        if (src && src.includes('_next/image')) {
          optimizedImages++;
        }
        
      } catch (e) {
        // Skip problematic images
      }
    }
    
    const totalImages = images.length;
    
    if (totalImages > 0) {
      const responsivePercentage = (responsiveImages / totalImages) * 100;
      const lazyPercentage = (lazyImages / totalImages) * 100;
      const optimizedPercentage = (optimizedImages / totalImages) * 100;
      
      testResults.responsiveImages.total = 3;
      
      // Score responsive images
      if (responsivePercentage >= 80) {
        testResults.responsiveImages.passed++;
        testResults.responsiveImages.details.push(`✅ Responsive images: ${responsiveImages}/${totalImages} (${responsivePercentage.toFixed(1)}%)`);
      } else {
        testResults.responsiveImages.details.push(`❌ Responsive images: ${responsiveImages}/${totalImages} (${responsivePercentage.toFixed(1)}%)`);
      }
      
      // Score lazy loading
      if (lazyPercentage >= 50) {
        testResults.responsiveImages.passed++;
        testResults.responsiveImages.details.push(`✅ Lazy loading: ${lazyImages}/${totalImages} (${lazyPercentage.toFixed(1)}%)`);
      } else {
        testResults.responsiveImages.details.push(`❌ Lazy loading: ${lazyImages}/${totalImages} (${lazyPercentage.toFixed(1)}%)`);
      }
      
      // Score optimization
      if (optimizedPercentage >= 80) {
        testResults.responsiveImages.passed++;
        testResults.responsiveImages.details.push(`✅ Next.js optimization: ${optimizedImages}/${totalImages} (${optimizedPercentage.toFixed(1)}%)`);
      } else {
        testResults.responsiveImages.details.push(`❌ Next.js optimization: ${optimizedImages}/${totalImages} (${optimizedPercentage.toFixed(1)}%)`);
      }
      
      console.log(`   Total images: ${totalImages}`);
      console.log(`   Responsive: ${responsiveImages} (${responsivePercentage.toFixed(1)}%)`);
      console.log(`   Lazy loading: ${lazyImages} (${lazyPercentage.toFixed(1)}%)`);
      console.log(`   Optimized: ${optimizedImages} (${optimizedPercentage.toFixed(1)}%)`);
    }
    
    testResults.responsiveImages.score = testResults.responsiveImages.total > 0 ? 
      (testResults.responsiveImages.passed / testResults.responsiveImages.total) * 100 : 100;
    
    console.log(`   Score: ${testResults.responsiveImages.score.toFixed(1)}/100\n`);
    
  } catch (error) {
    console.error('   Error testing responsive images:', error);
  } finally {
    await context.close();
  }
}

async function testMobilePerformance(browser) {
  console.log('⚡ Test 5: Mobile Performance Testing');
  console.log('-'.repeat(40));
  
  const viewport = MOBILE_VIEWPORTS[1]; // iPhone 12 Pro
  const context = await browser.newContext({
    viewport: { width: viewport.width, height: viewport.height },
    deviceScaleFactor: viewport.deviceScaleFactor,
    isMobile: viewport.isMobile,
    hasTouch: viewport.hasTouch
  });
  
  const page = await context.newPage();
  
  try {
    // Simulate slower mobile connection
    await page.route('**/*', route => {
      setTimeout(() => route.continue(), 50); // Add 50ms delay
    });
    
    const startTime = Date.now();
    await page.goto(TARGET_URL);
    await page.waitForLoadState('networkidle');
    const loadTime = Date.now() - startTime;
    
    // Test Core Web Vitals
    const webVitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals = {};
        
        // Largest Contentful Paint
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          if (entries.length > 0) {
            vitals.lcp = entries[entries.length - 1].startTime;
          }
        }).observe({ entryTypes: ['largest-contentful-paint'] });
        
        // First Input Delay would require actual user interaction
        // Cumulative Layout Shift
        new PerformanceObserver((list) => {
          let cls = 0;
          list.getEntries().forEach((entry) => {
            if (!entry.hadRecentInput) {
              cls += entry.value;
            }
          });
          vitals.cls = cls;
        }).observe({ entryTypes: ['layout-shift'] });
        
        setTimeout(() => resolve(vitals), 3000);
      });
    });
    
    testResults.mobilePerformance.total = 3;
    
    // Score load time
    if (loadTime < 3000) {
      testResults.mobilePerformance.passed++;
      testResults.mobilePerformance.details.push(`✅ Page load time: ${loadTime}ms (< 3s)`);
    } else {
      testResults.mobilePerformance.details.push(`❌ Page load time: ${loadTime}ms (≥ 3s)`);
    }
    
    // Score LCP
    if (webVitals.lcp && webVitals.lcp < 2500) {
      testResults.mobilePerformance.passed++;
      testResults.mobilePerformance.details.push(`✅ LCP: ${webVitals.lcp.toFixed(0)}ms (< 2.5s)`);
    } else {
      testResults.mobilePerformance.details.push(`❌ LCP: ${webVitals.lcp ? webVitals.lcp.toFixed(0) : 'N/A'}ms (≥ 2.5s)`);
    }
    
    // Score CLS
    if (webVitals.cls !== undefined && webVitals.cls < 0.1) {
      testResults.mobilePerformance.passed++;
      testResults.mobilePerformance.details.push(`✅ CLS: ${webVitals.cls.toFixed(3)} (< 0.1)`);
    } else {
      testResults.mobilePerformance.details.push(`❌ CLS: ${webVitals.cls ? webVitals.cls.toFixed(3) : 'N/A'} (≥ 0.1)`);
    }
    
    console.log(`   Load time: ${loadTime}ms`);
    console.log(`   LCP: ${webVitals.lcp ? webVitals.lcp.toFixed(0) : 'N/A'}ms`);
    console.log(`   CLS: ${webVitals.cls ? webVitals.cls.toFixed(3) : 'N/A'}`);
    
    testResults.mobilePerformance.score = (testResults.mobilePerformance.passed / testResults.mobilePerformance.total) * 100;
    
    console.log(`   Score: ${testResults.mobilePerformance.score.toFixed(1)}/100\n`);
    
  } catch (error) {
    console.error('   Error testing mobile performance:', error);
  } finally {
    await context.close();
  }
}

async function testMobileUsability(browser) {
  console.log('📱 Test 6: Mobile Usability Testing');
  console.log('-'.repeat(40));
  
  const viewport = MOBILE_VIEWPORTS[1]; // iPhone 12 Pro
  const context = await browser.newContext({
    viewport: { width: viewport.width, height: viewport.height },
    deviceScaleFactor: viewport.deviceScaleFactor,
    isMobile: viewport.isMobile,
    hasTouch: viewport.hasTouch
  });
  
  const page = await context.newPage();
  
  try {
    await page.goto(TARGET_URL);
    await page.waitForLoadState('networkidle');
    
    testResults.mobileUsability.total = 5;
    
    // Test font size readability
    const bodyFontSize = await page.evaluate(() => {
      const body = document.body;
      return parseInt(window.getComputedStyle(body).fontSize);
    });
    
    if (bodyFontSize >= 16) {
      testResults.mobileUsability.passed++;
      testResults.mobileUsability.details.push(`✅ Font size: ${bodyFontSize}px (≥ 16px)`);
    } else {
      testResults.mobileUsability.details.push(`❌ Font size: ${bodyFontSize}px (< 16px)`);
    }
    
    // Test mobile navigation
    const mobileNav = await page.locator('button[aria-label*="menu"], .mobile-menu, .hamburger').count();
    if (mobileNav > 0) {
      testResults.mobileUsability.passed++;
      testResults.mobileUsability.details.push('✅ Mobile navigation found');
    } else {
      testResults.mobileUsability.details.push('❌ Mobile navigation not found');
    }
    
    // Test form usability
    const forms = await page.locator('form, input').count();
    if (forms > 0) {
      const inputTypes = await page.locator('input[type="email"], input[type="tel"], input[type="number"]').count();
      if (inputTypes > 0) {
        testResults.mobileUsability.passed++;
        testResults.mobileUsability.details.push('✅ Mobile-optimized input types found');
      } else {
        testResults.mobileUsability.details.push('⚠️ Consider mobile-optimized input types');
      }
    } else {
      testResults.mobileUsability.passed++; // No forms to test
      testResults.mobileUsability.details.push('✅ No forms to validate');
    }
    
    // Test text contrast and readability
    const hasGoodContrast = await page.evaluate(() => {
      // Simple contrast check
      const elements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span');
      let goodContrast = 0;
      
      for (let i = 0; i < Math.min(elements.length, 10); i++) {
        const element = elements[i];
        const styles = window.getComputedStyle(element);
        const color = styles.color;
        const backgroundColor = styles.backgroundColor;
        
        // Basic check - if text is visible, assume good contrast
        if (color !== backgroundColor) {
          goodContrast++;
        }
      }
      
      return goodContrast >= Math.min(elements.length, 10) * 0.8;
    });
    
    if (hasGoodContrast) {
      testResults.mobileUsability.passed++;
      testResults.mobileUsability.details.push('✅ Text contrast appears adequate');
    } else {
      testResults.mobileUsability.details.push('❌ Text contrast may be insufficient');
    }
    
    // Test scroll performance
    await page.evaluate(() => {
      window.scrollBy(0, 500);
    });
    
    await page.waitForTimeout(100);
    
    const scrollPosition = await page.evaluate(() => window.pageYOffset);
    if (scrollPosition > 0) {
      testResults.mobileUsability.passed++;
      testResults.mobileUsability.details.push('✅ Smooth scrolling works');
    } else {
      testResults.mobileUsability.details.push('❌ Scrolling issues detected');
    }
    
    testResults.mobileUsability.score = (testResults.mobileUsability.passed / testResults.mobileUsability.total) * 100;
    
    console.log(`   Font size: ${bodyFontSize}px`);
    console.log(`   Mobile nav elements: ${mobileNav}`);
    console.log(`   Form elements: ${forms}`);
    console.log(`   Score: ${testResults.mobileUsability.score.toFixed(1)}/100\n`);
    
  } catch (error) {
    console.error('   Error testing mobile usability:', error);
  } finally {
    await context.close();
  }
}

function generateFinalReport() {
  console.log('📊 MOBILE SEO & RESPONSIVENESS TEST RESULTS');
  console.log('='.repeat(60));
  
  const categories = [
    { name: 'Viewport Configuration', key: 'viewport' },
    { name: 'Responsive Design', key: 'responsiveDesign' },
    { name: 'Touch Targets', key: 'touchTargets' },
    { name: 'Responsive Images', key: 'responsiveImages' },
    { name: 'Mobile Performance', key: 'mobilePerformance' },
    { name: 'Mobile Usability', key: 'mobileUsability' }
  ];
  
  let totalScore = 0;
  let weightedTotal = 0;
  
  categories.forEach(category => {
    const result = testResults[category.key];
    const weight = category.key === 'responsiveDesign' || category.key === 'mobilePerformance' ? 2 : 1;
    
    console.log(`\n${category.name}: ${result.score.toFixed(1)}/100`);
    result.details.forEach(detail => console.log(`  ${detail}`));
    
    totalScore += result.score * weight;
    weightedTotal += 100 * weight;
  });
  
  const overallScore = totalScore / weightedTotal * 100;
  
  console.log('\n' + '='.repeat(60));
  console.log(`📱 OVERALL MOBILE SEO SCORE: ${overallScore.toFixed(1)}/100`);
  console.log('='.repeat(60));
  
  // Recommendations
  console.log('\n💡 RECOMMENDATIONS:');
  
  if (testResults.viewport.score < 100) {
    console.log('• Fix viewport meta tag configuration');
  }
  
  if (testResults.responsiveDesign.score < 90) {
    console.log('• Review responsive design for all viewports');
  }
  
  if (testResults.touchTargets.score < 80) {
    console.log('• Increase touch target sizes to minimum 44px');
  }
  
  if (testResults.responsiveImages.score < 90) {
    console.log('• Implement responsive images with srcset/sizes');
  }
  
  if (testResults.mobilePerformance.score < 80) {
    console.log('• Optimize mobile performance and Core Web Vitals');
  }
  
  if (testResults.mobileUsability.score < 90) {
    console.log('• Improve mobile usability and accessibility');
  }
  
  if (overallScore >= 90) {
    console.log('🎉 Excellent mobile optimization!');
  } else if (overallScore >= 80) {
    console.log('✅ Good mobile optimization with room for improvement');
  } else {
    console.log('⚠️ Mobile optimization needs significant improvement');
  }
}

// Run the test
runMobileSEOTest().catch(console.error);