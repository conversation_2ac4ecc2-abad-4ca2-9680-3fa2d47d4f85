#!/usr/bin/env node

/**
 * JIRA Batch Issue Creator
 * Processes the bulk export JSON and creates JIRA issues in batches
 * Usage: node jira-batch-processor.js
 */

const fs = require('fs');
const path = require('path');

// Load the bulk export data
const exportData = JSON.parse(fs.readFileSync('./jira-tickets-bulk-export.json', 'utf8'));

// Simulate the MCP JIRA creation (replace with actual MCP calls)
async function createJiraIssue(issueData) {
  // This would be replaced with actual MCP atlassian createJiraIssue calls
  console.log(`Creating ${issueData.issue_type}: ${issueData.summary}`);
  
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  return {
    success: true,
    key: `CAS-${Math.floor(Math.random() * 1000)}`,
    ...issueData
  };
}

// Convert our JSON structure to JIRA-compatible format
function convertToJiraFormat(item) {
  const baseIssue = {
    cloudId: "https://rebateray.atlassian.net", // Replace with actual cloud ID
    projectKey: exportData.project_key,
    issueTypeName: item.issue_type,
    summary: item.summary,
    description: formatDescription(item),
    priority: item.priority
  };

  // Add assignee if specified
  if (item.assignee) {
    baseIssue.assignee_account_id = item.assignee;
  }

  // Add additional fields for Stories and Tasks
  if (item.parent_epic) {
    baseIssue.additional_fields = {
      ...baseIssue.additional_fields,
      "Epic Link": item.parent_epic
    };
  }

  if (item.story_points) {
    baseIssue.additional_fields = {
      ...baseIssue.additional_fields,
      "Story Points": item.story_points
    };
  }

  if (item.sprint) {
    baseIssue.additional_fields = {
      ...baseIssue.additional_fields,
      "Sprint": item.sprint
    };
  }

  return baseIssue;
}

function formatDescription(item) {
  let description = item.description || '';
  
  // Add Gherkin acceptance criteria
  if (item.acceptance_criteria_gherkin) {
    description += '\n\n## Acceptance Criteria (Gherkin)\n\n';
    description += '```gherkin\n';
    description += 'Feature: ' + item.summary + '\n\n';
    description += 'Scenario: ' + item.summary.replace(/^(Implement|Create|Add|Build)/, 'User can use') + '\n';
    item.acceptance_criteria_gherkin.forEach(criteria => {
      description += '  ' + criteria + '\n';
    });
    description += '```\n';
  }

  // Add technical details
  if (item.technical_details) {
    description += '\n\n## Technical Details\n\n';
    description += `**Component**: ${item.technical_details.component}\n`;
    description += `**Implementation**: ${item.technical_details.implementation}\n`;
    if (item.technical_details.dependencies) {
      description += `**Dependencies**: ${item.technical_details.dependencies.join(', ')}\n`;
    }
  }

  // Add business value for Epics
  if (item.business_value) {
    description += '\n\n## Business Value\n\n' + item.business_value;
  }

  return description;
}

// Collect all items for batch processing
function collectAllItems() {
  const allItems = [];
  
  exportData.epics.forEach(epic => {
    // Add the epic itself
    allItems.push(epic);
    
    // Add stories
    if (epic.stories) {
      epic.stories.forEach(story => {
        allItems.push(story);
        
        // Add tasks
        if (story.tasks) {
          story.tasks.forEach(task => {
            allItems.push(task);
          });
        }
      });
    }
    
    // Add QA/monitoring tasks
    if (epic.qa_monitoring_tasks) {
      epic.qa_monitoring_tasks.forEach(qaTask => {
        allItems.push(qaTask);
      });
    }
  });
  
  return allItems;
}

// Process items in batches
async function processBatches() {
  const allItems = collectAllItems();
  const batchSize = exportData.batch_size || 20;
  const results = [];
  
  console.log(`Processing ${allItems.length} items in batches of ${batchSize}...`);
  
  for (let i = 0; i < allItems.length; i += batchSize) {
    const batch = allItems.slice(i, i + batchSize);
    console.log(`\nProcessing batch ${Math.floor(i/batchSize) + 1} (${batch.length} items)...`);
    
    const batchResults = [];
    
    for (const item of batch) {
      try {
        const jiraIssue = convertToJiraFormat(item);
        const result = await createJiraIssue(jiraIssue);
        batchResults.push(result);
        console.log(`✅ Created: ${result.key} - ${item.summary}`);
      } catch (error) {
        console.error(`❌ Failed to create: ${item.summary}`, error.message);
        batchResults.push({ success: false, item, error: error.message });
      }
    }
    
    results.push(...batchResults);
    
    // Rate limiting: wait between batches
    if (i + batchSize < allItems.length) {
      console.log('Waiting 2 seconds before next batch...');
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  return results;
}

// Generate summary report
function generateReport(results) {
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log('\n' + '='.repeat(50));
  console.log('BATCH PROCESSING SUMMARY');
  console.log('='.repeat(50));
  console.log(`Total items processed: ${results.length}`);
  console.log(`Successful: ${successful.length}`);
  console.log(`Failed: ${failed.length}`);
  
  if (failed.length > 0) {
    console.log('\nFailed items:');
    failed.forEach(item => {
      console.log(`- ${item.item?.summary || 'Unknown'}: ${item.error}`);
    });
  }
  
  // Save detailed results
  const reportData = {
    timestamp: new Date().toISOString(),
    summary: {
      total: results.length,
      successful: successful.length,
      failed: failed.length
    },
    successful_items: successful.map(r => ({ key: r.key, summary: r.summary })),
    failed_items: failed.map(r => ({ summary: r.item?.summary, error: r.error }))
  };
  
  fs.writeFileSync('./jira-batch-results.json', JSON.stringify(reportData, null, 2));
  console.log('\nDetailed results saved to: jira-batch-results.json');
}

// Main execution
async function main() {
  try {
    console.log('🚀 Starting JIRA batch issue creation...');
    console.log(`Project: ${exportData.project_key}`);
    console.log(`Total EPICs: ${exportData.epics.length}`);
    
    const results = await processBatches();
    generateReport(results);
    
    console.log('\n✅ Batch processing completed!');
  } catch (error) {
    console.error('💥 Batch processing failed:', error);
    process.exit(1);
  }
}

// Export for use as module or run directly
if (require.main === module) {
  main();
}

module.exports = {
  processBatches,
  convertToJiraFormat,
  createJiraIssue
};