// Jest Environment Setup
// This file loads test-specific environment variables before running tests

const dotenv = require('dotenv');
const path = require('path');

// Load test environment variables
const testEnvPath = path.resolve(__dirname, '.env.test');
const result = dotenv.config({ path: testEnvPath });

if (result.error) {
  console.warn('Warning: Could not load .env.test file:', result.error.message);
  console.warn('Tests will use default environment variables');
} else {
  console.log('✅ Loaded test environment configuration from .env.test');
}

// Set NODE_ENV to test if not already set
if (!process.env.NODE_ENV) {
  process.env.NODE_ENV = 'test';
}

// Ensure critical test environment variables are set
const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY'
];

const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

if (missingVars.length > 0) {
  console.error('❌ Missing required environment variables for tests:');
  missingVars.forEach(varName => {
    console.error(`  - ${varName}`);
  });
  throw new Error('Missing required environment variables for tests');
}

// Log test environment configuration (without sensitive values)
console.log('🧪 Test Environment Configuration:');
console.log(`  NODE_ENV: ${process.env.NODE_ENV}`);
console.log(`  ENABLE_HMAC_AUTH: ${process.env.ENABLE_HMAC_AUTH}`);
console.log(`  ENABLE_SEARCH_AUTH: ${process.env.ENABLE_SEARCH_AUTH}`);
console.log(`  DISABLE_HMAC_VALIDATE: ${process.env.DISABLE_HMAC_VALIDATE}`);
console.log(`  ENABLE_CORS_STRICT: ${process.env.ENABLE_CORS_STRICT}`);
console.log(`  ENABLE_IP_ALLOWLIST: ${process.env.ENABLE_IP_ALLOWLIST}`);
console.log(`  ENABLE_SENTRY: ${process.env.ENABLE_SENTRY}`);